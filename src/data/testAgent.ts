import { Agent<PERSON><PERSON> } from '@/services/agentService';
import { BlockType } from '@/hooks/useAgentBuilder';

/**
 * Realistic Test Agent for AI Agent Builder
 *
 * This test agent demonstrates ONLY the actually implemented block types.
 * It represents a functional day trading strategy using available components:
 * - WHEN_RUN (entry point)
 * - INDICATOR (RSI, SMA, EMA, MACD)
 * - PRICE (close, high, low, volume)
 * - CONDITION (comparisons)
 * - OPERATOR (math operations)
 * - CANDLE_PATTERN (pattern detection)
 * - MOVING_AVERAGE (dedicated component)
 * - MOMENTUM_INDICATOR (dedicated component)
 * - POSITION_SIZE (dedicated component)
 * - TRIGGER (final signals)
 * - BULLISH/BEARISH_CONFIDENCE_BOOST
 * - GenericBlock types (configured properly)
 *
 * Strategy Overview:
 * 1. Price data inputs
 * 2. Technical indicators (RSI, Moving Averages)
 * 3. Conditions and logic
 * 4. Position sizing
 * 5. Final signal generation
 */

export const comprehensiveTestAgent: {
  name: string;
  description: string;
  blocks: AgentBlock[];
  connections: Array<{
    sourceId: string;
    targetId: string;
    sourceHandle: string;
    targetHandle: string;
  }>;
} = {
  name: "Realistic Day Trading Strategy Test",
  description: "A functional test agent using only implemented block types: price data, technical indicators, conditions, position sizing, and signal generation.",

  blocks: [
    // ===== ENTRY POINT =====
    {
      id: "when-run-1",
      type: BlockType.WHEN_RUN,
      position: { x: 100, y: 300 }
    },

    // ===== PRICE DATA BLOCKS =====
    {
      id: "price-close",
      type: BlockType.PRICE,
      position: { x: 300, y: 200 },
      dataPoint: "close",
      timeframe: "5min"
    },
    {
      id: "price-volume",
      type: BlockType.PRICE,
      position: { x: 300, y: 300 },
      dataPoint: "volume",
      timeframe: "5min"
    },

    // ===== TECHNICAL INDICATORS =====
    {
      id: "rsi-indicator",
      type: BlockType.INDICATOR,
      position: { x: 500, y: 150 },
      indicator: "rsi",
      parameters: { period: 14, overbought: 70, oversold: 30 }
    },
    {
      id: "sma-20",
      type: BlockType.INDICATOR,
      position: { x: 500, y: 250 },
      indicator: "sma",
      parameters: { period: 20 }
    },
    {
      id: "ema-50",
      type: BlockType.INDICATOR,
      position: { x: 500, y: 350 },
      indicator: "ema",
      parameters: { period: 50 }
    },

    // ===== MOVING AVERAGE BLOCKS (Dedicated Components) =====
    {
      id: "moving-avg-20",
      type: BlockType.MOVING_AVERAGE,
      position: { x: 300, y: 450 },
      averageType: "sma",
      period: 20,
      timeframe: "5min",
      source: "close"
    },
    {
      id: "moving-avg-50",
      type: BlockType.MOVING_AVERAGE,
      position: { x: 300, y: 550 },
      averageType: "ema",
      period: 50,
      timeframe: "5min",
      source: "close"
    },

    // ===== MOMENTUM INDICATOR BLOCKS (Dedicated Components) =====
    {
      id: "momentum-rsi",
      type: BlockType.MOMENTUM_INDICATOR,
      position: { x: 300, y: 650 },
      indicator: "rsi",
      period: 14,
      timeframe: "5min",
      overbought: 70,
      oversold: 30
    },

    // ===== CANDLE PATTERN BLOCK (Dedicated Component) =====
    {
      id: "candle-pattern",
      type: BlockType.CANDLE_PATTERN,
      position: { x: 500, y: 450 },
      pattern: "any_pattern",
      timeframe: "5min",
      sensitivity: "normal"
    },

    // ===== POSITION SIZE BLOCK (Dedicated Component) =====
    {
      id: "position-size",
      type: BlockType.POSITION_SIZE,
      position: { x: 500, y: 550 },
      method: "risk_based",
      riskPercentage: 1.0,
      accountPercentage: 10.0
    },

    // ===== GENERIC BLOCKS (Using GenericBlock component) =====
    {
      id: "trend-indicator",
      type: BlockType.TREND_INDICATOR,
      position: { x: 700, y: 150 },
      indicator: "macd",
      timeframe: "5min",
      fastPeriod: 12,
      slowPeriod: 26
    },
    {
      id: "volume-indicator",
      type: BlockType.VOLUME_INDICATOR,
      position: { x: 700, y: 250 },
      indicator: "vwap",
      timeframe: "5min"
    },
    {
      id: "volatility-indicator",
      type: BlockType.VOLATILITY_INDICATOR,
      position: { x: 700, y: 350 },
      indicator: "atr",
      period: 14,
      timeframe: "5min"
    },
    {
      id: "time-filter",
      type: BlockType.TIME_FILTER,
      position: { x: 700, y: 450 },
      filterType: "time_of_day",
      startTime: "09:30",
      endTime: "16:00"
    },

    // ===== CONDITIONS (Implemented Components) =====
    {
      id: "rsi-oversold-condition",
      type: BlockType.CONDITION,
      position: { x: 900, y: 150 },
      operator: ">",
      compareValue: 30,
      description: "RSI > 30 (not oversold)"
    },
    {
      id: "rsi-overbought-condition",
      type: BlockType.CONDITION,
      position: { x: 900, y: 250 },
      operator: "<",
      compareValue: 70,
      description: "RSI < 70 (not overbought)"
    },
    {
      id: "price-above-sma-condition",
      type: BlockType.CONDITION,
      position: { x: 900, y: 350 },
      operator: ">",
      compareValue: 0,
      description: "Price > SMA 20"
    },

    // ===== OPERATORS (Math Operations) =====
    {
      id: "volume-average",
      type: BlockType.OPERATOR,
      position: { x: 900, y: 450 },
      operation: "average",
      description: "Volume average calculation"
    },

    // ===== CONFIDENCE BOOST BLOCKS =====
    {
      id: "bullish-boost",
      type: BlockType.BULLISH_CONFIDENCE_BOOST,
      position: { x: 1100, y: 200 },
      percentage: 15
    },
    {
      id: "bearish-boost",
      type: BlockType.BEARISH_CONFIDENCE_BOOST,
      position: { x: 1100, y: 300 },
      percentage: 10
    },

    // ===== FINAL SIGNAL OUTPUTS (SIGNAL blocks) =====
    {
      id: "bullish-signal",
      type: BlockType.SIGNAL,
      position: { x: 1300, y: 200 },
      signal: "bullish",
      confidence: 80,
      description: "Buy signal when conditions align"
    },
    {
      id: "bearish-signal",
      type: BlockType.SIGNAL,
      position: { x: 1300, y: 300 },
      signal: "bearish",
      confidence: 75,
      description: "Sell signal when conditions align"
    },
    {
      id: "neutral-signal",
      type: BlockType.SIGNAL,
      position: { x: 1300, y: 400 },
      signal: "neutral",
      confidence: 60,
      description: "Hold signal when no clear direction"
    }
  ],

  connections: [
    // ===== ENTRY POINT TO PRICE DATA =====
    { sourceId: "when-run-1", targetId: "price-close", sourceHandle: "output", targetHandle: "input" },
    { sourceId: "when-run-1", targetId: "price-volume", sourceHandle: "output", targetHandle: "input" },

    // ===== PRICE DATA TO INDICATORS =====
    { sourceId: "price-close", targetId: "rsi-indicator", sourceHandle: "output", targetHandle: "input" },
    { sourceId: "price-close", targetId: "sma-20", sourceHandle: "output", targetHandle: "input" },
    { sourceId: "price-close", targetId: "ema-50", sourceHandle: "output", targetHandle: "input" },
    { sourceId: "price-close", targetId: "moving-avg-20", sourceHandle: "output", targetHandle: "input" },
    { sourceId: "price-close", targetId: "moving-avg-50", sourceHandle: "output", targetHandle: "input" },
    { sourceId: "price-close", targetId: "momentum-rsi", sourceHandle: "output", targetHandle: "input" },
    { sourceId: "price-close", targetId: "candle-pattern", sourceHandle: "output", targetHandle: "input" },

    // ===== INDICATORS TO GENERIC BLOCKS =====
    { sourceId: "price-close", targetId: "trend-indicator", sourceHandle: "output", targetHandle: "input" },
    { sourceId: "price-volume", targetId: "volume-indicator", sourceHandle: "output", targetHandle: "input" },
    { sourceId: "price-close", targetId: "volatility-indicator", sourceHandle: "output", targetHandle: "input" },

    // ===== INDICATORS TO CONDITIONS =====
    { sourceId: "rsi-indicator", targetId: "rsi-oversold-condition", sourceHandle: "output", targetHandle: "input" },
    { sourceId: "momentum-rsi", targetId: "rsi-overbought-condition", sourceHandle: "output", targetHandle: "input" },
    { sourceId: "price-close", targetId: "price-above-sma-condition", sourceHandle: "output", targetHandle: "input" },
    { sourceId: "sma-20", targetId: "price-above-sma-condition", sourceHandle: "output", targetHandle: "compare" },

    // ===== VOLUME CALCULATIONS =====
    { sourceId: "price-volume", targetId: "volume-average", sourceHandle: "output", targetHandle: "input" },

    // ===== CONDITIONS TO CONFIDENCE BOOSTS =====
    { sourceId: "rsi-oversold-condition", targetId: "bullish-boost", sourceHandle: "output", targetHandle: "input" },
    { sourceId: "rsi-overbought-condition", targetId: "bearish-boost", sourceHandle: "output", targetHandle: "input" },

    // ===== TIME FILTER CONNECTIONS =====
    { sourceId: "when-run-1", targetId: "time-filter", sourceHandle: "output", targetHandle: "input" },

    // ===== POSITION SIZING =====
    { sourceId: "volatility-indicator", targetId: "position-size", sourceHandle: "output", targetHandle: "input" },

    // ===== FINAL SIGNAL GENERATION =====
    { sourceId: "bullish-boost", targetId: "bullish-trigger", sourceHandle: "output", targetHandle: "input" },
    { sourceId: "bearish-boost", targetId: "bearish-trigger", sourceHandle: "output", targetHandle: "input" },
    { sourceId: "time-filter", targetId: "neutral-trigger", sourceHandle: "output", targetHandle: "input" },

    // ===== PATTERN CONFIRMATIONS =====
    { sourceId: "candle-pattern", targetId: "bullish-trigger", sourceHandle: "bullish", targetHandle: "confirmation" },
    { sourceId: "price-above-sma-condition", targetId: "bullish-trigger", sourceHandle: "output", targetHandle: "condition" }
  ]
};
