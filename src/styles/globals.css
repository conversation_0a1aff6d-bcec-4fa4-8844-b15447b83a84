/* Using SF Pro system font */

:root {
  --font-size-base: 14px;
  --line-height-base: 1.5;
}

html {
  font-size: var(--font-size-base);
}

body {
  font-size: 0.875rem;
  line-height: var(--line-height-base);
}

/* Scale down headings */
h1 {
  font-size: 1.75rem;
}

h2 {
  font-size: 1.5rem;
}

h3 {
  font-size: 1.25rem;
}

/* Adjust spacing */
.p-4 {
  padding: 0.75rem;
}

.p-6 {
  padding: 1rem;
}

.gap-4 {
  gap: 0.75rem;
}

/* Adjust button sizes */
.btn, .button {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
}

/* Adjust input sizes */
input, textarea, select {
  font-size: 0.75rem;
  padding: 0.375rem 0.5rem;
}

/* Rest of your CSS */
@keyframes fadeIn {
  from { 
    opacity: 0; 
    transform: translateY(5px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

.drop-shadow-glow {
  filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.5));
}

.backdrop-blur-sm {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.glow-text {
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

@keyframes wave {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-3px); }
}

.animate-wave {
  animation: wave 1.5s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% { filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.4)); }
  50% { filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.6)); }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

@keyframes scan {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes pulse-subtle {
  0% { box-shadow: 0 0 15px rgba(var(--glow-color), 0.3); }
  50% { box-shadow: 0 0 25px rgba(var(--glow-color), 0.5); }
  100% { box-shadow: 0 0 15px rgba(var(--glow-color), 0.3); }
}

@keyframes float {
  0% { transform: translateY(0) translateX(0); opacity: 0; }
  50% { opacity: 0.8; }
  100% { transform: translateY(-20px) translateX(10px); opacity: 0; }
}

.animate-scan {
  animation: scan 1.5s linear infinite;
}

.animate-pulse-subtle {
  animation: pulse-subtle 2s ease-in-out infinite;
}

.animate-float {
  animation: float 3s ease-out infinite;
} 