/**
 * API endpoint for creating Whop charges
 * This handles the server-side payment processing for the trading onboarding
 */

import { whopIntermediaryClient } from '@/lib/whopIntermediaryClient';

export interface CreateChargeRequest {
  amount: number;
  currency?: string;
  description?: string;
  metadata?: Record<string, any>;
}

export interface CreateChargeResponse {
  success: boolean;
  data?: {
    inAppPurchase: {
      id: string;
      planId: string;
    };
    status: 'needs_action' | 'success';
  };
  error?: string;
}

/**
 * Create a Whop charge for the current user
 */
export const createWhopCharge = async (request: CreateChargeRequest): Promise<CreateChargeResponse> => {
  try {
    console.log('💳 Creating Whop charge:', request);

    // Validate request
    if (!request.amount || request.amount <= 0) {
      return {
        success: false,
        error: 'Invalid amount specified'
      };
    }

    // Create charge via intermediary server
    const response = await whopIntermediaryClient.createCharge(
      request.amount,
      request.currency || 'usd',
      request.description || 'Trade Sensei Premium Access'
    );

    if (!response.success) {
      console.error('❌ Failed to create Whop charge:', response.error);
      return {
        success: false,
        error: response.error || 'Failed to create charge'
      };
    }

    console.log('✅ Whop charge created successfully:', response.data);
    return {
      success: true,
      data: response.data
    };

  } catch (error) {
    console.error('❌ Error creating Whop charge:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Verify a payment receipt (for future use)
 */
export const verifyPaymentReceipt = async (receiptId: string): Promise<{
  success: boolean;
  verified?: boolean;
  error?: string;
}> => {
  try {
    console.log('🔍 Verifying payment receipt:', receiptId);

    // This would typically call the Whop API to verify the receipt
    // For now, we'll return a placeholder response
    return {
      success: true,
      verified: true
    };

  } catch (error) {
    console.error('❌ Error verifying payment receipt:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};
