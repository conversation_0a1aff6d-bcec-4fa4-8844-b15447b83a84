import express from 'express';
import { YoutubeTranscript } from 'youtube-transcript-api';
import cors from 'cors';

const router = express.Router();

router.use(cors());

router.get('/api/transcript/:videoId', async (req, res) => {
  try {
    const { videoId } = req.params;
    
    if (!videoId) {
      return res.status(400).json({ error: 'Video ID is required' });
    }

    const transcriptItems = await YoutubeTranscript.fetchTranscript(videoId);
    
    if (!transcriptItems || transcriptItems.length === 0) {
      return res.status(404).json({ error: 'No transcript found for this video' });
    }

    // Combine transcript items into a single text with timestamps
    const transcript = transcriptItems.map(item => 
      `[${formatTime(item.offset)}] ${item.text}`
    ).join('\n');

    res.json({ transcript });
  } catch (error) {
    console.error('Error fetching transcript:', error);
    res.status(500).json({ error: 'Failed to fetch transcript' });
  }
});

// Helper function to format time in MM:SS format
function formatTime(ms: number): string {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}

export default router; 