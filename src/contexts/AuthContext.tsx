import React, { createContext, useState, useEffect, useContext } from 'react';
import { supabase } from "@/integrations/supabase/client";
import { useNavigate } from 'react-router-dom';
import PostAuthHandler from '@/components/auth/PostAuthHandler';

type AuthContextType = {
  isAuthenticated: boolean | null;
  user: any | null;
  signOut: () => Promise<void>;
  isNewUser: boolean | null;
  refreshToken: () => Promise<string | null>;
};

const AuthContext = createContext<AuthContextType>({
  isAuthenticated: null,
  user: null,
  isNewUser: null,
  signOut: async () => {},
  refreshToken: async () => null,
});

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [user, setUser] = useState<any | null>(null);
  const [isNewUser, setIsNewUser] = useState<boolean>(false);
  const [isAuthenticating, setIsAuthenticating] = useState<boolean>(true);

  const getRedirectPath = (currentPath: string) => {
    if (currentPath === '/' || currentPath === '/auth' || !currentPath) {
      return '/';
    }
    return currentPath;
  };

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Get current session
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) throw error;

        if (session) {
          setIsAuthenticated(true);
          setUser(session.user);
        } else {
          setIsAuthenticated(false);
          setUser(null);
        }
      } catch (error) {
        console.error('Auth check error:', error);
        setIsAuthenticated(false);
        setUser(null);
      } finally {
        setIsAuthenticating(false);
      }
    };

    // Initial check
    checkAuth();

    // Subscribe to auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (session) {
          setIsAuthenticated(true);
          setUser(session.user);
        } else {
          setIsAuthenticated(false);
          setUser(null);
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;

      // Update state after successful sign out
      setIsAuthenticated(false);
      setUser(null);
      setIsNewUser(false);

      // Clear any local storage items if needed
      localStorage.removeItem('supabase.auth.token');

      return true;
    } catch (error) {
      console.error('Error during sign out:', error);
      throw error; // Re-throw to allow handling in components
    }
  };

  // Function to refresh the authentication token
  const refreshToken = async (): Promise<string | null> => {
    try {
      console.log('[AuthContext] Refreshing authentication token...');
      const { data, error } = await supabase.auth.refreshSession();

      if (error) {
        console.error('[AuthContext] Error refreshing token:', error);
        return null;
      }

      if (data.session) {
        console.log('[AuthContext] Token refreshed successfully');
        // Update the user state with the new session data
        setIsAuthenticated(true);
        setUser(data.session.user);
        return data.session.access_token;
      }

      return null;
    } catch (error) {
      console.error('[AuthContext] Error in refreshToken:', error);
      return null;
    }
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated, user, isNewUser, signOut, refreshToken }}>
      {isAuthenticated && <PostAuthHandler />}
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
