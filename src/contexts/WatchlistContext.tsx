import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import { watchlistService, WatchlistItem, WatchlistStock } from '@/services/watchlistService';
import { watchlistPriceService } from '@/services/watchlistPriceService';

interface WatchlistContextType {
  watchlist: WatchlistItem[];
  isLoading: boolean;
  isInWatchlist: (symbol: string) => boolean;
  addToWatchlist: (stock: WatchlistStock) => Promise<boolean>;
  removeFromWatchlist: (symbol: string) => Promise<boolean>;
  refreshWatchlist: () => Promise<void>;
  refreshPrices: () => Promise<void>;
}

const WatchlistContext = createContext<WatchlistContextType | undefined>(undefined);

interface WatchlistProviderProps {
  children: ReactNode;
}

export const WatchlistProvider: React.FC<WatchlistProviderProps> = ({ children }) => {
  const [watchlist, setWatchlist] = useState<WatchlistItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize watchlist on mount
  useEffect(() => {
    const initializeAndStartUpdates = async () => {
      await initializeWatchlist();

      // Wait a moment for watchlist to load, then start price updates
      setTimeout(() => {
        console.log('🎯 WatchlistContext: Starting price updates...');
        watchlistPriceService.startPriceUpdates(120000); // Update every 2 minutes
      }, 2000);
    };

    initializeAndStartUpdates();

    // Cleanup on unmount
    return () => {
      watchlistPriceService.stopPriceUpdates();
    };
  }, []);

  const initializeWatchlist = async () => {
    setIsLoading(true);
    try {
      await watchlistService.initializeCache();
      const items = watchlistService.getCachedWatchlist();
      setWatchlist(items);
    } catch (error) {
      console.error('Error initializing watchlist:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshWatchlist = useCallback(async () => {
    try {
      const items = await watchlistService.getWatchlist();
      setWatchlist(items);
    } catch (error) {
      console.error('🎯 WatchlistContext: Error refreshing watchlist:', error);
    }
  }, []);

  const addToWatchlist = useCallback(async (stock: WatchlistStock): Promise<boolean> => {
    try {
      const success = await watchlistService.addToWatchlist(stock);
      if (success) {
        // Update price for the newly added stock
        await watchlistPriceService.updateSingleSymbol(stock.symbol);
        await refreshWatchlist();
      }
      return success;
    } catch (error) {
      console.error('🎯 WatchlistContext: Error adding to watchlist:', error);
      return false;
    }
  }, [refreshWatchlist]);

  const removeFromWatchlist = useCallback(async (symbol: string): Promise<boolean> => {
    try {
      const success = await watchlistService.removeFromWatchlist(symbol);
      if (success) {
        await refreshWatchlist();
      }
      return success;
    } catch (error) {
      console.error('Error removing from watchlist:', error);
      return false;
    }
  }, [refreshWatchlist]);

  const refreshPrices = useCallback(async () => {
    try {
      await watchlistPriceService.updateAllWatchlistPrices();
      await refreshWatchlist();
    } catch (error) {
      console.error('🎯 WatchlistContext: Error refreshing prices:', error);
    }
  }, [refreshWatchlist]);

  const isInWatchlist = useCallback((symbol: string): boolean => {
    return watchlist.some(item => item.symbol.toUpperCase() === symbol.toUpperCase());
  }, [watchlist]);

  const value: WatchlistContextType = {
    watchlist,
    isLoading,
    isInWatchlist,
    addToWatchlist,
    removeFromWatchlist,
    refreshWatchlist,
    refreshPrices
  };

  return (
    <WatchlistContext.Provider value={value}>
      {children}
    </WatchlistContext.Provider>
  );
};

export const useWatchlist = (): WatchlistContextType => {
  const context = useContext(WatchlistContext);
  if (context === undefined) {
    throw new Error('useWatchlist must be used within a WatchlistProvider');
  }
  return context;
};
