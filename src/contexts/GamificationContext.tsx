import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { Star, Trophy, Target, TrendingUp, Zap, Award, Crown, Sparkles } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  progress: number;
  maxProgress: number;
  unlocked: boolean;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  xpReward: number;
}

// Removed notifications - we only want the main success animation

interface UserProgress {
  level: number;
  xp: number;
  xpToNext: number;
  streak: number;
  totalActions: number;
  portfoliosCreated: number;
  agentsBuilt: number;
  scansCompleted: number;
  stocksScanned: number;
  backtestsCompleted: number;
  tradesExecuted: number;
  blocksAdded: number;
  publicAgentsCreated: number;
  // Track first-time actions
  hasCompletedFirstScan: boolean;
  hasCompletedFirstBacktest: boolean;
  hasCreatedFirstPortfolio: boolean;
  hasVisitedDiscoverPage: boolean;
  hasCreatedFirstPublicAgent: boolean;
  unlockedAchievements: string[]; // Array of achievement IDs that have been unlocked
}

interface GamificationContextType {
  userProgress: UserProgress;
  achievements: Achievement[];
  showSuccessAnimation: boolean;
  successAnimationType: string;
  successAnimationData: any;

  // Actions
  addXP: (amount: number, reason?: string) => void;
  incrementStreak: () => void;
  resetStreak: () => void;
  trackAction: (action: string, data?: any) => Promise<void>;
  triggerSuccessAnimation: (type: string, data?: any) => void;
  hideSuccessAnimation: () => void;
  reloadUserProgress: () => Promise<void>;
}

const GamificationContext = createContext<GamificationContextType | undefined>(undefined);

export const useGamification = () => {
  const context = useContext(GamificationContext);
  if (!context) {
    throw new Error('useGamification must be used within a GamificationProvider');
  }
  return context;
};

export const GamificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const [userProgress, setUserProgress] = useState<UserProgress>({
    level: 1,
    xp: 0,
    xpToNext: 1000,
    streak: 0,
    totalActions: 0,
    portfoliosCreated: 0,
    agentsBuilt: 0,
    scansCompleted: 0,
    stocksScanned: 0,
    backtestsCompleted: 0,
    tradesExecuted: 0,
    blocksAdded: 0,
    publicAgentsCreated: 0,
    hasCompletedFirstScan: false,
    hasCompletedFirstBacktest: false,
    hasCreatedFirstPortfolio: false,
    hasVisitedDiscoverPage: false,
    hasCreatedFirstPublicAgent: false,
    unlockedAchievements: []
  });

  const [showSuccessAnimation, setShowSuccessAnimation] = useState(false);
  const [successAnimationType, setSuccessAnimationType] = useState('');
  const [successAnimationData, setSuccessAnimationData] = useState<any>(null);

  // Load user progress from database on mount
  useEffect(() => {
    const loadUserProgress = async () => {
      if (!user?.id) return;

      // Only reload if user ID actually changed
      if (currentUserId === user.id) return;

      setCurrentUserId(user.id);
      console.log('Loading user progress from database for user:', user.id);

      try {
        const { data: profile, error } = await supabase
          .from('profiles')
          .select('headquarters_progress')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('Error loading user progress:', error);
          return;
        }

        console.log('Loaded profile data:', profile);

        if (profile?.headquarters_progress) {
          const savedProgress = profile.headquarters_progress as UserProgress;
          console.log('Found saved progress:', savedProgress);
          setUserProgress(prev => ({
            ...prev,
            ...savedProgress,
            // Ensure we have all required fields with defaults
            level: savedProgress.level || 1,
            xp: savedProgress.xp || 0,
            xpToNext: savedProgress.xpToNext || 1000,
            streak: savedProgress.streak || 0,
            totalActions: savedProgress.totalActions || 0,
            portfoliosCreated: savedProgress.portfoliosCreated || 0,
            agentsBuilt: savedProgress.agentsBuilt || 0,
            scansCompleted: savedProgress.scansCompleted || 0,
            stocksScanned: savedProgress.stocksScanned || 0,
            backtestsCompleted: savedProgress.backtestsCompleted || 0,
            tradesExecuted: savedProgress.tradesExecuted || 0,
            blocksAdded: savedProgress.blocksAdded || 0,
            publicAgentsCreated: savedProgress.publicAgentsCreated || 0,
            hasCompletedFirstScan: savedProgress.hasCompletedFirstScan || false,
            hasCompletedFirstBacktest: savedProgress.hasCompletedFirstBacktest || false,
            hasCreatedFirstPortfolio: savedProgress.hasCreatedFirstPortfolio || false,
            hasVisitedDiscoverPage: savedProgress.hasVisitedDiscoverPage || false,
            hasCreatedFirstPublicAgent: savedProgress.hasCreatedFirstPublicAgent || false,
            unlockedAchievements: savedProgress.unlockedAchievements || []
          }));

          // Update achievements state with unlocked status from database
          if (savedProgress.unlockedAchievements && savedProgress.unlockedAchievements.length > 0) {
            setAchievements(prev => prev.map(achievement => ({
              ...achievement,
              unlocked: savedProgress.unlockedAchievements.includes(achievement.id),
              progress: savedProgress.unlockedAchievements.includes(achievement.id) ? achievement.maxProgress : achievement.progress
            })));
          }
        } else {
          console.log('No saved progress found, using defaults');
        }
      } catch (error) {
        console.error('Error loading user progress:', error);
      }
    };

    loadUserProgress();
  }, [user?.id, currentUserId]);

  // Reload user progress from database
  const reloadUserProgress = useCallback(async () => {
    if (!user?.id) return;

    console.log('🔄 Reloading user progress from database for user:', user.id);

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('headquarters_progress')
        .eq('id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('❌ Error loading user progress:', error);
        return;
      }

      if (data?.headquarters_progress) {
        console.log('✅ Loaded user progress from database:', data.headquarters_progress);
        console.log('📊 Portfolio count in database:', data.headquarters_progress.portfoliosCreated);
        console.log('🏁 hasCreatedFirstPortfolio in database:', data.headquarters_progress.hasCreatedFirstPortfolio);

        setUserProgress(prev => {
          const newProgress = {
            ...prev,
            ...data.headquarters_progress,
            unlockedAchievements: data.headquarters_progress.unlockedAchievements || []
          };
          console.log('🔄 Updated userProgress state:', newProgress);
          return newProgress;
        });

        // Update achievements state with unlocked status from database
        if (data.headquarters_progress.unlockedAchievements && data.headquarters_progress.unlockedAchievements.length > 0) {
          setAchievements(prev => prev.map(achievement => ({
            ...achievement,
            unlocked: data.headquarters_progress.unlockedAchievements.includes(achievement.id),
            progress: data.headquarters_progress.unlockedAchievements.includes(achievement.id) ? achievement.maxProgress : achievement.progress
          })));
        }
      } else {
        console.log('⚠️ No headquarters_progress data found in database');
      }
    } catch (error) {
      console.error('❌ Error reloading user progress:', error);
    }
  }, [user?.id]);

  // Save user progress to database
  const saveUserProgress = useCallback(async (progress: UserProgress) => {
    if (!user?.id) return;

    console.log('Saving user progress to database:', progress);

    try {
      // Use upsert to ensure the profile exists and is updated
      const { data, error } = await supabase
        .from('profiles')
        .upsert({
          id: user.id,
          headquarters_progress: progress,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'id'
        })
        .select();

      if (error) {
        console.error('Error saving user progress:', error);
      } else {
        console.log('User progress saved successfully:', data);

        // Verify the save by reading it back
        const { data: verifyData, error: verifyError } = await supabase
          .from('profiles')
          .select('headquarters_progress')
          .eq('id', user.id)
          .single();

        if (verifyError) {
          console.error('Error verifying saved progress:', verifyError);
        } else {
          console.log('Verified saved progress:', verifyData?.headquarters_progress);
        }
      }
    } catch (error) {
      console.error('Error saving user progress:', error);
    }
  }, [user?.id]);

  // Log user activity to database
  const logActivity = useCallback(async (activityType: string, activityData?: any) => {
    if (!user?.id) return;

    try {
      const { error } = await supabase
        .from('user_activity_log')
        .insert({
          user_id: user.id,
          activity_type: activityType,
          activity_data: activityData || {},
          created_at: new Date().toISOString()
        });

      if (error) {
        console.error('Error logging activity:', error);
      }
    } catch (error) {
      console.error('Error logging activity:', error);
    }
  }, [user?.id]);

  // Save achievement unlock to database
  const saveAchievementUnlock = useCallback(async (achievementId: string, achievementTitle: string, achievementDescription: string) => {
    if (!user?.id) return;

    try {
      // Insert into user_achievements table
      const { error } = await supabase
        .from('user_achievements')
        .insert({
          user_id: user.id,
          achievement_id: achievementId,
          achievement_title: achievementTitle,
          achievement_description: achievementDescription,
          unlocked_at: new Date().toISOString()
        });

      if (error) {
        console.error('Error saving achievement unlock:', error);
      } else {
        console.log('Achievement unlock saved to database:', achievementId);
      }
    } catch (error) {
      console.error('Error saving achievement unlock:', error);
    }
  }, [user?.id]);

  // Define achievements
  const [achievements, setAchievements] = useState<Achievement[]>([
    {
      id: 'first_portfolio',
      title: 'First Steps',
      description: 'Create your first AI-optimized portfolio',
      icon: <TrendingUp className="w-4 h-4" />,
      progress: 0,
      maxProgress: 1,
      unlocked: false,
      rarity: 'common',
      xpReward: 100
    },
    {
      id: 'portfolio_master',
      title: 'Portfolio Master',
      description: 'Create 10 different portfolios',
      icon: <Crown className="w-4 h-4" />,
      progress: 0,
      maxProgress: 10,
      unlocked: false,
      rarity: 'epic',
      xpReward: 500
    },
    {
      id: 'first_agent',
      title: 'Agent Creator',
      description: 'Build your first trading agent',
      icon: <Zap className="w-4 h-4" />,
      progress: 0,
      maxProgress: 1,
      unlocked: false,
      rarity: 'common',
      xpReward: 150
    },
    {
      id: 'agent_architect',
      title: 'Agent Architect',
      description: 'Build 5 different trading agents',
      icon: <Award className="w-4 h-4" />,
      progress: 0,
      maxProgress: 5,
      unlocked: false,
      rarity: 'rare',
      xpReward: 300
    },
    {
      id: 'scan_novice',
      title: 'Market Scanner',
      description: 'Complete your first market scan',
      icon: <Target className="w-4 h-4" />,
      progress: 0,
      maxProgress: 1,
      unlocked: false,
      rarity: 'common',
      xpReward: 75
    },
    {
      id: 'scan_expert',
      title: 'Scan Expert',
      description: 'Complete 50 market scans',
      icon: <Star className="w-4 h-4" />,
      progress: 0,
      maxProgress: 50,
      unlocked: false,
      rarity: 'legendary',
      xpReward: 1000
    },
    {
      id: 'streak_warrior',
      title: 'Streak Warrior',
      description: 'Maintain a 7-day trading streak',
      icon: <Sparkles className="w-4 h-4" />,
      progress: 0,
      maxProgress: 7,
      unlocked: false,
      rarity: 'rare',
      xpReward: 400
    },
    {
      id: 'block_builder',
      title: 'Block Builder',
      description: 'Add 25 blocks to your agents',
      icon: <Trophy className="w-4 h-4" />,
      progress: 0,
      maxProgress: 25,
      unlocked: false,
      rarity: 'epic',
      xpReward: 600
    }
  ]);

  // Calculate level from XP
  const calculateLevel = (xp: number) => {
    return Math.floor(xp / 1000) + 1;
  };

  // Calculate XP needed for next level
  const calculateXPToNext = (level: number) => {
    return level * 1000;
  };

  // Add XP and check for level ups
  const addXP = useCallback((amount: number, reason?: string) => {
    setUserProgress(prev => {
      const newXP = prev.xp + amount;
      const newLevel = calculateLevel(newXP);
      const leveledUp = newLevel > prev.level;

      if (leveledUp) {
        // Trigger clean level up success animation
        triggerSuccessAnimation('level', {
          title: 'Level Up!',
          subtitle: `You've reached level ${newLevel}!`,
          value: newLevel
        });
      }

      const newProgress = {
        ...prev,
        xp: newXP,
        level: newLevel,
        xpToNext: calculateXPToNext(newLevel)
      };

      // Save progress to database
      saveUserProgress(newProgress);

      return newProgress;
    });
  }, [saveUserProgress]);

  // Track various actions
  const trackAction = useCallback(async (action: string, data?: any) => {
    return new Promise<void>((resolve) => {
      setUserProgress(prev => {
        const newProgress = { ...prev, totalActions: prev.totalActions + 1 };

        switch (action) {

        case 'agent_built':
          newProgress.agentsBuilt += 1;
          addXP(150, 'Agent built');
          triggerSuccessAnimation('agent', { title: 'Agent Built!', subtitle: 'Your trading agent is ready to analyze' });
          break;
        case 'scan_completed':
          newProgress.scansCompleted += 1;
          // Add the number of stocks scanned from the data parameter
          const stocksScannedCount = data?.stocksScanned || 0;
          newProgress.stocksScanned += stocksScannedCount;
          console.log(`Scan completed: ${stocksScannedCount} stocks scanned. Total stocks scanned: ${newProgress.stocksScanned}`);
          addXP(75, 'Market scan completed');
          const isFirstScan = !newProgress.hasCompletedFirstScan;
          if (isFirstScan) {
            newProgress.hasCompletedFirstScan = true;
          }
          triggerSuccessAnimation('scan', {
            title: isFirstScan ? 'Your First Scan!' : 'Scan Complete!',
            subtitle: `Market analysis finished - ${stocksScannedCount} stocks scanned`,
            isFirstTime: isFirstScan
          });
          break;
        case 'trade_executed':
          newProgress.tradesExecuted += 1;
          addXP(50, 'Trade executed');
          break;
        case 'backtest_completed':
          console.log('Processing backtest_completed action. Current backtests:', newProgress.backtestsCompleted);
          newProgress.backtestsCompleted += 1;
          addXP(100, 'Backtest completed');
          const isFirstBacktest = !newProgress.hasCompletedFirstBacktest;
          if (isFirstBacktest) {
            newProgress.hasCompletedFirstBacktest = true;
            console.log('This is the first backtest! Setting hasCompletedFirstBacktest to true');
          }
          console.log('New backtest count:', newProgress.backtestsCompleted, 'hasCompletedFirstBacktest:', newProgress.hasCompletedFirstBacktest);
          triggerSuccessAnimation('backtest', {
            title: isFirstBacktest ? 'Your First Backtest!' : 'Backtest Complete!',
            subtitle: 'Strategy analysis finished',
            isFirstTime: isFirstBacktest
          });
          break;
        case 'block_added':
          newProgress.blocksAdded += 1;
          addXP(25, 'Block added to agent');
          triggerSuccessAnimation('block', { title: 'Block Added!', subtitle: 'Your agent is getting smarter' });
          break;
        case 'discover_page_visited':
          console.log('Processing discover_page_visited action');
          if (!newProgress.hasVisitedDiscoverPage) {
            newProgress.hasVisitedDiscoverPage = true;
            addXP(25, 'Visited discover page');
            console.log('First time visiting discover page! Setting hasVisitedDiscoverPage to true');
            triggerSuccessAnimation('discover', {
              title: 'Explorer!',
              subtitle: 'You discovered the community agents page',
              isFirstTime: true
            });
          }
          break;
        case 'public_agent_created':
          console.log('Processing public_agent_created action');
          newProgress.publicAgentsCreated += 1;
          addXP(75, 'Made agent public');
          const isFirstPublicAgent = !newProgress.hasCreatedFirstPublicAgent;
          if (isFirstPublicAgent) {
            newProgress.hasCreatedFirstPublicAgent = true;
            console.log('First public agent created! Setting hasCreatedFirstPublicAgent to true');
          }
          triggerSuccessAnimation('public_agent', {
            title: isFirstPublicAgent ? 'Your First Public Agent!' : 'Agent Made Public!',
            subtitle: 'Sharing knowledge with the community',
            isFirstTime: isFirstPublicAgent
          });
          break;
        case 'portfolio_created':
          console.log('Processing portfolio_created action');
          newProgress.portfoliosCreated += 1;
          addXP(50, 'Created portfolio');
          const isFirstPortfolio = !newProgress.hasCreatedFirstPortfolio;
          if (isFirstPortfolio) {
            newProgress.hasCreatedFirstPortfolio = true;
            console.log('First portfolio created! Setting hasCreatedFirstPortfolio to true');
          }
          triggerSuccessAnimation('portfolio', {
            title: isFirstPortfolio ? 'Your First Portfolio!' : 'Portfolio Created!',
            subtitle: 'Building your investment strategy',
            isFirstTime: isFirstPortfolio
          });
          break;
      }

        // Save progress to database
        console.log('About to save progress after trackAction:', newProgress);
        saveUserProgress(newProgress).then(() => {
          // Log activity to database
          logActivity(action, data);
          resolve();
        });

        return newProgress;
      });

      // Check achievements
      checkAchievements(action);
    });
  }, [addXP, saveUserProgress, logActivity]);

  // Check and unlock achievements
  const checkAchievements = useCallback((action: string) => {
    setAchievements(prev => {
      return prev.map(achievement => {
        let newProgress = achievement.progress;

        switch (achievement.id) {
          case 'first_portfolio':
            if (action === 'portfolio_created') newProgress = Math.min(achievement.maxProgress, newProgress + 1);
            break;
          case 'portfolio_master':
            if (action === 'portfolio_created') newProgress = Math.min(achievement.maxProgress, newProgress + 1);
            break;
          case 'first_agent':
            if (action === 'agent_built') newProgress = Math.min(achievement.maxProgress, newProgress + 1);
            break;
          case 'agent_architect':
            if (action === 'agent_built') newProgress = Math.min(achievement.maxProgress, newProgress + 1);
            break;
          case 'scan_novice':
            if (action === 'scan_completed') newProgress = Math.min(achievement.maxProgress, newProgress + 1);
            break;
          case 'scan_expert':
            if (action === 'scan_completed') newProgress = Math.min(achievement.maxProgress, newProgress + 1);
            break;
          case 'block_builder':
            if (action === 'block_added') newProgress = Math.min(achievement.maxProgress, newProgress + 1);
            break;
        }

        const wasUnlocked = achievement.unlocked;
        const isNowUnlocked = newProgress >= achievement.maxProgress;

        // Only trigger animation and save if this is the first time unlocking
        if (!wasUnlocked && isNowUnlocked) {
          // Check if this achievement was already unlocked in the database
          setUserProgress(currentProgress => {
            if (!currentProgress.unlockedAchievements.includes(achievement.id)) {
              // This is truly the first time unlocking - trigger animation and save
              triggerSuccessAnimation('achievement', {
                title: 'Achievement Unlocked!',
                subtitle: achievement.title,
                value: `+${achievement.xpReward} XP`
              });
              addXP(achievement.xpReward, `Achievement: ${achievement.title}`);

              // Save achievement unlock to database
              saveAchievementUnlock(achievement.id, achievement.title, achievement.description);

              // Update the unlocked achievements list
              const updatedProgress = {
                ...currentProgress,
                unlockedAchievements: [...currentProgress.unlockedAchievements, achievement.id]
              };
              saveUserProgress(updatedProgress);
              return updatedProgress;
            }
            return currentProgress;
          });
        }

        return {
          ...achievement,
          progress: newProgress,
          unlocked: isNowUnlocked
        };
      });
    });
  }, [addXP, saveAchievementUnlock, saveUserProgress]);

  // Streak management
  const incrementStreak = useCallback(() => {
    setUserProgress(prev => {
      const newStreak = prev.streak + 1;

      // Check streak achievements
      setAchievements(prevAchievements => {
        return prevAchievements.map(achievement => {
          if (achievement.id === 'streak_warrior') {
            const newProgress = Math.min(achievement.maxProgress, newStreak);
            const wasUnlocked = achievement.unlocked;
            const isNowUnlocked = newProgress >= achievement.maxProgress;

            // Only trigger animation and save if this is the first time unlocking
            if (!wasUnlocked && isNowUnlocked) {
              // Check if this achievement was already unlocked in the database
              setUserProgress(currentProgress => {
                if (!currentProgress.unlockedAchievements.includes(achievement.id)) {
                  // This is truly the first time unlocking - trigger animation and save
                  triggerSuccessAnimation('achievement', {
                    title: 'Achievement Unlocked!',
                    subtitle: 'Streak Warrior',
                    value: `+${achievement.xpReward} XP`
                  });
                  addXP(achievement.xpReward, 'Achievement: Streak Warrior');

                  // Save achievement unlock to database
                  saveAchievementUnlock(achievement.id, achievement.title, achievement.description);

                  // Update the unlocked achievements list
                  const updatedProgress = {
                    ...currentProgress,
                    unlockedAchievements: [...currentProgress.unlockedAchievements, achievement.id]
                  };
                  saveUserProgress(updatedProgress);
                  return updatedProgress;
                }
                return currentProgress;
              });
            }

            return {
              ...achievement,
              progress: newProgress,
              unlocked: isNowUnlocked
            };
          }
          return achievement;
        });
      });

      const newProgress = { ...prev, streak: newStreak };

      // Save progress to database
      saveUserProgress(newProgress);

      return newProgress;
    });
  }, [addXP, saveUserProgress]);

  const resetStreak = useCallback(() => {
    setUserProgress(prev => {
      const newProgress = { ...prev, streak: 0 };

      // Save progress to database
      saveUserProgress(newProgress);

      return newProgress;
    });
  }, [saveUserProgress]);

  // Success animation management
  const triggerSuccessAnimation = useCallback((type: string, data?: any) => {
    setSuccessAnimationType(type);
    setSuccessAnimationData(data);
    setShowSuccessAnimation(true);
  }, []);

  const hideSuccessAnimation = useCallback(() => {
    setShowSuccessAnimation(false);
    setSuccessAnimationType('');
    setSuccessAnimationData(null);
  }, []);

  const value: GamificationContextType = {
    userProgress,
    achievements,
    showSuccessAnimation,
    successAnimationType,
    successAnimationData,
    addXP,
    incrementStreak,
    resetStreak,
    trackAction,
    triggerSuccessAnimation,
    hideSuccessAnimation,
    reloadUserProgress
  };

  return (
    <GamificationContext.Provider value={value}>
      {children}
    </GamificationContext.Provider>
  );
};
