# AI Trading Agent Test Suite

This comprehensive test suite ensures the reliability, accuracy, and performance of the AI trading agent system. The tests cover all aspects from individual block functionality to complete agent workflows.

## Test Structure

```
src/test/
├── components/
│   ├── blocks/                    # Block component tests
│   │   ├── TechnicalIndicatorBlock.test.tsx
│   │   ├── RiskManagementBlock.test.tsx
│   │   ├── SignalGenerationBlock.test.tsx
│   │   └── LogicFlowBlock.test.tsx
│   └── AgentBuilder.test.tsx      # UI interaction tests
├── integration/
│   └── AgentWorkflow.test.tsx     # End-to-end workflow tests
├── performance/
│   └── AgentPerformance.test.ts   # Performance benchmarks
├── utils/
│   ├── mockMarketData.ts          # Market data fixtures
│   └── testHelpers.ts             # Test utilities
├── setup.ts                       # Global test configuration
└── README.md                      # This file
```

## Test Categories

### 1. Block Functionality Tests

Tests for individual trading strategy blocks:

- **Technical Indicators**: RSI, MACD, Moving Averages, Bollinger Bands, etc.
- **Risk Management**: Position sizing, stop losses, take profits, portfolio risk
- **Signal Generation**: Price action signals, divergence detection, volume confirmation
- **Logic & Flow Control**: IF-THEN-ELSE, AND/OR operators, signal confirmation
- **Market Analysis**: Support/resistance, trend lines, market structure

### 2. Integration Tests

Complete agent workflow tests:

- Signal propagation through connected blocks
- Multi-timeframe analysis strategies
- Risk management integration
- Data flow validation
- Connection validation

### 3. Performance Tests

Benchmarks for computational efficiency:

- Technical indicator calculations with large datasets
- Real-time signal generation performance
- Memory usage optimization
- Concurrent processing capabilities

### 4. UI Component Tests

Agent builder interface tests:

- Block palette interactions
- Drag and drop functionality
- Block configuration panels
- Connection management
- Error handling and validation

## Running Tests

### Quick Start

```bash
# Run all tests
npm test

# Run specific test suites
npm run test:unit
npm run test:integration
npm run test:performance
npm run test:blocks

# Run with coverage
npm run test:coverage

# Run in watch mode
npm run test:watch
```

### Advanced Usage

```bash
# Run with custom options
node scripts/test-runner.js unit --coverage --verbose
node scripts/test-runner.js integration --bail
node scripts/test-runner.js --benchmark

# Validate test environment
npm run test:validate

# CI/CD pipeline
npm run test:ci
```

## Test Utilities

### Mock Market Data

The `mockMarketData.ts` utility provides realistic market data for testing:

```typescript
import { generateMockPriceData, MARKET_SCENARIOS } from '@/test/utils/mockMarketData';

// Generate realistic price data
const priceData = generateMockPriceData(100, 100, 0.02);

// Use predefined market scenarios
const bullTrend = MARKET_SCENARIOS.BULL_TREND;
const bearTrend = MARKET_SCENARIOS.BEAR_TREND;
const sideways = MARKET_SCENARIOS.SIDEWAYS;
```

### Test Helpers

Common testing utilities in `testHelpers.ts`:

```typescript
import { createMockAgent, validateSignalConsistency } from '@/test/utils/testHelpers';

// Create mock agent for testing
const agent = createMockAgent();

// Validate signal consistency
const isValid = validateSignalConsistency(signals);
```

### Custom Matchers

Trading-specific assertion matchers:

```typescript
// Validate trading data
expect(price).toBeValidPrice();
expect(rsiValue).toBeValidRSI();
expect(signal).toBeValidSignal();
expect(confidence).toBeValidConfidence();
```

## Test Coverage Requirements

The test suite maintains high coverage standards:

- **Branches**: 75%
- **Functions**: 75%
- **Lines**: 75%
- **Statements**: 75%

Critical trading logic components require higher coverage:

- Technical indicator calculations: 90%+
- Risk management logic: 95%+
- Signal generation: 90%+

## Performance Benchmarks

### Technical Indicators

- RSI calculation: < 0.1ms per 1000 data points
- MACD calculation: < 0.2ms per 1000 data points
- Moving averages: < 0.05ms per 1000 data points

### Signal Generation

- Real-time signal processing: < 10ms per market update
- Complex multi-indicator strategies: < 50ms
- High-frequency data processing: < 500ms for 10k ticks

### Memory Usage

- Large dataset processing: < 100MB memory increase
- Agent execution: < 50MB per agent
- UI rendering: < 20MB for 100 blocks

## Best Practices

### Writing Tests

1. **Use descriptive test names** that explain the scenario
2. **Test both positive and negative cases**
3. **Include edge cases and boundary conditions**
4. **Mock external dependencies** appropriately
5. **Keep tests isolated** and independent

### Performance Testing

1. **Set realistic benchmarks** based on production requirements
2. **Test with various data sizes** to identify scaling issues
3. **Monitor memory usage** during long-running tests
4. **Use consistent test environments** for reliable results

### Market Data Testing

1. **Use realistic market scenarios** that reflect actual trading conditions
2. **Test with historical data patterns** when possible
3. **Include stress testing** with extreme market conditions
4. **Validate indicator calculations** against known expected values

## Contributing

When adding new features:

1. **Write tests first** (TDD approach)
2. **Ensure adequate coverage** for new code
3. **Add performance benchmarks** for computationally intensive features
4. **Update test documentation** as needed
5. **Validate against real market scenarios**