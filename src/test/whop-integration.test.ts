import { describe, it, expect, beforeEach, vi } from 'vitest';
import { validateWhopConfig, isWhopContext } from '@/lib/whop-sdk';
import { 
  verifyWhopUserToken, 
  checkWhopAccess, 
  getWhopUserTokenFromContext,
  isInWhopEnvironment 
} from '@/utils/whopAuth';
import { 
  enableWhopDevMode, 
  disableWhopDevMode, 
  isWhopDevMode 
} from '@/utils/whopDev';

// Mock environment variables
const mockEnv = {
  VITE_WHOP_APP_ID: 'app_test_123',
  WHOP_API_KEY: 'test_api_key',
  VITE_WHOP_AGENT_USER_ID: 'user_test_123',
  VITE_WHOP_COMPANY_ID: 'biz_test_123'
};

// Mock import.meta.env
vi.mock('import.meta', () => ({
  env: mockEnv
}));

// Mock window object
const mockWindow = {
  location: {
    pathname: '/',
    search: '',
    href: 'http://localhost:3000/'
  },
  localStorage: {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn()
  },
  history: {
    pushState: vi.fn(),
    replaceState: vi.fn()
  }
};

Object.defineProperty(global, 'window', {
  value: mockWindow,
  writable: true
});

describe('Whop Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockWindow.location.pathname = '/';
    mockWindow.location.search = '';
    mockWindow.localStorage.getItem.mockReturnValue(null);
  });

  describe('Configuration Validation', () => {
    it('should validate complete Whop configuration', () => {
      const isValid = validateWhopConfig();
      expect(isValid).toBe(true);
    });

    it('should detect incomplete configuration', () => {
      const originalAppId = mockEnv.VITE_WHOP_APP_ID;
      mockEnv.VITE_WHOP_APP_ID = '';
      
      const isValid = validateWhopConfig();
      expect(isValid).toBe(false);
      
      // Restore
      mockEnv.VITE_WHOP_APP_ID = originalAppId;
    });
  });

  describe('Context Detection', () => {
    it('should detect Whop context from experience path', () => {
      mockWindow.location.pathname = '/experiences/exp_test_123';
      
      const isWhop = isWhopContext();
      expect(isWhop).toBe(true);
    });

    it('should detect Whop context from query parameter', () => {
      mockWindow.location.search = '?whop=true';
      
      const isWhop = isWhopContext();
      expect(isWhop).toBe(true);
    });

    it('should not detect Whop context on regular pages', () => {
      mockWindow.location.pathname = '/home';
      mockWindow.location.search = '';
      
      const isWhop = isWhopContext();
      expect(isWhop).toBe(false);
    });
  });

  describe('Token Management', () => {
    it('should retrieve token from URL parameters', () => {
      mockWindow.location.search = '?whop_token=test_token_123';
      
      const token = getWhopUserTokenFromContext();
      expect(token).toBe('test_token_123');
    });

    it('should retrieve token from localStorage', () => {
      mockWindow.localStorage.getItem.mockReturnValue('stored_token_123');
      
      const token = getWhopUserTokenFromContext();
      expect(token).toBe('stored_token_123');
    });

    it('should return null when no token is available', () => {
      const token = getWhopUserTokenFromContext();
      expect(token).toBe(null);
    });
  });

  describe('Development Mode', () => {
    it('should enable development mode', () => {
      enableWhopDevMode('exp_test_123');
      
      expect(mockWindow.localStorage.setItem).toHaveBeenCalledWith(
        'whop_user_token', 
        'mock-whop-token-for-development'
      );
      expect(mockWindow.history.pushState).toHaveBeenCalled();
    });

    it('should disable development mode', () => {
      disableWhopDevMode();
      
      expect(mockWindow.localStorage.removeItem).toHaveBeenCalledWith('whop_user_token');
      expect(mockWindow.history.pushState).toHaveBeenCalled();
    });

    it('should detect development mode from URL', () => {
      mockWindow.location.search = '?whop_dev=true';
      
      const isDevMode = isWhopDevMode();
      expect(isDevMode).toBe(true);
    });
  });

  describe('Environment Detection', () => {
    it('should detect Whop environment from experience path', () => {
      mockWindow.location.pathname = '/experiences/exp_test_123';
      
      const isWhopEnv = isInWhopEnvironment();
      expect(isWhopEnv).toBe(true);
    });

    it('should detect Whop environment from query parameters', () => {
      mockWindow.location.search = '?whop=true';
      
      const isWhopEnv = isInWhopEnvironment();
      expect(isWhopEnv).toBe(true);
    });

    it('should not detect Whop environment on regular pages', () => {
      mockWindow.location.pathname = '/home';
      
      const isWhopEnv = isInWhopEnvironment();
      expect(isWhopEnv).toBe(false);
    });
  });
});

describe('Whop Authentication (Mocked)', () => {
  // These tests would require mocking the Whop SDK
  // For now, we'll test the structure and error handling

  it('should handle invalid token gracefully', async () => {
    // Mock the SDK to throw an error
    const result = await verifyWhopUserToken('invalid_token').catch(() => null);
    expect(result).toBe(null);
  });

  it('should handle access check errors gracefully', async () => {
    const result = await checkWhopAccess('invalid_user', 'invalid_exp').catch(() => ({
      hasAccess: false,
      accessLevel: 'no_access' as const,
      userId: 'invalid_user',
      experienceId: 'invalid_exp'
    }));
    
    expect(result.hasAccess).toBe(false);
    expect(result.accessLevel).toBe('no_access');
  });
});

describe('Integration Points', () => {
  it('should have all required components exported', () => {
    // Test that all main integration points are available
    expect(validateWhopConfig).toBeDefined();
    expect(isWhopContext).toBeDefined();
    expect(verifyWhopUserToken).toBeDefined();
    expect(checkWhopAccess).toBeDefined();
    expect(enableWhopDevMode).toBeDefined();
    expect(disableWhopDevMode).toBeDefined();
  });

  it('should handle missing window object gracefully', () => {
    const originalWindow = global.window;
    // @ts-ignore
    delete global.window;
    
    // These should not throw errors
    expect(() => getWhopUserTokenFromContext()).not.toThrow();
    expect(() => isInWhopEnvironment()).not.toThrow();
    
    // Restore window
    global.window = originalWindow;
  });
});
