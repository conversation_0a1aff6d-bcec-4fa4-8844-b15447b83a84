/**
 * Mock Market Data Utilities for Testing
 * Provides consistent market data fixtures for testing trading algorithms
 */

export interface MockPriceData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface MockIndicatorData {
  rsi: number[];
  macd: { macd: number[]; signal: number[]; histogram: number[] };
  sma: number[];
  ema: number[];
  bollinger: { upper: number[]; middle: number[]; lower: number[] };
  atr: number[];
  stochastic: { k: number[]; d: number[] };
}

/**
 * Generates realistic price data for testing
 */
export function generateMockPriceData(
  days: number = 100,
  startPrice: number = 100,
  volatility: number = 0.02
): MockPriceData[] {
  const data: MockPriceData[] = [];
  let currentPrice = startPrice;
  const baseTime = Date.now() - (days * 24 * 60 * 60 * 1000);

  for (let i = 0; i < days; i++) {
    const timestamp = baseTime + (i * 24 * 60 * 60 * 1000);
    
    // Generate realistic OHLC data
    const change = (Math.random() - 0.5) * volatility * currentPrice;
    const open = currentPrice;
    const close = currentPrice + change;
    
    // High and low based on intraday volatility
    const intradayRange = Math.abs(change) * (1 + Math.random());
    const high = Math.max(open, close) + (intradayRange * Math.random());
    const low = Math.min(open, close) - (intradayRange * Math.random());
    
    // Volume with some randomness
    const volume = Math.floor(1000000 + (Math.random() * 5000000));
    
    data.push({
      timestamp,
      open,
      high,
      low,
      close,
      volume
    });
    
    currentPrice = close;
  }
  
  return data;
}

/**
 * Generates trending price data (upward or downward)
 */
export function generateTrendingData(
  days: number = 50,
  startPrice: number = 100,
  trendStrength: number = 0.001, // Daily trend percentage
  volatility: number = 0.02
): MockPriceData[] {
  const data: MockPriceData[] = [];
  let currentPrice = startPrice;
  const baseTime = Date.now() - (days * 24 * 60 * 60 * 1000);

  for (let i = 0; i < days; i++) {
    const timestamp = baseTime + (i * 24 * 60 * 60 * 1000);
    
    // Apply trend + random volatility
    const trendChange = currentPrice * trendStrength;
    const randomChange = (Math.random() - 0.5) * volatility * currentPrice;
    const totalChange = trendChange + randomChange;
    
    const open = currentPrice;
    const close = currentPrice + totalChange;
    
    const intradayRange = Math.abs(totalChange) * (1 + Math.random() * 0.5);
    const high = Math.max(open, close) + (intradayRange * Math.random() * 0.3);
    const low = Math.min(open, close) - (intradayRange * Math.random() * 0.3);
    
    const volume = Math.floor(1000000 + (Math.random() * 3000000));
    
    data.push({
      timestamp,
      open,
      high,
      low,
      close,
      volume
    });
    
    currentPrice = close;
  }
  
  return data;
}

/**
 * Generates ranging/sideways price data
 */
export function generateRangingData(
  days: number = 50,
  centerPrice: number = 100,
  rangePercent: number = 0.1, // 10% range
  volatility: number = 0.015
): MockPriceData[] {
  const data: MockPriceData[] = [];
  const rangeTop = centerPrice * (1 + rangePercent / 2);
  const rangeBottom = centerPrice * (1 - rangePercent / 2);
  let currentPrice = centerPrice;
  const baseTime = Date.now() - (days * 24 * 60 * 60 * 1000);

  for (let i = 0; i < days; i++) {
    const timestamp = baseTime + (i * 24 * 60 * 60 * 1000);
    
    // Mean reversion tendency
    const distanceFromCenter = (currentPrice - centerPrice) / centerPrice;
    const meanReversionForce = -distanceFromCenter * 0.1;
    
    const randomChange = (Math.random() - 0.5) * volatility * currentPrice;
    const totalChange = (meanReversionForce * currentPrice) + randomChange;
    
    const open = currentPrice;
    let close = currentPrice + totalChange;
    
    // Keep within range bounds
    close = Math.max(rangeBottom, Math.min(rangeTop, close));
    
    const intradayRange = Math.abs(totalChange) * (1 + Math.random() * 0.3);
    const high = Math.max(open, close) + (intradayRange * Math.random() * 0.2);
    const low = Math.min(open, close) - (intradayRange * Math.random() * 0.2);
    
    const volume = Math.floor(800000 + (Math.random() * 2000000));
    
    data.push({
      timestamp,
      open,
      high,
      low,
      close,
      volume
    });
    
    currentPrice = close;
  }
  
  return data;
}

/**
 * Calculates RSI for mock data
 */
export function calculateMockRSI(prices: number[], period: number = 14): number[] {
  const rsi: number[] = [];
  
  for (let i = 0; i < prices.length; i++) {
    if (i < period) {
      rsi.push(50); // Default neutral RSI
      continue;
    }
    
    let gains = 0;
    let losses = 0;
    
    for (let j = i - period + 1; j <= i; j++) {
      const change = prices[j] - prices[j - 1];
      if (change > 0) gains += change;
      else losses += Math.abs(change);
    }
    
    const avgGain = gains / period;
    const avgLoss = losses / period;
    
    if (avgLoss === 0) {
      rsi.push(100);
    } else {
      const rs = avgGain / avgLoss;
      rsi.push(100 - (100 / (1 + rs)));
    }
  }
  
  return rsi;
}

/**
 * Calculates Simple Moving Average for mock data
 */
export function calculateMockSMA(prices: number[], period: number): number[] {
  const sma: number[] = [];
  
  for (let i = 0; i < prices.length; i++) {
    if (i < period - 1) {
      sma.push(prices[i]); // Not enough data yet
      continue;
    }
    
    let sum = 0;
    for (let j = i - period + 1; j <= i; j++) {
      sum += prices[j];
    }
    sma.push(sum / period);
  }
  
  return sma;
}

/**
 * Generates complete mock indicator data
 */
export function generateMockIndicators(priceData: MockPriceData[]): MockIndicatorData {
  const closes = priceData.map(d => d.close);
  const highs = priceData.map(d => d.high);
  const lows = priceData.map(d => d.low);
  
  return {
    rsi: calculateMockRSI(closes, 14),
    macd: {
      macd: closes.map((_, i) => Math.sin(i * 0.1) * 2), // Mock MACD
      signal: closes.map((_, i) => Math.sin(i * 0.1 - 0.2) * 1.8), // Mock Signal
      histogram: closes.map((_, i) => Math.sin(i * 0.1) * 0.5) // Mock Histogram
    },
    sma: calculateMockSMA(closes, 20),
    ema: closes.map((price, i) => price * (1 + Math.sin(i * 0.05) * 0.01)), // Mock EMA
    bollinger: {
      upper: closes.map(price => price * 1.02),
      middle: calculateMockSMA(closes, 20),
      lower: closes.map(price => price * 0.98)
    },
    atr: highs.map((high, i) => {
      const low = lows[i];
      return (high - low) / closes[i] * 100; // Mock ATR as percentage
    }),
    stochastic: {
      k: closes.map((_, i) => 50 + Math.sin(i * 0.15) * 30), // Mock %K
      d: closes.map((_, i) => 50 + Math.sin(i * 0.15 - 0.1) * 25) // Mock %D
    }
  };
}

/**
 * Market scenarios for testing different conditions
 */
export const MARKET_SCENARIOS = {
  BULL_TREND: generateTrendingData(100, 100, 0.002, 0.02),
  BEAR_TREND: generateTrendingData(100, 100, -0.002, 0.025),
  SIDEWAYS: generateRangingData(100, 100, 0.15, 0.015),
  HIGH_VOLATILITY: generateMockPriceData(100, 100, 0.05),
  LOW_VOLATILITY: generateMockPriceData(100, 100, 0.005),
  BREAKOUT_UP: [
    ...generateRangingData(50, 100, 0.1, 0.01),
    ...generateTrendingData(30, 105, 0.003, 0.02)
  ],
  BREAKOUT_DOWN: [
    ...generateRangingData(50, 100, 0.1, 0.01),
    ...generateTrendingData(30, 95, -0.003, 0.025)
  ]
};

/**
 * Expected indicator values for known scenarios (for validation)
 */
export const EXPECTED_VALUES = {
  RSI_OVERBOUGHT: 75,
  RSI_OVERSOLD: 25,
  RSI_NEUTRAL: 50,
  TREND_STRENGTH_STRONG: 0.8,
  TREND_STRENGTH_WEAK: 0.3,
  VOLATILITY_HIGH: 0.04,
  VOLATILITY_LOW: 0.01
};
