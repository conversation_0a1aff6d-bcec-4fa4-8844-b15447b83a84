/**
 * Test Helper Utilities for Agent Builder Testing
 */

import { vi } from 'vitest';
import { AgentBlock } from '@/services/agentService';
import { BlockType } from '@/hooks/useAgentBuilder';
import { MockPriceData, generateMockPriceData } from './mockMarketData';

/**
 * Creates a mock agent block with default properties
 */
export function createMockBlock(
  type: BlockType,
  id: string = 'test-block',
  additionalProps: Partial<AgentBlock> = {}
): AgentBlock {
  const baseBlock: AgentBlock = {
    id,
    type,
    position: { x: 100, y: 100 },
    inputConnections: [],
    outputConnections: [],
    ...additionalProps
  };

  // Add type-specific default properties
  switch (type) {
    case BlockType.INDICATOR:
      return {
        ...baseBlock,
        indicator: 'rsi',
        parameters: { period: 14 }
      };
    
    case BlockType.PRICE:
      return {
        ...baseBlock,
        dataPoint: 'close',
        lookback: 0
      };
    
    case BlockType.CONDITION:
      return {
        ...baseBlock,
        operator: 'greater_than',
        compareValue: 50
      };
    
    case BlockType.TRIGGER:
      return {
        ...baseBlock,
        signal: 'bullish',
        confidence: 75
      };
    
    case BlockType.MOVING_AVERAGE:
      return {
        ...baseBlock,
        averageType: 'sma',
        period: 20,
        timeframe: 'day',
        source: 'close'
      };
    
    case BlockType.MOMENTUM_INDICATOR:
      return {
        ...baseBlock,
        indicator: 'rsi',
        period: 14,
        timeframe: 'day',
        overbought: 70,
        oversold: 30
      };
    
    case BlockType.TREND_INDICATOR:
      return {
        ...baseBlock,
        indicator: 'macd',
        parameters: { fastPeriod: 12, slowPeriod: 26, signalPeriod: 9 },
        timeframe: 'day'
      };
    
    case BlockType.POSITION_SIZING:
      return {
        ...baseBlock,
        method: 'percentage',
        parameters: { percentage: 2 }
      };
    
    case BlockType.STOP_LOSS:
      return {
        ...baseBlock,
        method: 'fixed_percentage',
        parameters: { percentage: 2 }
      };
    
    default:
      return baseBlock;
  }
}

/**
 * Creates a mock agent with multiple connected blocks
 */
export function createMockAgent(blocks: AgentBlock[] = []): {
  blocks: AgentBlock[];
  connections: any[];
  entryBlockId: string;
} {
  const defaultBlocks = blocks.length > 0 ? blocks : [
    createMockBlock(BlockType.WHEN_RUN, 'entry'),
    createMockBlock(BlockType.PRICE, 'price'),
    createMockBlock(BlockType.INDICATOR, 'rsi'),
    createMockBlock(BlockType.CONDITION, 'condition'),
    createMockBlock(BlockType.TRIGGER, 'signal')
  ];

  const connections = [
    { sourceId: 'entry', targetId: 'price', sourceHandle: 'output', targetHandle: 'input' },
    { sourceId: 'price', targetId: 'rsi', sourceHandle: 'output', targetHandle: 'input' },
    { sourceId: 'rsi', targetId: 'condition', sourceHandle: 'output', targetHandle: 'input' },
    { sourceId: 'condition', targetId: 'signal', sourceHandle: 'true', targetHandle: 'input' }
  ];

  return {
    blocks: defaultBlocks,
    connections,
    entryBlockId: 'entry'
  };
}

/**
 * Mock functions for agent builder operations
 */
export const mockAgentBuilderFunctions = {
  onBlockUpdate: vi.fn(),
  onBlockRemove: vi.fn(),
  onConnectionAdd: vi.fn(),
  onConnectionRemove: vi.fn(),
  onSetEntryBlock: vi.fn(),
  addBlock: vi.fn()
};

/**
 * Mock market data API responses
 */
export function mockPolygonAPI() {
  return {
    getCurrentPrice: vi.fn().mockResolvedValue({
      symbol: 'AAPL',
      price: 150.25,
      change: 2.15,
      changePercent: 1.45
    }),
    
    getHistoricalData: vi.fn().mockResolvedValue({
      symbol: 'AAPL',
      data: generateMockPriceData(100, 150, 0.02)
    }),
    
    getIndicators: vi.fn().mockResolvedValue({
      rsi: [45, 52, 48, 55, 62, 58, 51],
      macd: {
        macd: [0.5, 0.8, 1.2, 0.9, 0.3],
        signal: [0.3, 0.6, 1.0, 1.1, 0.7],
        histogram: [0.2, 0.2, 0.2, -0.2, -0.4]
      }
    })
  };
}

/**
 * Performance testing utilities
 */
export class PerformanceTimer {
  private startTime: number = 0;
  private endTime: number = 0;

  start(): void {
    this.startTime = performance.now();
  }

  stop(): number {
    this.endTime = performance.now();
    return this.endTime - this.startTime;
  }

  getElapsed(): number {
    return this.endTime - this.startTime;
  }
}

/**
 * Memory usage tracking for performance tests
 */
export function getMemoryUsage(): number {
  if (typeof window !== 'undefined' && 'performance' in window && 'memory' in window.performance) {
    return (window.performance as any).memory.usedJSHeapSize;
  }
  return 0;
}

/**
 * Validates technical indicator calculations
 */
export function validateIndicatorOutput(
  output: number[],
  expectedRange: { min: number; max: number },
  tolerance: number = 0.01
): boolean {
  return output.every(value => {
    if (isNaN(value)) return false;
    return value >= expectedRange.min - tolerance && value <= expectedRange.max + tolerance;
  });
}

/**
 * Validates signal generation consistency
 */
export function validateSignalConsistency(
  signals: Array<{ signal: 'bullish' | 'bearish' | 'neutral'; confidence: number }>,
  minConfidence: number = 0,
  maxConfidence: number = 100
): boolean {
  return signals.every(signal => {
    // Check signal type is valid
    if (!['bullish', 'bearish', 'neutral'].includes(signal.signal)) return false;
    
    // Check confidence is in valid range
    if (signal.confidence < minConfidence || signal.confidence > maxConfidence) return false;
    
    return true;
  });
}

/**
 * Creates test scenarios for different market conditions
 */
export function createTestScenarios() {
  return {
    bullishTrend: {
      name: 'Bullish Trend',
      data: generateMockPriceData(50, 100, 0.02).map((d, i) => ({
        ...d,
        close: d.close * (1 + i * 0.001) // Gradual uptrend
      })),
      expectedSignals: ['bullish', 'neutral'],
      expectedRSI: { min: 45, max: 80 }
    },
    
    bearishTrend: {
      name: 'Bearish Trend',
      data: generateMockPriceData(50, 100, 0.02).map((d, i) => ({
        ...d,
        close: d.close * (1 - i * 0.001) // Gradual downtrend
      })),
      expectedSignals: ['bearish', 'neutral'],
      expectedRSI: { min: 20, max: 55 }
    },
    
    sidewaysMarket: {
      name: 'Sideways Market',
      data: generateMockPriceData(50, 100, 0.01), // Low volatility
      expectedSignals: ['neutral'],
      expectedRSI: { min: 40, max: 60 }
    },
    
    highVolatility: {
      name: 'High Volatility',
      data: generateMockPriceData(50, 100, 0.05), // High volatility
      expectedSignals: ['bullish', 'bearish', 'neutral'],
      expectedRSI: { min: 10, max: 90 }
    }
  };
}

/**
 * Assertion helpers for testing
 */
export const testAssertions = {
  /**
   * Asserts that a value is within a percentage range
   */
  assertWithinPercentage(actual: number, expected: number, percentage: number): boolean {
    const tolerance = expected * (percentage / 100);
    return Math.abs(actual - expected) <= tolerance;
  },

  /**
   * Asserts that an array contains values within expected ranges
   */
  assertArrayInRange(values: number[], min: number, max: number): boolean {
    return values.every(value => value >= min && value <= max);
  },

  /**
   * Asserts that signals are logically consistent
   */
  assertSignalLogic(
    rsiValue: number,
    signal: 'bullish' | 'bearish' | 'neutral',
    overbought: number = 70,
    oversold: number = 30
  ): boolean {
    if (rsiValue > overbought && signal === 'bearish') return true;
    if (rsiValue < oversold && signal === 'bullish') return true;
    if (rsiValue >= oversold && rsiValue <= overbought && signal === 'neutral') return true;
    return false;
  }
};

/**
 * Mock React Flow for testing
 */
export const mockReactFlow = {
  ReactFlow: vi.fn(),
  Handle: vi.fn(),
  Position: {
    Top: 'top',
    Bottom: 'bottom',
    Left: 'left',
    Right: 'right'
  }
};

/**
 * Mock UI components for testing
 */
export const mockUIComponents = {
  Button: vi.fn(),
  Input: vi.fn(),
  Select: vi.fn(),
  Card: vi.fn(),
  CardHeader: vi.fn(),
  CardContent: vi.fn()
};
