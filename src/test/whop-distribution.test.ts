import { describe, it, expect, beforeEach, vi } from 'vitest';
import { 
  distributeAgentToWhopMembers,
  getWhopDistributedAgents,
  getWhopMemberAgents,
  canDistributeToWhopMembers,
  removeWhopAgentDistribution
} from '@/services/whopDistributionService';

// Mock the dependencies
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    auth: {
      getSession: vi.fn(() => Promise.resolve({
        data: { session: { access_token: 'mock-token' } }
      })),
      getUser: vi.fn(() => Promise.resolve({
        data: { user: { id: 'user-123', user_metadata: { whop_user_id: 'whop-user-123' } } }
      }))
    },
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          eq: vi.fn(() => ({
            order: vi.fn(() => Promise.resolve({ data: [], error: null }))
          }))
        }))
      })),
      update: vi.fn(() => ({
        eq: vi.fn(() => ({
          eq: vi.fn(() => Promise.resolve({ error: null }))
        }))
      }))
    }))
  }
}));

vi.mock('@/services/authService', () => ({
  getAuthenticatedUser: vi.fn(() => Promise.resolve({
    id: 'user-123',
    user_metadata: { whop_user_id: 'whop-user-123' }
  }))
}));

// Mock fetch for the distribution function
global.fetch = vi.fn();

describe('Whop Distribution Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('distributeAgentToWhopMembers', () => {
    it('should successfully distribute an agent to Whop members', async () => {
      const mockResponse = {
        success: true,
        message: 'Agent distributed successfully',
        distributionId: 'dist-123',
        membersAdded: 5,
        errors: []
      };

      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const request = {
        agentId: 'agent-123',
        whopCompanyId: 'company-123'
      };

      const result = await distributeAgentToWhopMembers(request);

      expect(result.success).toBe(true);
      expect(result.membersAdded).toBe(5);
      expect(result.distributionId).toBe('dist-123');
    });

    it('should handle distribution errors', async () => {
      (global.fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: () => Promise.resolve({ error: 'Agent not found' })
      });

      const request = {
        agentId: 'invalid-agent',
        whopCompanyId: 'company-123'
      };

      const result = await distributeAgentToWhopMembers(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Agent not found');
    });

    it('should require Whop user authentication', async () => {
      // Mock non-Whop user
      const { getAuthenticatedUser } = await import('@/services/authService');
      (getAuthenticatedUser as any).mockResolvedValueOnce({
        id: 'user-123',
        user_metadata: {} // No whop_user_id
      });

      const request = {
        agentId: 'agent-123',
        whopCompanyId: 'company-123'
      };

      const result = await distributeAgentToWhopMembers(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Only Whop users can distribute');
    });
  });

  describe('canDistributeToWhopMembers', () => {
    it('should return true for Whop users', async () => {
      const result = await canDistributeToWhopMembers('company-123');

      expect(result.canDistribute).toBe(true);
    });

    it('should return false for non-Whop users', async () => {
      // Mock non-Whop user
      const { getAuthenticatedUser } = await import('@/services/authService');
      (getAuthenticatedUser as any).mockResolvedValueOnce({
        id: 'user-123',
        user_metadata: {} // No whop_user_id
      });

      const result = await canDistributeToWhopMembers('company-123');

      expect(result.canDistribute).toBe(false);
      expect(result.reason).toContain('Only Whop users can distribute');
    });

    it('should return false for unauthenticated users', async () => {
      // Mock unauthenticated user
      const { getAuthenticatedUser } = await import('@/services/authService');
      (getAuthenticatedUser as any).mockResolvedValueOnce(null);

      const result = await canDistributeToWhopMembers('company-123');

      expect(result.canDistribute).toBe(false);
      expect(result.reason).toContain('Authentication required');
    });
  });

  describe('getWhopDistributedAgents', () => {
    it('should fetch distributed agents for a company', async () => {
      const mockAgents = [
        {
          id: 'dist-1',
          agent_id: 'agent-1',
          distributor_id: 'user-123',
          whop_company_id: 'company-123',
          distributed_at: '2025-07-02T10:00:00Z',
          is_active: true,
          agent: {
            id: 'agent-1',
            name: 'Test Agent',
            description: 'A test agent'
          }
        }
      ];

      const { supabase } = await import('@/integrations/supabase/client');
      (supabase.from as any).mockReturnValueOnce({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            eq: vi.fn(() => ({
              order: vi.fn(() => Promise.resolve({ data: mockAgents, error: null }))
            }))
          }))
        }))
      });

      const result = await getWhopDistributedAgents('company-123');

      expect(result).toEqual(mockAgents);
    });
  });

  describe('getWhopMemberAgents', () => {
    it('should fetch agents distributed to the current user', async () => {
      const mockMemberAgents = [
        {
          id: 'member-1',
          whop_distribution_id: 'dist-1',
          member_user_id: 'user-123',
          whop_member_id: 'whop-member-123',
          agent_id: 'agent-1',
          custom_name: 'My Distributed Agent',
          added_at: '2025-07-02T10:00:00Z',
          is_active: true,
          agent: {
            id: 'agent-1',
            name: 'Test Agent',
            description: 'A test agent',
            configuration: { blocks: [], entryBlockId: '' }
          }
        }
      ];

      const { supabase } = await import('@/integrations/supabase/client');
      (supabase.from as any).mockReturnValueOnce({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            eq: vi.fn(() => ({
              order: vi.fn(() => Promise.resolve({ data: mockMemberAgents, error: null }))
            }))
          }))
        }))
      });

      const result = await getWhopMemberAgents();

      expect(result).toEqual(mockMemberAgents);
    });
  });

  describe('removeWhopAgentDistribution', () => {
    it('should successfully remove a distribution', async () => {
      const { supabase } = await import('@/integrations/supabase/client');
      (supabase.from as any).mockReturnValueOnce({
        update: vi.fn(() => ({
          eq: vi.fn(() => ({
            eq: vi.fn(() => Promise.resolve({ error: null }))
          }))
        }))
      });

      const result = await removeWhopAgentDistribution('dist-123');

      expect(result.success).toBe(true);
    });

    it('should handle removal errors', async () => {
      const { supabase } = await import('@/integrations/supabase/client');
      (supabase.from as any).mockReturnValueOnce({
        update: vi.fn(() => ({
          eq: vi.fn(() => ({
            eq: vi.fn(() => Promise.resolve({ error: new Error('Database error') }))
          }))
        }))
      });

      const result = await removeWhopAgentDistribution('dist-123');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Database error');
    });
  });
});

describe('Whop Distribution Integration', () => {
  it('should have all required environment variables', () => {
    // Test that the required environment variables are available
    const requiredVars = [
      'VITE_WHOP_COMPANY_ID',
      'VITE_SUPABASE_URL'
    ];

    // In a real environment, these would be set
    // For testing, we just verify the structure is correct
    expect(typeof import.meta.env).toBe('object');
  });

  it('should handle the complete distribution workflow', async () => {
    // This is an integration test that would verify the complete workflow:
    // 1. User is authenticated as Whop admin
    // 2. User selects an agent to distribute
    // 3. System verifies permissions
    // 4. System fetches all Whop members
    // 5. System creates distribution records
    // 6. Members can see the distributed agent in their library

    // For now, we just verify the structure is in place
    expect(distributeAgentToWhopMembers).toBeDefined();
    expect(getWhopMemberAgents).toBeDefined();
    expect(canDistributeToWhopMembers).toBeDefined();
  });
});
