import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { GamificationProvider, useGamification } from '@/contexts/GamificationContext';
import { AuthProvider } from '@/contexts/AuthContext';

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn(() => Promise.resolve({ data: null, error: null }))
        }))
      })),
      upsert: vi.fn(() => ({
        select: vi.fn(() => Promise.resolve({ data: [], error: null }))
      })),
      insert: vi.fn(() => Promise.resolve({ error: null }))
    })),
    auth: {
      getUser: vi.fn(() => Promise.resolve({ 
        data: { user: { id: 'test-user-id' } }, 
        error: null 
      }))
    }
  }
}));

// Mock AuthContext
vi.mock('@/contexts/AuthContext', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useAuth: () => ({
    user: { id: 'test-user-id', email: '<EMAIL>' }
  })
}));

// Test component to interact with gamification context
const TestComponent = () => {
  const { userProgress, achievements, trackAction } = useGamification();
  
  return (
    <div>
      <div data-testid="xp">{userProgress.xp}</div>
      <div data-testid="level">{userProgress.level}</div>
      <div data-testid="unlocked-achievements">
        {userProgress.unlockedAchievements.join(',')}
      </div>
      <div data-testid="achievements-count">
        {achievements.filter(a => a.unlocked).length}
      </div>
      <button 
        data-testid="create-portfolio" 
        onClick={() => trackAction('portfolio_created')}
      >
        Create Portfolio
      </button>
      <button 
        data-testid="build-agent" 
        onClick={() => trackAction('agent_built')}
      >
        Build Agent
      </button>
      <button 
        data-testid="complete-scan" 
        onClick={() => trackAction('scan_completed', { stocksScanned: 10 })}
      >
        Complete Scan
      </button>
    </div>
  );
};

const renderWithProviders = (component: React.ReactNode) => {
  return render(
    <AuthProvider>
      <GamificationProvider>
        {component}
      </GamificationProvider>
    </AuthProvider>
  );
};

describe('Achievement System', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should initialize with default values', () => {
    renderWithProviders(<TestComponent />);
    
    expect(screen.getByTestId('xp')).toHaveTextContent('0');
    expect(screen.getByTestId('level')).toHaveTextContent('1');
    expect(screen.getByTestId('unlocked-achievements')).toHaveTextContent('');
    expect(screen.getByTestId('achievements-count')).toHaveTextContent('0');
  });

  it('should unlock first portfolio achievement only once', async () => {
    renderWithProviders(<TestComponent />);

    const createPortfolioBtn = screen.getByTestId('create-portfolio');

    // First portfolio creation should unlock achievement
    createPortfolioBtn.click();

    await waitFor(() => {
      expect(screen.getByTestId('unlocked-achievements')).toHaveTextContent('first_portfolio');
      expect(screen.getByTestId('achievements-count')).toHaveTextContent('1');
      // Note: Due to async nature, we might see just the action XP initially
      expect(parseInt(screen.getByTestId('xp').textContent || '0')).toBeGreaterThanOrEqual(50);
    });

    // Second portfolio creation should NOT unlock achievement again
    createPortfolioBtn.click();

    await waitFor(() => {
      // Achievement should still only be unlocked once
      expect(screen.getByTestId('unlocked-achievements')).toHaveTextContent('first_portfolio');
      expect(screen.getByTestId('achievements-count')).toHaveTextContent('1');
      // Should have at least 100 XP (50 + 50 for two portfolios, achievement XP may be async)
      expect(parseInt(screen.getByTestId('xp').textContent || '0')).toBeGreaterThanOrEqual(100);
    });
  });

  it('should unlock first agent achievement only once', async () => {
    renderWithProviders(<TestComponent />);

    const buildAgentBtn = screen.getByTestId('build-agent');

    // First agent build should unlock achievement
    buildAgentBtn.click();

    await waitFor(() => {
      expect(screen.getByTestId('unlocked-achievements')).toHaveTextContent('first_agent');
      expect(screen.getByTestId('achievements-count')).toHaveTextContent('1');
      // Should have at least 150 XP for the agent action
      expect(parseInt(screen.getByTestId('xp').textContent || '0')).toBeGreaterThanOrEqual(150);
    });

    // Second agent build should NOT unlock achievement again
    buildAgentBtn.click();

    await waitFor(() => {
      // Achievement should still only be unlocked once
      expect(screen.getByTestId('unlocked-achievements')).toHaveTextContent('first_agent');
      expect(screen.getByTestId('achievements-count')).toHaveTextContent('1');
      // Should have at least 300 XP (150 + 150 for two agents, achievement XP may be async)
      expect(parseInt(screen.getByTestId('xp').textContent || '0')).toBeGreaterThanOrEqual(300);
    });
  });

  it('should unlock scan achievement only once', async () => {
    renderWithProviders(<TestComponent />);

    const completeScanBtn = screen.getByTestId('complete-scan');

    // First scan should unlock achievement
    completeScanBtn.click();

    await waitFor(() => {
      expect(screen.getByTestId('unlocked-achievements')).toHaveTextContent('scan_novice');
      expect(screen.getByTestId('achievements-count')).toHaveTextContent('1');
      // Should have at least 75 XP for the scan action
      expect(parseInt(screen.getByTestId('xp').textContent || '0')).toBeGreaterThanOrEqual(75);
    });

    // Second scan should NOT unlock achievement again
    completeScanBtn.click();

    await waitFor(() => {
      // Achievement should still only be unlocked once
      expect(screen.getByTestId('unlocked-achievements')).toHaveTextContent('scan_novice');
      expect(screen.getByTestId('achievements-count')).toHaveTextContent('1');
      // Should have at least 150 XP (75 + 75 for two scans, achievement XP may be async)
      expect(parseInt(screen.getByTestId('xp').textContent || '0')).toBeGreaterThanOrEqual(150);
    });
  });

  it('should unlock multiple different achievements', async () => {
    renderWithProviders(<TestComponent />);

    const createPortfolioBtn = screen.getByTestId('create-portfolio');
    const buildAgentBtn = screen.getByTestId('build-agent');
    const completeScanBtn = screen.getByTestId('complete-scan');

    // Trigger different actions to unlock multiple achievements
    createPortfolioBtn.click();
    buildAgentBtn.click();
    completeScanBtn.click();

    await waitFor(() => {
      const unlockedAchievements = screen.getByTestId('unlocked-achievements').textContent;
      expect(unlockedAchievements).toContain('first_portfolio');
      expect(unlockedAchievements).toContain('first_agent');
      expect(unlockedAchievements).toContain('scan_novice');
      expect(screen.getByTestId('achievements-count')).toHaveTextContent('3');

      // Should have at least 275 XP (50 + 150 + 75 for actions, achievement XP may be async)
      expect(parseInt(screen.getByTestId('xp').textContent || '0')).toBeGreaterThanOrEqual(275);
    });
  });
});
