import { describe, it, expect, vi, beforeEach } from 'vitest';
import { PerformanceTimer, getMemoryUsage } from '@/test/utils/testHelpers';
import { generateMockPriceData, calculateMockRSI, MARKET_SCENARIOS } from '@/test/utils/mockMarketData';

describe('Agent Performance Benchmarks', () => {
  let timer: PerformanceTimer;

  beforeEach(() => {
    timer = new PerformanceTimer();
  });

  describe('Technical Indicator Performance', () => {
    it('calculates RSI for large datasets efficiently', () => {
      const datasetSizes = [100, 1000, 5000, 10000];
      
      datasetSizes.forEach(size => {
        const priceData = generateMockPriceData(size, 100, 0.02);
        const prices = priceData.map(d => d.close);
        
        timer.start();
        const rsiValues = calculateMockRSI(prices, 14);
        const calculationTime = timer.stop();
        
        expect(rsiValues).toHaveLength(size);
        expect(calculationTime).toBeLessThan(size * 0.1); // Should be O(n) or better
        
        console.log(`RSI calculation for ${size} data points: ${calculationTime.toFixed(2)}ms`);
      });
    });

    it('calculates multiple indicators simultaneously', () => {
      const priceData = generateMockPriceData(1000, 100, 0.02);
      const prices = priceData.map(d => d.close);
      const highs = priceData.map(d => d.high);
      const lows = priceData.map(d => d.low);
      const volumes = priceData.map(d => d.volume);
      
      timer.start();
      
      // Calculate multiple indicators
      const rsi = calculateMockRSI(prices, 14);
      const sma20 = calculateSMA(prices, 20);
      const sma50 = calculateSMA(prices, 50);
      const ema12 = calculateEMA(prices, 12);
      const atr = calculateATR(highs, lows, prices, 14);
      const bb = calculateBollingerBands(prices, 20, 2);
      
      const totalTime = timer.stop();
      
      expect(rsi).toHaveLength(1000);
      expect(sma20).toHaveLength(1000);
      expect(sma50).toHaveLength(1000);
      expect(ema12).toHaveLength(1000);
      expect(atr).toHaveLength(1000);
      expect(bb.upper).toHaveLength(1000);
      
      expect(totalTime).toBeLessThan(100); // All indicators in under 100ms
      console.log(`Multiple indicators calculation: ${totalTime.toFixed(2)}ms`);
    });

    it('benchmarks indicator calculation complexity', () => {
      const periods = [14, 20, 50, 100, 200];
      const dataSize = 1000;
      
      periods.forEach(period => {
        const priceData = generateMockPriceData(dataSize, 100, 0.02);
        const prices = priceData.map(d => d.close);
        
        timer.start();
        const sma = calculateSMA(prices, period);
        const smaTime = timer.stop();
        
        timer.start();
        const ema = calculateEMA(prices, period);
        const emaTime = timer.stop();
        
        expect(sma).toHaveLength(dataSize);
        expect(ema).toHaveLength(dataSize);
        
        // EMA should be similar or faster than SMA (allow for timing precision)
        expect(emaTime).toBeLessThanOrEqual(Math.max(smaTime * 2, 5)); // At least 5ms tolerance
        
        console.log(`Period ${period} - SMA: ${smaTime.toFixed(2)}ms, EMA: ${emaTime.toFixed(2)}ms`);
      });
    });
  });

  describe('Signal Generation Performance', () => {
    it('generates signals for high-frequency data', () => {
      const tickData = Array.from({ length: 10000 }, (_, i) => ({
        timestamp: Date.now() + i * 100, // 100ms intervals
        price: 100 + Math.sin(i * 0.01) * 5 + Math.random() * 0.5,
        volume: 1000 + Math.random() * 500
      }));
      
      timer.start();
      
      const signals = tickData.map((tick, i) => {
        if (i < 14) return { signal: 'neutral', confidence: 50 };
        
        // Simple momentum signal
        const recentPrices = tickData.slice(i - 14, i).map(t => t.price);
        const avgPrice = recentPrices.reduce((sum, p) => sum + p, 0) / recentPrices.length;
        const momentum = (tick.price - avgPrice) / avgPrice;
        
        if (momentum > 0.01) return { signal: 'bullish', confidence: Math.min(90, 50 + momentum * 1000) };
        if (momentum < -0.01) return { signal: 'bearish', confidence: Math.min(90, 50 + Math.abs(momentum) * 1000) };
        return { signal: 'neutral', confidence: 50 };
      });
      
      const processingTime = timer.stop();
      
      expect(signals).toHaveLength(10000);
      expect(processingTime).toBeLessThan(500); // Process 10k ticks in under 500ms
      
      console.log(`High-frequency signal generation: ${processingTime.toFixed(2)}ms for 10k ticks`);
    });

    it('benchmarks complex signal strategies', () => {
      const priceData = generateMockPriceData(1000, 100, 0.02);
      const prices = priceData.map(d => d.close);
      const volumes = priceData.map(d => d.volume);
      
      timer.start();
      
      // Complex multi-indicator strategy
      const rsi = calculateMockRSI(prices, 14);
      const sma20 = calculateSMA(prices, 20);
      const sma50 = calculateSMA(prices, 50);
      const volumeSMA = calculateSMA(volumes, 20);
      
      const complexSignals = prices.map((price, i) => {
        if (i < 50) return { signal: 'neutral', confidence: 50 };
        
        const currentRSI = rsi[i];
        const priceAboveSMA20 = price > sma20[i];
        const sma20AboveSMA50 = sma20[i] > sma50[i];
        const volumeAboveAvg = volumes[i] > volumeSMA[i] * 1.2;
        
        let signal = 'neutral';
        let confidence = 50;
        
        if (currentRSI < 30 && priceAboveSMA20 && sma20AboveSMA50 && volumeAboveAvg) {
          signal = 'bullish';
          confidence = 85;
        } else if (currentRSI > 70 && !priceAboveSMA20 && !sma20AboveSMA50) {
          signal = 'bearish';
          confidence = 80;
        }
        
        return { signal, confidence };
      });
      
      const strategyTime = timer.stop();
      
      expect(complexSignals).toHaveLength(1000);
      expect(strategyTime).toBeLessThan(50); // Complex strategy in under 50ms
      
      console.log(`Complex strategy calculation: ${strategyTime.toFixed(2)}ms`);
    });
  });

  describe('Memory Usage Benchmarks', () => {
    it('monitors memory usage during large calculations', () => {
      const initialMemory = getMemoryUsage();
      
      // Create large dataset
      const largeDataset = generateMockPriceData(50000, 100, 0.02);
      const afterCreationMemory = getMemoryUsage();
      
      // Process dataset
      const prices = largeDataset.map(d => d.close);
      const rsi = calculateMockRSI(prices, 14);
      const sma = calculateSMA(prices, 20);
      const afterProcessingMemory = getMemoryUsage();
      
      // Clean up
      largeDataset.length = 0;
      prices.length = 0;
      rsi.length = 0;
      sma.length = 0;
      
      if (initialMemory > 0) {
        const creationIncrease = afterCreationMemory - initialMemory;
        const processingIncrease = afterProcessingMemory - afterCreationMemory;
        
        console.log(`Memory usage - Creation: ${(creationIncrease / 1024 / 1024).toFixed(2)}MB`);
        console.log(`Memory usage - Processing: ${(processingIncrease / 1024 / 1024).toFixed(2)}MB`);
        
        // Memory usage should be reasonable
        expect(creationIncrease).toBeLessThan(100 * 1024 * 1024); // Less than 100MB
        expect(processingIncrease).toBeLessThan(50 * 1024 * 1024); // Less than 50MB additional
      }
    });

    it('tests memory efficiency of different data structures', () => {
      const dataSize = 10000;
      const initialMemory = getMemoryUsage();
      
      // Test array storage
      timer.start();
      const arrayData = Array.from({ length: dataSize }, (_, i) => ({
        timestamp: Date.now() + i,
        price: 100 + Math.random() * 10,
        volume: 1000000 + Math.random() * 500000
      }));
      const arrayTime = timer.stop();
      const arrayMemory = getMemoryUsage();
      
      // Test typed array storage
      timer.start();
      const priceBuffer = new Float64Array(dataSize);
      const volumeBuffer = new Float64Array(dataSize);
      const timestampBuffer = new Float64Array(dataSize);
      
      for (let i = 0; i < dataSize; i++) {
        timestampBuffer[i] = Date.now() + i;
        priceBuffer[i] = 100 + Math.random() * 10;
        volumeBuffer[i] = 1000000 + Math.random() * 500000;
      }
      const typedArrayTime = timer.stop();
      const typedArrayMemory = getMemoryUsage();
      
      if (initialMemory > 0) {
        const arrayMemoryUsage = arrayMemory - initialMemory;
        const typedArrayMemoryUsage = typedArrayMemory - arrayMemory;
        
        console.log(`Array creation: ${arrayTime.toFixed(2)}ms, Memory: ${(arrayMemoryUsage / 1024 / 1024).toFixed(2)}MB`);
        console.log(`Typed array creation: ${typedArrayTime.toFixed(2)}ms, Memory: ${(typedArrayMemoryUsage / 1024 / 1024).toFixed(2)}MB`);
        
        // Typed arrays should be more memory efficient (if memory measurement is available)
        if (arrayMemoryUsage > 0 && typedArrayMemoryUsage > 0) {
          expect(typedArrayMemoryUsage).toBeLessThan(arrayMemoryUsage);
        } else {
          // If memory measurement isn't available, just check that both completed successfully
          expect(arrayTime).toBeGreaterThanOrEqual(0);
          expect(typedArrayTime).toBeGreaterThanOrEqual(0);
        }
      }
    });
  });

  describe('Concurrent Processing Benchmarks', () => {
    it('benchmarks parallel indicator calculations', async () => {
      const priceData = generateMockPriceData(5000, 100, 0.02);
      const prices = priceData.map(d => d.close);
      
      // Sequential processing
      timer.start();
      const rsi1 = calculateMockRSI(prices, 14);
      const sma1 = calculateSMA(prices, 20);
      const ema1 = calculateEMA(prices, 12);
      const sequentialTime = timer.stop();
      
      // Parallel processing (simulated with Promise.all)
      timer.start();
      const [rsi2, sma2, ema2] = await Promise.all([
        Promise.resolve(calculateMockRSI(prices, 14)),
        Promise.resolve(calculateSMA(prices, 20)),
        Promise.resolve(calculateEMA(prices, 12))
      ]);
      const parallelTime = timer.stop();
      
      expect(rsi1).toEqual(rsi2);
      expect(sma1).toEqual(sma2);
      expect(ema1).toEqual(ema2);
      
      console.log(`Sequential: ${sequentialTime.toFixed(2)}ms, Parallel: ${parallelTime.toFixed(2)}ms`);
      
      // Parallel should be similar or faster (in real async scenarios, allow for timing precision)
      expect(parallelTime).toBeLessThanOrEqual(Math.max(sequentialTime * 1.5, 5)); // Allow more tolerance
    });
  });

  describe('Real-time Performance Simulation', () => {
    it('simulates real-time market data processing', async () => {
      const processingTimes: number[] = [];
      const signalLatencies: number[] = [];
      
      // Simulate 100 market updates
      for (let i = 0; i < 100; i++) {
        const updateStart = performance.now();
        
        // Simulate market data update
        const marketUpdate = {
          timestamp: Date.now(),
          price: 100 + Math.sin(i * 0.1) * 5,
          volume: 1000000 + Math.random() * 500000
        };
        
        // Process update and generate signal
        timer.start();
        const signal = processMarketUpdate(marketUpdate, i);
        const processingTime = timer.stop();
        
        const updateEnd = performance.now();
        const totalLatency = updateEnd - updateStart;
        
        processingTimes.push(processingTime);
        signalLatencies.push(totalLatency);
        
        expect(signal).toBeDefined();
        expect(processingTime).toBeLessThan(10); // Each update should process in under 10ms
      }
      
      const avgProcessingTime = processingTimes.reduce((sum, time) => sum + time, 0) / processingTimes.length;
      const maxProcessingTime = Math.max(...processingTimes);
      const avgLatency = signalLatencies.reduce((sum, time) => sum + time, 0) / signalLatencies.length;
      
      console.log(`Average processing time: ${avgProcessingTime.toFixed(2)}ms`);
      console.log(`Max processing time: ${maxProcessingTime.toFixed(2)}ms`);
      console.log(`Average total latency: ${avgLatency.toFixed(2)}ms`);
      
      expect(avgProcessingTime).toBeLessThan(5);
      expect(maxProcessingTime).toBeLessThan(20);
      expect(avgLatency).toBeLessThan(15);
    });
  });
});

// Helper functions for performance tests
function calculateSMA(prices: number[], period: number): number[] {
  const sma: number[] = [];
  for (let i = 0; i < prices.length; i++) {
    if (i < period - 1) {
      sma.push(prices[i]);
      continue;
    }
    
    let sum = 0;
    for (let j = i - period + 1; j <= i; j++) {
      sum += prices[j];
    }
    sma.push(sum / period);
  }
  return sma;
}

function calculateEMA(prices: number[], period: number): number[] {
  const ema: number[] = [];
  const multiplier = 2 / (period + 1);
  
  ema[0] = prices[0];
  
  for (let i = 1; i < prices.length; i++) {
    ema[i] = (prices[i] * multiplier) + (ema[i - 1] * (1 - multiplier));
  }
  
  return ema;
}

function calculateATR(highs: number[], lows: number[], closes: number[], period: number): number[] {
  const atr: number[] = [];
  const trueRanges: number[] = [];
  
  for (let i = 0; i < highs.length; i++) {
    if (i === 0) {
      trueRanges.push(highs[i] - lows[i]);
    } else {
      const tr1 = highs[i] - lows[i];
      const tr2 = Math.abs(highs[i] - closes[i - 1]);
      const tr3 = Math.abs(lows[i] - closes[i - 1]);
      trueRanges.push(Math.max(tr1, tr2, tr3));
    }
  }
  
  // Calculate ATR using SMA of true ranges
  for (let i = 0; i < trueRanges.length; i++) {
    if (i < period - 1) {
      atr.push(trueRanges[i]);
      continue;
    }
    
    let sum = 0;
    for (let j = i - period + 1; j <= i; j++) {
      sum += trueRanges[j];
    }
    atr.push(sum / period);
  }
  
  return atr;
}

function calculateBollingerBands(prices: number[], period: number, stdDev: number): { upper: number[], middle: number[], lower: number[] } {
  const middle = calculateSMA(prices, period);
  const upper: number[] = [];
  const lower: number[] = [];
  
  for (let i = 0; i < prices.length; i++) {
    if (i < period - 1) {
      upper.push(prices[i]);
      lower.push(prices[i]);
      continue;
    }
    
    // Calculate standard deviation
    const slice = prices.slice(i - period + 1, i + 1);
    const mean = middle[i];
    const variance = slice.reduce((sum, price) => sum + Math.pow(price - mean, 2), 0) / period;
    const standardDeviation = Math.sqrt(variance);
    
    upper.push(mean + (standardDeviation * stdDev));
    lower.push(mean - (standardDeviation * stdDev));
  }
  
  return { upper, middle, lower };
}

function processMarketUpdate(update: { timestamp: number, price: number, volume: number }, index: number): { signal: string, confidence: number } {
  // Simple signal generation for performance testing
  const priceChange = Math.sin(index * 0.1) * 0.01;
  
  if (priceChange > 0.005) {
    return { signal: 'bullish', confidence: 75 };
  } else if (priceChange < -0.005) {
    return { signal: 'bearish', confidence: 75 };
  } else {
    return { signal: 'neutral', confidence: 50 };
  }
}
