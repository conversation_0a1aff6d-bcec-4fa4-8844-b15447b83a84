export interface WhopCompetition {
  id: string;
  name: string;
  description?: string;
  creator_id: string;
  starting_balance: number;
  max_participants?: number;
  entry_fee?: number;
  prize_pool?: number;
  rules?: any;
  allowed_securities?: string[];
  position_limits?: any;
  status: 'draft' | 'open' | 'active' | 'completed' | 'cancelled';
  registration_start?: string;
  registration_end?: string;
  competition_start: string;
  competition_end: string;
  created_at: string;
  updated_at: string;
  
  // Whop-specific fields
  whop_company_id?: string;
  whop_business_id?: string;
  whop_business_handle?: string;
  is_cross_community?: boolean;
  allowed_whop_communities?: string[];
  competition_scope: 'public' | 'whop_local' | 'whop_cross_community';
}

export interface CreateWhopCompetitionRequest {
  name: string;
  description?: string;
  starting_balance: number;
  max_participants?: number;
  entry_fee?: number;
  prize_pool?: number;
  rules?: any;
  allowed_securities?: string[];
  position_limits?: any;
  registration_start?: string;
  registration_end?: string;
  competition_start: string;
  competition_end: string;
  competition_scope: 'public' | 'whop_local' | 'whop_cross_community';
  is_cross_community?: boolean;
  allowed_whop_communities?: string[];
}

export interface WhopCompetitionPermissions {
  canCreateLocal: boolean;
  canCreateCrossCommunity: boolean;
  isWhopOwner: boolean;
  isOfficialOsis: boolean;
  whopCompanyId?: string;
  whopBusinessId?: string;
  whopBusinessHandle?: string;
}

export interface WhopCommunityInfo {
  whopUserId?: string;
  whopAccessLevel?: string;
  whopCompanyId?: string;
  whopBusinessId?: string;
  whopBusinessHandle?: string;
  isWhopUser: boolean;
  isWhopOwner: boolean;
  isOfficialOsis: boolean;
}

export interface CompetitionScopeOption {
  value: 'public' | 'whop_local' | 'whop_cross_community';
  label: string;
  description: string;
  requiresWhopOwner: boolean;
  requiresOfficialOsis: boolean;
  icon?: string;
}

export const COMPETITION_SCOPE_OPTIONS: CompetitionScopeOption[] = [
  {
    value: 'public',
    label: 'Public Competition',
    description: 'Open to all users on the platform',
    requiresWhopOwner: false,
    requiresOfficialOsis: false,
    icon: '🌍'
  },
  {
    value: 'whop_local',
    label: 'Community Competition',
    description: 'Restricted to members of your Whop community only',
    requiresWhopOwner: true,
    requiresOfficialOsis: false,
    icon: '👥'
  },
  {
    value: 'whop_cross_community',
    label: 'Cross-Community Competition',
    description: 'Open to all Whop users across different communities',
    requiresWhopOwner: true,
    requiresOfficialOsis: true,
    icon: '🌐'
  }
];

export interface WhopCompetitionJoinPermission {
  canJoin: boolean;
  reason?: string;
  requiresWhopMembership?: boolean;
  requiredCommunity?: string;
}

export interface WhopCompetitionFilter {
  scope?: 'all' | 'public' | 'whop_local' | 'whop_cross_community';
  status?: 'all' | 'open' | 'active' | 'completed';
  userCanJoin?: boolean;
}

// Constants for official Osis Whop identification
export const OFFICIAL_OSIS_BUSINESS_ID = 'biz_OGyv6Pz0Le35Fa';
export const OFFICIAL_OSIS_HANDLE = 'tryosis';
