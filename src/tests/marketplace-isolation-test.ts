/**
 * Marketplace Isolation Test Suite
 * Verifies strict separation between free and paid marketplace sections
 */

import { supabase } from '@/integrations/supabase/client';
import { getPaidMarketplaceAgents } from '@/services/marketplaceService';
import { getFreeMarketplaceAgents } from '@/services/discoverService';

interface IsolationTestResult {
  testName: string;
  passed: boolean;
  error?: string;
  details?: any;
}

export class MarketplaceIsolationTester {
  private results: IsolationTestResult[] = [];

  async runAllTests(): Promise<IsolationTestResult[]> {
    console.log('🔒 Starting marketplace isolation tests...');

    await this.testPaidAgentIsolation();
    await this.testFreeAgentIsolation();
    await this.testCrossContamination();
    await this.testCompleteRemoval();
    await this.testDatabaseConsistency();

    console.log('✅ All isolation tests completed');
    return this.results;
  }

  private async testPaidAgentIsolation(): Promise<void> {
    try {
      console.log('💰 Testing paid agent isolation...');
      
      const result = await getPaidMarketplaceAgents({ limit: 50 });

      if (result.success) {
        // Verify all returned agents are truly paid
        const invalidAgents = result.agents.filter(agent => 
          !agent.is_paid || 
          !agent.price || 
          agent.price <= 0 || 
          !agent.is_for_sale ||
          !agent.is_public
        );

        // Check for cross-contamination with published_agents
        const agentIds = result.agents.map(a => a.id);
        const { data: publishedCheck } = await supabase
          .from('published_agents')
          .select('agent_id')
          .in('agent_id', agentIds)
          .eq('is_active', true);

        const crossContaminated = publishedCheck?.length || 0;

        if (invalidAgents.length === 0 && crossContaminated === 0) {
          this.addResult('Paid Agent Isolation', true, undefined, {
            totalPaidAgents: result.agents.length,
            invalidAgents: 0,
            crossContaminated: 0
          });
        } else {
          this.addResult('Paid Agent Isolation', false, 
            `Found ${invalidAgents.length} invalid agents and ${crossContaminated} cross-contaminated agents`);
        }
      } else {
        this.addResult('Paid Agent Isolation', false, result.error);
      }
    } catch (error) {
      this.addResult('Paid Agent Isolation', false, error.message);
    }
  }

  private async testFreeAgentIsolation(): Promise<void> {
    try {
      console.log('🆓 Testing free agent isolation...');
      
      const result = await getFreeMarketplaceAgents({ limit: 50 });

      if (result.success) {
        // Verify all returned agents are truly free
        const agentIds = result.agents.map(a => a.agentId || a.id);
        
        // Check for cross-contamination with paid agents
        const { data: paidCheck } = await supabase
          .from('agents')
          .select('id, is_for_sale, price')
          .in('id', agentIds)
          .eq('is_for_sale', true);

        const crossContaminated = paidCheck?.filter(a => a.price && a.price > 0).length || 0;

        if (crossContaminated === 0) {
          this.addResult('Free Agent Isolation', true, undefined, {
            totalFreeAgents: result.agents.length,
            crossContaminated: 0
          });
        } else {
          this.addResult('Free Agent Isolation', false, 
            `Found ${crossContaminated} agents that are also for sale`);
        }
      } else {
        this.addResult('Free Agent Isolation', false, result.error);
      }
    } catch (error) {
      this.addResult('Free Agent Isolation', false, error.message);
    }
  }

  private async testCrossContamination(): Promise<void> {
    try {
      console.log('🔍 Testing for cross-contamination...');
      
      // Find agents that appear in both marketplaces
      const { data: crossContaminated, error } = await supabase
        .from('agents')
        .select(`
          id, name, is_for_sale, price, is_public,
          published_agents!inner(id, is_active)
        `)
        .eq('is_for_sale', true)
        .eq('is_public', true)
        .gt('price', 0)
        .eq('published_agents.is_active', true);

      if (error) {
        this.addResult('Cross-Contamination Check', false, error.message);
        return;
      }

      if (!crossContaminated || crossContaminated.length === 0) {
        this.addResult('Cross-Contamination Check', true, undefined, {
          crossContaminatedAgents: 0
        });
      } else {
        this.addResult('Cross-Contamination Check', false, 
          `Found ${crossContaminated.length} agents in both marketplaces`, {
          crossContaminatedAgents: crossContaminated.length,
          agents: crossContaminated.map(a => ({ id: a.id, name: a.name }))
        });
      }
    } catch (error) {
      this.addResult('Cross-Contamination Check', false, error.message);
    }
  }

  private async testCompleteRemoval(): Promise<void> {
    try {
      console.log('🗑️ Testing complete removal functionality...');
      
      // Check for agents that should be completely removed but still appear
      const { data: shouldBeRemoved, error } = await supabase
        .from('agents')
        .select(`
          id, name, is_for_sale, is_public, price,
          published_agents(id, is_active)
        `)
        .eq('is_for_sale', false)
        .eq('is_public', false)
        .is('price', null);

      if (error) {
        this.addResult('Complete Removal Test', false, error.message);
        return;
      }

      // Filter for agents that are marked as removed but still have active published entries
      const stillInMarketplace = shouldBeRemoved?.filter(agent => 
        agent.published_agents && 
        agent.published_agents.some((pa: any) => pa.is_active)
      ) || [];

      if (stillInMarketplace.length === 0) {
        this.addResult('Complete Removal Test', true, undefined, {
          removedAgents: shouldBeRemoved?.length || 0,
          stillInMarketplace: 0
        });
      } else {
        this.addResult('Complete Removal Test', false, 
          `Found ${stillInMarketplace.length} agents that should be removed but still appear in marketplace`);
      }
    } catch (error) {
      this.addResult('Complete Removal Test', false, error.message);
    }
  }

  private async testDatabaseConsistency(): Promise<void> {
    try {
      console.log('🔍 Testing database consistency...');
      
      // Check for invalid states
      const { data: invalidStates, error } = await supabase
        .from('agents')
        .select('id, name, is_for_sale, is_public, price')
        .or('and(is_for_sale.eq.true,price.is.null),and(is_for_sale.eq.true,price.lte.0),and(is_for_sale.eq.true,is_public.eq.false)');

      if (error) {
        this.addResult('Database Consistency', false, error.message);
        return;
      }

      if (!invalidStates || invalidStates.length === 0) {
        this.addResult('Database Consistency', true, undefined, {
          invalidStates: 0
        });
      } else {
        this.addResult('Database Consistency', false, 
          `Found ${invalidStates.length} agents with invalid states`);
      }
    } catch (error) {
      this.addResult('Database Consistency', false, error.message);
    }
  }

  private addResult(testName: string, passed: boolean, error?: string, details?: any): void {
    this.results.push({
      testName,
      passed,
      error,
      details
    });

    const status = passed ? '✅' : '❌';
    console.log(`${status} ${testName}: ${passed ? 'PASSED' : 'FAILED'}${error ? ` - ${error}` : ''}`);
  }

  printSummary(): void {
    const passed = this.results.filter(r => r.passed).length;
    const total = this.results.length;
    
    console.log('\n🔒 Marketplace Isolation Test Summary:');
    console.log(`Passed: ${passed}/${total}`);
    console.log(`Failed: ${total - passed}/${total}`);
    
    if (total - passed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results.filter(r => !r.passed).forEach(result => {
        console.log(`  - ${result.testName}: ${result.error}`);
      });
    }
  }
}

// Export a function to run the isolation tests
export async function runMarketplaceIsolationTests(): Promise<IsolationTestResult[]> {
  const tester = new MarketplaceIsolationTester();
  const results = await tester.runAllTests();
  tester.printSummary();
  return results;
}
