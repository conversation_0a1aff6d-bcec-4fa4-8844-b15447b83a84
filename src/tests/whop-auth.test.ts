/**
 * Test for Whop User Automatic Authentication Flow
 * 
 * This test verifies that:
 * 1. Whop users are created with the correct email format (<EMAIL>)
 * 2. Whop users are automatically signed in after creation
 * 3. Re-authentication works when a session is lost
 * 4. Error handling works correctly
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock the Supabase client
const mockSupabase = {
  auth: {
    getUser: vi.fn(),
    signInWithPassword: vi.fn(),
    signOut: vi.fn(),
    onAuthStateChange: vi.fn(() => ({ data: { subscription: { unsubscribe: vi.fn() } } }))
  }
};

// Mock the whop intermediary client
const mockWhopIntermediaryClient = {
  initializeAuth: vi.fn(),
  createSupabaseUser: vi.fn()
};

// Mock the whop API client
const mockWhopApiClient = {
  createSupabaseUser: vi.fn()
};

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
};

// Mock the imports
vi.mock('@/integrations/supabase/client', () => ({
  supabase: mockSupabase
}));

vi.mock('@/lib/whopIntermediaryClient', () => ({
  whopIntermediaryClient: mockWhopIntermediaryClient
}));

vi.mock('@/lib/whop-sdk', () => ({
  whopApiClient: mockWhopApiClient
}));

// Mock localStorage
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
});

describe('Whop Authentication Flow', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks();
    
    // Setup default mock implementations
    mockLocalStorage.getItem.mockReturnValue(null);
    mockSupabase.auth.getUser.mockResolvedValue({ data: { user: null } });
    mockWhopIntermediaryClient.initializeAuth.mockResolvedValue({
      isWhopUser: false,
      user: null,
      accessResult: null
    });
  });

  it('should create Whop user with correct email format', async () => {
    const mockWhopUser = {
      id: 'test_user_123',
      username: 'testuser',
      email: null,
      profilePicUrl: 'https://example.com/avatar.jpg',
      isWhopUser: true
    };

    const expectedCredentials = {
      email: '<EMAIL>',
      password: 'test_user_123'
    };

    mockWhopApiClient.createSupabaseUser.mockResolvedValue({
      success: true,
      whopUser: mockWhopUser,
      supabaseUser: {
        id: 'supabase_user_id',
        email: expectedCredentials.email,
        user_metadata: { isWhopUser: true }
      },
      credentials: expectedCredentials
    });

    const result = await mockWhopApiClient.createSupabaseUser();

    expect(result.success).toBe(true);
    expect(result.credentials.email).toBe('<EMAIL>');
    expect(result.credentials.password).toBe('test_user_123');
  });

  it('should automatically sign in Whop user after creation', async () => {
    const mockWhopUser = {
      id: 'test_user_123',
      username: 'testuser',
      email: null,
      profilePicUrl: 'https://example.com/avatar.jpg',
      isWhopUser: true
    };

    const expectedCredentials = {
      email: '<EMAIL>',
      password: 'test_user_123'
    };

    mockWhopApiClient.createSupabaseUser.mockResolvedValue({
      success: true,
      whopUser: mockWhopUser,
      supabaseUser: {
        id: 'supabase_user_id',
        email: expectedCredentials.email,
        user_metadata: { isWhopUser: true }
      },
      credentials: expectedCredentials
    });

    mockSupabase.auth.signInWithPassword.mockResolvedValue({
      data: {
        session: {
          access_token: 'mock_token',
          user: {
            id: 'supabase_user_id',
            email: expectedCredentials.email,
            user_metadata: { isWhopUser: true }
          }
        }
      },
      error: null
    });

    // Simulate the authentication flow
    const createResult = await mockWhopApiClient.createSupabaseUser();
    expect(createResult.success).toBe(true);

    // Simulate automatic sign-in
    const signInResult = await mockSupabase.auth.signInWithPassword({
      email: createResult.credentials.email,
      password: createResult.credentials.password
    });

    expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'test_user_123'
    });
    expect(signInResult.data.session).toBeTruthy();
    expect(signInResult.data.session.user.user_metadata.isWhopUser).toBe(true);
  });

  it('should handle sign-in errors gracefully', async () => {
    const mockWhopUser = {
      id: 'test_user_123',
      username: 'testuser',
      email: null,
      profilePicUrl: 'https://example.com/avatar.jpg',
      isWhopUser: true
    };

    const expectedCredentials = {
      email: '<EMAIL>',
      password: 'test_user_123'
    };

    mockWhopApiClient.createSupabaseUser.mockResolvedValue({
      success: true,
      whopUser: mockWhopUser,
      supabaseUser: {
        id: 'supabase_user_id',
        email: expectedCredentials.email,
        user_metadata: { isWhopUser: true }
      },
      credentials: expectedCredentials
    });

    mockSupabase.auth.signInWithPassword.mockResolvedValue({
      data: { session: null },
      error: { message: 'Invalid credentials' }
    });

    // Simulate the authentication flow
    const createResult = await mockWhopApiClient.createSupabaseUser();
    expect(createResult.success).toBe(true);

    // Simulate automatic sign-in failure
    const signInResult = await mockSupabase.auth.signInWithPassword({
      email: createResult.credentials.email,
      password: createResult.credentials.password
    });

    expect(signInResult.error).toBeTruthy();
    expect(signInResult.error.message).toBe('Invalid credentials');
    expect(signInResult.data.session).toBeNull();
  });

  it('should handle re-authentication for existing Whop users', async () => {
    const storedWhopUser = {
      id: 'test_user_123',
      username: 'testuser',
      email: null,
      profilePicUrl: 'https://example.com/avatar.jpg',
      isWhopUser: true
    };

    // Mock localStorage to return stored Whop user data
    mockLocalStorage.getItem.mockReturnValue(JSON.stringify({
      isWhopUser: true,
      user: storedWhopUser,
      accessResult: { hasAccess: true, accessLevel: 'customer' }
    }));

    // Mock no existing Supabase session
    mockSupabase.auth.getUser.mockResolvedValue({ data: { user: null } });

    // Mock successful re-authentication
    mockSupabase.auth.signInWithPassword.mockResolvedValue({
      data: {
        session: {
          access_token: 'mock_token',
          user: {
            id: 'supabase_user_id',
            email: '<EMAIL>',
            user_metadata: { isWhopUser: true }
          }
        }
      },
      error: null
    });

    // Simulate re-authentication
    const signInResult = await mockSupabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'test_user_123'
    });

    expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'test_user_123'
    });
    expect(signInResult.data.session).toBeTruthy();
    expect(signInResult.data.session.user.user_metadata.isWhopUser).toBe(true);
  });

  it('should handle function validation errors', () => {
    // Test that the code handles cases where functions might not be defined
    const refreshWhopAuth = undefined;
    
    // This should not throw an error
    expect(() => {
      if (typeof refreshWhopAuth === 'function') {
        refreshWhopAuth();
      } else {
        console.error('refreshWhopAuth is not a function:', typeof refreshWhopAuth);
      }
    }).not.toThrow();
  });
});
