import { 
  getAppTypeFromExperienceName, 
  getRouteFromExperienceName 
} from '../experienceUtils';

describe('experienceUtils', () => {
  describe('getAppTypeFromExperienceName', () => {
    it('should detect trading experiences', () => {
      expect(getAppTypeFromExperienceName('Trade Sensei')).toBe('trading');
      expect(getAppTypeFromExperienceName('Trading Dashboard')).toBe('trading');
      expect(getAppTypeFromExperienceName('TRADE APP')).toBe('trading');
      expect(getAppTypeFromExperienceName('My Trade Experience')).toBe('trading');
    });

    it('should detect OSIS experiences', () => {
      expect(getAppTypeFromExperienceName('OSIS Dashboard')).toBe('osis');
      expect(getAppTypeFromExperienceName('osis app')).toBe('osis');
      expect(getAppTypeFromExperienceName('My OSIS Experience')).toBe('osis');
      expect(getAppTypeFromExperienceName('')).toBe('osis');
      expect(getAppTypeFromExperienceName('   ')).toBe('osis');
    });

    it('should handle unknown experiences', () => {
      expect(getAppTypeFromExperienceName('Random App')).toBe('unknown');
      expect(getAppTypeFromExperienceName('Something Else')).toBe('unknown');
    });
  });

  describe('getRouteFromExperienceName', () => {
    it('should return correct routes for trading experiences', () => {
      expect(getRouteFromExperienceName('Trade Sensei')).toBe('/trading');
      expect(getRouteFromExperienceName('Trading Dashboard')).toBe('/trading');
    });

    it('should return correct routes for OSIS experiences', () => {
      expect(getRouteFromExperienceName('OSIS Dashboard')).toBe('/');
      expect(getRouteFromExperienceName('')).toBe('/');
    });

    it('should default to trading for unknown experiences', () => {
      expect(getRouteFromExperienceName('Random App')).toBe('/trading');
    });
  });
});
