/**
 * Utility functions for making secure API calls to the Aura edge function
 */

import { encryptData, obfuscateData } from './cryptoUtils';
import { getValidAccessToken, handleAuthError } from './tokenUtils';

// Get the encryption key from environment variables
const ENCRYPTION_KEY = import.meta.env.VITE_ENCRYPTION_KEY || '';

/**
 * Makes a secure API call to the Aura edge function
 * @param action - The action to perform (e.g., 'analyze')
 * @param params - The parameters for the action
 * @returns The API response
 */
export async function callAuraAPI(action: string, params: Record<string, any> = {}) {
  try {
    // Get a valid access token using our token utility
    const accessToken = await getValidAccessToken();

    if (!accessToken) {
      throw new Error('No authentication token found. Please log in.');
    }

    // Combine action and params into a single object
    const requestData = {
      action,
      ...params
    };

    // Encrypt the request data
    const encryptedData = await encryptData(JSON.stringify(requestData), ENCRYPTION_KEY);

    // Create payload with encrypted data
    const payload = {
      data: encryptedData,
      metadata: obfuscateData({
        timestamp: Date.now()
      })
    };

    // Use the Supabase edge function
    const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/aura`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify(payload)
    });

    // Check for authentication errors
    if (response.status === 401) {
      console.warn('Authentication error in API call, attempting to refresh token...');
      // Try to refresh the token and retry the request
      const newToken = await getValidAccessToken();

      if (newToken) {
        // Retry the request with the new token
        const retryResponse = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/aura`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${newToken}`
          },
          body: JSON.stringify(payload)
        });

        // If retry succeeded, use this response
        if (retryResponse.ok) {
          const retryData = await retryResponse.json();
          return retryData;
        }
      }
    }

    // Parse the response
    const responseData = await response.json();

    // Check for errors
    if (responseData.error) {
      // Try to handle authentication errors
      const isAuthError = await handleAuthError({ message: responseData.error });

      if (isAuthError) {
        // Retry the request with a fresh token
        return await callAuraAPI(action, params);
      }

      throw new Error(responseData.error);
    }

    return responseData;
  } catch (error) {
    console.error('API call error:', error);

    // Try to handle authentication errors
    const isAuthError = await handleAuthError(error);

    if (isAuthError) {
      // Retry the request
      return await callAuraAPI(action, params);
    }

    throw error;
  }
}
