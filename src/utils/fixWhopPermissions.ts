import { supabase } from '@/integrations/supabase/client';

/**
 * Fix Whop owner permissions for the current user
 * This is a temporary utility to set the correct metadata for Whop owners
 */
export const fixWhopOwnerPermissions = async (): Promise<boolean> => {
  try {
    console.log('🔧 Fixing Whop owner permissions...');
    
    // Get current user
    const { data: { user }, error: getUserError } = await supabase.auth.getUser();
    
    if (getUserError) {
      console.error('❌ Error getting user:', getUserError);
      return false;
    }
    
    if (!user) {
      console.error('❌ No user found');
      return false;
    }

    console.log('👤 Current user:', {
      id: user.id,
      email: user.email,
      currentMetadata: user.user_metadata
    });

    // Update user metadata with Whop owner permissions
    const updatedMetadata = {
      ...user.user_metadata,
      whop_user_id: user.id, // Set whop_user_id to current user ID
      whop_access_level: 'admin', // Set as admin/owner
      whop_company_id: import.meta.env.VITE_WHOP_COMPANY_ID || import.meta.env.VITE_TRADING_WHOP_COMPANY_ID,
      whop_business_id: 'biz_OGyv6Pz0Le35Fa', // Official Osis business ID
      whop_business_handle: 'tryosis',
      isWhopUser: true // Ensure this is set
    };

    console.log('🔄 Updating metadata with:', updatedMetadata);

    const { error: updateError } = await supabase.auth.updateUser({
      data: updatedMetadata
    });

    if (updateError) {
      console.error('❌ Error updating user metadata:', updateError);
      return false;
    }

    console.log('✅ User metadata updated successfully!');
    console.log('🔄 Please refresh the page to see the changes.');
    
    return true;
  } catch (error) {
    console.error('❌ Unexpected error fixing Whop permissions:', error);
    return false;
  }
};

/**
 * Check current Whop permissions
 */
export const checkCurrentWhopPermissions = async () => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      console.log('❌ No user found');
      return null;
    }

    const metadata = user.user_metadata || {};
    
    const permissions = {
      whop_user_id: metadata.whop_user_id,
      whop_access_level: metadata.whop_access_level,
      whop_company_id: metadata.whop_company_id,
      whop_business_id: metadata.whop_business_id,
      whop_business_handle: metadata.whop_business_handle,
      isWhopUser: metadata.isWhopUser,
      isWhopOwner: metadata.whop_access_level === 'admin',
      isOfficialOsis: metadata.whop_business_id === 'biz_OGyv6Pz0Le35Fa' || 
                     metadata.whop_business_handle === 'tryosis'
    };

    console.log('🔍 Current Whop permissions:', permissions);
    
    return permissions;
  } catch (error) {
    console.error('❌ Error checking permissions:', error);
    return null;
  }
};

// Make functions available globally for console access
if (typeof window !== 'undefined') {
  (window as any).fixWhopOwnerPermissions = fixWhopOwnerPermissions;
  (window as any).checkCurrentWhopPermissions = checkCurrentWhopPermissions;
}
