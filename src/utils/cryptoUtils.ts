/**
 * Utility functions for encrypting and decrypting data
 * Uses AES-GCM encryption with a derived key
 */

// A non-sensitive salt used for key derivation
const SALT = new Uint8Array([
  0x63, 0x72, 0x79, 0x70, 0x74, 0x6f, 0x53, 0x61, 
  0x6c, 0x74, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36
]);

/**
 * Derives an encryption key from a password
 * @param password - The password to derive the key from
 * @returns The derived key
 */
async function deriveKey(password: string): Promise<CryptoKey> {
  // Convert password to buffer
  const encoder = new TextEncoder();
  const passwordBuffer = encoder.encode(password);
  
  // Import the password as a key
  const baseKey = await crypto.subtle.importKey(
    'raw',
    passwordBuffer,
    { name: 'PBKDF2' },
    false,
    ['deriveKey']
  );
  
  // Derive a key using PBKDF2
  return crypto.subtle.deriveKey(
    {
      name: 'PBKDF2',
      salt: SALT,
      iterations: 100000,
      hash: 'SHA-256'
    },
    baseKey,
    { name: 'AES-GCM', length: 256 },
    false,
    ['encrypt', 'decrypt']
  );
}

/**
 * Encrypts data using AES-GCM
 * @param data - The data to encrypt
 * @param password - The password to use for encryption
 * @returns Base64 encoded encrypted data with IV
 */
export async function encryptData(data: string, password: string): Promise<string> {
  try {
    // Generate a random IV
    const iv = crypto.getRandomValues(new Uint8Array(12));
    
    // Derive the key
    const key = await deriveKey(password);
    
    // Encode the data
    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(data);
    
    // Encrypt the data
    const encryptedBuffer = await crypto.subtle.encrypt(
      {
        name: 'AES-GCM',
        iv
      },
      key,
      dataBuffer
    );
    
    // Combine IV and encrypted data
    const result = new Uint8Array(iv.length + encryptedBuffer.byteLength);
    result.set(iv);
    result.set(new Uint8Array(encryptedBuffer), iv.length);
    
    // Convert to base64
    return btoa(String.fromCharCode(...result));
  } catch (error) {
    console.error('Encryption error:', error);
    throw new Error('Failed to encrypt data');
  }
}

/**
 * Decrypts data using AES-GCM
 * @param encryptedData - Base64 encoded encrypted data with IV
 * @param password - The password to use for decryption
 * @returns The decrypted data
 */
export async function decryptData(encryptedData: string, password: string): Promise<string> {
  try {
    // Convert from base64
    const encryptedBytes = Uint8Array.from(atob(encryptedData), c => c.charCodeAt(0));
    
    // Extract IV (first 12 bytes)
    const iv = encryptedBytes.slice(0, 12);
    
    // Extract encrypted data (everything after IV)
    const data = encryptedBytes.slice(12);
    
    // Derive the key
    const key = await deriveKey(password);
    
    // Decrypt the data
    const decryptedBuffer = await crypto.subtle.decrypt(
      {
        name: 'AES-GCM',
        iv
      },
      key,
      data
    );
    
    // Decode the data
    const decoder = new TextDecoder();
    return decoder.decode(decryptedBuffer);
  } catch (error) {
    console.error('Decryption error:', error);
    throw new Error('Failed to decrypt data');
  }
}

/**
 * Generates a random string to use as a password
 * @param length - The length of the password
 * @returns A random string
 */
export function generateRandomPassword(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()';
  let result = '';
  const randomValues = new Uint8Array(length);
  crypto.getRandomValues(randomValues);
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(randomValues[i] % chars.length);
  }
  
  return result;
}

/**
 * Obfuscates the purpose of the data by adding a generic name
 * @param data - The data to obfuscate
 * @returns An object with a generic name and the data
 */
export function obfuscateData(data: any): any {
  // Create a generic object with random field names
  return {
    timestamp: Date.now(),
    session_id: crypto.randomUUID(),
    client_data: data,
    version: '1.0.0'
  };
}

/**
 * Deobfuscates the data by extracting it from the generic object
 * @param data - The obfuscated data
 * @returns The original data
 */
export function deobfuscateData(data: any): any {
  return data.client_data;
}
