/**
 * Temporary test file to debug environment variables
 */

export const testEnvironmentVariables = () => {
  console.log('🧪 Environment Variable Test:', {
    NODE_ENV: import.meta.env.NODE_ENV,
    MODE: import.meta.env.MODE,
    VITE_GROWI_PUBLIC_ID: import.meta.env.VITE_GROWI_PUBLIC_ID,
    allEnvVars: import.meta.env
  });
  
  // Test if we can set a test value
  const testValue = 'test-growi-id-123';
  console.log('🧪 Test value:', testValue);
  
  return {
    hasGrowiId: !!import.meta.env.VITE_GROWI_PUBLIC_ID,
    growiId: import.meta.env.VITE_GROWI_PUBLIC_ID,
    envCount: Object.keys(import.meta.env).length
  };
};
