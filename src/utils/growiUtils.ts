/**
 * Growi affiliate tracking utilities
 */

declare global {
  interface Window {
    growi?: {
      affiliate_code?: string;
    };
  }
}

/**
 * Gets the affiliate code from the Growi tracking script
 * @returns The affiliate code if available, null otherwise
 */
export const getAffiliateCode = (): string | null => {
  try {
    // Check if Growi tracking script has loaded and captured an affiliate code
    if (window.growi && window.growi.affiliate_code) {
      return window.growi.affiliate_code;
    }
    
    // Fallback: Check URL parameters for affiliate codes
    const urlParams = new URLSearchParams(window.location.search);
    const affiliateParam = urlParams.get('affiliate') || urlParams.get('ref') || urlParams.get('aff');
    
    if (affiliateParam) {
      return affiliateParam;
    }
    
    return null;
  } catch (error) {
    console.warn('Error getting affiliate code:', error);
    return null;
  }
};

/**
 * Waits for the Growi script to load and capture affiliate code
 * @param timeout Maximum time to wait in milliseconds (default: 3000)
 * @returns Promise that resolves with the affiliate code or null
 */
export const waitForAffiliateCode = (timeout: number = 3000): Promise<string | null> => {
  return new Promise((resolve) => {
    const startTime = Date.now();
    
    const checkForCode = () => {
      const code = getAffiliateCode();
      
      if (code) {
        resolve(code);
        return;
      }
      
      // Check if timeout exceeded
      if (Date.now() - startTime >= timeout) {
        resolve(null);
        return;
      }
      
      // Check again in 100ms
      setTimeout(checkForCode, 100);
    };
    
    checkForCode();
  });
};
