/**
 * Growi script loader utility
 * Dynamically loads the Growi tracking script with the correct environment variable
 */

let growiLoaded = false;

/**
 * Loads the Growi tracking script if not already loaded
 */
export const loadGrowiScript = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    // Check if already loaded
    const existingScript = document.querySelector('script[src*="growi.js"]');
    if (growiLoaded || existingScript) {
      console.log('🔄 Growi script already exists:', existingScript);
      resolve();
      return;
    }

    // Remove any existing Growi scripts that might be malformed
    const allGrowiScripts = document.querySelectorAll('script[src*="growi.js"]');
    allGrowiScripts.forEach(script => {
      console.log('🗑️ Removing existing Growi script:', script);
      script.remove();
    });

    // Get the Growi Public ID from environment variables
    let growiPublicId = import.meta.env.VITE_GROWI_PUBLIC_ID;

    // Fallback for testing - use a test ID if no env var is set
    if (!growiPublicId) {
      growiPublicId = 'test-growi-id-123';
      console.warn('⚠️ VITE_GROWI_PUBLIC_ID not set, using test ID:', growiPublicId);
    }

    console.log('🔍 Growi Debug:', {
      growiPublicId,
      envVars: import.meta.env,
      hasGrowiId: !!growiPublicId,
      isTestId: growiPublicId === 'test-growi-id-123'
    });

    // Create and load the script
    const script = document.createElement('script');
    script.src = 'https://cdn.growi.io/growi.js';
    script.async = true;
    script.setAttribute('data-growi-id', growiPublicId);

    console.log('🚀 Creating Growi script element:', {
      src: script.src,
      growiId: script.getAttribute('data-growi-id'),
      async: script.async
    });

    script.onload = () => {
      growiLoaded = true;
      console.log('✅ Growi script loaded successfully');
      resolve();
    };

    script.onerror = (error) => {
      console.error('❌ Failed to load Growi script:', error);
      reject(error);
    };

    // Add to document head
    document.head.appendChild(script);
    console.log('📝 Growi script added to document head');
  });
};

/**
 * Initialize Growi tracking
 * Call this early in your app initialization
 */
export const initializeGrowiTracking = async (): Promise<void> => {
  try {
    await loadGrowiScript();
  } catch (error) {
    console.error('Failed to initialize Growi tracking:', error);
  }
};
