import { supabase } from '@/integrations/supabase/client';
import { getCurrentWhopSupabaseUser } from '@/services/whopAuthService';

export interface AuthenticatedUser {
  id: string;
  email: string;
  user_metadata?: {
    username?: string;
    full_name?: string;
    avatar_url?: string;
    isWhopUser?: boolean;
    whop_user_id?: string;
    [key: string]: any;
  };
  isWhopUser: boolean;
}

/**
 * Get the current authenticated user (either regular Supabase user or Whop user)
 * This function should be used by all services instead of supabase.auth.getUser()
 */
export const getCurrentAuthenticatedUser = async (): Promise<AuthenticatedUser | null> => {
  try {
    // First, try to get regular Supabase user
    const { data: { user: supabaseUser }, error: supabaseError } = await supabase.auth.getUser();
    
    if (supabaseUser && !supabaseError) {
      console.log('✅ Found regular Supabase user:', supabaseUser.id);
      return {
        id: supabaseUser.id,
        email: supabaseUser.email!,
        user_metadata: supabaseUser.user_metadata,
        isWhopUser: supabaseUser.user_metadata?.isWhopUser === true
      };
    }

    // If no regular user, check for Whop user
    const whopSupabaseUser = await getCurrentWhopSupabaseUser();
    if (whopSupabaseUser) {
      console.log('✅ Found Whop Supabase user:', whopSupabaseUser.id);
      return {
        id: whopSupabaseUser.id,
        email: whopSupabaseUser.email,
        user_metadata: whopSupabaseUser.user_metadata,
        isWhopUser: true
      };
    }

    // If no Supabase user found, check if we have Whop user data
    // and try to find the corresponding Supabase user
    const whopUserData = localStorage.getItem('whop_user_data');
    if (whopUserData) {
      try {
        const whopUser = JSON.parse(whopUserData);
        console.log('⚠️ Found Whop user in localStorage but no active session:', whopUser.username);

        // Try to find the Whop user in Supabase by calling the whop-user-lookup function
        try {
          console.log('🔍 Looking up Whop user in Supabase...');
          const { data: lookupResult, error: lookupError } = await supabase.functions.invoke('whop-user-lookup', {
            body: { whopUserId: whopUser.id }
          });

          if (!lookupError && lookupResult?.user) {
            console.log('✅ Found Whop user in Supabase:', lookupResult.user.id);
            return {
              id: lookupResult.user.id,
              email: lookupResult.user.email,
              user_metadata: lookupResult.user.user_metadata,
              isWhopUser: true
            };
          } else {
            console.log('❌ Whop user not found in Supabase or lookup failed:', lookupError);
          }
        } catch (lookupError) {
          console.error('❌ Error looking up Whop user:', lookupError);
        }

        console.log('❌ Whop user found in localStorage but not properly authenticated in Supabase');
      } catch (parseError) {
        console.error('❌ Error parsing Whop user from localStorage:', parseError);
      }
    }

    console.log('❌ No authenticated user found');
    return null;

  } catch (error) {
    console.error('❌ Error getting authenticated user:', error);
    return null;
  }
};

/**
 * Check if a user is authenticated (either regular or Whop user)
 */
export const isUserAuthenticated = async (): Promise<boolean> => {
  const user = await getCurrentAuthenticatedUser();
  return user !== null;
};

/**
 * Get the user ID for database operations
 * For Whop users, this might be a temporary ID until they're fully integrated
 */
export const getCurrentUserId = async (): Promise<string | null> => {
  const user = await getCurrentAuthenticatedUser();
  return user?.id || null;
};

/**
 * Throw an error if user is not authenticated
 * This is a helper for services that require authentication
 */
export const requireAuthentication = async (): Promise<AuthenticatedUser> => {
  const user = await getCurrentAuthenticatedUser();
  
  if (!user) {
    throw new Error('User not authenticated');
  }
  
  return user;
};

/**
 * Check if the current user is a Whop user
 */
export const isWhopUser = async (): Promise<boolean> => {
  const user = await getCurrentAuthenticatedUser();
  return user?.isWhopUser === true;
};
