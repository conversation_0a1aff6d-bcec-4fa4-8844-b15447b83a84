import { supabase } from "@/integrations/supabase/client";

// Global variable to store the refreshToken function
// This will be set by the application when it initializes
let refreshTokenFunction: (() => Promise<string | null>) | null = null;

/**
 * Sets the refresh token function from the AuthContext
 * This should be called when the application initializes
 */
export function setRefreshTokenFunction(fn: () => Promise<string | null>) {
  refreshTokenFunction = fn;
  console.log('[tokenUtils] Refresh token function set');
}

// Time before token expiry when we should refresh (5 minutes in milliseconds)
const REFRESH_THRESHOLD = 5 * 60 * 1000;

// Cache for the last refresh time to prevent multiple refreshes
let lastRefreshTime = 0;
const REFRESH_COOLDOWN = 30 * 1000; // 30 seconds cooldown between refresh attempts

/**
 * Checks if the current session token is valid and refreshes it if needed
 * @returns A valid access token or null if unable to get one
 */
export async function ensureValidToken(): Promise<string | null> {
  try {
    // Get the current session
    const { data: { session } } = await supabase.auth.getSession();

    // If no session, return null
    if (!session) {
      console.log('[tokenUtils] No session found');
      return null;
    }

    // Check if we've refreshed recently to avoid multiple refreshes
    const now = Date.now();
    if (now - lastRefreshTime < REFRESH_COOLDOWN) {
      console.log('[tokenUtils] Token was refreshed recently, using current token');
      return session.access_token;
    }

    // Check if token is about to expire
    const expiresAt = session.expires_at ? session.expires_at * 1000 : 0;
    const timeUntilExpiry = expiresAt - now;

    // If token is about to expire or already expired, refresh it
    if (timeUntilExpiry < REFRESH_THRESHOLD) {
      console.log('[tokenUtils] Token is about to expire, refreshing...');

      // Update the last refresh time
      lastRefreshTime = now;

      // Try to use the refreshTokenFunction from AuthContext if available
      if (refreshTokenFunction) {
        console.log('[tokenUtils] Using AuthContext refreshToken function');
        const newToken = await refreshTokenFunction();

        if (newToken) {
          console.log('[tokenUtils] Token refreshed successfully via AuthContext');
          return newToken;
        }
      }

      // Fall back to direct Supabase refresh if AuthContext function fails or is not available
      console.log('[tokenUtils] Falling back to direct Supabase refresh');
      const { data, error } = await supabase.auth.refreshSession();

      if (error) {
        console.error('[tokenUtils] Error refreshing token:', error);
        // If refresh fails but we still have a valid token, return it
        if (timeUntilExpiry > 0) {
          return session.access_token;
        }
        return null;
      }

      if (data.session) {
        console.log('[tokenUtils] Token refreshed successfully via Supabase');
        return data.session.access_token;
      }
    }

    // Token is still valid
    return session.access_token;
  } catch (error) {
    console.error('[tokenUtils] Error in ensureValidToken:', error);
    return null;
  }
}

/**
 * Gets a valid access token for API calls, refreshing if necessary
 * @returns A valid access token or throws an error if unable to get one
 */
export async function getValidAccessToken(): Promise<string> {
  const token = await ensureValidToken();

  if (!token) {
    throw new Error('Unable to get a valid authentication token. Please try logging in again.');
  }

  return token;
}

/**
 * Handles authentication errors by attempting to refresh the token
 * @param error The error that occurred
 * @returns True if the error was handled, false otherwise
 */
export async function handleAuthError(error: any): Promise<boolean> {
  // Check if this is an authentication error
  if (
    error?.message?.includes('auth') ||
    error?.message?.includes('token') ||
    error?.message?.includes('unauthorized') ||
    error?.message?.includes('Unauthorized') ||
    error?.status === 401
  ) {
    console.log('[tokenUtils] Handling auth error:', error.message);

    // Try to refresh the token
    const token = await ensureValidToken();

    // If we got a new token, the error was handled
    return !!token;
  }

  // Not an auth error or couldn't be handled
  return false;
}
