/**
 * Expert-level agent optimization utilities for handling large, complex agents
 * Supports agents with 1000+ blocks while maintaining performance
 */

import { AgentBlock } from '@/services/agentService';
import { BlockType } from '@/types/agent';

export interface OptimizationResult {
  optimizedBlocks: AgentBlock[];
  performance: {
    originalComplexity: number;
    optimizedComplexity: number;
    reductionPercentage: number;
    estimatedExecutionTime: number;
  };
  suggestions: string[];
  warnings: string[];
}

export interface PerformanceMetrics {
  blockCount: number;
  connectionCount: number;
  maxDepth: number;
  branchingFactor: number;
  redundancyScore: number;
  complexityScore: number;
}

/**
 * Optimize agent for performance while maintaining functionality
 */
export function optimizeAgent(blocks: AgentBlock[]): OptimizationResult {
  const originalMetrics = calculatePerformanceMetrics(blocks);
  let optimizedBlocks = [...blocks];
  const suggestions: string[] = [];
  const warnings: string[] = [];

  // Step 1: Remove redundant blocks
  const redundancyResult = removeRedundantBlocks(optimizedBlocks);
  optimizedBlocks = redundancyResult.blocks;
  suggestions.push(...redundancyResult.suggestions);

  // Step 2: Consolidate similar operations
  const consolidationResult = consolidateSimilarBlocks(optimizedBlocks);
  optimizedBlocks = consolidationResult.blocks;
  suggestions.push(...consolidationResult.suggestions);

  // Step 3: Optimize connection patterns
  const connectionResult = optimizeConnections(optimizedBlocks);
  optimizedBlocks = connectionResult.blocks;
  suggestions.push(...connectionResult.suggestions);

  // Step 4: Validate large agent performance
  if (optimizedBlocks.length > 500) {
    warnings.push('Large agent detected. Consider breaking into smaller sub-agents for better performance.');
  }

  if (optimizedBlocks.length > 1000) {
    warnings.push('Very large agent. Performance may be significantly impacted. Strong recommendation to modularize.');
  }

  const optimizedMetrics = calculatePerformanceMetrics(optimizedBlocks);
  const reductionPercentage = ((originalMetrics.complexityScore - optimizedMetrics.complexityScore) / originalMetrics.complexityScore) * 100;

  return {
    optimizedBlocks,
    performance: {
      originalComplexity: originalMetrics.complexityScore,
      optimizedComplexity: optimizedMetrics.complexityScore,
      reductionPercentage: Math.max(0, reductionPercentage),
      estimatedExecutionTime: estimateExecutionTime(optimizedMetrics)
    },
    suggestions,
    warnings
  };
}

/**
 * Calculate comprehensive performance metrics
 */
export function calculatePerformanceMetrics(blocks: AgentBlock[]): PerformanceMetrics {
  const connectionCount = blocks.reduce((count, block) => {
    return count + (block.outputConnections?.length || 0) +
           (block.trueConnection ? 1 : 0) +
           (block.falseConnection ? 1 : 0) +
           (block.bullishConnection ? 1 : 0) +
           (block.bearishConnection ? 1 : 0) +
           (block.neutralConnection ? 1 : 0);
  }, 0);

  const maxDepth = calculateMaxExecutionDepth(blocks);
  const branchingFactor = calculateAverageBranchingFactor(blocks);
  const redundancyScore = calculateRedundancyScore(blocks);
  
  // Complex scoring algorithm considering all factors
  const complexityScore = (
    blocks.length * 1.0 +
    connectionCount * 0.5 +
    maxDepth * 2.0 +
    branchingFactor * 1.5 +
    redundancyScore * 3.0
  );

  return {
    blockCount: blocks.length,
    connectionCount,
    maxDepth,
    branchingFactor,
    redundancyScore,
    complexityScore
  };
}

/**
 * Remove redundant blocks that don't add value
 */
function removeRedundantBlocks(blocks: AgentBlock[]): { blocks: AgentBlock[]; suggestions: string[] } {
  const suggestions: string[] = [];
  let filteredBlocks = [...blocks];

  // Find duplicate indicators with same parameters
  const indicatorBlocks = blocks.filter(b => b.type === BlockType.INDICATOR);
  const indicatorGroups = new Map<string, AgentBlock[]>();

  indicatorBlocks.forEach(block => {
    const key = `${block.indicator}-${JSON.stringify(block.parameters)}-${block.timeframe}`;
    if (!indicatorGroups.has(key)) {
      indicatorGroups.set(key, []);
    }
    indicatorGroups.get(key)!.push(block);
  });

  // Remove duplicates, keeping the first one
  indicatorGroups.forEach((group, key) => {
    if (group.length > 1) {
      const toRemove = group.slice(1);
      filteredBlocks = filteredBlocks.filter(b => !toRemove.includes(b));
      suggestions.push(`Removed ${toRemove.length} duplicate ${key} indicator(s)`);
    }
  });

  // Find orphaned blocks (no connections)
  const orphanedBlocks = filteredBlocks.filter(block => {
    const hasInputs = block.inputConnections && block.inputConnections.length > 0;
    const hasOutputs = (block.outputConnections && block.outputConnections.length > 0) ||
                      block.trueConnection || block.falseConnection ||
                      block.bullishConnection || block.bearishConnection || block.neutralConnection;
    
    return !hasInputs && !hasOutputs && block.type !== BlockType.WHEN_RUN;
  });

  if (orphanedBlocks.length > 0) {
    filteredBlocks = filteredBlocks.filter(b => !orphanedBlocks.includes(b));
    suggestions.push(`Removed ${orphanedBlocks.length} orphaned block(s)`);
  }

  return { blocks: filteredBlocks, suggestions };
}

/**
 * Consolidate similar blocks to reduce complexity
 */
function consolidateSimilarBlocks(blocks: AgentBlock[]): { blocks: AgentBlock[]; suggestions: string[] } {
  const suggestions: string[] = [];
  // Implementation for consolidating similar operations
  // This is a placeholder for more complex consolidation logic
  
  return { blocks, suggestions };
}

/**
 * Optimize connection patterns for better performance
 */
function optimizeConnections(blocks: AgentBlock[]): { blocks: AgentBlock[]; suggestions: string[] } {
  const suggestions: string[] = [];
  // Implementation for optimizing connection patterns
  // This could include reducing unnecessary branching, optimizing data flow, etc.
  
  return { blocks, suggestions };
}

/**
 * Calculate maximum execution depth
 */
function calculateMaxExecutionDepth(blocks: AgentBlock[]): number {
  const visited = new Set<string>();
  let maxDepth = 0;

  function dfs(blockId: string, depth: number) {
    if (visited.has(blockId)) return;
    visited.add(blockId);
    maxDepth = Math.max(maxDepth, depth);

    const block = blocks.find(b => b.id === blockId);
    if (!block) return;

    const outputs = [
      ...(block.outputConnections || []),
      block.trueConnection,
      block.falseConnection,
      block.bullishConnection,
      block.bearishConnection,
      block.neutralConnection
    ].filter(Boolean);

    outputs.forEach(outputId => dfs(outputId!, depth + 1));
  }

  const entryBlocks = blocks.filter(b => b.type === BlockType.WHEN_RUN);
  entryBlocks.forEach(block => dfs(block.id, 1));

  return maxDepth;
}

/**
 * Calculate average branching factor
 */
function calculateAverageBranchingFactor(blocks: AgentBlock[]): number {
  let totalBranches = 0;
  let branchingBlocks = 0;

  blocks.forEach(block => {
    const outputs = [
      ...(block.outputConnections || []),
      block.trueConnection,
      block.falseConnection,
      block.bullishConnection,
      block.bearishConnection,
      block.neutralConnection
    ].filter(Boolean);

    if (outputs.length > 1) {
      branchingBlocks++;
      totalBranches += outputs.length;
    }
  });

  return branchingBlocks > 0 ? totalBranches / branchingBlocks : 1;
}

/**
 * Calculate redundancy score
 */
function calculateRedundancyScore(blocks: AgentBlock[]): number {
  let redundancyScore = 0;

  // Check for duplicate indicators
  const indicators = blocks.filter(b => b.type === BlockType.INDICATOR);
  const indicatorMap = new Map<string, number>();
  
  indicators.forEach(block => {
    const key = `${block.indicator}-${JSON.stringify(block.parameters)}`;
    indicatorMap.set(key, (indicatorMap.get(key) || 0) + 1);
  });

  indicatorMap.forEach(count => {
    if (count > 1) redundancyScore += count - 1;
  });

  return redundancyScore;
}

/**
 * Estimate execution time based on metrics
 */
function estimateExecutionTime(metrics: PerformanceMetrics): number {
  // Base time per block type (in milliseconds)
  const baseTime = 10;
  const depthPenalty = metrics.maxDepth * 5;
  const branchingPenalty = metrics.branchingFactor * 3;
  const redundancyPenalty = metrics.redundancyScore * 15;

  return baseTime * metrics.blockCount + depthPenalty + branchingPenalty + redundancyPenalty;
}

/**
 * Generate performance recommendations
 */
export function generatePerformanceRecommendations(metrics: PerformanceMetrics): string[] {
  const recommendations: string[] = [];

  if (metrics.blockCount > 100) {
    recommendations.push('Consider breaking large agent into smaller, focused modules');
  }

  if (metrics.maxDepth > 10) {
    recommendations.push('Reduce decision tree depth to improve execution speed');
  }

  if (metrics.branchingFactor > 4) {
    recommendations.push('Simplify branching logic to reduce complexity');
  }

  if (metrics.redundancyScore > 5) {
    recommendations.push('Remove duplicate calculations and consolidate similar operations');
  }

  if (metrics.complexityScore > 500) {
    recommendations.push('Agent complexity is very high. Consider architectural redesign');
  }

  return recommendations;
}
