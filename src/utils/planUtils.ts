// Plan utility functions for managing plan selection

// LocalStorage keys
export const PLAN_TYPE_KEY = 'Osis_selected_plan_type';

// Plan types
export const PLAN_TYPES = {
  PREMIUM: 'premium'
};

// Utility function - Always return true since we use legacy account for all users now
export const isLegacyUser = (userCreatedAt: string): boolean => {
  return true; // All users use legacy account now to get #fidpam URLs
};

// Price IDs for Premium plan - Using only legacy account for all users
// All users use pk_live_51Qq1bA... account - These generate URLs with #fidpam
export const LEGACY_PLAN_PRICE_IDS = {
  [PLAN_TYPES.PREMIUM]: {
    weekly: 'price_1ROYLKDebmd1GpTvct491Kw6',    // Weekly Premium
    yearly: 'price_1RVyr7Debmd1GpTvgWmmS7R1'     // Annual Premium
  }
};

// Using legacy for all users now
export const CURRENT_PLAN_PRICE_IDS = LEGACY_PLAN_PRICE_IDS;

// Legacy price IDs for backward compatibility - Premium only
export const PLAN_PRICE_IDS = {
  [PLAN_TYPES.PREMIUM]: 'price_1ROYLKDebmd1GpTvct491Kw6' // Default to legacy premium weekly
};

/**
 * Set the selected plan type in localStorage
 * @param planType The plan type to store
 */
export const setSelectedPlanType = (planType: string): void => {
  try {
    localStorage.setItem(PLAN_TYPE_KEY, planType);
  } catch (error) {
    console.error('Error setting plan type in localStorage:', error);
  }
};

/**
 * Get the selected plan type from localStorage
 * @returns The stored plan type or null if not found
 */
export const getSelectedPlanType = (): string | null => {
  try {
    return localStorage.getItem(PLAN_TYPE_KEY);
  } catch (error) {
    console.error('Error getting plan type from localStorage:', error);
    return null;
  }
};

/**
 * Clear the selected plan type from localStorage
 */
export const clearSelectedPlanType = (): void => {
  try {
    localStorage.removeItem(PLAN_TYPE_KEY);
  } catch (error) {
    console.error('Error clearing plan type from localStorage:', error);
  }
};

/**
 * Get the price IDs - Always use legacy account for all users
 * @returns The price ID configuration
 */
export const getPriceIdsForUser = () => {
  return LEGACY_PLAN_PRICE_IDS;
};

/**
 * Get the price ID for a given plan type and billing period
 * @param planType The plan type
 * @param billingPeriod The billing period ('weekly' or 'yearly')
 * @param userCreatedAt The user's creation date (optional, defaults to current config)
 * @returns The price ID for the plan type or the basic plan price ID as default
 */
export const getPriceIdForPlanType = (
  planType: string | null,
  billingPeriod: 'weekly' | 'yearly' = 'weekly',
  userCreatedAt?: string
): string => {
  console.log('🔍 getPriceIdForPlanType called with:', {
    planType,
    billingPeriod,
    userCreatedAt,
    hasUserCreatedAt: !!userCreatedAt
  });

  if (!planType) {
    planType = PLAN_TYPES.PREMIUM; // Default to premium plan
    console.log('📝 No planType provided, defaulting to:', planType);
  }

  // Always use legacy account for all users
  const priceIds = getPriceIdsForUser();
  const selectedPriceId = priceIds[planType]?.[billingPeriod] || priceIds[PLAN_TYPES.PREMIUM][billingPeriod];

  console.log('📊 Price ID selection (legacy account):', {
    planType,
    billingPeriod,
    selectedPriceId
  });

  return selectedPriceId;
};

/**
 * Get the legacy price ID for a given plan type (for backward compatibility)
 * @param planType The plan type
 * @returns The price ID for the plan type or the basic plan price ID as default
 */
export const getLegacyPriceIdForPlanType = (planType: string | null): string => {
  if (!planType) {
    return PLAN_PRICE_IDS[PLAN_TYPES.PREMIUM]; // Default to premium plan
  }

  return PLAN_PRICE_IDS[planType] || PLAN_PRICE_IDS[PLAN_TYPES.PREMIUM];
};
