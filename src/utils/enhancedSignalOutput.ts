// Enhanced Signal Output Format for AI Trading Agents
// This demonstrates how the new risk analysis blocks enhance signal generation

export interface EnhancedSignalOutput {
  // Core Signal
  signal: 'bullish' | 'bearish' | 'neutral';
  confidence: number; // 0-100
  reasoning: string;
  
  // Risk Management Guidance
  risk_management?: {
    entry_price: number;
    stop_loss: {
      price: number;
      percentage: number;
      method: string;
      reasoning: string;
    };
    take_profit: {
      targets: Array<{
        price: number;
        percentage: number;
        probability: number;
        level_type: string;
      }>;
      method: string;
    };
    risk_reward_ratio: number;
    position_size_suggestion: {
      percentage_of_portfolio: number;
      dollar_amount: number;
      reasoning: string;
    };
  };
  
  // Advanced Analysis
  advanced_analysis?: {
    trend_strength: {
      score: number; // 0-100
      direction: 'up' | 'down' | 'sideways';
      method: string;
    };
    volatility_assessment: {
      level: 'low' | 'moderate' | 'high';
      percentile: number;
      suitable_for_strategy: boolean;
    };
    support_resistance: {
      nearest_support: number;
      nearest_resistance: number;
      strength_score: number;
    };
    correlation_analysis: {
      market_correlation: number;
      sector_correlation: number;
      relative_strength: number;
    };
    momentum_analysis: {
      momentum_score: number;
      momentum_shift_detected: boolean;
      momentum_direction: 'increasing' | 'decreasing' | 'stable';
    };
  };
  
  // Signal Quality Metrics
  signal_quality?: {
    overall_score: number; // 0-100
    volume_confirmation: boolean;
    trend_alignment: boolean;
    multiple_timeframe_agreement: boolean;
    quality_factors: string[];
  };
  
  // Educational Information
  educational?: {
    strategy_explanation: string;
    risk_warnings: string[];
    market_context: string;
    similar_historical_setups: number;
  };
}

// Example enhanced signal outputs
export const exampleEnhancedSignals: EnhancedSignalOutput[] = [
  {
    signal: 'bullish',
    confidence: 85,
    reasoning: 'RSI oversold (28) with bullish divergence, strong support at $145.50, and increasing volume',
    
    risk_management: {
      entry_price: 150.00,
      stop_loss: {
        price: 145.50,
        percentage: -3.0,
        method: 'ATR-based support level',
        reasoning: 'Stop placed below key support with 2x ATR buffer for volatility'
      },
      take_profit: {
        targets: [
          {
            price: 157.50,
            percentage: 5.0,
            probability: 75,
            level_type: 'First resistance / 0.382 Fibonacci'
          },
          {
            price: 165.00,
            percentage: 10.0,
            probability: 45,
            level_type: 'Major resistance / 0.618 Fibonacci'
          }
        ],
        method: 'Fibonacci resistance levels with probability weighting'
      },
      risk_reward_ratio: 2.5,
      position_size_suggestion: {
        percentage_of_portfolio: 2.0,
        dollar_amount: 2000,
        reasoning: 'Conservative 2% risk based on 3% stop loss distance'
      }
    },
    
    advanced_analysis: {
      trend_strength: {
        score: 72,
        direction: 'up',
        method: 'ADX-based with moving average slope analysis'
      },
      volatility_assessment: {
        level: 'moderate',
        percentile: 45,
        suitable_for_strategy: true
      },
      support_resistance: {
        nearest_support: 145.50,
        nearest_resistance: 157.50,
        strength_score: 85
      },
      correlation_analysis: {
        market_correlation: 0.65,
        sector_correlation: 0.78,
        relative_strength: 12.5
      },
      momentum_analysis: {
        momentum_score: 68,
        momentum_shift_detected: true,
        momentum_direction: 'increasing'
      }
    },
    
    signal_quality: {
      overall_score: 82,
      volume_confirmation: true,
      trend_alignment: true,
      multiple_timeframe_agreement: false,
      quality_factors: [
        'Strong volume confirmation',
        'Clear support level',
        'Bullish divergence pattern',
        'Favorable risk/reward ratio'
      ]
    },
    
    educational: {
      strategy_explanation: 'This is a mean reversion setup where the stock has become oversold but maintains strong support. The bullish divergence suggests selling pressure is weakening.',
      risk_warnings: [
        'Market correlation is moderate - watch overall market direction',
        'Earnings announcement in 2 weeks could cause volatility',
        'Volume should continue to support the move'
      ],
      market_context: 'Technology sector showing relative strength with moderate market volatility',
      similar_historical_setups: 23
    }
  },
  
  {
    signal: 'bearish',
    confidence: 78,
    reasoning: 'Failed breakout above resistance, declining volume, and bearish MACD divergence',
    
    risk_management: {
      entry_price: 152.00,
      stop_loss: {
        price: 157.50,
        percentage: 3.6,
        method: 'Resistance level with buffer',
        reasoning: 'Stop above failed breakout level with volatility buffer'
      },
      take_profit: {
        targets: [
          {
            price: 145.00,
            percentage: -4.6,
            probability: 70,
            level_type: 'Previous support level'
          },
          {
            price: 138.50,
            percentage: -8.9,
            probability: 40,
            level_type: 'Major support / 200-day MA'
          }
        ],
        method: 'Support levels with volume profile analysis'
      },
      risk_reward_ratio: 2.1,
      position_size_suggestion: {
        percentage_of_portfolio: 1.5,
        dollar_amount: 1500,
        reasoning: 'Reduced position size due to higher volatility environment'
      }
    },
    
    signal_quality: {
      overall_score: 76,
      volume_confirmation: false,
      trend_alignment: true,
      multiple_timeframe_agreement: true,
      quality_factors: [
        'Clear failed breakout pattern',
        'Multiple timeframe agreement',
        'Good risk/reward setup'
      ]
    }
  }
];

// Utility function to format enhanced signals for display
export function formatEnhancedSignal(signal: EnhancedSignalOutput): string {
  let output = `
🎯 **${signal.signal.toUpperCase()} SIGNAL** (${signal.confidence}% confidence)
${signal.reasoning}

`;

  if (signal.risk_management) {
    const rm = signal.risk_management;
    output += `📊 **RISK MANAGEMENT**
Entry: $${rm.entry_price.toFixed(2)}
Stop Loss: $${rm.stop_loss.price.toFixed(2)} (${rm.stop_loss.percentage.toFixed(1)}%)
Take Profit: $${rm.take_profit.targets[0].price.toFixed(2)} (${rm.take_profit.targets[0].percentage.toFixed(1)}%)
Risk/Reward: ${rm.risk_reward_ratio.toFixed(1)}:1
Position Size: ${rm.position_size_suggestion.percentage_of_portfolio}% of portfolio

`;
  }

  if (signal.signal_quality) {
    const sq = signal.signal_quality;
    output += `✅ **SIGNAL QUALITY** (${sq.overall_score}/100)
${sq.quality_factors.map(factor => `• ${factor}`).join('\n')}

`;
  }

  if (signal.educational) {
    output += `📚 **STRATEGY NOTES**
${signal.educational.strategy_explanation}

⚠️ **RISK WARNINGS**
${signal.educational.risk_warnings.map(warning => `• ${warning}`).join('\n')}
`;
  }

  return output;
}

// Function to demonstrate how blocks would enhance a basic signal
export function enhanceBasicSignal(
  basicSignal: { signal: string; confidence: number; reasoning: string },
  riskAnalysis: any,
  targetAnalysis: any,
  qualityMetrics: any
): EnhancedSignalOutput {
  return {
    signal: basicSignal.signal as any,
    confidence: basicSignal.confidence,
    reasoning: basicSignal.reasoning,
    risk_management: riskAnalysis,
    signal_quality: qualityMetrics,
    educational: {
      strategy_explanation: 'Enhanced signal with comprehensive risk analysis',
      risk_warnings: ['Always manage risk appropriately'],
      market_context: 'Current market conditions analyzed',
      similar_historical_setups: 15
    }
  };
}
