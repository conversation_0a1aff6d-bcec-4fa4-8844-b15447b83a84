/**
 * Utility functions for making secure API calls
 */

import { encryptData, obfuscateData } from './cryptoUtils';

// Get the encryption key from environment variables
const ENCRYPTION_KEY = import.meta.env.VITE_ENCRYPTION_KEY || '';

import { getValidAccessToken, handleAuthError } from './tokenUtils';

/**
 * Makes a secure API call to the Osis API through the api-processor edge function
 * @param endpoint - The API endpoint to call
 * @param data - The data to send to the API
 * @returns The API response
 */
export async function callOsisAPI(endpoint: string, data: Record<string, any> = {}) {
  try {
    // Get a valid access token using our token utility
    const accessToken = await getValidAccessToken();

    if (!accessToken) {
      throw new Error('No authentication token found. Please log in.');
    }

    // Combine endpoint and data into a single object
    const requestData = {
      endpoint,
      ...data
    };

    // Encrypt the request data
    const encryptedData = await encryptData(JSON.stringify(requestData), ENCRYPTION_KEY);

    // Create payload with encrypted data
    const payload = {
      data: encryptedData,
      metadata: obfuscateData({
        timestamp: Date.now()
      })
    };

    // Use the Supabase edge function with a generic name
    const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/api-processor`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify(payload)
    });

    // Check for authentication errors
    if (response.status === 401) {
      console.warn('Authentication error in API call');
      throw new Error('Authentication failed');
    }

    // Parse the response
    const responseData = await response.json();

    // Check for errors
    if (responseData.error) {
      // Try to handle authentication errors
      const isAuthError = await handleAuthError({ message: responseData.error });

      if (isAuthError) {
        // Retry the request with a fresh token
        return await callOsisAPI(endpoint, data);
      }

      throw new Error(responseData.error);
    }

    return responseData;
  } catch (error) {
    console.error('API call error:', error);

    // Try to handle authentication errors
    const isAuthError = await handleAuthError(error);

    if (isAuthError) {
      // Retry the request
      return await callOsisAPI(endpoint, data);
    }

    throw error;
  }
}
