/**
 * Utility functions for Stripe integration
 */

import { supabase } from '@/integrations/supabase/client';
import { getAffiliateCode } from './growiUtils';

/**
 * Validates a Stripe checkout session
 * @param sessionId The session ID to validate
 * @returns Promise<{valid: boolean, error?: string, session?: any}>
 */
export const validateStripeSession = async (sessionId: string) => {
  try {
    // Get the current session
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) {
      throw new Error("No authentication token found");
    }

    // Validate the session using the edge function
    const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe-checkout`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      },
      body: JSON.stringify({
        action: 'validate-session',
        sessionId
      })
    });

    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.error || `HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error('❌ Session validation failed:', error);
    return {
      valid: false,
      error: error.message || 'Session validation failed'
    };
  }
};

// REMOVED: extractSessionIdFromUrl - not needed for bare bones approach

// REMOVED: All URL corruption detection and fixing logic
// These functions were incorrectly flagging valid Stripe URLs as corrupted
// and modifying legitimate URLs, causing checkout failures

/**
 * Creates a checkout session with validation
 * @param priceId The price ID for the subscription
 * @param options Additional options for the checkout session
 * @returns Promise<{url?: string, sessionId?: string, error?: string}>
 */
export const createValidatedCheckoutSession = async (
  priceId: string,
  options: {
    returnUrl?: string;
    action?: string;
  } = {}
) => {
  try {
    // Get the current session
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) {
      throw new Error("No authentication token found");
    }

    const {
      returnUrl = window.location.origin + '/subscription/manage',
      action = 'create-checkout-session'
    } = options;



    // Get affiliate code for Growi tracking
    const affiliateCode = getAffiliateCode();

    // Create the checkout session
    const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe-checkout`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      },
      body: JSON.stringify({
        action,
        priceId,
        returnUrl,
        affiliateCode
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Server returned ${response.status}: ${errorText}`);
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(data.error);
    }

    // BARE BONES: Minimal validation
    if (!data.url && !data.sessionId) throw new Error("No URL or session ID");
    if (data.url && !data.url.includes('checkout.stripe.com')) throw new Error('Invalid Stripe URL');

    return data;
  } catch (error) {
    return { error: error.message || 'Failed to create checkout session' };
  }
};

// ALL REDIRECT CODE REMOVED
