// Pre-flight Execution Validation System
import { AgentBlock } from '@/services/agentService';
import { BlockType } from '@/hooks/useAgentBuilder';
import { Edge } from 'reactflow';

export interface ExecutionValidationResult {
  canExecute: boolean;
  criticalErrors: ExecutionError[];
  warnings: ExecutionWarning[];
  executionPath: string[];
  estimatedExecutionTime: number;
  resourceRequirements: ResourceRequirement[];
}

export interface ExecutionError {
  id: string;
  blockId?: string;
  type: 'missing_parameter' | 'invalid_value' | 'missing_connection' | 'circular_dependency' | 'resource_error';
  message: string;
  severity: 'critical' | 'high';
  suggestedFix?: string;
}

export interface ExecutionWarning {
  id: string;
  blockId?: string;
  type: 'performance' | 'data_quality' | 'best_practice';
  message: string;
  impact: 'high' | 'medium' | 'low';
}

export interface ResourceRequirement {
  type: 'api_call' | 'computation' | 'memory' | 'storage';
  description: string;
  estimated_cost: number;
  required: boolean;
}

// Validate agent for execution readiness
export function validateForExecution(
  blocks: AgentBlock[], 
  edges: Edge[]
): ExecutionValidationResult {
  const criticalErrors: ExecutionError[] = [];
  const warnings: ExecutionWarning[] = [];
  const resourceRequirements: ResourceRequirement[] = [];

  // Validate execution path
  const executionPath = calculateExecutionPath(blocks, edges);
  if (executionPath.length === 0) {
    criticalErrors.push({
      id: 'no-execution-path',
      type: 'missing_connection',
      message: 'No valid execution path found from entry to output',
      severity: 'critical',
      suggestedFix: 'Connect your blocks from WHEN_RUN to TRIGGER'
    });
  }

  // Validate each block in execution order
  executionPath.forEach(blockId => {
    const block = blocks.find(b => b.id === blockId);
    if (block) {
      validateBlockForExecution(block, blocks, edges, criticalErrors, warnings, resourceRequirements);
    }
  });

  // Check for circular dependencies
  validateCircularDependencies(blocks, edges, criticalErrors);

  // Estimate execution time
  const estimatedExecutionTime = estimateExecutionTime(blocks, edges);

  // Check resource availability
  validateResourceAvailability(resourceRequirements, warnings);

  return {
    canExecute: criticalErrors.length === 0,
    criticalErrors,
    warnings,
    executionPath,
    estimatedExecutionTime,
    resourceRequirements
  };
}

function calculateExecutionPath(blocks: AgentBlock[], edges: Edge[]): string[] {
  // Find entry block
  const entryBlock = blocks.find(block => 
    block.type === BlockType.WHEN_RUN
  );
  
  if (!entryBlock) return [];

  // Build execution path using topological sort
  const visited = new Set<string>();
  const path: string[] = [];
  
  function dfs(blockId: string) {
    if (visited.has(blockId)) return;
    visited.add(blockId);
    path.push(blockId);
    
    // Find connected blocks
    const outgoingEdges = edges.filter(edge => edge.source === blockId);
    outgoingEdges.forEach(edge => {
      dfs(edge.target);
    });
  }
  
  dfs(entryBlock.id);
  return path;
}

function validateBlockForExecution(
  block: AgentBlock,
  allBlocks: AgentBlock[],
  edges: Edge[],
  errors: ExecutionError[],
  warnings: ExecutionWarning[],
  resources: ResourceRequirement[]
) {
  const blockType = block.type as BlockType;

  // Validate required parameters
  const requiredParams = getRequiredExecutionParameters(blockType);
  requiredParams.forEach(param => {
    if (!block.parameters || block.parameters[param] === undefined || block.parameters[param] === null) {
      errors.push({
        id: `missing-param-${block.id}-${param}`,
        blockId: block.id,
        type: 'missing_parameter',
        message: `Block "${blockType}" missing required parameter: ${param}`,
        severity: 'critical',
        suggestedFix: `Set the ${param} parameter in block settings`
      });
    }
  });

  // Validate parameter values
  if (block.parameters) {
    validateParameterValues(block, errors, warnings);
  }

  // Validate connections
  validateBlockConnections(block, edges, errors);

  // Add resource requirements
  addResourceRequirements(block, resources);

  // Block-specific validations
  performBlockSpecificValidation(block, allBlocks, edges, errors, warnings);
}

function getRequiredExecutionParameters(blockType: BlockType): string[] {
  const parameterMap: { [key in BlockType]?: string[] } = {
    [BlockType.INDICATOR]: ['indicator', 'period'],
    [BlockType.CONDITION]: ['operator', 'value'],
    [BlockType.MOVING_AVERAGE]: ['averageType', 'period'],
    [BlockType.MOMENTUM_INDICATOR]: ['indicator', 'period'],
    [BlockType.TREND_INDICATOR]: ['indicator'],
    [BlockType.VOLUME_INDICATOR]: ['indicator'],
    [BlockType.VOLATILITY_INDICATOR]: ['indicator', 'period'],
    [BlockType.TRIGGER]: ['signal'],
    [BlockType.RISK_ANALYZER]: ['method'],
    [BlockType.TARGET_ANALYZER]: ['method'],
    [BlockType.SIGNAL_QUALITY_FILTER]: ['min_confidence'],
    [BlockType.CONFIDENCE_THRESHOLD]: ['minimum_confidence']
  };

  return parameterMap[blockType] || [];
}

function validateParameterValues(
  block: AgentBlock,
  errors: ExecutionError[],
  warnings: ExecutionWarning[]
) {
  const params = block.parameters!;
  
  Object.entries(params).forEach(([key, value]) => {
    // Check for invalid numbers
    if (typeof value === 'number') {
      if (isNaN(value) || !isFinite(value)) {
        errors.push({
          id: `invalid-number-${block.id}-${key}`,
          blockId: block.id,
          type: 'invalid_value',
          message: `Invalid number value for ${key}: ${value}`,
          severity: 'critical',
          suggestedFix: `Enter a valid number for ${key}`
        });
      }
      
      // Check for reasonable ranges
      if (key === 'period' && (value < 1 || value > 200)) {
        warnings.push({
          id: `period-range-${block.id}`,
          blockId: block.id,
          type: 'best_practice',
          message: `Period value ${value} may be outside typical range (1-200)`,
          impact: 'medium'
        });
      }
      
      if (key.includes('confidence') && (value < 0 || value > 100)) {
        errors.push({
          id: `confidence-range-${block.id}`,
          blockId: block.id,
          type: 'invalid_value',
          message: `Confidence value must be between 0 and 100, got ${value}`,
          severity: 'high',
          suggestedFix: 'Set confidence between 0 and 100'
        });
      }
    }
    
    // Check for empty strings
    if (typeof value === 'string' && value.trim() === '') {
      warnings.push({
        id: `empty-string-${block.id}-${key}`,
        blockId: block.id,
        type: 'data_quality',
        message: `Empty value for parameter ${key}`,
        impact: 'medium'
      });
    }
  });
}

function validateBlockConnections(
  block: AgentBlock,
  edges: Edge[],
  errors: ExecutionError[]
) {
  const blockType = block.type as BlockType;
  
  // Check for required inputs
  const requiresInput = ![BlockType.WHEN_RUN, BlockType.PRICE, BlockType.FUNDAMENTAL].includes(blockType);
  
  if (requiresInput) {
    const hasInput = edges.some(edge => edge.target === block.id);
    if (!hasInput) {
      errors.push({
        id: `no-input-${block.id}`,
        blockId: block.id,
        type: 'missing_connection',
        message: `Block "${blockType}" requires an input connection`,
        severity: 'critical',
        suggestedFix: 'Connect an input block to this block'
      });
    }
  }
  
  // Check for required outputs (except terminal blocks)
  const isTerminal = [BlockType.TRIGGER, BlockType.SIGNAL, BlockType.CONSOLE_LOG].includes(blockType);
  
  if (!isTerminal) {
    const hasOutput = edges.some(edge => edge.source === block.id);
    if (!hasOutput) {
      errors.push({
        id: `no-output-${block.id}`,
        blockId: block.id,
        type: 'missing_connection',
        message: `Block "${blockType}" should connect to another block`,
        severity: 'high',
        suggestedFix: 'Connect this block to a downstream block'
      });
    }
  }
}

function addResourceRequirements(block: AgentBlock, resources: ResourceRequirement[]) {
  const blockType = block.type as BlockType;
  
  // API call requirements
  if ([BlockType.PRICE, BlockType.FUNDAMENTAL, BlockType.STOCK_SENTIMENT].includes(blockType)) {
    resources.push({
      type: 'api_call',
      description: `${blockType} data fetch`,
      estimated_cost: 0.001,
      required: true
    });
  }
  
  // Computation requirements
  if ([BlockType.INDICATOR, BlockType.MOVING_AVERAGE, BlockType.MOMENTUM_INDICATOR].includes(blockType)) {
    resources.push({
      type: 'computation',
      description: `${blockType} calculation`,
      estimated_cost: 0.0001,
      required: true
    });
  }
  
  // Memory requirements for complex analysis
  if ([BlockType.CORRELATION_ANALYSIS, BlockType.FIBONACCI_LEVELS, BlockType.SUPPORT_RESISTANCE].includes(blockType)) {
    resources.push({
      type: 'memory',
      description: `${blockType} analysis buffer`,
      estimated_cost: 0.0005,
      required: true
    });
  }
}

function performBlockSpecificValidation(
  block: AgentBlock,
  allBlocks: AgentBlock[],
  edges: Edge[],
  errors: ExecutionError[],
  warnings: ExecutionWarning[]
) {
  const blockType = block.type as BlockType;
  
  switch (blockType) {
    case BlockType.CONDITION:
      validateConditionBlock(block, errors);
      break;
      
    case BlockType.TRIGGER:
      validateTriggerBlock(block, allBlocks, errors, warnings);
      break;
      
    case BlockType.RISK_ANALYZER:
      validateRiskAnalyzerBlock(block, warnings);
      break;
  }
}

function validateConditionBlock(block: AgentBlock, errors: ExecutionError[]) {
  const params = block.parameters;
  if (!params) return;
  
  const validOperators = ['greater_than', 'less_than', 'equal_to', 'not_equal_to', 'between'];
  if (params.operator && !validOperators.includes(params.operator)) {
    errors.push({
      id: `invalid-operator-${block.id}`,
      blockId: block.id,
      type: 'invalid_value',
      message: `Invalid operator: ${params.operator}`,
      severity: 'critical',
      suggestedFix: 'Choose a valid operator from the dropdown'
    });
  }
}

function validateTriggerBlock(
  block: AgentBlock, 
  allBlocks: AgentBlock[], 
  errors: ExecutionError[], 
  warnings: ExecutionWarning[]
) {
  // Check if there are risk management blocks
  const hasRiskManagement = allBlocks.some(b => 
    [BlockType.RISK_ANALYZER, BlockType.TARGET_ANALYZER].includes(b.type as BlockType)
  );
  
  if (!hasRiskManagement) {
    warnings.push({
      id: `no-risk-management-${block.id}`,
      blockId: block.id,
      type: 'best_practice',
      message: 'Consider adding risk management blocks for better signal quality',
      impact: 'high'
    });
  }
}

function validateRiskAnalyzerBlock(block: AgentBlock, warnings: ExecutionWarning[]) {
  const params = block.parameters;
  if (!params) return;
  
  if (params.method === 'atr_based' && (!params.atr_multiplier || params.atr_multiplier < 1)) {
    warnings.push({
      id: `low-atr-multiplier-${block.id}`,
      blockId: block.id,
      type: 'best_practice',
      message: 'ATR multiplier below 1 may result in very tight stops',
      impact: 'medium'
    });
  }
}

function validateCircularDependencies(
  blocks: AgentBlock[], 
  edges: Edge[], 
  errors: ExecutionError[]
) {
  const visited = new Set<string>();
  const recursionStack = new Set<string>();
  
  function hasCycle(blockId: string): boolean {
    if (recursionStack.has(blockId)) return true;
    if (visited.has(blockId)) return false;
    
    visited.add(blockId);
    recursionStack.add(blockId);
    
    const outgoingEdges = edges.filter(edge => edge.source === blockId);
    for (const edge of outgoingEdges) {
      if (hasCycle(edge.target)) return true;
    }
    
    recursionStack.delete(blockId);
    return false;
  }

  blocks.forEach(block => {
    if (hasCycle(block.id)) {
      errors.push({
        id: `circular-dependency-${block.id}`,
        blockId: block.id,
        type: 'circular_dependency',
        message: 'Circular dependency detected in execution flow',
        severity: 'critical',
        suggestedFix: 'Remove circular connections between blocks'
      });
    }
  });
}

function estimateExecutionTime(blocks: AgentBlock[], edges: Edge[]): number {
  // Base time estimates per block type (in milliseconds)
  const timeEstimates: { [key in BlockType]?: number } = {
    [BlockType.WHEN_RUN]: 10,
    [BlockType.PRICE]: 100,
    [BlockType.FUNDAMENTAL]: 150,
    [BlockType.INDICATOR]: 50,
    [BlockType.CONDITION]: 5,
    [BlockType.TRIGGER]: 20,
    [BlockType.RISK_ANALYZER]: 30,
    [BlockType.CORRELATION_ANALYSIS]: 200,
    [BlockType.FIBONACCI_LEVELS]: 100
  };
  
  return blocks.reduce((total, block) => {
    const blockTime = timeEstimates[block.type as BlockType] || 25;
    return total + blockTime;
  }, 0);
}

function validateResourceAvailability(
  requirements: ResourceRequirement[], 
  warnings: ExecutionWarning[]
) {
  const totalCost = requirements.reduce((sum, req) => sum + req.estimated_cost, 0);
  
  if (totalCost > 0.01) {
    warnings.push({
      id: 'high-resource-cost',
      type: 'performance',
      message: `Estimated execution cost: $${totalCost.toFixed(4)}`,
      impact: 'medium'
    });
  }
  
  const apiCalls = requirements.filter(req => req.type === 'api_call').length;
  if (apiCalls > 5) {
    warnings.push({
      id: 'many-api-calls',
      type: 'performance',
      message: `High number of API calls (${apiCalls}) may slow execution`,
      impact: 'medium'
    });
  }
}
