import { whopIntermediaryClient } from '@/lib/whopIntermediaryClient';

/**
 * Comprehensive test suite for the Whop intermediary server integration
 */
export const testWhopIntegration = async () => {
  console.log('🧪 Starting Whop Integration Test Suite...');
  
  const results = {
    healthCheck: null as any,
    testConnection: null as any,
    testUser: null as any,
    currentUser: null as any,
    checkAccess: null as any,
    initializeAuth: null as any,
    summary: {
      total: 6,
      passed: 0,
      failed: 0,
      skipped: 0
    }
  };

  // Test 1: Health Check
  try {
    console.log('1️⃣ Testing health check...');
    results.healthCheck = await whopIntermediaryClient.healthCheck();
    if (results.healthCheck.success) {
      results.summary.passed++;
      console.log('✅ Health check passed');
    } else {
      results.summary.failed++;
      console.log('❌ Health check failed:', results.healthCheck.error);
    }
  } catch (error) {
    results.summary.failed++;
    results.healthCheck = { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    console.log('❌ Health check error:', error);
  }

  // Test 2: Test Connection
  try {
    console.log('2️⃣ Testing Whop connection...');
    results.testConnection = await whopIntermediaryClient.testConnection();
    if (results.testConnection.success) {
      results.summary.passed++;
      console.log('✅ Test connection passed');
    } else {
      results.summary.failed++;
      console.log('❌ Test connection failed:', results.testConnection.error);
    }
  } catch (error) {
    results.summary.failed++;
    results.testConnection = { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    console.log('❌ Test connection error:', error);
  }

  // Test 3: Test User (should work)
  try {
    console.log('3️⃣ Testing get test user...');
    results.testUser = await whopIntermediaryClient.getTestUser();
    if (results.testUser.success) {
      results.summary.passed++;
      console.log('✅ Get test user passed');
    } else {
      results.summary.failed++;
      console.log('❌ Get test user failed:', results.testUser.error);
    }
  } catch (error) {
    results.summary.failed++;
    results.testUser = { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    console.log('❌ Get test user error:', error);
  }

  // Test 4: Current User (expected to fail outside Whop iframe)
  try {
    console.log('4️⃣ Testing get current user (expected to fail outside Whop iframe)...');
    results.currentUser = await whopIntermediaryClient.getCurrentUser();
    if (results.currentUser.success) {
      results.summary.passed++;
      console.log('✅ Get current user passed (unexpected - are you in a Whop iframe?)');
    } else {
      results.summary.skipped++;
      console.log('⏭️ Get current user failed as expected (not in Whop iframe):', results.currentUser.error);
    }
  } catch (error) {
    results.summary.skipped++;
    results.currentUser = { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    console.log('⏭️ Get current user error as expected:', error);
  }

  // Test 5: Check Access (expected to fail outside Whop iframe)
  try {
    console.log('5️⃣ Testing check access (expected to fail outside Whop iframe)...');
    results.checkAccess = await whopIntermediaryClient.checkUserAccess();
    if (results.checkAccess.success) {
      results.summary.passed++;
      console.log('✅ Check access passed (unexpected - are you in a Whop iframe?)');
    } else {
      results.summary.skipped++;
      console.log('⏭️ Check access failed as expected (not in Whop iframe):', results.checkAccess.error);
    }
  } catch (error) {
    results.summary.skipped++;
    results.checkAccess = { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    console.log('⏭️ Check access error as expected:', error);
  }

  // Test 6: Initialize Auth (expected to fail outside Whop iframe)
  try {
    console.log('6️⃣ Testing initialize auth (expected to fail outside Whop iframe)...');
    results.initializeAuth = await whopIntermediaryClient.initializeAuth();
    if (results.initializeAuth.isWhopUser) {
      results.summary.passed++;
      console.log('✅ Initialize auth passed (unexpected - are you in a Whop iframe?)');
    } else {
      results.summary.skipped++;
      console.log('⏭️ Initialize auth failed as expected (not in Whop iframe)');
    }
  } catch (error) {
    results.summary.skipped++;
    results.initializeAuth = { isWhopUser: false, user: null, accessResult: null, error: error instanceof Error ? error.message : 'Unknown error' };
    console.log('⏭️ Initialize auth error as expected:', error);
  }

  // Summary
  console.log('\n📊 Test Suite Summary:');
  console.log(`✅ Passed: ${results.summary.passed}/${results.summary.total}`);
  console.log(`❌ Failed: ${results.summary.failed}/${results.summary.total}`);
  console.log(`⏭️ Skipped (expected): ${results.summary.skipped}/${results.summary.total}`);
  
  const criticalTests = results.summary.passed >= 3; // Health, Connection, TestUser should pass
  if (criticalTests) {
    console.log('🎉 Critical tests passed! Intermediary server is working correctly.');
    console.log('ℹ️ Tests that failed are expected when not running in a Whop iframe.');
  } else {
    console.log('⚠️ Some critical tests failed. Check your intermediary server setup.');
  }

  return results;
};

// Export for use in console
(window as any).testWhopIntegration = testWhopIntegration;
