// Intelligent Canvas Layout Engine for AI Agent Builder
import { Node, <PERSON> } from 'reactflow';
import { Agent<PERSON><PERSON> } from '@/services/agentService';

// Define block types locally to avoid circular imports
enum BlockType {
  WHEN_RUN = 'WHEN_RUN',
  INDICATOR = 'INDICATOR',
  PRICE = 'PRICE',
  FUNDAMENTAL = 'FUNDAMENTAL',
  CONDITION = 'CONDITION',
  TRIGGER = 'TRIGGER',
  CONFIDENCE_BOOST = 'CONFIDENCE_BOOST',
  CANDLE_PATTERN = 'CANDLE_PATTERN',
  STOCK_SENTIMENT = 'STOCK_SENTIMENT',
  CHART_PATTERN = 'CHART_PATTERN',
  BREAKOUT_DETECTION = 'BREAKOUT_DETECTION',
  GAP_ANALYSIS = 'GAP_ANALYSIS',
  MOMENTUM_INDICATOR = 'MOMENTUM_INDICATOR',
  MOVING_AVERAGE = 'MOVING_AVERAGE',

  TREND_INDICATOR = 'TREND_INDICATOR',
  VOLUME_INDICATOR = 'VOLUME_INDICATOR',
  VOLATILITY_INDICATOR = 'VOLATILITY_INDICATOR',
  TREND_LINE_ANALYSIS = 'TREND_LINE_ANALYSIS',
  MARKET_STRUCTURE = 'MARKET_STRUCTURE',

  NOT_OPERATOR = 'NOT_OPERATOR',
  AND = 'AND',
  OR = 'OR',
  TIME_FILTER = 'TIME_FILTER',
  MARKET_CONDITION_FILTER = 'MARKET_CONDITION_FILTER',
  SESSION_FILTER = 'SESSION_FILTER',
  BULLISH_CONFIDENCE_BOOST = 'BULLISH_CONFIDENCE_BOOST',
  BEARISH_CONFIDENCE_BOOST = 'BEARISH_CONFIDENCE_BOOST'
}

export interface LayoutConfig {
  horizontalSpacing: number;
  verticalSpacing: number;
  gridSize: number;
  layerHeight: number;
  blockWidth: number;
  blockHeight: number;
}

export const DEFAULT_LAYOUT_CONFIG: LayoutConfig = {
  horizontalSpacing: 200,
  verticalSpacing: 120,
  gridSize: 20,
  layerHeight: 150,
  blockWidth: 280,
  blockHeight: 120
};

// Block categorization for intelligent layout
export const BLOCK_CATEGORIES = {
  ENTRY: [BlockType.WHEN_RUN],
  DATA_SOURCE: [
    BlockType.PRICE,
    BlockType.FUNDAMENTAL,
    BlockType.INDICATOR,
    BlockType.MOVING_AVERAGE,
    BlockType.MOMENTUM_INDICATOR,
    BlockType.TREND_INDICATOR,
    BlockType.VOLUME_INDICATOR,
    BlockType.VOLATILITY_INDICATOR
  ],
  ANALYSIS: [
    BlockType.CANDLE_PATTERN,
    BlockType.CHART_PATTERN,
    BlockType.BREAKOUT_DETECTION,
    BlockType.GAP_ANALYSIS,
    BlockType.TREND_LINE_ANALYSIS,
    BlockType.MARKET_STRUCTURE,
    BlockType.STOCK_SENTIMENT
  ],
  LOGIC: [
    BlockType.CONDITION,
    BlockType.NOT_OPERATOR,
    BlockType.AND,
    BlockType.OR
  ],
  FILTERS: [
    BlockType.TIME_FILTER,
    BlockType.MARKET_CONDITION_FILTER,
    BlockType.SESSION_FILTER
  ],
  OUTPUT: [
    BlockType.TRIGGER,
    BlockType.SIGNAL,
    BlockType.CONFIDENCE_BOOST,
    BlockType.BULLISH_CONFIDENCE_BOOST,
    BlockType.BEARISH_CONFIDENCE_BOOST
  ]
};

// Get block category for layout purposes
export function getBlockCategory(blockType: string): string {
  for (const [category, types] of Object.entries(BLOCK_CATEGORIES)) {
    if (types.includes(blockType as BlockType)) {
      return category;
    }
  }
  return 'MISC';
}

// Calculate layer index for block type
export function getBlockLayer(blockType: string): number {
  const category = getBlockCategory(blockType);
  const layerMap = {
    ENTRY: 0,
    DATA_SOURCE: 1,
    ANALYSIS: 2,
    LOGIC: 3,
    FILTERS: 4,
    OUTPUT: 5,
    MISC: 6
  };
  return layerMap[category as keyof typeof layerMap] || 6;
}

// Snap position to grid
export function snapToGrid(position: { x: number; y: number }, gridSize: number): { x: number; y: number } {
  return {
    x: Math.round(position.x / gridSize) * gridSize,
    y: Math.round(position.y / gridSize) * gridSize
  };
}



// Calculate optimal canvas bounds
export function calculateCanvasBounds(blocks: AgentBlock[]): { 
  minX: number; 
  minY: number; 
  maxX: number; 
  maxY: number; 
  width: number; 
  height: number; 
} {
  if (blocks.length === 0) {
    return { minX: 0, minY: 0, maxX: 800, maxY: 600, width: 800, height: 600 };
  }

  const positions = blocks.map(block => block.position || { x: 0, y: 0 });
  const minX = Math.min(...positions.map(p => p.x)) - 100;
  const minY = Math.min(...positions.map(p => p.y)) - 100;
  const maxX = Math.max(...positions.map(p => p.x + DEFAULT_LAYOUT_CONFIG.blockWidth)) + 100;
  const maxY = Math.max(...positions.map(p => p.y + DEFAULT_LAYOUT_CONFIG.blockHeight)) + 100;

  return {
    minX,
    minY,
    maxX,
    maxY,
    width: maxX - minX,
    height: maxY - minY
  };
}

// Check for block overlaps
export function detectOverlaps(blocks: AgentBlock[], config: LayoutConfig = DEFAULT_LAYOUT_CONFIG): boolean {
  for (let i = 0; i < blocks.length; i++) {
    for (let j = i + 1; j < blocks.length; j++) {
      const block1 = blocks[i];
      const block2 = blocks[j];
      
      if (!block1.position || !block2.position) continue;

      const overlap = !(
        block1.position.x + config.blockWidth < block2.position.x ||
        block2.position.x + config.blockWidth < block1.position.x ||
        block1.position.y + config.blockHeight < block2.position.y ||
        block2.position.y + config.blockHeight < block1.position.y
      );

      if (overlap) return true;
    }
  }
  return false;
}

// Resolve overlaps by adjusting positions
export function resolveOverlaps(blocks: AgentBlock[], config: LayoutConfig = DEFAULT_LAYOUT_CONFIG): AgentBlock[] {
  const resolvedBlocks = [...blocks];
  let hasOverlaps = true;
  let iterations = 0;
  const maxIterations = 10;

  while (hasOverlaps && iterations < maxIterations) {
    hasOverlaps = false;
    iterations++;

    for (let i = 0; i < resolvedBlocks.length; i++) {
      for (let j = i + 1; j < resolvedBlocks.length; j++) {
        const block1 = resolvedBlocks[i];
        const block2 = resolvedBlocks[j];
        
        if (!block1.position || !block2.position) continue;

        const overlap = !(
          block1.position.x + config.blockWidth < block2.position.x ||
          block2.position.x + config.blockWidth < block1.position.x ||
          block1.position.y + config.blockHeight < block2.position.y ||
          block2.position.y + config.blockHeight < block1.position.y
        );

        if (overlap) {
          hasOverlaps = true;
          // Move block2 to the right of block1
          block2.position.x = block1.position.x + config.blockWidth + config.horizontalSpacing;
          block2.position = snapToGrid(block2.position, config.gridSize);
        }
      }
    }
  }

  return resolvedBlocks;
}

// Generate intelligent position for new block
export function getIntelligentPosition(
  blockType: string,
  existingBlocks: AgentBlock[],
  config: LayoutConfig = DEFAULT_LAYOUT_CONFIG
): { x: number; y: number } {
  const layer = getBlockLayer(blockType);
  const category = getBlockCategory(blockType);

  // Find blocks in the same category
  const sameLayerBlocks = existingBlocks.filter(block =>
    getBlockLayer(block.type) === layer
  );

  if (sameLayerBlocks.length === 0) {
    // First block in this layer
    const baseY = 100 + (layer * (config.blockHeight + config.verticalSpacing));
    return snapToGrid({ x: 200, y: baseY }, config.gridSize);
  }

  // Find the rightmost position in this layer
  const rightmostX = Math.max(...sameLayerBlocks.map(block => 
    (block.position?.x || 0) + config.blockWidth
  ));

  const layerY = 100 + (layer * (config.blockHeight + config.verticalSpacing));
  const newX = rightmostX + config.horizontalSpacing;

  return snapToGrid({ x: newX, y: layerY }, config.gridSize);
}

// Create hierarchical grouping for complex agents
export function createBlockGroups(blocks: AgentBlock[]): { [category: string]: AgentBlock[] } {
  const groups: { [category: string]: AgentBlock[] } = {};

  blocks.forEach(block => {
    const category = getBlockCategory(block.type);
    if (!groups[category]) groups[category] = [];
    groups[category].push(block);
  });

  return groups;
}
