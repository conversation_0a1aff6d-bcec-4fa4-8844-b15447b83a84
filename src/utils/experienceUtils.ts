import { whopIntermediaryClient } from '@/lib/whopIntermediaryClient';
import { detectCurrentAppWithExperience } from '@/lib/whop-app-config';

export interface ExperienceInfo {
  name: string;
  id: string;
  description?: string;
}

// Cache for experience info to avoid repeated API calls
const experienceCache = new Map<string, ExperienceInfo>();

/**
 * Get experience information by ID
 * Uses caching to avoid repeated API calls
 */
export async function getExperienceInfo(experienceId: string): Promise<ExperienceInfo | null> {
  // Check cache first
  if (experienceCache.has(experienceId)) {
    return experienceCache.get(experienceId)!;
  }

  try {
    console.log('🔍 Fetching experience info for:', experienceId);
    
    const response = await whopIntermediaryClient.apiCall(`/experience/${experienceId}`);
    
    if (response.success && response.data) {
      const experience = response.data.experience;
      const experienceInfo: ExperienceInfo = {
        name: experience.name || '',
        id: experience.id || experienceId,
        description: experience.description
      };
      
      // Cache the result
      experienceCache.set(experienceId, experienceInfo);
      
      console.log('✅ Experience info loaded and cached:', experienceInfo);
      return experienceInfo;
    } else {
      console.warn('⚠️ Failed to fetch experience info:', response.error);
      return null;
    }
  } catch (error) {
    console.error('❌ Error fetching experience info:', error);
    return null;
  }
}

/**
 * Determine app type based on experience name
 */
export function getAppTypeFromExperienceName(experienceName: string): 'trading' | 'osis' | 'unknown' {
  const name = experienceName.toLowerCase().trim();
  
  if (name.includes('trade')) {
    return 'trading';
  }
  
  if (name.includes('osis') || !name) {
    return 'osis';
  }
  
  return 'unknown';
}

/**
 * Get routing path based on experience name
 */
export function getRouteFromExperienceName(experienceName: string): string {
  const appType = getAppTypeFromExperienceName(experienceName);
  
  switch (appType) {
    case 'trading':
      return '/trading';
    case 'osis':
      return '/';
    default:
      // Default to trading for unknown types
      return '/trading';
  }
}

/**
 * Get experience info and determine app type using enhanced detection
 * This combines experience fetching with app detection for better accuracy
 */
export async function getExperienceInfoWithAppDetection(experienceId: string): Promise<{
  experienceInfo: ExperienceInfo | null;
  appType: string;
}> {
  try {
    // Use the enhanced app detection that fetches experience app name
    const appType = await detectCurrentAppWithExperience();

    // Also get the detailed experience info
    const experienceInfo = await getExperienceInfo(experienceId);

    console.log('✅ Experience info and app type determined:', {
      experienceId,
      appType,
      experienceName: experienceInfo?.name
    });

    return {
      experienceInfo,
      appType
    };
  } catch (error) {
    console.error('❌ Error getting experience info with app detection:', error);
    return {
      experienceInfo: null,
      appType: 'osis' // Default fallback
    };
  }
}

/**
 * Clear experience cache (useful for testing or when experience info changes)
 */
export function clearExperienceCache(): void {
  experienceCache.clear();
  console.log('🧹 Experience cache cleared');
}
