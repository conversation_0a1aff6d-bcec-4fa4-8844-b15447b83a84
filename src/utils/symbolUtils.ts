import { callGeminiAPI } from './geminiUtils';

// Cache for symbol type detection to reduce API calls
const symbolTypeCache: Record<string, boolean> = {};

// Known cryptocurrency symbols to avoid API calls
const knownCryptoSymbols = new Set([
  'BTC', 'ETH', 'XRP', 'LTC', 'DOGE', 'SOL', 'ADA', 'DOT', 'AVAX', 'MATIC', 'LINK', 'UNI', 'SHIB'
]);

/**
 * Intelligently detects if a symbol is likely a cryptocurrency based on patterns
 * without relying on hardcoded lists
 */
export async function isCryptoCurrency(symbol: string, userEmail?: string): Promise<boolean> {
  if (!symbol) return false;

  const upperSymbol = symbol.toUpperCase();

  // Check cache first
  if (symbolTypeCache[upperSymbol] !== undefined) {
    return symbolTypeCache[upperSymbol];
  }

  // Check if it's a one-letter stock symbol
  const oneLetterStockSymbols = new Set([
    'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K',
    'L', 'M', 'O', 'R', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'
  ]);

  if (upperSymbol.length === 1 && oneLetterStockSymbols.has(upperSymbol)) {
    symbolTypeCache[upperSymbol] = false;
    return false;
  }

  // Check known crypto symbols
  if (knownCryptoSymbols.has(upperSymbol)) {
    symbolTypeCache[upperSymbol] = true;
    return true;
  }

  try {

    const prompt = `Is ${symbol} a cryptocurrency symbol? Answer with ONLY "true" for crypto or "false" for stock.

    Note: The following one-letter symbols are stocks, not cryptocurrencies:
    A: Agilent Technologies
    B: Barnes Group
    C: Citigroup
    D: Dominion Energy
    E: Eni S.p.A.
    F: Ford Motor Company
    G: Genpact Limited
    H: Hyatt Hotels
    J: Jacobs Engineering Group
    K: Kellogg Company
    L: Loews Corporation
    M: Macy's
    O: Realty Income Corporation
    R: Ryder System
    T: AT&T
    U: Unity Software
    V: Visa Inc.
    W: Wayfair
    X: United States Steel Corporation
    Y: Alleghany Corporation
    Z: Zillow Group`;

    const data = await callGeminiAPI(prompt, {
      distinctId: userEmail || 'anonymous',
      traceId: crypto.randomUUID(),
      properties: {
        symbol: symbol,
        check_type: 'crypto_detection',
        function: 'isCryptoCurrency'
      }
    }, {
      generationConfig: {
        temperature: 0.1,
        maxOutputTokens: 10
      }
    });

    const result = data?.candidates?.[0]?.content?.parts?.[0]?.text?.trim().toLowerCase();

    if (!result) {
      // Default to false and don't cache on empty result
      return false;
    }

    // Convert string "true"/"false" to boolean and cache the result
    const isCrypto = result === 'true';
    symbolTypeCache[upperSymbol] = isCrypto;
    return isCrypto;
  } catch (error) {
    console.error(`Error detecting if ${symbol} is a cryptocurrency:`, error);

    // For known crypto symbols, return true even on error
    if (knownCryptoSymbols.has(upperSymbol)) {
      symbolTypeCache[upperSymbol] = true;
      return true;
    }

    // Default to false but don't cache on error
    return false;
  }
}

/**
 * Formats a symbol for the Polygon API based on detected type
 */
export async function formatPolygonSymbol(symbol: string, userEmail?: string): Promise<string> {
  if (!symbol) return '';

  const upperSymbol = symbol.toUpperCase();

  // Check if it's already formatted for Polygon
  if (upperSymbol.startsWith('X:') && upperSymbol.endsWith('USD')) {
    return upperSymbol;
  }

  try {
    // Use the cached/optimized isCryptoCurrency function
    const isCrypto = await isCryptoCurrency(symbol, userEmail);

    if (isCrypto) {
      // Remove any USD suffix if present
      const baseSymbol = upperSymbol
        .replace('/USD', '')
        .replace('-USD', '')
        .replace('USD', '');

      const formatted = `X:${baseSymbol}USD`;
      return formatted;
    }

    // For stocks, just return the uppercase symbol
    return upperSymbol;
  } catch (error) {
    console.error(`Error formatting symbol ${symbol} for Polygon:`, error);

    // Check if it's a known crypto symbol even if the API call failed
    if (knownCryptoSymbols.has(upperSymbol)) {
      const baseSymbol = upperSymbol
        .replace('/USD', '')
        .replace('-USD', '')
        .replace('USD', '');

      return `X:${baseSymbol}USD`;
    }

    // Return uppercase symbol as fallback
    return upperSymbol;
  }
}

/**
 * Gets a display name for the symbol
 */
export async function getSymbolDisplayName(symbol: string, userEmail?: string): Promise<string> {
  if (!symbol) return '';

  const upperSymbol = symbol.toUpperCase();

  try {
    // Use the cached/optimized isCryptoCurrency function
    const isCrypto = await isCryptoCurrency(symbol, userEmail);

    if (isCrypto) {
      // If it's a crypto, display it with USD pair
      const baseSymbol = upperSymbol
        .replace('X:', '')
        .replace('USD', '')
        .replace('/USD', '')
        .replace('-USD', '');

      return `${baseSymbol}/USD`;
    }

    return upperSymbol;
  } catch (error) {
    console.error(`Error getting display name for symbol ${symbol}:`, error);

    // Check if it's a known crypto symbol even if the API call failed
    if (knownCryptoSymbols.has(upperSymbol.replace('X:', '').replace('USD', ''))) {
      const baseSymbol = upperSymbol
        .replace('X:', '')
        .replace('USD', '')
        .replace('/USD', '')
        .replace('-USD', '');

      return `${baseSymbol}/USD`;
    }

    // Return uppercase symbol as fallback
    return upperSymbol;
  }
}
