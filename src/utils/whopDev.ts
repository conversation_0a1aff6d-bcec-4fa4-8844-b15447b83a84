/**
 * Development utilities for testing Whop integration
 * These functions help simulate Whop environment for development and testing
 */

import { storeWhopUserToken, clearWhopUserToken } from './whopAuth';

// Mock Whop user token for development
const MOCK_WHOP_TOKEN = 'mock-whop-token-for-development';

// Mock experience IDs for testing
export const MOCK_EXPERIENCE_IDS = {
  BASIC: 'exp_basic_123',
  PREMIUM: 'exp_premium_456',
  ADMIN: 'exp_admin_789'
};

/**
 * Enable Whop development mode
 * This simulates being in a Whop iframe environment
 */
export const enableWhopDevMode = (experienceId?: string) => {
  if (typeof window === 'undefined') return;

  console.log('🔧 Enabling Whop development mode...');

  // Store mock token
  storeWhopUserToken(MOCK_WHOP_TOKEN);

  // Add Whop-specific URL parameters
  const url = new URL(window.location.href);
  url.searchParams.set('whop', 'true');
  url.searchParams.set('whop_dev', 'true');
  
  if (experienceId) {
    // Navigate to experience page
    window.history.pushState({}, '', `/experiences/${experienceId}?whop=true&whop_dev=true`);
  } else {
    // Just add query parameters
    window.history.pushState({}, '', url.toString());
  }

  // Simulate iframe environment
  Object.defineProperty(window, 'parent', {
    value: {
      ...window.parent,
      location: {
        hostname: 'whop.com'
      }
    },
    writable: false
  });

  console.log('✅ Whop development mode enabled');
  console.log('🔄 Reload the page to see Whop integration in action');
};

/**
 * Disable Whop development mode
 */
export const disableWhopDevMode = () => {
  if (typeof window === 'undefined') return;

  console.log('🔧 Disabling Whop development mode...');

  // Clear mock token
  clearWhopUserToken();

  // Remove Whop-specific URL parameters
  const url = new URL(window.location.href);
  url.searchParams.delete('whop');
  url.searchParams.delete('whop_dev');
  url.searchParams.delete('whop_token');

  // Navigate back to regular page if on experience page
  if (window.location.pathname.startsWith('/experiences/')) {
    window.history.pushState({}, '', '/');
  } else {
    window.history.pushState({}, '', url.toString());
  }

  console.log('✅ Whop development mode disabled');
  console.log('🔄 Reload the page to return to normal mode');
};

/**
 * Check if we're in Whop development mode
 */
export const isWhopDevMode = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get('whop_dev') === 'true';
};

/**
 * Get development tools for Whop integration
 */
export const getWhopDevTools = () => {
  return {
    enableWhopDevMode,
    disableWhopDevMode,
    isWhopDevMode: isWhopDevMode(),
    mockExperienceIds: MOCK_EXPERIENCE_IDS,

    // Quick access functions
    enableBasicExperience: () => enableWhopDevMode(MOCK_EXPERIENCE_IDS.BASIC),
    enablePremiumExperience: () => enableWhopDevMode(MOCK_EXPERIENCE_IDS.PREMIUM),
    enableAdminExperience: () => enableWhopDevMode(MOCK_EXPERIENCE_IDS.ADMIN),

    // Utility functions
    getCurrentExperienceId: () => {
      const match = window.location.pathname.match(/\/experiences\/([^\/]+)/);
      return match ? match[1] : null;
    },

    // API testing functions
    testApiConnection: async () => {
      try {
        console.log('🧪 Testing Whop API connection...');
        const response = await fetch('/api/whop/test-connection');
        const result = await response.json();
        console.log('✅ API Connection Test Result:', result);
        return result;
      } catch (error) {
        console.error('❌ API Connection Test Failed:', error);
        return { success: false, error: error.message };
      }
    },

    testTokenVerification: async () => {
      try {
        console.log('🔐 Testing token verification...');
        const token = localStorage.getItem('whop_user_token') || MOCK_WHOP_TOKEN;
        const response = await fetch('/api/whop/verify-user', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-whop-user-token': token
          }
        });
        const result = await response.json();
        console.log('✅ Token Verification Test Result:', result);
        return result;
      } catch (error) {
        console.error('❌ Token Verification Test Failed:', error);
        return { success: false, error: error.message };
      }
    },

    // Console helpers
    logWhopStatus: () => {
      console.log('🔍 Whop Development Status:', {
        isDevMode: isWhopDevMode(),
        currentPath: window.location.pathname,
        experienceId: window.location.pathname.match(/\/experiences\/([^\/]+)/)?.[1],
        hasWhopToken: !!localStorage.getItem('whop_user_token'),
        urlParams: Object.fromEntries(new URLSearchParams(window.location.search)),
        apiServer: 'http://localhost:3001'
      });
    }
  };
};

/**
 * Initialize Whop development tools in global scope for easy access
 */
export const initWhopDevTools = () => {
  if (typeof window === 'undefined' || import.meta.env.PROD) return;

  // Add to global scope for console access
  (window as any).whopDev = getWhopDevTools();

  console.log('🛠️ Whop development tools available at window.whopDev');
  console.log('📖 Available commands:');
  console.log('  - whopDev.enableWhopDevMode() - Enable Whop dev mode');
  console.log('  - whopDev.disableWhopDevMode() - Disable Whop dev mode');
  console.log('  - whopDev.enableBasicExperience() - Test basic experience');
  console.log('  - whopDev.enablePremiumExperience() - Test premium experience');
  console.log('  - whopDev.enableAdminExperience() - Test admin experience');
  console.log('  - whopDev.testApiConnection() - Test API server connection');
  console.log('  - whopDev.testTokenVerification() - Test token verification');
  console.log('  - whopDev.logWhopStatus() - Show current status');
};

// Auto-initialize in development
if (import.meta.env.DEV) {
  initWhopDevTools();
}
