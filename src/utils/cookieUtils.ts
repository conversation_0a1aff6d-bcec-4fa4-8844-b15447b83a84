// Cookie utility functions for managing subscription status

// <PERSON>ie names
export const SUBSCRIPTION_COOKIE_NAME = 'Osis_subscription_status';
export const SUBSCRIPTION_COOKIE_MAX_AGE = 60 * 60 * 24 * 7; // 7 days in seconds

/**
 * Set a subscription status cookie
 * @param hasSubscription Boolean indicating if user has an active subscription
 */
export const setSubscriptionCookie = (hasSubscription: boolean): void => {
  try {
    document.cookie = `${SUBSCRIPTION_COOKIE_NAME}=${hasSubscription}; path=/; max-age=${SUBSCRIPTION_COOKIE_MAX_AGE}`;
  } catch (error) {
    console.error('Error setting subscription cookie:', error);
  }
};

/**
 * Get the subscription status from cookies
 * @returns Boolean indicating if user has an active subscription (or null if cookie not found)
 */
export const getSubscriptionCookie = (): boolean | null => {
  try {
    if (!document.cookie) {
      return null;
    }

    const cookies = document.cookie.split(';');
    for (const cookie of cookies) {
      const parts = cookie.trim().split('=');
      if (parts.length !== 2) continue;

      const [name, value] = parts;
      if (name === SUBSCRIPTION_COOKIE_NAME) {
        return value === 'true';
      }
    }

    return null;
  } catch (error) {
    console.error('Error getting subscription cookie:', error);
    return null;
  }
};

/**
 * Clear the subscription status cookie
 */
export const clearSubscriptionCookie = (): void => {
  try {
    document.cookie = `${SUBSCRIPTION_COOKIE_NAME}=; path=/; max-age=0`;
  } catch (error) {
    console.error('Error clearing subscription cookie:', error);
  }
};
