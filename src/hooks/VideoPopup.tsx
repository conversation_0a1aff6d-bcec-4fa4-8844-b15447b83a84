import { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface VideoPopupProps {
  videoUrl: string;
  onClose: () => void;
}

const VideoPopup = ({ videoUrl, onClose }: VideoPopupProps) => {
  return (
    <motion.div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/70 backdrop-blur-sm p-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
    >
      <motion.div
        className="relative w-full max-w-2xl bg-[#121212]/90 rounded-2xl border border-white/[0.08] overflow-hidden shadow-2xl"
        initial={{ scale: 0.95, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.95, opacity: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
        style={{ boxShadow: '0 0 30px rgba(0,0,0,0.4), 0 0 0 1px rgba(255,255,255,0.05)' }}
      >
        {/* Clean header */}
        <div className="bg-[#0A0A0A]/80 backdrop-blur-sm px-5 py-3 flex items-center justify-between border-b border-white/[0.06]">
          <h3 className="text-white text-base font-medium tracking-tight">Welcome to Osis</h3>
          <button
            onClick={onClose}
            className="flex items-center justify-center w-7 h-7 rounded-full bg-black/30 text-white/80 hover:bg-black/50 transition-colors hover:text-white"
            aria-label="Close"
          >
            <X size={16} />
          </button>
        </div>

        <div className="aspect-video w-full p-3 pt-2">
          <video
            src={videoUrl}
            className="w-full h-full object-cover rounded-lg overflow-hidden"
            controls
            autoPlay
            playsInline
          />
        </div>

        <div className="px-5 py-3 text-center border-t border-white/[0.06]">
          <p className="text-white/50 text-xs">Watch this quick demo to see how Osis works</p>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default VideoPopup;
