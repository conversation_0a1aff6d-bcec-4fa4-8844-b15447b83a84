import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useSubscription } from './useSubscription';

// Constants for plans and limits
export const PLAN_TYPES = {
  basic: 'basic',
  pro: 'pro',
  premium: 'premium'
} as const;

// Define message limits based on plan
export const MESSAGE_LIMITS = {
  [PLAN_TYPES.basic]: 100,
  [PLAN_TYPES.pro]: 200,
  [PLAN_TYPES.premium]: 300
} as const;



// Map price IDs to plan types (includes both legacy and current price IDs)
export const PRICE_ID_TO_PLAN = {
  // Legacy price IDs (before 6/3/25)
  'price_1RVyDZFagIdq3bE61UyNGiAj': PLAN_TYPES.basic,    // $9.99/week
  'price_1RVyt2FagIdq3bE6Fb4d5OS2': PLAN_TYPES.basic,    // $360/year
  'price_1RVyE6FagIdq3bE6RP8yRJEv': PLAN_TYPES.pro,      // $14.99/week & $540/year
  'price_1RVyEvFagIdq3bE6TEMbZaUg': PLAN_TYPES.premium,  // $19.99/week
  'price_1RVysMFagIdq3bE6O30J3j9z': PLAN_TYPES.premium,  // $720/year

  // Current price IDs (6/3/25 and after)
  'price_1ROYLKDebmd1GpTvct491Kw6': PLAN_TYPES.basic,    // $9.99/week
  'price_1RVyr7Debmd1GpTvgWmmS7R1': PLAN_TYPES.basic,    // $360/year
  'price_1ROYKjDebmd1GpTv5oYNMKMv': PLAN_TYPES.pro,      // $14.99/week
  'price_1RVyqhDebmd1GpTvwoY0ridy': PLAN_TYPES.pro,      // $540/year
  'price_1RVylIDebmd1GpTvXxDXQBwH': PLAN_TYPES.premium,  // $19.99/week
  'price_1RVypVDebmd1GpTvvh0jJVWT': PLAN_TYPES.premium,  // $720/year

  // Legacy Plans (keeping for backward compatibility)
  'price_1R6LbqDebmd1GpTvkP4EXccx': PLAN_TYPES.basic,    // $25/mo
  'price_1R6LivDebmd1GpTvMMAnjTFo': PLAN_TYPES.basic,    // $225/yr
  'price_1R6LcLDebmd1GpTv1orxNWsv': PLAN_TYPES.basic,    // $40/mo
  'price_1R6LkMDebmd1GpTvzpfm5qwJ': PLAN_TYPES.basic,    // $360/yr
  'price_1R6LclDebmd1GpTvAIesiTY0': PLAN_TYPES.basic,    // $60/mo
  'price_1R6LkXDebmd1GpTvOz2OEbUh': PLAN_TYPES.basic,    // $540/yr
  'price_1R6LdFDebmd1GpTvURHjz9F0': PLAN_TYPES.pro,      // $40/mo
  'price_1R6LknDebmd1GpTvDnBj2Em1': PLAN_TYPES.pro,      // $360/yr
  'price_1R6LdZDebmd1GpTvb0gRgBEV': PLAN_TYPES.pro,      // $60/mo
  'price_1R6LlQDebmd1GpTvum6KZMSn': PLAN_TYPES.pro,      // $540/yr
  'price_1R6LeNDebmd1GpTvs5Q8ps55': PLAN_TYPES.pro,      // $80/mo
  'price_1R6LmSDebmd1GpTvvurNQOYM': PLAN_TYPES.pro,      // $720/yr
  'price_1R6LekDebmd1GpTvaH38JNpc': PLAN_TYPES.premium,  // $55/mo
  'price_1R6LmsDebmd1GpTvnTJli7Qm': PLAN_TYPES.premium,  // $495/yr
  'price_1R6LfCDebmd1GpTvnRMTfHyR': PLAN_TYPES.premium,  // $80/mo
  'price_1R6LnVDebmd1GpTvVObrSaaQ': PLAN_TYPES.premium,  // $720/yr
  'price_1R6LfQDebmd1GpTvJGpE8rIX': PLAN_TYPES.premium,  // $100/mo
  'price_1R6LnjDebmd1GpTvckCrIH71': PLAN_TYPES.premium   // $900/yr
};

// Define interfaces for database tables
interface UserTokensRecord {
  id: string;
  user_id: string;
  tokens_remaining: number;
  last_reset: string;
  last_reset_date: string;
  created_at: string;
  updated_at: string;
  plan_type?: string; // Add plan_type field to track the subscription type
}

export type PlanType = typeof PLAN_TYPES[keyof typeof PLAN_TYPES];
export type MessageLimit = typeof MESSAGE_LIMITS[keyof typeof PLAN_TYPES];

interface UserLimitsState {
  isLoading: boolean;
  messagesUsed: number;
  messagesLimit: MessageLimit;
  messagesRemaining: number;
  lastReset: string | null;
  planType: PlanType;
}

const initialState: UserLimitsState = {
  isLoading: true,
  messagesUsed: 0,
  messagesLimit: MESSAGE_LIMITS[PLAN_TYPES.basic],
  messagesRemaining: MESSAGE_LIMITS[PLAN_TYPES.basic],
  lastReset: null,
  planType: PLAN_TYPES.basic
};

// Helper function to check if it's a new month since the last reset
const isNewMonth = (lastResetDate: string | null): boolean => {
  if (!lastResetDate) return true;

  const lastReset = new Date(lastResetDate);
  const now = new Date();

  return (
    now.getMonth() !== lastReset.getMonth() ||
    now.getFullYear() !== lastReset.getFullYear()
  );
};

// All pages are now accessible - no locked pages
export const LOCKED_PAGES_FOR_BASIC: string[] = [];

export const useUserLimits = () => {
  const { subscription, isLoadingSubscription, refetch: refetchSubscription } = useSubscription();

  // Minimal state for UI rendering ONLY - not the source of truth
  const [limitsState, setLimitsState] = useState<UserLimitsState>(initialState);

  // Function to determine subscription type from various sources
  const determineSubscriptionType = async (userId: string) => {
    try {
      // Get subscription data from subscriptions table
      const { data: subscriptionData, error: subscriptionError } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (subscriptionError && subscriptionError.code !== 'PGRST116') {
        console.error('[useUserLimits] Error fetching subscription data:', subscriptionError);
      }

      // Get profile data
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('subscription_type')
        .eq('id', userId)
        .single();

      if (profileError && profileError.code !== 'PGRST116') {
        console.error('[useUserLimits] Error fetching profile data:', profileError);
      }

      // Determine subscription type with clear priority logic
      let subscriptionType = PLAN_TYPES.basic; // Default to basic

      // If we have a valid subscription, use that type
      if (subscriptionData?.status === 'active' && subscriptionData?.stripe_price_id) {
        // Use the shared PRICE_ID_TO_PLAN mapping
        subscriptionType = PRICE_ID_TO_PLAN[subscriptionData.stripe_price_id] || PLAN_TYPES.basic;
      } else if (profileData?.subscription_type) {
        // Use profile subscription type if no active subscription
        subscriptionType = profileData.subscription_type;
      }

      // Validate the subscription type
      if (!Object.values(PLAN_TYPES).includes(subscriptionType as any)) {
        console.warn('[useUserLimits] Invalid subscription type:', subscriptionType);
        subscriptionType = PLAN_TYPES.basic;
      }

      return subscriptionType;
    } catch (error) {
      console.error('[useUserLimits] Error determining subscription type:', error);
      return PLAN_TYPES.basic; // Default to basic on error
    }
  };

  const fetchUserLimits = useCallback(async (forceCheckReset = false) => {
    try {
      const session = await supabase.auth.getSession();

      if (!session.data.session?.user?.id) {
        const fallbackData = {
          isLoading: false,
          messagesLimit: MESSAGE_LIMITS[PLAN_TYPES.basic],
          messagesUsed: 0,
          messagesRemaining: MESSAGE_LIMITS[PLAN_TYPES.basic],
          lastReset: null,
          planType: PLAN_TYPES.basic
        };
        setLimitsState(fallbackData);
        return fallbackData;
      }

      // Get subscription type
      const subscriptionType = await determineSubscriptionType(session.data.session.user.id);

      // Get message limit based on subscription type
      let messageLimit;

      // Get message limit based on subscription type
      messageLimit = subscriptionType ?
        MESSAGE_LIMITS[subscriptionType as keyof typeof MESSAGE_LIMITS] || MESSAGE_LIMITS[PLAN_TYPES.basic] :
        MESSAGE_LIMITS[PLAN_TYPES.basic];

      let isActualPlanChange = false;

      try {
        // Get the current subscription_type from profiles table
        const { data: profileData } = await supabase
          .from('profiles')
          .select('subscription_type')
          .eq('id', session.data.session.user.id)
          .single();

        // If profile data exists and subscription type is different, it's a real plan change
        if (profileData && profileData.subscription_type !== subscriptionType) {
          isActualPlanChange = true;

          // Update the profile with the new subscription type
          await supabase
            .from('profiles')
            .update({ subscription_type: subscriptionType })
            .eq('id', session.data.session.user.id);
        }
      } catch (error) {
        console.error('[useUserLimits] Error checking profile subscription type:', error);
      }

      // Get user tokens from database
      const { data: userTokens, error: tokensError } = await supabase
        .from('user_tokens')
        .select('*')
        .eq('user_id', session.data.session.user.id)
        .single();

      // Handle error fetching tokens
      if (tokensError && tokensError.code !== 'PGRST116') {
        console.error('[useUserLimits] Error fetching user tokens:', tokensError);

        const errorData = {
          isLoading: false,
          messagesLimit: messageLimit,
          messagesUsed: 0,
          messagesRemaining: messageLimit,
          lastReset: null,
          planType: subscriptionType
        };
        setLimitsState(errorData);
        return errorData;
      }

      // If no tokens record exists, create one
      if (!userTokens) {
        try {
          const { error: createError } = await supabase
            .from('user_tokens')
            .insert({
              user_id: session.data.session.user.id,
              tokens_remaining: messageLimit,
              last_reset: new Date().toISOString(),
              last_reset_date: new Date().toISOString(),
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });

          if (createError) {
            console.error('[useUserLimits] Error creating tokens record:', createError);
            throw createError;
          }

          const newData = {
            isLoading: false,
            messagesLimit: messageLimit,
            messagesUsed: 0,
            messagesRemaining: messageLimit,
            lastReset: new Date().toISOString(),
            planType: subscriptionType
          };
          setLimitsState(newData);
          return newData;
        } catch (error) {
          console.error('[useUserLimits] Error creating user tokens:', error);
          throw error;
        }
      }

      // Check if we need to reset tokens
      let shouldReset = false;

      if (userTokens.last_reset) {
        const lastReset = new Date(userTokens.last_reset);
        const now = new Date();
        const daysSinceReset = Math.floor((now.getTime() - lastReset.getTime()) / (1000 * 60 * 60 * 24));

        if (forceCheckReset && daysSinceReset >= 30) {
          // For paid users, reset monthly
          shouldReset = true;
        }
      }

      // Reset tokens if needed or if plan changed
      if (shouldReset || isActualPlanChange || forceCheckReset) {
        try {
          const { error: updateError } = await supabase
            .from('user_tokens')
            .update({
              tokens_remaining: messageLimit,
              last_reset: new Date().toISOString(),
              last_reset_date: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .eq('user_id', session.data.session.user.id);

          if (updateError) {
            console.error('[useUserLimits] Error updating user tokens:', updateError);
            throw updateError;
          }

          const resetData = {
            isLoading: false,
            messagesLimit: messageLimit,
            messagesUsed: 0,
            messagesRemaining: messageLimit,
            lastReset: new Date().toISOString(),
            planType: subscriptionType
          };
          setLimitsState(resetData);
          return resetData;
        } catch (error) {
          console.error('[useUserLimits] Error updating user tokens:', error);
        }
      }

      // Use existing tokens data
      const tokensRemaining = userTokens.tokens_remaining || 0;
      const tokensUsed = Math.max(0, messageLimit - tokensRemaining);

      const existingData = {
        isLoading: false,
        messagesLimit: messageLimit,
        messagesUsed: tokensUsed,
        messagesRemaining: tokensRemaining,
        lastReset: userTokens.last_reset,
        planType: subscriptionType
      };

      setLimitsState(existingData);
      return existingData;
    } catch (error) {
      console.error('[useUserLimits] Error in fetchUserLimits:', error);
      const fallbackData = {
        isLoading: false,
        messagesLimit: MESSAGE_LIMITS[PLAN_TYPES.basic],
        messagesUsed: 0,
        messagesRemaining: MESSAGE_LIMITS[PLAN_TYPES.basic],
        lastReset: null,
        planType: PLAN_TYPES.basic
      };
      setLimitsState(fallbackData);
      return fallbackData;
    }
  }, []);

  // Fetch user limits data on mount and when subscription changes
  useEffect(() => {
    // Skip when not authenticated or subscription is loading
    if (isLoadingSubscription) return;

    // On page load, NEVER force a reset check
    fetchUserLimits(false);
  }, [
    fetchUserLimits,
    isLoadingSubscription
  ]);

  // Function to manually refresh limits data
  const refreshLimits = async () => {
    return await fetchUserLimits(true);
  };

  // Function to check if user has reached their limit
  const hasReachedLimit = () => {
    return limitsState.messagesRemaining <= 0;
  };

  // Function to increment messages used
  const incrementMessagesUsed = async () => {
    const session = await supabase.auth.getSession();

    if (!session.data.session?.user?.id) {
      console.error('[useUserLimits] No authenticated user');
      return { success: false, message: 'No authenticated user' };
    }

    // Update local state immediately for better UX
    const newMessagesRemaining = Math.max(0, limitsState.messagesRemaining - 1);
    const newMessagesUsed = limitsState.messagesUsed + 1;

    setLimitsState(prev => ({
      ...prev,
      messagesUsed: newMessagesUsed,
      messagesRemaining: newMessagesRemaining
    }));

    const { error: updateError } = await supabase
      .from('user_tokens')
      .update({
        tokens_remaining: newMessagesRemaining,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', session.data.session.user.id);

    if (updateError) {
      // Revert local state if database update fails
      console.error('[useUserLimits] Error updating tokens:', updateError);
      setLimitsState(prev => ({
        ...prev,
        messagesUsed: prev.messagesUsed - 1,
        messagesRemaining: prev.messagesRemaining + 1
      }));
      return { success: false, message: 'Failed to update message count' };
    }

    // Fetch the latest limits from the database to ensure UI is up-to-date
    await fetchUserLimits();

    // Add warning if this was their last message
    const isWarning = newMessagesRemaining <= 1;
    return {
      success: true,
      isWarning,
      message: isWarning ? `This was your last message. Please upgrade your plan to continue chatting.` : undefined
    };
  };

  // Function to handle plan upgrade
  const handlePlanUpgrade = async () => {
    try {
      await refetchSubscription();
      const result = await fetchUserLimits(true);
      return result;
    } catch (error) {
      console.error('[useUserLimits] Error handling plan upgrade:', error);
      throw error;
    }
  };

  // Listen for changes in messagesUsed and update the component's state
  useEffect(() => {
    // Update the component's state here
  }, [limitsState.messagesUsed]);

  return {
    isLoading: limitsState.isLoading,
    messagesUsed: limitsState.messagesUsed,
    messagesLimit: limitsState.messagesLimit,
    messagesRemaining: limitsState.messagesRemaining,
    lastReset: limitsState.lastReset,
    planType: limitsState.planType,
    incrementMessagesUsed,
    refreshLimits,
    hasReachedLimit,
    subscription,
    handlePlanUpgrade
  };
};