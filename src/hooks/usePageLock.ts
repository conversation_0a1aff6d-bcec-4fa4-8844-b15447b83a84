import { useState, useEffect, useCallback, useMemo } from 'react';
import { supabase } from '@/integrations/supabase/client';

// No locked pages - all users have access to all features
const LOCKED_PAGES_FOR_BASIC: string[] = [];

// Cache for subscription data to prevent excessive API calls
let subscriptionCache: { type: string | null; timestamp: number } | null = null;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export const usePageLock = () => {
  const [subscriptionType, setSubscriptionType] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Optimized fetch function with caching
  const fetchSubscriptionType = useCallback(async () => {
    try {
      // Check cache first
      if (subscriptionCache && Date.now() - subscriptionCache.timestamp < CACHE_DURATION) {
        setSubscriptionType(subscriptionCache.type);
        setIsLoading(false);
        return;
      }

      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        const defaultType = 'premium';
        setSubscriptionType(defaultType);
        subscriptionCache = { type: defaultType, timestamp: Date.now() };
        setIsLoading(false);
        return;
      }

      const { data: profile, error } = await supabase
        .from('profiles')
        .select('subscription_type')
        .eq('id', user.id)
        .single();

      const type = error ? 'premium' : (profile?.subscription_type || 'premium');

      setSubscriptionType(type);
      subscriptionCache = { type, timestamp: Date.now() };

      if (error) {
        console.error('Error fetching subscription type:', error);
      }
    } catch (error) {
      console.error('Error in fetchSubscriptionType:', error);
      const defaultType = 'premium';
      setSubscriptionType(defaultType);
      subscriptionCache = { type: defaultType, timestamp: Date.now() };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fetch subscription type on mount and auth changes
  useEffect(() => {
    fetchSubscriptionType();

    // Listen for auth changes with debouncing
    let timeoutId: NodeJS.Timeout;
    const { data: { subscription } } = supabase.auth.onAuthStateChange(() => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        // Clear cache on auth change
        subscriptionCache = null;
        fetchSubscriptionType();
      }, 500); // Debounce auth changes
    });

    return () => {
      clearTimeout(timeoutId);
      subscription.unsubscribe();
    };
  }, [fetchSubscriptionType]);

  // No pages are locked - all users have full access
  const isPageLocked = useCallback((path: string): boolean => {
    return false; // Never lock any pages
  }, [isLoading, subscriptionType]);

  // Memoized helper functions
  const isBasicPlan = useMemo(() => false, [subscriptionType]); // No basic plan anymore
  const isPremiumPlan = useMemo(() => true, [subscriptionType]); // Everyone is premium

  const getLockedPages = useMemo(() => {
    return []; // No locked pages
  }, [subscriptionType]);

  // Optimized refresh function
  const refreshSubscriptionType = useCallback(async () => {
    // Clear cache to force fresh fetch
    subscriptionCache = null;
    setIsLoading(true);
    await fetchSubscriptionType();
  }, [fetchSubscriptionType]);

  return {
    isPageLocked,
    isBasicPlan,
    isPremiumPlan,
    getLockedPages,
    refreshSubscriptionType,
    planType: subscriptionType,
    isLoading
  };
};
