import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';

export type Trade = {
  id: string;
  user_id: string;
  ticker: string;
  created_at: string;
  entry_price: number;
  take_profit: number;
  stop_loss: number;
  direction: 'LONG' | 'SHORT';
  confidence: 'LOW' | 'MEDIUM' | 'HIGH';
  timeframe: string;
  status: 'suggested' | 'executed' | 'closed' | 'canceled';
  reasoning?: string;
  outcome?: 'take_profit' | 'stop_loss' | null;
  close_price?: number;
  close_date?: string;
  profit_amount?: number;
};

export type TimeFilter = 'all' | '1d' | '5d' | 'ytd' | '1y' | '5y';

export const useTrades = (timeFilter: TimeFilter = 'all') => {
  const { user } = useAuth();
  const [trades, setTrades] = useState<Trade[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTrades = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get the current date
        const now = new Date();

        // Calculate the start date based on the time filter
        let startDate: Date | null = null;

        switch (timeFilter) {
          case '1d':
            startDate = new Date(now);
            startDate.setDate(now.getDate() - 1);
            break;
          case '5d':
            startDate = new Date(now);
            startDate.setDate(now.getDate() - 5);
            break;
          case 'ytd':
            startDate = new Date(now.getFullYear(), 0, 1); // January 1st of current year
            break;
          case '1y':
            startDate = new Date(now);
            startDate.setFullYear(now.getFullYear() - 1);
            break;
          case '5y':
            startDate = new Date(now);
            startDate.setFullYear(now.getFullYear() - 5);
            break;
          case 'all':
          default:
            // No start date filter for 'all'
            break;
        }

        // Build the query
        let query = supabase
          .from('trades')
          .select('*')
          .order('created_at', { ascending: false });

        // Add date filter if applicable
        if (startDate) {
          query = query.gte('created_at', startDate.toISOString());
        }

        // Always show all trades, regardless of filterByUser parameter
        // No user filtering applied

        // Execute the query
        const { data, error } = await query;

        if (error) {
          throw error;
        }

        setTrades(data || []);
      } catch (err) {
        console.error('Error fetching trades:', err);
        setError(err.message || 'Failed to fetch trades');
      } finally {
        setLoading(false);
      }
    };

    fetchTrades();
  }, [timeFilter]);

  // Calculate performance metrics
  const calculatePerformance = () => {
    if (!trades.length) return {
      totalTrades: 0,
      longTrades: 0,
      shortTrades: 0,
      inProgressTrades: 0,
      takeProfitTrades: 0,
      stopLossTrades: 0,
      currentBalance: 10000, // Initial investment
      totalProfit: 0
    };

    const longTrades = trades.filter(trade => trade.direction === 'LONG').length;
    const shortTrades = trades.filter(trade => trade.direction === 'SHORT').length;

    // Count trades in progress (status is 'suggested' or 'executed')
    const inProgressTrades = trades.filter(trade =>
      trade.status === 'suggested' || trade.status === 'executed'
    ).length;

    // For TP and SL trades, use the outcome field if available
    // Otherwise fall back to the deterministic approach
    const closedTrades = trades.filter(trade => trade.status === 'closed');

    let takeProfitTrades = 0;
    let stopLossTrades = 0;
    let totalProfit = 0;

    closedTrades.forEach(trade => {
      // If we have the outcome field, use it
      if (trade.outcome) {
        if (trade.outcome === 'take_profit') {
          takeProfitTrades++;
        } else if (trade.outcome === 'stop_loss') {
          stopLossTrades++;
        }

        // Add profit amount if available
        if (trade.profit_amount !== undefined && trade.profit_amount !== null) {
          totalProfit += Number(trade.profit_amount);
        }
      } else {
        // Fall back to deterministic approach if outcome is not available
        let takeProfitProbability = 0.5; // Default medium
        if (trade.confidence === 'HIGH') takeProfitProbability = 0.65;
        if (trade.confidence === 'LOW') takeProfitProbability = 0.35;

        const tradeIdSum = trade.id.split('').reduce((sum, char) => sum + char.charCodeAt(0), 0);
        const normalizedValue = (tradeIdSum % 100) / 100; // Between 0 and 1

        if (normalizedValue < takeProfitProbability) {
          // Hit take profit
          takeProfitTrades++;
        } else {
          // Hit stop loss
          stopLossTrades++;
        }
      }
    });

    // Calculate current balance (initial investment + total profit)
    const initialInvestment = 10000;
    const currentBalance = initialInvestment + totalProfit;

    // Calculate win rate
    const closedTradesCount = takeProfitTrades + stopLossTrades;
    const winRate = closedTradesCount > 0 ? (takeProfitTrades / closedTradesCount) * 100 : 0;

    return {
      totalTrades: trades.length,
      longTrades,
      shortTrades,
      inProgressTrades,
      takeProfitTrades,
      stopLossTrades,
      currentBalance,
      totalProfit,
      winRate
    };
  };

  const performance = calculatePerformance();

  return { trades, loading, error, performance };
};
