import { useState, useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { Agent, AgentBlock } from '@/services/agentService';
import { getBlockLayer } from '@/utils/canvasLayoutEngine';

// Block type enum
export enum BlockType {
  WHEN_RUN = 'WHEN_RUN', // Changed to uppercase for edge function compatibility
  INDICATOR = 'INDICATOR',
  PRICE = 'PRICE',
  FUNDAMENTAL = 'FUNDAMENTAL',
  CONDITION = 'CONDITION',
  COMPARISON = 'COMPARISON',
  TRIGGER = 'TRIGGER',
  OPERATOR = 'OPERATOR',
  BULLISH_CONFIDENCE_BOOST = 'BULLISH_CONFIDENCE_BOOST',
  BEARISH_CONFIDENCE_BOOST = 'BEARISH_CONFIDENCE_BOOST',
  CONFIDENCE_BOOST = 'CONFIDENCE_BOOST', // New unified confidence boost block
  CANDLE_PATTERN = 'CANDLE_PATTERN',
  STOCK_SENTIMENT = 'STOCK_SENTIMENT',
  // Additional block types
  CHART_PATTERN = 'CHART_PATTERN',
  BREAKOUT_DETECTION = 'BREAKOUT_DETECTION',
  GAP_ANALYSIS = 'GAP_ANALYSIS',
  CONSOLE_LOG = 'CONSOLE_LOG',
  MOMENTUM_INDICATOR = 'MOMENTUM_INDICATOR',
  MOVING_AVERAGE = 'MOVING_AVERAGE',
  POSITION_SIZE = 'POSITION_SIZE',
  SIGNAL = 'SIGNAL',

  // Technical Indicators - Comprehensive Set
  TREND_INDICATOR = 'TREND_INDICATOR',
  VOLUME_INDICATOR = 'VOLUME_INDICATOR',
  VOLATILITY_INDICATOR = 'VOLATILITY_INDICATOR',

  // Market Analysis Blocks
  SUPPORT_RESISTANCE = 'SUPPORT_RESISTANCE',
  TREND_LINE_ANALYSIS = 'TREND_LINE_ANALYSIS',
  MARKET_STRUCTURE = 'MARKET_STRUCTURE',

  // Signal Generation Blocks
  PRICE_ACTION_SIGNAL = 'PRICE_ACTION_SIGNAL',
  MULTI_TIMEFRAME_ANALYSIS = 'MULTI_TIMEFRAME_ANALYSIS',
  DIVERGENCE_DETECTION = 'DIVERGENCE_DETECTION',
  VOLUME_CONFIRMATION = 'VOLUME_CONFIRMATION',
  MARKET_REGIME = 'MARKET_REGIME',

  // Logic & Flow Control
  NOT_OPERATOR = 'NOT_OPERATOR',
  TIME_FILTER = 'TIME_FILTER',
  MARKET_CONDITION_FILTER = 'MARKET_CONDITION_FILTER',

  // Legacy blocks for compatibility
  POSITION_SIZING = 'POSITION_SIZING',
  STOP_LOSS = 'STOP_LOSS',
  TAKE_PROFIT = 'TAKE_PROFIT',
  PORTFOLIO_RISK = 'PORTFOLIO_RISK',
  ENTRY_TIMING = 'ENTRY_TIMING',
  EXIT_CONDITIONS = 'EXIT_CONDITIONS',
  SESSION_FILTER = 'SESSION_FILTER',
  ECONOMIC_CALENDAR = 'ECONOMIC_CALENDAR',
  RISK_LIMIT = 'RISK_LIMIT',
  MARKET_BREADTH = 'MARKET_BREADTH',
  MULTI_TIMEFRAME = 'MULTI_TIMEFRAME',
  CONFLUENCE = 'CONFLUENCE',
  SCALE_STRATEGY = 'SCALE_STRATEGY',
  PARTIAL_PROFIT = 'PARTIAL_PROFIT',
  AND = 'AND',
  OR = 'OR'
}

// Connection type enum
export enum ConnectionType {
  INPUT = 'input',
  OUTPUT = 'output',
  TRUE = 'true',
  FALSE = 'false'
}

// Connection interface
export interface Connection {
  sourceId: string;
  targetId: string;
  sourceHandle: string;
  targetHandle: string;
}

// Hook for managing agent builder state

// ALLOW EVERYTHING - No restrictions needed

// Intelligent block parameter defaults
const getIntelligentBlockDefaults = (blockType: BlockType, existingBlocks: AgentBlock[]): Record<string, any> => {
  const defaults: Record<string, any> = {};

  switch (blockType) {
    case BlockType.INDICATOR:
      // Choose RSI if no other RSI exists, otherwise diversify
      const hasRSI = existingBlocks.some(b => b.type === BlockType.INDICATOR && b.indicator === 'rsi');
      const hasMACD = existingBlocks.some(b => b.type === BlockType.INDICATOR && b.indicator === 'macd');

      if (!hasRSI) {
        defaults.indicator = 'rsi';
        defaults.parameters = { period: 14 };
      } else if (!hasMACD) {
        defaults.indicator = 'macd';
        defaults.parameters = { fastPeriod: 12, slowPeriod: 26, signalPeriod: 9 };
      } else {
        defaults.indicator = 'bollinger';
        defaults.parameters = { period: 20, stdDev: 2 };
      }
      break;

    case BlockType.CONDITION:
      // Intelligent operator selection based on connected indicators
      defaults.operator = 'less_than';
      defaults.value = 30; // Default for RSI oversold
      break;

    case BlockType.CANDLE_PATTERN:
      defaults.pattern = 'any';
      defaults.timeframe = 'day';
      break;

    case BlockType.CHART_PATTERN:
      defaults.pattern = 'any';
      defaults.timeframe = 'day';
      defaults.lookbackPeriod = 50;
      defaults.minPatternSize = 10;
      break;

    case BlockType.CONFIDENCE_BOOST:
      defaults.boostType = 'bullish';
      defaults.percentage = 25;
      break;

    case BlockType.FUNDAMENTAL:
      // Choose diverse fundamental metrics
      const fundamentalMetrics = ['return_on_equity', 'price_to_earnings', 'debt_to_equity', 'current_ratio'];
      const usedMetrics = existingBlocks
        .filter(b => b.type === BlockType.FUNDAMENTAL)
        .map(b => b.metric);
      const availableMetrics = fundamentalMetrics.filter(m => !usedMetrics.includes(m));

      defaults.metric = availableMetrics[0] || 'return_on_equity';
      defaults.statement = 'calculated';
      defaults.period = 'quarterly';
      defaults.parameters = {};
      break;

    // Signal Generation Blocks
    case BlockType.PRICE_ACTION_SIGNAL:
      defaults.parameters = {
        signalType: 'breakout',
        lookbackPeriod: 20,
        volumeConfirmation: true,
        minBreakoutSize: 1
      };
      break;

    case BlockType.MULTI_TIMEFRAME_ANALYSIS:
      defaults.parameters = {
        signalType: 'trend_alignment',
        primaryTimeframe: 'day',
        secondaryTimeframe: 'hour',
        confirmationRequired: true
      };
      break;

    case BlockType.DIVERGENCE_DETECTION:
      defaults.parameters = {
        signalType: 'rsi_divergence',
        rsiPeriod: 14,
        lookbackPeriod: 20,
        minDivergenceStrength: 0.7
      };
      break;

    case BlockType.VOLUME_CONFIRMATION:
      defaults.parameters = {
        signalType: 'volume_spike',
        volumeThreshold: 1.5,
        lookbackPeriod: 10,
        confirmationPeriod: 3
      };
      break;

    case BlockType.MARKET_REGIME:
      defaults.parameters = {
        signalType: 'trend_regime',
        trendPeriod: 50,
        volatilityPeriod: 20,
        regimeThreshold: 0.6
      };
      break;

    case BlockType.SUPPORT_RESISTANCE:
      defaults.parameters = {
        method: 'pivot_points',
        timeframe: 'day',
        lookbackPeriod: 20,
        minTouchCount: 2
      };
      break;

    // Logic Flow Blocks
    case BlockType.NOT_OPERATOR:
      defaults.parameters = {
        invert: true
      };
      break;

    case BlockType.TIME_FILTER:
      defaults.parameters = {
        method: 'trading_hours',
        startTime: '09:30',
        endTime: '16:00'
      };
      break;

    case BlockType.MARKET_CONDITION_FILTER:
      defaults.parameters = {
        method: 'volatility',
        threshold: 20
      };
      break;

    // Execution Timing Blocks
    case BlockType.ENTRY_TIMING:
      defaults.parameters = {
        timingType: 'market_open',
        offsetMinutes: 0,
        timezone: 'US/Eastern'
      };
      break;

    case BlockType.EXIT_CONDITIONS:
      defaults.parameters = {
        timingType: 'time_based',
        maxHoldingPeriod: 24,
        timeUnit: 'hours'
      };
      break;

    case BlockType.SESSION_FILTER:
      defaults.parameters = {
        timingType: 'regular_hours',
        startTime: '09:30',
        endTime: '16:00',
        timezone: 'US/Eastern'
      };
      break;

    case BlockType.ECONOMIC_CALENDAR:
      defaults.parameters = {
        timingType: 'earnings_filter',
        beforeAfter: 'exclude_both',
        offsetHours: 2
      };
      break;

    case BlockType.POSITION_SIZE:
      defaults.parameters = {
        method: 'fixed_amount',
        fixedAmount: 1000,
        accountPercentage: 2,
        riskPercentage: 1,
        atrMultiplier: 2
      };
      break;

  }

  return defaults;
};

// Ensure position is valid with intelligent positioning
const ensureValidPosition = (position: { x: number; y: number } | undefined, index = 0, blockType?: BlockType, existingBlocks: AgentBlock[] = []): { x: number; y: number } => {
  if (!position) {
    // Intelligent positioning based on block type and flow
    return getIntelligentPosition(blockType, existingBlocks, index);
  }

  return {
    x: typeof position.x === 'number' && !isNaN(position.x) ? position.x : 400 + (index * 1200), // MASSIVE horizontal fallback spacing
    y: typeof position.y === 'number' && !isNaN(position.y) ? position.y : 200 + (index * 400)   // Tall vertical fallback spacing
  };
};

// Much better positioning system that spreads blocks out properly
const getIntelligentPosition = (blockType?: BlockType, existingBlocks: AgentBlock[] = [], index = 0): { x: number; y: number } => {
  if (!blockType) {
    return { x: 200 + (index * 300), y: 200 + (index * 200) };
  }

  // Define logical flow layers with much better spacing
  const layers = {
    entry: 0,      // WHEN_RUN
    data: 1,       // PRICE, INDICATOR, FUNDAMENTAL
    analysis: 2,   // CANDLE_PATTERN, CHART_PATTERN, STOCK_SENTIMENT
    logic: 3,      // CONDITION, AND, OR
    boost: 4,      // CONFIDENCE_BOOST blocks
    output: 5      // TRIGGER, SIGNAL
  };

  let layer = 0;
  let column = 0;

  // Determine layer based on block type
  switch (blockType) {
    case BlockType.WHEN_RUN:
      layer = layers.entry;
      // Center the WHEN_RUN block with extra horizontal space
      return { x: 400, y: 400 };

    case BlockType.PRICE:
    case BlockType.INDICATOR:
    case BlockType.FUNDAMENTAL:
      layer = layers.data;
      column = existingBlocks.filter(b =>
        b.type === BlockType.PRICE ||
        b.type === BlockType.INDICATOR ||
        b.type === BlockType.FUNDAMENTAL
      ).length;
      break;

    case BlockType.CANDLE_PATTERN:
    case BlockType.CHART_PATTERN:
    case BlockType.STOCK_SENTIMENT:
    case BlockType.SUPPORT_RESISTANCE:
    case BlockType.TREND_LINE_ANALYSIS:
    case BlockType.MARKET_STRUCTURE:
    case BlockType.PRICE_ACTION_SIGNAL:
    case BlockType.MULTI_TIMEFRAME_ANALYSIS:
    case BlockType.DIVERGENCE_DETECTION:
    case BlockType.VOLUME_CONFIRMATION:
    case BlockType.MARKET_REGIME:
      layer = layers.analysis;
      column = existingBlocks.filter(b =>
        b.type === BlockType.CANDLE_PATTERN ||
        b.type === BlockType.CHART_PATTERN ||
        b.type === BlockType.STOCK_SENTIMENT ||
        b.type === BlockType.SUPPORT_RESISTANCE ||
        b.type === BlockType.TREND_LINE_ANALYSIS ||
        b.type === BlockType.MARKET_STRUCTURE ||
        b.type === BlockType.PRICE_ACTION_SIGNAL ||
        b.type === BlockType.MULTI_TIMEFRAME_ANALYSIS ||
        b.type === BlockType.DIVERGENCE_DETECTION ||
        b.type === BlockType.VOLUME_CONFIRMATION ||
        b.type === BlockType.MARKET_REGIME
      ).length;
      break;

    case BlockType.CONDITION:
    case BlockType.AND:
    case BlockType.OR:
      layer = layers.logic;
      column = existingBlocks.filter(b =>
        b.type === BlockType.CONDITION ||
        b.type === BlockType.AND ||
        b.type === BlockType.OR
      ).length;
      break;

    case BlockType.BULLISH_CONFIDENCE_BOOST:
    case BlockType.BEARISH_CONFIDENCE_BOOST:
    case BlockType.CONFIDENCE_BOOST:
      layer = layers.boost;
      column = existingBlocks.filter(b =>
        b.type === BlockType.BULLISH_CONFIDENCE_BOOST ||
        b.type === BlockType.BEARISH_CONFIDENCE_BOOST ||
        b.type === BlockType.CONFIDENCE_BOOST
      ).length;
      break;

    case BlockType.TRIGGER:
    case BlockType.SIGNAL:
      layer = layers.output;
      column = existingBlocks.filter(b =>
        b.type === BlockType.TRIGGER ||
        b.type === BlockType.SIGNAL
      ).length;
      break;

    default:
      // For unknown types, place them in a separate area with massive horizontal space
      return { x: 400 + (index * 1200), y: 1200 + (index * 400) };
  }

  // MASSIVE horizontal spacing to prevent connection line overlap
  const baseX = 400 + (layer * 1200); // 1200px between layers for connection line separation!
  const baseY = 200 + (column * 400); // 400px between blocks in same layer

  // Reduce randomization to keep things more organized
  const randomOffsetX = (Math.random() - 0.5) * 30; // ±15px random offset (reduced)
  const randomOffsetY = (Math.random() - 0.5) * 30; // ±15px random offset (reduced)

  return {
    x: Math.max(150, baseX + randomOffsetX), // Ensure minimum distance from edge
    y: Math.max(150, baseY + randomOffsetY)  // Ensure minimum distance from edge
  };
};

// Expert-level validation helper functions
const calculateAgentComplexity = (blocks: AgentBlock[]) => {
  const metrics = {
    totalBlocks: blocks.length,
    dataBlocks: blocks.filter(b => [BlockType.PRICE, BlockType.INDICATOR, BlockType.FUNDAMENTAL].includes(b.type as BlockType)).length,
    logicBlocks: blocks.filter(b => [BlockType.CONDITION, BlockType.AND, BlockType.OR].includes(b.type as BlockType)).length,
    patternBlocks: blocks.filter(b => [BlockType.CANDLE_PATTERN, BlockType.CHART_PATTERN].includes(b.type as BlockType)).length,
    outputBlocks: blocks.filter(b => [BlockType.TRIGGER, BlockType.SIGNAL, BlockType.CONFIDENCE_BOOST].includes(b.type as BlockType)).length,
    maxDepth: calculateMaxDepth(blocks),
    branchingFactor: calculateBranchingFactor(blocks)
  };

  // Calculate complexity score (0-100)
  let score = 0;
  score += Math.min(metrics.totalBlocks * 2, 40); // Up to 40 points for block count
  score += Math.min(metrics.logicBlocks * 5, 25); // Up to 25 points for logic complexity
  score += Math.min(metrics.maxDepth * 3, 20); // Up to 20 points for depth
  score += Math.min(metrics.branchingFactor * 2, 15); // Up to 15 points for branching

  let level: 'Simple' | 'Moderate' | 'Complex' | 'Expert';
  if (score < 25) level = 'Simple';
  else if (score < 50) level = 'Moderate';
  else if (score < 75) level = 'Complex';
  else level = 'Expert';

  return { score, level, metrics };
};

const calculateMaxDepth = (blocks: AgentBlock[]): number => {
  const visited = new Set<string>();
  let maxDepth = 0;

  const dfs = (blockId: string, depth: number) => {
    if (visited.has(blockId)) return;
    visited.add(blockId);
    maxDepth = Math.max(maxDepth, depth);

    const block = blocks.find(b => b.id === blockId);
    if (!block) return;

    const outputs = [
      ...(block.outputConnections || []),
      block.trueConnection,
      block.falseConnection,
      block.bullishConnection,
      block.bearishConnection,
      block.neutralConnection
    ].filter(Boolean);

    outputs.forEach(outputId => dfs(outputId!, depth + 1));
  };

  const entryBlocks = blocks.filter(b => b.type === BlockType.WHEN_RUN);
  entryBlocks.forEach(block => dfs(block.id, 1));

  return maxDepth;
};

const calculateBranchingFactor = (blocks: AgentBlock[]): number => {
  let totalBranches = 0;
  let branchingBlocks = 0;

  blocks.forEach(block => {
    const outputs = [
      ...(block.outputConnections || []),
      block.trueConnection,
      block.falseConnection,
      block.bullishConnection,
      block.bearishConnection,
      block.neutralConnection
    ].filter(Boolean);

    if (outputs.length > 1) {
      branchingBlocks++;
      totalBranches += outputs.length;
    }
  });

  return branchingBlocks > 0 ? totalBranches / branchingBlocks : 1;
};

// ALLOW EVERYTHING - Minimal validation
const validateBlockConfiguration = (
  blocks: AgentBlock[],
  errors: string[],
  errorDetails: Record<string, string[]>,
  suggestions: string[]
) => {
  // No validation - everything is allowed
};

const validateDataFlow = (
  blocks: AgentBlock[],
  errors: string[],
  errorDetails: Record<string, string[]>,
  suggestions: string[]
) => {
  // Simplified validation for user-friendly approach
  // Only check for critical data flow issues, not complexity

  const dataSourceBlocks = blocks.filter(b =>
    [BlockType.PRICE, BlockType.INDICATOR, BlockType.FUNDAMENTAL].includes(b.type as BlockType)
  );

  // Only flag completely disconnected data blocks as errors
  dataSourceBlocks.forEach(block => {
    if (!block.outputConnections || block.outputConnections.length === 0) {
      const hasOtherConnections = block.trueConnection || block.falseConnection ||
                                 block.bullishConnection || block.bearishConnection;
      if (!hasOtherConnections) {
        // Only add as suggestion, not error for simple agents
        suggestions.push(`Consider connecting your ${block.type} block to make use of its data`);
      }
    }
  });

  // Remove complexity suggestions - keep it simple
  // No suggestions about diversity or consolidation for simple agents
};

const validateLogicalStructure = (
  blocks: AgentBlock[],
  errors: string[],
  errorDetails: Record<string, string[]>,
  suggestions: string[]
) => {
  // Simplified validation for user-friendly approach
  const conditionBlocks = blocks.filter(b => b.type === BlockType.CONDITION);
  const logicBlocks = blocks.filter(b => [BlockType.AND, BlockType.OR].includes(b.type as BlockType));
  const outputBlocks = blocks.filter(b =>
    [BlockType.TRIGGER, BlockType.SIGNAL, BlockType.CONFIDENCE_BOOST].includes(b.type as BlockType)
  );

  // Simplified validation - only check critical issues
  // Don't require condition blocks to have both true and false connections for simple agents
  conditionBlocks.forEach(block => {
    if (!block.trueConnection && !block.falseConnection) {
      // Only suggest, don't error for simple agents
      suggestions.push('Consider connecting your condition block to define what happens when the condition is met');
    }
  });

  // For logic blocks, only suggest improvements, don't error
  logicBlocks.forEach(block => {
    if (!block.inputConnections || block.inputConnections.length < 2) {
      suggestions.push(`Your ${block.type} block works best with multiple inputs to combine conditions`);
    }
  });

  // Only require output blocks as a critical error
  if (outputBlocks.length === 0) {
    errors.push('Your agent needs at least one trigger block to generate trading signals');
    suggestions.push('Add a Trigger block to define when your agent should signal buy/sell');
  }

  // Remove complexity suggestions for simple agents
  // Keep it simple and user-friendly
};

const validatePerformanceOptimization = (blocks: AgentBlock[], suggestions: string[]) => {
  // Check for performance optimization opportunities
  const totalBlocks = blocks.length;

  if (totalBlocks > 50) {
    suggestions.push('Large agent detected. Consider breaking into smaller, focused sub-agents');
  }

  if (totalBlocks > 100) {
    suggestions.push('Very large agent. Performance may be impacted. Consider optimization');
  }

  // Check for redundant calculations
  const indicatorBlocks = blocks.filter(b => b.type === BlockType.INDICATOR);
  const indicatorTypes = indicatorBlocks.map(b => b.indicator);
  const duplicateIndicators = indicatorTypes.filter((item, index) => indicatorTypes.indexOf(item) !== index);

  if (duplicateIndicators.length > 0) {
    suggestions.push(`Duplicate indicators detected: ${[...new Set(duplicateIndicators)].join(', ')}. Consider consolidating.`);
  }

  // Check for timeframe consistency
  const timeframes = blocks
    .filter(b => b.timeframe)
    .map(b => b.timeframe)
    .filter(Boolean);
  const uniqueTimeframes = [...new Set(timeframes)];

  if (uniqueTimeframes.length > 3) {
    suggestions.push('Multiple timeframes detected. Ensure this is intentional for multi-timeframe analysis');
  }
};

const calculateDecisionDepth = (blocks: AgentBlock[]): number => {
  let maxDepth = 0;
  const visited = new Set<string>();

  const dfs = (blockId: string, depth: number) => {
    if (visited.has(blockId)) return;
    visited.add(blockId);

    const block = blocks.find(b => b.id === blockId);
    if (!block) return;

    if ([BlockType.CONDITION, BlockType.AND, BlockType.OR].includes(block.type as BlockType)) {
      maxDepth = Math.max(maxDepth, depth);
    }

    const outputs = [
      block.trueConnection,
      block.falseConnection,
      block.bullishConnection,
      block.bearishConnection,
      block.neutralConnection,
      ...(block.outputConnections || [])
    ].filter(Boolean);

    const nextDepth = [BlockType.CONDITION, BlockType.AND, BlockType.OR].includes(block.type as BlockType) ? depth + 1 : depth;
    outputs.forEach(outputId => dfs(outputId!, nextDepth));
  };

  const entryBlocks = blocks.filter(b => b.type === BlockType.WHEN_RUN);
  entryBlocks.forEach(block => dfs(block.id, 0));

  return maxDepth;
};

// Create a default "When Run" block
const createDefaultWhenRunBlock = (): AgentBlock => {
  const id = uuidv4();
  return {
    id,
    type: BlockType.WHEN_RUN,
    position: { x: 100, y: 100 },
    outputConnections: []
  };
};

export function useAgentBuilder(initialAgent?: Agent) {
  // Create a default "When Run" block if no initial agent is provided
  const defaultBlocks = initialAgent?.configuration?.blocks || [createDefaultWhenRunBlock()];
  const defaultEntryBlockId = initialAgent?.configuration?.entryBlockId ||
    (defaultBlocks.length > 0 ? defaultBlocks[0].id : '');

  // State for blocks and connections
  const [blocks, setBlocks] = useState<AgentBlock[]>(defaultBlocks);
  const [connections, setConnections] = useState<Connection[]>([]);
  const [entryBlockId, setEntryBlockId] = useState<string>(defaultEntryBlockId);
  const [agentName, setAgentName] = useState<string>(
    initialAgent?.name || 'New Agent'
  );
  const [agentDescription, setAgentDescription] = useState<string>(
    initialAgent?.description || ''
  );
  const [isPublic, setIsPublic] = useState<boolean>(
    initialAgent?.is_public || false
  );
  const [publicDescription, setPublicDescription] = useState<string>(
    initialAgent?.public_description || ''
  );
  const [tags, setTags] = useState<string[]>(
    initialAgent?.tags || []
  );
  const [isInMarketplace, setIsInMarketplace] = useState<boolean>(false);

  // Track changes for autosave
  const [hasChanges, setHasChanges] = useState<boolean>(false);
  const resetChanges = useCallback(() => {
    setHasChanges(false);
  }, []);

  // Undo/Redo state management
  interface AgentState {
    blocks: AgentBlock[];
    connections: Connection[];
    entryBlockId: string;
  }

  const [history, setHistory] = useState<AgentState[]>([]);
  const [historyIndex, setHistoryIndex] = useState<number>(-1);
  const [isUndoRedoOperation, setIsUndoRedoOperation] = useState<boolean>(false);

  // Save current state to history
  const saveToHistory = useCallback(() => {
    if (isUndoRedoOperation) return; // Don't save during undo/redo operations

    const currentState: AgentState = {
      blocks: [...blocks],
      connections: [...connections],
      entryBlockId
    };

    setHistory(prevHistory => {
      // Remove any future history if we're not at the end
      const newHistory = prevHistory.slice(0, historyIndex + 1);
      // Add current state
      newHistory.push(currentState);
      // Limit history to 50 states
      if (newHistory.length > 50) {
        newHistory.shift();
        return newHistory;
      }
      return newHistory;
    });

    setHistoryIndex(prevIndex => {
      const newIndex = Math.min(prevIndex + 1, 49);
      return newIndex;
    });
  }, [blocks, connections, entryBlockId, historyIndex, isUndoRedoOperation]);

  // Undo function
  const undo = useCallback(() => {
    if (historyIndex > 0) {
      setIsUndoRedoOperation(true);
      const previousState = history[historyIndex - 1];
      setBlocks(previousState.blocks);
      setConnections(previousState.connections);
      setEntryBlockId(previousState.entryBlockId);
      setHistoryIndex(historyIndex - 1);
      setHasChanges(true);

      // Reset the flag after state updates
      setTimeout(() => setIsUndoRedoOperation(false), 0);
    }
  }, [history, historyIndex]);

  // Redo function
  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      setIsUndoRedoOperation(true);
      const nextState = history[historyIndex + 1];
      setBlocks(nextState.blocks);
      setConnections(nextState.connections);
      setEntryBlockId(nextState.entryBlockId);
      setHistoryIndex(historyIndex + 1);
      setHasChanges(true);

      // Reset the flag after state updates
      setTimeout(() => setIsUndoRedoOperation(false), 0);
    }
  }, [history, historyIndex]);

  // Check if undo/redo is available
  const canUndo = historyIndex > 0;
  const canRedo = historyIndex < history.length - 1;

  // Add a new block with intelligent auto-completion
  const addBlock = useCallback((type: BlockType | string, position: { x: number; y: number }, properties: Record<string, any> = {}) => {
    // Save current state to history before making changes
    saveToHistory();

    const blockType = typeof type === 'string' ? type as BlockType : type;

    // Get intelligent defaults for this block type
    const intelligentDefaults = getIntelligentBlockDefaults(blockType, blocks);

    // Ensure position is valid with intelligent positioning
    const validPosition = ensureValidPosition(position, blocks.length, blockType, blocks);

    const newBlock: AgentBlock = {
      id: uuidv4(),
      type: blockType,
      position: validPosition,
      ...intelligentDefaults,
      ...properties // User properties override intelligent defaults
    };

    // Set comprehensive default properties based on block type
    switch (blockType) {
      case BlockType.INDICATOR:
        newBlock.indicator = newBlock.indicator || intelligentDefaults.indicator || 'rsi';
        newBlock.parameters = { ...intelligentDefaults.parameters, ...newBlock.parameters };
        newBlock.timeframe = newBlock.timeframe || 'day';
        newBlock.outputConnections = newBlock.outputConnections || [];
        break;
      case BlockType.PRICE:
        newBlock.dataPoint = newBlock.dataPoint || 'close';
        newBlock.timeframe = newBlock.timeframe || 'day';
        newBlock.lookback = newBlock.lookback || 0;
        newBlock.outputConnections = newBlock.outputConnections || [];
        break;
      case BlockType.FUNDAMENTAL:
        newBlock.metric = newBlock.metric || intelligentDefaults.metric || 'return_on_equity';
        newBlock.statement = newBlock.statement || intelligentDefaults.statement || 'calculated';
        newBlock.period = newBlock.period || intelligentDefaults.period || 'quarterly';
        newBlock.parameters = { ...intelligentDefaults.parameters, ...newBlock.parameters };
        newBlock.outputConnections = newBlock.outputConnections || [];
        break;
      case BlockType.CONDITION:
        newBlock.operator = properties.operator || '>';
        newBlock.compareValue = properties.compareValue !== undefined ? properties.compareValue : 0;
        newBlock.compareValue2 = properties.compareValue2 !== undefined ? properties.compareValue2 : 0;
        newBlock.inputConnections = properties.inputConnections || [];
        newBlock.trueConnection = properties.trueConnection || undefined;
        newBlock.falseConnection = properties.falseConnection || undefined;
        break;
      case BlockType.TRIGGER:
        newBlock.signal = properties.signal || 'bullish';
        newBlock.confidence = properties.confidence || 75;
        newBlock.inputConnections = properties.inputConnections || [];
        break;
      case BlockType.OPERATOR:
        newBlock.operation = properties.operation || 'add';
        newBlock.inputConnections = properties.inputConnections || [];
        newBlock.outputConnections = properties.outputConnections || [];
        break;
      case BlockType.BULLISH_CONFIDENCE_BOOST:
        newBlock.percentage = properties.percentage || 10;
        newBlock.inputConnections = properties.inputConnections || [];
        newBlock.outputConnections = properties.outputConnections || [];
        break;
      case BlockType.BEARISH_CONFIDENCE_BOOST:
        newBlock.percentage = properties.percentage || 10;
        newBlock.inputConnections = properties.inputConnections || [];
        newBlock.outputConnections = properties.outputConnections || [];
        break;
      case BlockType.CONFIDENCE_BOOST:
        newBlock.boostType = properties.boostType || 'bullish';
        newBlock.percentage = properties.percentage || 10;
        newBlock.inputConnections = properties.inputConnections || [];
        newBlock.outputConnections = properties.outputConnections || [];
        break;
      case BlockType.CANDLE_PATTERN:
        newBlock.pattern = properties.pattern || 'doji';
        newBlock.timeframe = properties.timeframe || 'day';
        newBlock.inputConnections = properties.inputConnections || [];
        newBlock.trueConnection = properties.trueConnection || undefined;
        newBlock.falseConnection = properties.falseConnection || undefined;
        newBlock.bullishConnection = properties.bullishConnection || undefined;
        newBlock.bearishConnection = properties.bearishConnection || undefined;
        newBlock.neutralConnection = properties.neutralConnection || undefined;
        break;
      case BlockType.CHART_PATTERN:
        newBlock.pattern = properties.pattern || 'any';
        newBlock.timeframe = properties.timeframe || 'day';
        newBlock.inputConnections = properties.inputConnections || [];
        newBlock.trueConnection = properties.trueConnection || undefined;
        newBlock.falseConnection = properties.falseConnection || undefined;
        newBlock.bullishConnection = properties.bullishConnection || undefined;
        newBlock.bearishConnection = properties.bearishConnection || undefined;
        newBlock.neutralConnection = properties.neutralConnection || undefined;
        break;
      case BlockType.BREAKOUT_DETECTION:
        newBlock.breakoutType = properties.breakoutType || 'support_resistance';
        newBlock.breakoutDirection = properties.breakoutDirection || 'any';
        newBlock.timeframe = properties.timeframe || 'day';
        newBlock.volumeConfirmation = properties.volumeConfirmation || false;
        newBlock.inputConnections = properties.inputConnections || [];
        newBlock.trueConnection = properties.trueConnection || undefined;
        newBlock.falseConnection = properties.falseConnection || undefined;
        break;
      case BlockType.GAP_ANALYSIS:
        newBlock.gapType = properties.gapType || 'any';
        newBlock.minGapSize = properties.minGapSize || 1;
        newBlock.timeframe = properties.timeframe || 'day';
        newBlock.inputConnections = properties.inputConnections || [];
        newBlock.gapUpConnection = properties.gapUpConnection || undefined;
        newBlock.gapDownConnection = properties.gapDownConnection || undefined;
        newBlock.noGapConnection = properties.noGapConnection || undefined;
        break;
      case BlockType.CONSOLE_LOG:
        newBlock.message = properties.message || 'Debug message';
        newBlock.inputConnections = properties.inputConnections || [];
        newBlock.outputConnections = properties.outputConnections || [];
        break;
      case BlockType.MOMENTUM_INDICATOR:
        newBlock.indicator = properties.indicator || 'stochastic';
        newBlock.period = properties.period || 14;
        newBlock.timeframe = properties.timeframe || 'day';
        newBlock.overbought = properties.overbought || 70;
        newBlock.oversold = properties.oversold || 30;
        newBlock.smoothK = properties.smoothK || 3;
        newBlock.smoothD = properties.smoothD || 3;
        newBlock.outputConnections = properties.outputConnections || [];
        break;
      case BlockType.MOVING_AVERAGE:
        newBlock.averageType = properties.averageType || 'sma';
        newBlock.period = properties.period || 20;
        newBlock.timeframe = properties.timeframe || 'day';
        newBlock.source = properties.source || 'close';
        newBlock.outputConnections = properties.outputConnections || [];
        break;
      case BlockType.POSITION_SIZE:
        // Ensure parameters object exists with proper defaults
        newBlock.parameters = {
          method: 'fixed_amount',
          fixedAmount: 1000,
          accountPercentage: 2,
          riskPercentage: 1,
          atrMultiplier: 2,
          ...properties.parameters
        };
        newBlock.inputConnections = properties.inputConnections || [];
        newBlock.outputConnections = properties.outputConnections || [];
        break;

      // Signal Generation Blocks
      case BlockType.PRICE_ACTION_SIGNAL:
      case BlockType.MULTI_TIMEFRAME_ANALYSIS:
      case BlockType.DIVERGENCE_DETECTION:
      case BlockType.VOLUME_CONFIRMATION:
      case BlockType.MARKET_REGIME:
        // Ensure parameters object exists with proper defaults
        newBlock.parameters = {
          ...intelligentDefaults.parameters,
          ...properties.parameters
        };
        newBlock.inputConnections = properties.inputConnections || [];
        newBlock.outputConnections = properties.outputConnections || [];
        newBlock.bullishConnection = properties.bullishConnection || undefined;
        newBlock.bearishConnection = properties.bearishConnection || undefined;
        newBlock.neutralConnection = properties.neutralConnection || undefined;
        break;

      // Market Analysis Blocks
      case BlockType.SUPPORT_RESISTANCE:
      case BlockType.TREND_LINE_ANALYSIS:
      case BlockType.MARKET_STRUCTURE:
        // Ensure parameters object exists with proper defaults
        newBlock.parameters = {
          ...intelligentDefaults.parameters,
          ...properties.parameters
        };
        newBlock.inputConnections = properties.inputConnections || [];
        newBlock.outputConnections = properties.outputConnections || [];
        newBlock.trueConnection = properties.trueConnection || undefined;
        newBlock.falseConnection = properties.falseConnection || undefined;
        break;

      // Execution Timing Blocks
      case BlockType.ENTRY_TIMING:
      case BlockType.EXIT_CONDITIONS:
      case BlockType.SESSION_FILTER:
      case BlockType.ECONOMIC_CALENDAR:
        // Ensure parameters object exists with proper defaults
        newBlock.parameters = {
          ...intelligentDefaults.parameters,
          ...properties.parameters
        };
        newBlock.inputConnections = properties.inputConnections || [];
        newBlock.outputConnections = properties.outputConnections || [];
        break;

      // Additional Technical Indicator Blocks
      case BlockType.VOLUME_INDICATOR:
      case BlockType.VOLATILITY_INDICATOR:
        newBlock.indicator = properties.indicator || 'volume';
        newBlock.period = properties.period || 20;
        newBlock.timeframe = properties.timeframe || 'day';
        newBlock.inputConnections = properties.inputConnections || [];
        newBlock.outputConnections = properties.outputConnections || [];
        break;

      // Logic & Flow Control Blocks
      case BlockType.NOT_OPERATOR:
      case BlockType.TIME_FILTER:
      case BlockType.MARKET_CONDITION_FILTER:
        // Ensure parameters object exists with proper defaults
        newBlock.parameters = {
          ...intelligentDefaults.parameters,
          ...properties.parameters
        };
        newBlock.inputConnections = properties.inputConnections || [];
        newBlock.outputConnections = properties.outputConnections || [];
        newBlock.trueConnection = properties.trueConnection || undefined;
        newBlock.falseConnection = properties.falseConnection || undefined;
        break;



      // Remove non-existent block types for now
      // These can be added back when the block types are properly defined

      // Remove non-existent signal generation block types

      // Remove non-existent advanced analysis block types

      // Remove non-existent logic flow and filtering block types
    }

    setBlocks(prevBlocks => [...prevBlocks, newBlock]);
    setHasChanges(true);

    // If this is the first block, set it as the entry block
    if (blocks.length === 0) {
      setEntryBlockId(newBlock.id);
    }

    return newBlock.id;
  }, [blocks, saveToHistory]);

  // Update a block
  const updateBlock = useCallback((id: string, properties: Partial<AgentBlock>) => {
    setBlocks(prevBlocks => {
      // Find the block to update
      const blockToUpdate = prevBlocks.find(block => block.id === id);
      if (!blockToUpdate) return prevBlocks;

      // Special handling for position updates to ensure they're valid
      if (properties.position) {
        const position = properties.position;

        // Ensure position has valid x and y coordinates
        if (typeof position.x !== 'number' || typeof position.y !== 'number' ||
            isNaN(position.x) || isNaN(position.y)) {
          // If position is invalid, keep the existing position
          const { position: _, ...otherProperties } = properties;
          return prevBlocks.map(block =>
            block.id === id ? { ...block, ...otherProperties } : block
          );
        }
      }

      // Normal update with all properties
      return prevBlocks.map(block =>
        block.id === id ? { ...block, ...properties } : block
      );
    });

    setHasChanges(true);
  }, []);

  // Remove a block
  const removeBlock = useCallback((id: string) => {
    // Check if this is a "When Run" block
    const blockToRemove = blocks.find(block => block.id === id);
    if (blockToRemove?.type === BlockType.WHEN_RUN) {
      // Don't allow removing the "When Run" block
      return;
    }

    // Save current state to history before making changes
    saveToHistory();

    // Remove the block and clean up any references to it in other blocks
    setBlocks(prevBlocks => {
      const filteredBlocks = prevBlocks.filter(block => block.id !== id);

      // Clean up any references to the removed block in other blocks
      return filteredBlocks.map(block => {
        const cleanedBlock = { ...block };

        // Clean up inputConnections
        if (cleanedBlock.inputConnections) {
          cleanedBlock.inputConnections = cleanedBlock.inputConnections.filter(connId => connId !== id);
        }

        // Clean up outputConnections
        if (cleanedBlock.outputConnections) {
          cleanedBlock.outputConnections = cleanedBlock.outputConnections.filter(connId => connId !== id);
        }

        // Clean up trueConnection
        if (cleanedBlock.trueConnection === id) {
          cleanedBlock.trueConnection = undefined;
        }

        // Clean up falseConnection
        if (cleanedBlock.falseConnection === id) {
          cleanedBlock.falseConnection = undefined;
        }

        return cleanedBlock;
      });
    });

    // Remove connections involving the block
    setConnections(prevConnections =>
      prevConnections.filter(conn =>
        conn.sourceId !== id && conn.targetId !== id
      )
    );

    // If the entry block is removed, set a new entry block if possible
    if (id === entryBlockId && blocks.length > 1) {
      // Find the "When Run" block or use the first block
      const whenRunBlock = blocks.find(block => block.type === BlockType.WHEN_RUN);
      const remainingBlocks = blocks.filter(block => block.id !== id);
      setEntryBlockId(whenRunBlock?.id || remainingBlocks[0].id);
    } else if (blocks.length <= 1) {
      // This shouldn't happen since we don't allow removing the "When Run" block
      const whenRunBlock = createDefaultWhenRunBlock();
      setBlocks([whenRunBlock]);
      setEntryBlockId(whenRunBlock.id);
    }

    setHasChanges(true);
  }, [blocks, entryBlockId, saveToHistory]);

  // ALLOW EVERYTHING - No restrictions except basics and logical validation
  const validateConnection = useCallback((connection: Connection): { valid: boolean; reason?: string } => {
    console.log('🚀 ALLOW EVERYTHING validation for:', connection);

    const sourceBlock = blocks.find(b => b.id === connection.sourceId);
    const targetBlock = blocks.find(b => b.id === connection.targetId);

    if (!sourceBlock || !targetBlock) {
      return { valid: false, reason: 'Source or target block not found' };
    }

    // Only prevent self-connections
    if (connection.sourceId === connection.targetId) {
      return { valid: false, reason: 'Cannot connect block to itself' };
    }

    // INTELLIGENT: Only prevent multiple output connections for specific block types that should have single outputs
    // Blocks like TRIGGER should only have one output, but data blocks (PRICE, MOVING_AVERAGE, etc.) can have multiple
    const singleOutputBlockTypes = [BlockType.TRIGGER, BlockType.CONDITION, BlockType.COMPARISON];

    if (singleOutputBlockTypes.includes(sourceBlock.type as BlockType)) {
      const existingOutputConnections = connections.filter(
        conn => conn.sourceId === connection.sourceId && conn.sourceHandle === (connection.sourceHandle || 'output')
      );
      if (existingOutputConnections.length > 0) {
        return {
          valid: false,
          reason: `${sourceBlock.type} blocks can only have one output connection. Each ${sourceBlock.type} block can only connect to one other block.`
        };
      }
    }

    // Prevent illogical condition block connections
    if (sourceBlock.type === BlockType.CONDITION) {
      // Check if both TRUE and FALSE outputs are trying to connect to the same target
      const existingConnections = connections.filter(conn =>
        conn.sourceId === connection.sourceId &&
        conn.targetId === connection.targetId
      );

      // If there's already a connection from this condition block to the same target
      if (existingConnections.length > 0) {
        const existingHandle = existingConnections[0].sourceHandle;
        const newHandle = connection.sourceHandle;

        // Prevent connecting both TRUE and FALSE to the same target
        if ((existingHandle === 'true' && newHandle === 'false') ||
            (existingHandle === 'false' && newHandle === 'true')) {
          return {
            valid: false,
            reason: 'Cannot connect both TRUE and FALSE outputs of a condition block to the same target. This defeats the purpose of conditional logic.'
          };
        }
      }
    }

    console.log('✅ CONNECTION APPROVED - validation passed');
    return { valid: true };
  }, [blocks, connections]);

  // Add a connection between blocks with intelligent validation
  const addConnection = useCallback((connection: Connection, validationOnly = false) => {
    console.log('addConnection called with:', { connection, validationOnly });
    console.log('Current connections:', connections);

    // Check if connection already exists
    const connectionExists = connections.some(
      conn =>
        conn.sourceId === connection.sourceId &&
        conn.targetId === connection.targetId &&
        conn.sourceHandle === connection.sourceHandle &&
        conn.targetHandle === connection.targetHandle
    );

    if (connectionExists) {
      console.log('Connection already exists, rejecting');
      return false;
    }

    // Validate the connection
    const validation = validateConnection(connection);
    console.log('Connection validation result:', validation);

    if (!validation.valid) {
      if (!validationOnly) {
      console.warn(`Connection rejected: ${validation.reason}`);
      }
      return false;
    }

    // If this is validation-only mode, return true without making changes
    if (validationOnly) {
      console.log('Validation-only mode, returning true');
      return true;
    }

    console.log('Adding connection to state...');

    // Save current state to history before making changes
    saveToHistory();

    // Add the connection
    setConnections(prevConnections => {
      const newConnections = [...prevConnections, connection];
      console.log('Updated connections:', newConnections);
      return newConnections;
    });

    // Update the blocks to reflect the connection
    setBlocks(prevBlocks => {
      return prevBlocks.map(block => {
        // Update source block
        if (block.id === connection.sourceId) {
          console.log('Updating source block:', block.id, 'with handle:', connection.sourceHandle);
          if (connection.sourceHandle === 'output') {
            return {
              ...block,
              outputConnections: [...(block.outputConnections || []), connection.targetId]
            };
          } else if (connection.sourceHandle === 'true') {
            return {
              ...block,
              trueConnection: connection.targetId
            };
          } else if (connection.sourceHandle === 'false') {
            return {
              ...block,
              falseConnection: connection.targetId
            };
          } else if (connection.sourceHandle === 'bullish') {
            return {
              ...block,
              bullishConnection: connection.targetId
            };
          } else if (connection.sourceHandle === 'bearish') {
            return {
              ...block,
              bearishConnection: connection.targetId
            };
          } else if (connection.sourceHandle === 'neutral') {
            return {
              ...block,
              neutralConnection: connection.targetId
            };
          } else if (connection.sourceHandle === 'at_level') {
            return {
              ...block,
              atLevelConnection: connection.targetId
            };
          } else if (connection.sourceHandle === 'not_at_level') {
            return {
              ...block,
              notAtLevelConnection: connection.targetId
            };
          } else if (connection.sourceHandle === 'gap_up') {
            return {
              ...block,
              gapUpConnection: connection.targetId
            };
          } else if (connection.sourceHandle === 'gap_down') {
            return {
              ...block,
              gapDownConnection: connection.targetId
            };
          } else if (connection.sourceHandle === 'no_gap') {
            return {
              ...block,
              noGapConnection: connection.targetId
            };
          }
        }

        // Update target block
        if (block.id === connection.targetId) {
          console.log('Updating target block:', block.id, 'with target handle:', connection.targetHandle);

          // Handle regular input connections
          if (connection.targetHandle === 'input') {
            return {
              ...block,
              inputConnections: [...(block.inputConnections || []), connection.sourceId]
            };
          }

          // Handle comparison block input connections (inputA and inputB)
          if (connection.targetHandle === 'inputA' || connection.targetHandle === 'inputB') {
            const currentInputs = block.inputConnections || [];
            const newInputs = [...currentInputs];

            // For inputA, it should be at index 0
            // For inputB, it should be at index 1
            const targetIndex = connection.targetHandle === 'inputA' ? 0 : 1;

            // Ensure the array is large enough
            while (newInputs.length <= targetIndex) {
              newInputs.push('');
            }

            // Set the connection at the correct index
            newInputs[targetIndex] = connection.sourceId;

            return {
              ...block,
              inputConnections: newInputs.filter(id => id !== '') // Remove empty strings
            };
          }
        }

        return block;
      });
    });

    setHasChanges(true);
    console.log('Connection successfully added');
    return true;
  }, [connections, saveToHistory, validateConnection]);

  // ALLOW EVERYTHING - No auto-connections, let user connect manually
  const suggestAndCreateConnections = useCallback((newBlockId: string) => {
    // No auto-connections - user can connect anything to anything
  }, []);



  // Remove a connection
  const removeConnection = useCallback((connection: Connection) => {
    // Remove the connection
    setConnections(prevConnections =>
      prevConnections.filter(conn =>
        !(conn.sourceId === connection.sourceId &&
          conn.targetId === connection.targetId &&
          conn.sourceHandle === connection.sourceHandle &&
          conn.targetHandle === connection.targetHandle)
      )
    );

    // Update the blocks to reflect the removed connection
    setBlocks(prevBlocks => {
      return prevBlocks.map(block => {
        // Update source block
        if (block.id === connection.sourceId) {
          if (connection.sourceHandle === 'output') {
            return {
              ...block,
              outputConnections: (block.outputConnections || []).filter(id => id !== connection.targetId)
            };
          } else if (connection.sourceHandle === 'true') {
            return {
              ...block,
              trueConnection: undefined
            };
          } else if (connection.sourceHandle === 'false') {
            return {
              ...block,
              falseConnection: undefined
            };
          } else if (connection.sourceHandle === 'bullish') {
            return {
              ...block,
              bullishConnection: undefined
            };
          } else if (connection.sourceHandle === 'bearish') {
            return {
              ...block,
              bearishConnection: undefined
            };
          } else if (connection.sourceHandle === 'neutral') {
            return {
              ...block,
              neutralConnection: undefined
            };
          } else if (connection.sourceHandle === 'at_level') {
            return {
              ...block,
              atLevelConnection: undefined
            };
          } else if (connection.sourceHandle === 'not_at_level') {
            return {
              ...block,
              notAtLevelConnection: undefined
            };
          }
        }

        // Update target block
        if (block.id === connection.targetId && connection.targetHandle === 'input') {
          return {
            ...block,
            inputConnections: (block.inputConnections || []).filter(id => id !== connection.sourceId)
          };
        }

        return block;
      });
    });

    setHasChanges(true);
  }, []);

  // Set the entry block
  const setEntryBlock = useCallback((id: string) => {
    if (blocks.some(block => block.id === id)) {
      setEntryBlockId(id);
      setHasChanges(true);
      return true;
    }
    return false;
  }, [blocks]);

  // Clear the builder
  const clearBuilder = useCallback(() => {
    // Create a new "When Run" block
    const whenRunBlock = createDefaultWhenRunBlock();
    setBlocks([whenRunBlock]);
    setConnections([]);
    setEntryBlockId(whenRunBlock.id);
    setHasChanges(true);
  }, []);

  // Load an agent
  const loadAgent = useCallback((agent: Agent) => {
    // Ensure there's a "When Run" block
    let agentBlocks = agent.configuration.blocks || [];
    let agentEntryBlockId = agent.configuration.entryBlockId || '';

    // Check if there's a "When Run" block
    const hasWhenRunBlock = agentBlocks.some(block => block.type === BlockType.WHEN_RUN);

    // If not, add one
    if (!hasWhenRunBlock) {
      const whenRunBlock = createDefaultWhenRunBlock();
      agentBlocks = [whenRunBlock, ...agentBlocks];

      // If there's no entry block, set the "When Run" block as the entry
      if (!agentEntryBlockId) {
        agentEntryBlockId = whenRunBlock.id;
      }
    }

    setBlocks(agentBlocks);
    setEntryBlockId(agentEntryBlockId);
    setAgentName(agent.name || 'New Agent');
    setAgentDescription(agent.description || '');
    setIsPublic(agent.is_public || false);
    setPublicDescription(agent.public_description || '');
    setTags(agent.tags || []);

    // Reconstruct connections from block data
    const newConnections: Connection[] = [];

    agentBlocks.forEach(block => {
      // Add output connections
      if (block.outputConnections) {
        block.outputConnections.forEach(targetId => {
          newConnections.push({
            sourceId: block.id,
            targetId,
            sourceHandle: 'output',
            targetHandle: 'input'
          });
        });
      }

      // Note: trueConnection and falseConnection are now handled generically below

      // Add input connections for comparison blocks (two inputs: A and B)
      if (block.type === BlockType.COMPARISON) {
        if (block.inputConnections && block.inputConnections.length > 0) {
          block.inputConnections.forEach((sourceId, index) => {
            // Only add connections for non-empty source IDs
            if (sourceId && sourceId.trim() !== '') {
              newConnections.push({
                sourceId,
                targetId: block.id,
                sourceHandle: 'output',
                targetHandle: index === 0 ? 'inputA' : 'inputB'
              });
            }
          });
        }
        // Note: trueConnection and falseConnection are now handled generically below
      }

      // Add connections for ALL blocks that have these connection properties
      // This ensures we don't miss any block types when reconstructing connections

      // Handle trueConnection and falseConnection for any block that has them
      if (block.trueConnection) {
        newConnections.push({
          sourceId: block.id,
          targetId: block.trueConnection,
          sourceHandle: 'true',
          targetHandle: 'input'
        });
      }

      if (block.falseConnection) {
        newConnections.push({
          sourceId: block.id,
          targetId: block.falseConnection,
          sourceHandle: 'false',
          targetHandle: 'input'
        });
      }

      // Handle bullish/bearish/neutral connections for any block that has them
      if (block.bullishConnection) {
        newConnections.push({
          sourceId: block.id,
          targetId: block.bullishConnection,
          sourceHandle: 'bullish',
          targetHandle: 'input'
        });
      }

      if (block.bearishConnection) {
        newConnections.push({
          sourceId: block.id,
          targetId: block.bearishConnection,
          sourceHandle: 'bearish',
          targetHandle: 'input'
        });
      }

      if (block.neutralConnection) {
        newConnections.push({
          sourceId: block.id,
          targetId: block.neutralConnection,
          sourceHandle: 'neutral',
          targetHandle: 'input'
        });
      }

      // Handle support/resistance specific connections
      if (block.atLevelConnection) {
        newConnections.push({
          sourceId: block.id,
          targetId: block.atLevelConnection,
          sourceHandle: 'at_level',
          targetHandle: 'input'
        });
      }

      if (block.notAtLevelConnection) {
        newConnections.push({
          sourceId: block.id,
          targetId: block.notAtLevelConnection,
          sourceHandle: 'not_at_level',
          targetHandle: 'input'
        });
      }

      // Handle gap analysis specific connections
      if (block.gapUpConnection) {
        newConnections.push({
          sourceId: block.id,
          targetId: block.gapUpConnection,
          sourceHandle: 'gap_up',
          targetHandle: 'input'
        });
      }

      if (block.gapDownConnection) {
        newConnections.push({
          sourceId: block.id,
          targetId: block.gapDownConnection,
          sourceHandle: 'gap_down',
          targetHandle: 'input'
        });
      }

      if (block.noGapConnection) {
        newConnections.push({
          sourceId: block.id,
          targetId: block.noGapConnection,
          sourceHandle: 'no_gap',
          targetHandle: 'input'
        });
      }

      // Note: gap analysis connections are now handled generically above
    });

    setConnections(newConnections);
  }, []);

  // Clean up orphaned connections
  const cleanupOrphanedConnections = useCallback((agentBlocks: AgentBlock[]) => {
    const blockIds = new Set(agentBlocks.map(block => block.id));

    return agentBlocks.map(block => {
      const cleanedBlock = { ...block };

      // Clean up inputConnections
      if (cleanedBlock.inputConnections) {
        cleanedBlock.inputConnections = cleanedBlock.inputConnections.filter(id => blockIds.has(id));
      }

      // Clean up outputConnections
      if (cleanedBlock.outputConnections) {
        cleanedBlock.outputConnections = cleanedBlock.outputConnections.filter(id => blockIds.has(id));
      }

      // Clean up trueConnection
      if (cleanedBlock.trueConnection && !blockIds.has(cleanedBlock.trueConnection)) {
        cleanedBlock.trueConnection = undefined;
      }

      // Clean up falseConnection
      if (cleanedBlock.falseConnection && !blockIds.has(cleanedBlock.falseConnection)) {
        cleanedBlock.falseConnection = undefined;
      }

      return cleanedBlock;
    });
  }, []);

  // Get the agent configuration
  const getAgentConfiguration = useCallback((): Agent => {
    // Clean up any orphaned connections before returning the configuration
    const cleanedBlocks = cleanupOrphanedConnections(blocks);

    return {
      name: agentName,
      description: agentDescription,
      is_public: isPublic,
      public_description: publicDescription,
      tags: tags,
      configuration: {
        blocks: cleanedBlocks,
        entryBlockId
      }
    };
  }, [blocks, entryBlockId, agentName, agentDescription, isPublic, publicDescription, tags, cleanupOrphanedConnections]);

  // Enhanced validation system with expert-level checks
  const validateAgent = useCallback((): {
    valid: boolean;
    errors: string[];
    errorDetails: Record<string, string[]>;
    disconnectedBlocks: AgentBlock[];
    suggestions: string[];
    complexity: {
      score: number;
      level: 'Simple' | 'Moderate' | 'Complex' | 'Expert';
      metrics: Record<string, number>;
    };
  } => {
    const errors: string[] = [];
    const errorDetails: Record<string, string[]> = {};
    const suggestions: string[] = [];

    // Calculate complexity metrics
    const complexity = calculateAgentComplexity(blocks);

    // Check if there are any blocks
    if (blocks.length === 0) {
      errors.push('Agent must have at least one block');
      return { valid: false, errors, errorDetails, disconnectedBlocks: [], suggestions, complexity };
    }

    // Check if there is an entry block
    if (!entryBlockId) {
      errors.push('Agent must have an entry block');
      return { valid: false, errors, errorDetails, disconnectedBlocks: [], suggestions, complexity };
    }

    // Check if the entry block exists
    if (!blocks.some(block => block.id === entryBlockId)) {
      errors.push('Entry block does not exist');
      return { valid: false, errors, errorDetails, disconnectedBlocks: [], suggestions, complexity };
    }

    // Expert-level validation checks
    validateBlockConfiguration(blocks, errors, errorDetails, suggestions);
    validateDataFlow(blocks, errors, errorDetails, suggestions);
    validateLogicalStructure(blocks, errors, errorDetails, suggestions);
    validatePerformanceOptimization(blocks, suggestions);

    // Check if there is at least one trigger block OR confidence boost block
    const triggerBlocks = blocks.filter(block => block.type === BlockType.TRIGGER);
    const confidenceBoostBlocks = blocks.filter(block =>
      block.type === BlockType.BULLISH_CONFIDENCE_BOOST ||
      block.type === BlockType.BEARISH_CONFIDENCE_BOOST ||
      block.type === BlockType.CONFIDENCE_BOOST
    );

    if (triggerBlocks.length === 0 && confidenceBoostBlocks.length === 0) {
      errors.push('Agent must have at least one trigger block or confidence boost block');
      return { valid: false, errors, errorDetails, disconnectedBlocks: [], suggestions, complexity };
    }

    // Check if all trigger blocks are connected (only if we have trigger blocks)
    if (triggerBlocks.length > 0) {
      const connectedTriggers = triggerBlocks.filter(block =>
        block.inputConnections && block.inputConnections.length > 0
      );

      if (connectedTriggers.length === 0) {
        errors.push('At least one trigger block must be connected to other blocks');
        triggerBlocks.forEach(block => {
          if (!errorDetails[block.id]) errorDetails[block.id] = [];
          errorDetails[block.id].push('Trigger block is not connected to any input');
        });
      }
    }

    // Check if all blocks are connected using the connections array
    const connectedBlockIds = new Set<string>();
    connectedBlockIds.add(entryBlockId);

    // Helper function to recursively find connected blocks using the connections array
    const findConnectedBlocks = (blockId: string) => {
      // Find all connections where this block is the source
      const outgoingConnections = connections.filter(conn => conn.sourceId === blockId);

      outgoingConnections.forEach(conn => {
        if (!connectedBlockIds.has(conn.targetId)) {
          connectedBlockIds.add(conn.targetId);
          findConnectedBlocks(conn.targetId);
        }
      });

      // Also check incoming connections to ensure bidirectional connectivity detection
      const incomingConnections = connections.filter(conn => conn.targetId === blockId);

      incomingConnections.forEach(conn => {
        if (!connectedBlockIds.has(conn.sourceId)) {
          connectedBlockIds.add(conn.sourceId);
          findConnectedBlocks(conn.sourceId);
        }
      });
    };

    findConnectedBlocks(entryBlockId);

    const disconnectedBlocks = blocks.filter(block => !connectedBlockIds.has(block.id));

    // Add specific error messages for disconnected blocks
    disconnectedBlocks.forEach(block => {
      if (!errorDetails[block.id]) errorDetails[block.id] = [];
      errorDetails[block.id].push('Block is disconnected from the flow');
    });

    if (disconnectedBlocks.length > 0) {
      errors.push(`There are ${disconnectedBlocks.length} disconnected blocks`);
    }

    // Check for illogical condition and comparison block connections and flag them as errors
    const conditionBlocks = blocks.filter(block => block.type === BlockType.CONDITION || block.type === BlockType.COMPARISON);
    conditionBlocks.forEach(block => {
      if (connectedBlockIds.has(block.id)) { // Only check connected condition blocks
        // Check for illogical connections where both TRUE and FALSE connect to the same target
        const trueConnections = connections.filter(conn =>
          conn.sourceId === block.id && conn.sourceHandle === 'true'
        );
        const falseConnections = connections.filter(conn =>
          conn.sourceId === block.id && conn.sourceHandle === 'false'
        );

        // Find targets that have both TRUE and FALSE connections
        const trueTargets = new Set(trueConnections.map(conn => conn.targetId));
        const falseTargets = new Set(falseConnections.map(conn => conn.targetId));

        const duplicateTargets = [...trueTargets].filter(target => falseTargets.has(target));

        if (duplicateTargets.length > 0) {
          if (!errorDetails[block.id]) errorDetails[block.id] = [];
          errorDetails[block.id].push('Condition block has both TRUE and FALSE outputs connected to the same target(s). This defeats the purpose of conditional logic.');
          errors.push(`Condition block has illogical connections - both TRUE and FALSE outputs connect to the same target`);
        }

        // Check if condition blocks have at least a true connection (false connection is optional)
        const hasTrueConnectionInBlock = !!block.trueConnection;
        const hasTrueConnectionInArray = trueConnections.length > 0;

        if (!hasTrueConnectionInBlock && !hasTrueConnectionInArray) {
          if (!errorDetails[block.id]) errorDetails[block.id] = [];
          errorDetails[block.id].push('Condition block is missing TRUE output connection');
          errors.push(`Condition block is missing TRUE output connection`);
        }

        // FALSE connection is now optional - no validation required
        // This allows for conditions where you only want to do something when true
      }
    });

    return {
      valid: errors.length === 0,
      errors,
      errorDetails,
      disconnectedBlocks,
      suggestions,
      complexity
    };
  }, [blocks, entryBlockId, connections]);

  // Custom setters that track changes
  const setAgentNameWithTracking = useCallback((name: string) => {
    setAgentName(name);
    setHasChanges(true);
  }, []);

  const setAgentDescriptionWithTracking = useCallback((description: string) => {
    setAgentDescription(description);
    setHasChanges(true);
  }, []);

  const setIsPublicWithTracking = useCallback((isPublic: boolean) => {
    setIsPublic(isPublic);
    setHasChanges(true);
  }, []);

  const setPublicDescriptionWithTracking = useCallback((description: string) => {
    setPublicDescription(description);
    setHasChanges(true);
  }, []);

  const setTagsWithTracking = useCallback((tags: string[]) => {
    setTags(tags);
    setHasChanges(true);
  }, []);

  // Delete selected connections
  const deleteSelectedConnections = useCallback((selectedConnectionIds: string[]) => {
    if (selectedConnectionIds.length === 0) return;

    // Save current state to history before making changes
    saveToHistory();

    // Remove selected connections
    setConnections(prevConnections =>
      prevConnections.filter((_, index) =>
        !selectedConnectionIds.includes(`e-${index}`)
      )
    );

    // Update blocks to reflect removed connections
    selectedConnectionIds.forEach(edgeId => {
      const edgeIndex = parseInt(edgeId.replace('e-', ''));
      const connection = connections[edgeIndex];

      if (connection) {
        // Update blocks to remove connection references
        setBlocks(prevBlocks => {
          return prevBlocks.map(block => {
            // Update source block
            if (block.id === connection.sourceId) {
              if (connection.sourceHandle === 'output') {
                return {
                  ...block,
                  outputConnections: (block.outputConnections || []).filter(id => id !== connection.targetId)
                };
              } else if (connection.sourceHandle === 'true') {
                return {
                  ...block,
                  trueConnection: undefined
                };
              } else if (connection.sourceHandle === 'false') {
                return {
                  ...block,
                  falseConnection: undefined
                };
              } else if (connection.sourceHandle === 'bullish') {
                return {
                  ...block,
                  bullishConnection: undefined
                };
              } else if (connection.sourceHandle === 'bearish') {
                return {
                  ...block,
                  bearishConnection: undefined
                };
              } else if (connection.sourceHandle === 'neutral') {
                return {
                  ...block,
                  neutralConnection: undefined
                };
              } else if (connection.sourceHandle === 'at_level') {
                return {
                  ...block,
                  atLevelConnection: undefined
                };
              } else if (connection.sourceHandle === 'not_at_level') {
                return {
                  ...block,
                  notAtLevelConnection: undefined
                };
              } else if (connection.sourceHandle === 'gap_up') {
                return {
                  ...block,
                  gapUpConnection: undefined
                };
              } else if (connection.sourceHandle === 'gap_down') {
                return {
                  ...block,
                  gapDownConnection: undefined
                };
              } else if (connection.sourceHandle === 'no_gap') {
                return {
                  ...block,
                  noGapConnection: undefined
                };
              }
            }

            // Update target block
            if (block.id === connection.targetId) {
              // Handle regular input connections
              if (connection.targetHandle === 'input') {
                return {
                  ...block,
                  inputConnections: (block.inputConnections || []).filter(id => id !== connection.sourceId)
                };
              }

              // Handle comparison block input connections (inputA and inputB)
              if (connection.targetHandle === 'inputA' || connection.targetHandle === 'inputB') {
                const currentInputs = block.inputConnections || [];
                const targetIndex = connection.targetHandle === 'inputA' ? 0 : 1;

                // Create a new array with the connection removed at the specific index
                const newInputs = [...currentInputs];
                if (newInputs[targetIndex] === connection.sourceId) {
                  newInputs[targetIndex] = ''; // Mark as empty
                }

                return {
                  ...block,
                  inputConnections: newInputs.filter(id => id !== '') // Remove empty strings
                };
              }
            }

            return block;
          });
        });
      }
    });

    setHasChanges(true);
  }, [connections, saveToHistory]);

  // Fix illogical condition and comparison block connections
  const fixIllogicalConnections = useCallback(() => {
    console.log('🔧 Fixing illogical condition and comparison block connections...');

    const conditionBlocks = blocks.filter(block => block.type === BlockType.CONDITION || block.type === BlockType.COMPARISON);
    let connectionsToRemove: any[] = [];

    conditionBlocks.forEach(block => {
      // Find all TRUE and FALSE connections for this condition block
      const trueConnections = connections.filter(conn =>
        conn.sourceId === block.id && conn.sourceHandle === 'true'
      );
      const falseConnections = connections.filter(conn =>
        conn.sourceId === block.id && conn.sourceHandle === 'false'
      );

      // Find targets that have both TRUE and FALSE connections
      const trueTargets = new Set(trueConnections.map(conn => conn.targetId));
      const falseTargets = new Set(falseConnections.map(conn => conn.targetId));

      const duplicateTargets = [...trueTargets].filter(target => falseTargets.has(target));

      if (duplicateTargets.length > 0) {
        console.log(`Found illogical connections for condition block ${block.id}:`, duplicateTargets);

        // Remove FALSE connections to duplicate targets (keep TRUE connections)
        duplicateTargets.forEach(targetId => {
          const falseConnectionsToRemove = falseConnections.filter(conn => conn.targetId === targetId);
          connectionsToRemove.push(...falseConnectionsToRemove);
        });
      }
    });

    // Remove the illogical connections
    if (connectionsToRemove.length > 0) {
      console.log(`Removing ${connectionsToRemove.length} illogical connections:`, connectionsToRemove);

      // Save to history before making changes
      saveToHistory();

      connectionsToRemove.forEach(connection => {
        removeConnection(connection);
      });

      setHasChanges(true);
      return connectionsToRemove.length;
    }

    return 0;
  }, [blocks, connections, saveToHistory, removeConnection, setHasChanges]);

  return {
    blocks,
    connections,
    entryBlockId,
    agentName,
    agentDescription,
    isPublic,
    publicDescription,
    tags,
    setAgentName: setAgentNameWithTracking,
    setAgentDescription: setAgentDescriptionWithTracking,
    setIsPublic: setIsPublicWithTracking,
    setPublicDescription: setPublicDescriptionWithTracking,
    setTags: setTagsWithTracking,
    addBlock,
    updateBlock,
    removeBlock,
    addConnection,
    removeConnection,
    setEntryBlock,
    clearBuilder,
    loadAgent,
    getAgentConfiguration,
    validateAgent,
    hasChanges,
    resetChanges,
    // Undo/Redo functionality
    undo,
    redo,
    canUndo,
    canRedo,
    // Connection deletion
    deleteSelectedConnections,
    // Auto-connection system
    suggestAndCreateConnections,
    // Fix illogical connections
    fixIllogicalConnections
  };
}
