import { useState, useEffect } from 'react';

// Custom hook to detect when a chart is fully rendered
export const useChartReady = (
  isLoading: boolean,
  marketData: any,
  setChartReady: (ready: boolean) => void
) => {
  useEffect(() => {
    // If we're not loading and we have market data, the chart should be rendering
    if (!isLoading && marketData) {
      // Give the chart some time to render
      const timer = setTimeout(() => {
        setChartReady(true);
      }, 2000); // Adjust this delay based on how long your chart typically takes to render
      
      return () => clearTimeout(timer);
    }
  }, [isLoading, marketData, setChartReady]);
};

export default useChartReady; 