import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';

export const useVideoPopup = () => {
  const [showVideoPopup, setShowVideoPopup] = useState<boolean>(false);
  const location = useLocation();

  useEffect(() => {
    // Check if user has seen the video popup before
    const hasSeenVideoPopup = localStorage.getItem('hasSeenVideoPopup');

    // Only show on main page and if user hasn't seen it before
    const isMainPage = location.pathname === '/' || location.pathname === '/chat';

    if (!hasSeenVideoPopup && isMainPage) {
      // If not seen before and on main page, show the popup
      // Add a small delay to ensure the page is fully loaded
      const timer = setTimeout(() => {
        setShowVideoPopup(true);
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [location.pathname]);

  const closeVideoPopup = () => {
    // Set state to hide popup
    setShowVideoPopup(false);

    // Save to localStorage to remember user has seen it
    localStorage.setItem('hasSeenVideoPopup', 'true');
  };

  return {
    showVideoPopup,
    closeVideoPopup
  };
};
