/**
 * Performance Monitoring Hook
 * Tracks app performance metrics and provides optimization insights
 */

import { useEffect, useRef, useState, useCallback } from 'react';
import { useLocation } from 'react-router-dom';

interface PerformanceMetrics {
  // Core Web Vitals
  fcp?: number; // First Contentful Paint
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  
  // Custom metrics
  routeLoadTime?: number;
  componentRenderTime?: number;
  apiResponseTime?: number;
  memoryUsage?: number;
  
  // Navigation metrics
  navigationStart?: number;
  domContentLoaded?: number;
  loadComplete?: number;
}

interface RoutePerformance {
  route: string;
  loadTime: number;
  renderTime: number;
  timestamp: number;
}

export const usePerformanceMonitor = () => {
  const location = useLocation();
  const [metrics, setMetrics] = useState<PerformanceMetrics>({});
  const [routeHistory, setRouteHistory] = useState<RoutePerformance[]>([]);
  const routeStartTime = useRef<number>(0);
  const renderStartTime = useRef<number>(0);

  // Measure Core Web Vitals
  const measureWebVitals = useCallback(() => {
    if (typeof window === 'undefined' || !('performance' in window)) return;

    try {
      // First Contentful Paint
      const fcpEntry = performance.getEntriesByName('first-contentful-paint')[0];
      if (fcpEntry) {
        setMetrics(prev => ({ ...prev, fcp: fcpEntry.startTime }));
      }

      // Largest Contentful Paint
      if ('PerformanceObserver' in window) {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          if (lastEntry) {
            setMetrics(prev => ({ ...prev, lcp: lastEntry.startTime }));
          }
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

        // First Input Delay
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            if (entry.name === 'first-input') {
              const fid = entry.processingStart - entry.startTime;
              setMetrics(prev => ({ ...prev, fid }));
            }
          });
        });
        fidObserver.observe({ entryTypes: ['first-input'] });

        // Cumulative Layout Shift
        const clsObserver = new PerformanceObserver((list) => {
          let clsValue = 0;
          const entries = list.getEntries();
          entries.forEach((entry) => {
            if (!(entry as any).hadRecentInput) {
              clsValue += (entry as any).value;
            }
          });
          setMetrics(prev => ({ ...prev, cls: clsValue }));
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });

        // Cleanup observers after 10 seconds
        setTimeout(() => {
          lcpObserver.disconnect();
          fidObserver.disconnect();
          clsObserver.disconnect();
        }, 10000);
      }
    } catch (error) {
      console.warn('Failed to measure web vitals:', error);
    }
  }, []);

  // Measure navigation performance
  const measureNavigationPerformance = useCallback(() => {
    if (typeof window === 'undefined' || !('performance' in window)) return;

    try {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        setMetrics(prev => ({
          ...prev,
          navigationStart: navigation.navigationStart,
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.navigationStart,
          loadComplete: navigation.loadEventEnd - navigation.navigationStart,
        }));
      }
    } catch (error) {
      console.warn('Failed to measure navigation performance:', error);
    }
  }, []);

  // Measure memory usage
  const measureMemoryUsage = useCallback(() => {
    if (typeof window === 'undefined') return;

    try {
      // Modern browsers
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        setMetrics(prev => ({
          ...prev,
          memoryUsage: memory.usedJSHeapSize / 1024 / 1024, // Convert to MB
        }));
      }
    } catch (error) {
      console.warn('Failed to measure memory usage:', error);
    }
  }, []);

  // Measure route performance
  const measureRoutePerformance = useCallback(() => {
    const now = performance.now();
    
    if (routeStartTime.current > 0) {
      const loadTime = now - routeStartTime.current;
      const renderTime = renderStartTime.current > 0 ? now - renderStartTime.current : 0;
      
      const routePerf: RoutePerformance = {
        route: location.pathname,
        loadTime,
        renderTime,
        timestamp: Date.now(),
      };
      
      setRouteHistory(prev => [...prev.slice(-9), routePerf]); // Keep last 10 routes
      
      // Log slow routes
      if (loadTime > 2000) {
        console.warn(`Slow route detected: ${location.pathname} took ${loadTime.toFixed(2)}ms`);
      }
    }
    
    routeStartTime.current = now;
  }, [location.pathname]);

  // Start render timing
  const startRenderTiming = useCallback(() => {
    renderStartTime.current = performance.now();
  }, []);

  // End render timing
  const endRenderTiming = useCallback(() => {
    if (renderStartTime.current > 0) {
      const renderTime = performance.now() - renderStartTime.current;
      setMetrics(prev => ({ ...prev, componentRenderTime: renderTime }));
      renderStartTime.current = 0;
    }
  }, []);

  // Measure API response time
  const measureApiResponseTime = useCallback((startTime: number, endTime: number) => {
    const responseTime = endTime - startTime;
    setMetrics(prev => ({ ...prev, apiResponseTime: responseTime }));
    
    // Log slow API calls
    if (responseTime > 3000) {
      console.warn(`Slow API call detected: ${responseTime.toFixed(2)}ms`);
    }
  }, []);

  // Get performance score
  const getPerformanceScore = useCallback((): number => {
    let score = 100;
    
    // Deduct points for poor Core Web Vitals
    if (metrics.fcp && metrics.fcp > 2500) score -= 20;
    if (metrics.lcp && metrics.lcp > 4000) score -= 25;
    if (metrics.fid && metrics.fid > 300) score -= 20;
    if (metrics.cls && metrics.cls > 0.25) score -= 15;
    
    // Deduct points for slow routes
    const avgRouteTime = routeHistory.length > 0 
      ? routeHistory.reduce((sum, route) => sum + route.loadTime, 0) / routeHistory.length 
      : 0;
    if (avgRouteTime > 2000) score -= 10;
    
    // Deduct points for high memory usage
    if (metrics.memoryUsage && metrics.memoryUsage > 100) score -= 10;
    
    return Math.max(0, score);
  }, [metrics, routeHistory]);

  // Get performance recommendations
  const getRecommendations = useCallback((): string[] => {
    const recommendations: string[] = [];
    
    if (metrics.fcp && metrics.fcp > 2500) {
      recommendations.push('Optimize First Contentful Paint by reducing render-blocking resources');
    }
    
    if (metrics.lcp && metrics.lcp > 4000) {
      recommendations.push('Improve Largest Contentful Paint by optimizing images and critical resources');
    }
    
    if (metrics.fid && metrics.fid > 300) {
      recommendations.push('Reduce First Input Delay by minimizing JavaScript execution time');
    }
    
    if (metrics.cls && metrics.cls > 0.25) {
      recommendations.push('Fix Cumulative Layout Shift by setting dimensions for images and ads');
    }
    
    const slowRoutes = routeHistory.filter(route => route.loadTime > 2000);
    if (slowRoutes.length > 0) {
      recommendations.push(`Optimize slow routes: ${slowRoutes.map(r => r.route).join(', ')}`);
    }
    
    if (metrics.memoryUsage && metrics.memoryUsage > 100) {
      recommendations.push('Reduce memory usage by optimizing component lifecycle and data structures');
    }
    
    return recommendations;
  }, [metrics, routeHistory]);

  // Initialize performance monitoring
  useEffect(() => {
    measureWebVitals();
    measureNavigationPerformance();
    
    // Set up periodic memory monitoring
    const memoryInterval = setInterval(measureMemoryUsage, 30000); // Every 30 seconds
    
    return () => {
      clearInterval(memoryInterval);
    };
  }, [measureWebVitals, measureNavigationPerformance, measureMemoryUsage]);

  // Monitor route changes
  useEffect(() => {
    measureRoutePerformance();
  }, [location.pathname, measureRoutePerformance]);

  // Log performance metrics in development
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Performance Metrics:', metrics);
      console.log('Performance Score:', getPerformanceScore());
      
      const recommendations = getRecommendations();
      if (recommendations.length > 0) {
        console.log('Performance Recommendations:', recommendations);
      }
    }
  }, [metrics, getPerformanceScore, getRecommendations]);

  return {
    metrics,
    routeHistory,
    startRenderTiming,
    endRenderTiming,
    measureApiResponseTime,
    getPerformanceScore,
    getRecommendations,
  };
};

// Higher-order component for measuring component render performance
export const withPerformanceMonitoring = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  componentName?: string
) => {
  return (props: P) => {
    const { startRenderTiming, endRenderTiming } = usePerformanceMonitor();
    
    useEffect(() => {
      startRenderTiming();
      return () => {
        endRenderTiming();
      };
    }, [startRenderTiming, endRenderTiming]);
    
    return <WrappedComponent {...props} />;
  };
};

// Hook for measuring specific operations
export const useOperationTiming = () => {
  const timers = useRef<Map<string, number>>(new Map());
  
  const startTimer = useCallback((operationName: string) => {
    timers.current.set(operationName, performance.now());
  }, []);
  
  const endTimer = useCallback((operationName: string): number => {
    const startTime = timers.current.get(operationName);
    if (startTime) {
      const duration = performance.now() - startTime;
      timers.current.delete(operationName);
      
      // Log slow operations
      if (duration > 1000) {
        console.warn(`Slow operation detected: ${operationName} took ${duration.toFixed(2)}ms`);
      }
      
      return duration;
    }
    return 0;
  }, []);
  
  return { startTimer, endTimer };
};
