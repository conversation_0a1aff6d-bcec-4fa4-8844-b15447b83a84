import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { ChartData } from '@/components/chat/types';

export const useMarketData = (symbol: string, timeframe: '1D' | '1W' | '1M') => {
  return useQuery({
    queryKey: ['marketData', symbol, timeframe],
    queryFn: async (): Promise<ChartData> => {
      
      const { data, error } = await supabase.functions.invoke('market-data', {
        body: JSON.stringify({
          symbol,
          timeframe
        })
      });

      if (error) {
        console.error('Market data fetch error:', error);
        throw error;
      }
      
      return data;
    },
    refetchInterval: timeframe === '1D' ? 60000 : false,
    enabled: !!symbol,
    retry: 2,
  });
};
