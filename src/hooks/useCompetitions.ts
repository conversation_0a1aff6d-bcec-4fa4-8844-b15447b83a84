import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';

export interface Competition {
  id: string;
  name: string;
  description?: string;
  creator_id: string;
  starting_balance: number;
  max_participants?: number;
  entry_fee?: number;
  prize_pool?: number;
  rules?: any;
  allowed_securities?: string[];
  position_limits?: any;
  status: 'draft' | 'open' | 'active' | 'completed' | 'cancelled';
  registration_start?: string;
  registration_end?: string;
  competition_start: string;
  competition_end: string;
  created_at: string;
  updated_at: string;
  participant_count?: number;

  // Whop-specific fields
  whop_company_id?: string;
  whop_business_id?: string;
  whop_business_handle?: string;
  is_cross_community?: boolean;
  allowed_whop_communities?: string[];
  competition_scope?: 'public' | 'whop_local' | 'whop_cross_community';
}

export interface CompetitionParticipant {
  id: string;
  competition_id: string;
  user_id: string;
  account_id: string;
  joined_at: string;
  is_active: boolean;
  final_rank?: number;
  final_return_percent?: number;
  final_portfolio_value?: number;
}

export interface LeaderboardEntry {
  id: string;
  competition_id: string;
  user_id: string;
  participant_id: string;
  username: string;
  current_rank: number;
  portfolio_value: number;
  total_return: number;
  return_percent: number;
  unrealized_pnl: number;
  realized_pnl: number;
  total_trades: number;
  last_updated: string;
}

export interface CreateCompetitionRequest {
  name: string;
  description?: string;
  starting_balance: number;
  max_participants?: number;
  entry_fee?: number;
  prize_pool?: number;
  rules?: any;
  allowed_securities?: string[];
  position_limits?: any;
  registration_start?: string;
  registration_end?: string;
  competition_start: string;
  competition_end: string;
}

export const useCompetitions = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [competitions, setCompetitions] = useState<Competition[]>([]);
  const [userCompetitions, setUserCompetitions] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch all available competitions (including Whop competitions)
  const fetchCompetitions = useCallback(async () => {
    if (!user) return;

    try {
      setLoading(true);
      const session = await supabase.auth.getSession();
      if (!session.data.session?.access_token) {
        throw new Error('No valid session');
      }

      // Fetch both regular and Whop competitions
      const [regularResponse, whopResponse] = await Promise.allSettled([
        supabase.functions.invoke('competition-management', {
          body: JSON.stringify({ action: 'get_competitions' }),
          headers: {
            Authorization: `Bearer ${session.data.session.access_token}`
          }
        }),
        supabase.functions.invoke('whop-competition-management', {
          body: JSON.stringify({ action: 'get_whop_competitions' }),
          headers: {
            Authorization: `Bearer ${session.data.session.access_token}`
          }
        })
      ]);

      let allCompetitions: Competition[] = [];

      // Process regular competitions
      if (regularResponse.status === 'fulfilled' && !regularResponse.value.error) {
        const data = regularResponse.value.data;
        if (data.success) {
          allCompetitions = [...allCompetitions, ...data.data];
        }
      }

      // Process Whop competitions
      if (whopResponse.status === 'fulfilled' && !whopResponse.value.error) {
        const whopData = whopResponse.value.data;
        if (whopData.success && Array.isArray(whopData.data)) {
          allCompetitions = [...allCompetitions, ...whopData.data];
        }
      }

      // Remove duplicates based on ID
      const uniqueCompetitions = allCompetitions.filter((comp, index, self) =>
        index === self.findIndex(c => c.id === comp.id)
      );

      setCompetitions(uniqueCompetitions);
      setError(null);
    } catch (err: any) {
      console.error('Error fetching competitions:', err);
      setError(err.message || 'Failed to fetch competitions');
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Fetch user's competitions
  const fetchUserCompetitions = useCallback(async () => {
    if (!user) return;

    try {
      const session = await supabase.auth.getSession();
      if (!session.data.session?.access_token) {
        throw new Error('No valid session');
      }

      const { data, error } = await supabase.functions.invoke('competition-management', {
        body: JSON.stringify({ action: 'get_user_competitions' }),
        headers: {
          Authorization: `Bearer ${session.data.session.access_token}`
        }
      });

      if (error) throw error;
      if (data.success) {
        setUserCompetitions(data.data);
      } else {
        throw new Error(data.error);
      }
    } catch (err: any) {
      console.error('Error fetching user competitions:', err);
    }
  }, [user]);

  // Create a new competition
  const createCompetition = useCallback(async (competitionData: CreateCompetitionRequest) => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    try {
      const session = await supabase.auth.getSession();
      if (!session.data.session?.access_token) {
        throw new Error('No valid session');
      }

      const { data, error } = await supabase.functions.invoke('competition-management', {
        body: JSON.stringify({ action: 'create_competition', ...competitionData }),
        headers: {
          Authorization: `Bearer ${session.data.session.access_token}`
        }
      });

      if (error) throw error;
      if (data.success) {
        await fetchCompetitions(); // Refresh competitions list
        
        toast({
          title: "Competition Created",
          description: `${competitionData.name} has been created successfully`,
        });
        
        return data.data;
      } else {
        throw new Error(data.error);
      }
    } catch (err: any) {
      console.error('Error creating competition:', err);
      toast({
        title: "Error",
        description: err.message || 'Failed to create competition',
        variant: "destructive"
      });
      throw err;
    }
  }, [user, fetchCompetitions, toast]);

  // Join a competition
  const joinCompetition = useCallback(async (competitionId: string) => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    try {
      const session = await supabase.auth.getSession();
      if (!session.data.session?.access_token) {
        throw new Error('No valid session');
      }

      const { data, error } = await supabase.functions.invoke('competition-management', {
        body: JSON.stringify({ 
          action: 'join_competition', 
          competition_id: competitionId 
        }),
        headers: {
          Authorization: `Bearer ${session.data.session.access_token}`
        }
      });

      if (error) throw error;
      if (data.success) {
        await fetchCompetitions(); // Refresh competitions list
        await fetchUserCompetitions(); // Refresh user competitions
        
        toast({
          title: "Joined Competition",
          description: "You have successfully joined the competition",
        });
        
        return data.data;
      } else {
        throw new Error(data.error);
      }
    } catch (err: any) {
      console.error('Error joining competition:', err);
      toast({
        title: "Error",
        description: err.message || 'Failed to join competition',
        variant: "destructive"
      });
      throw err;
    }
  }, [user, fetchCompetitions, fetchUserCompetitions, toast]);

  // Get competition details with leaderboard
  const getCompetitionDetails = useCallback(async (competitionId: string) => {
    if (!user) return null;

    try {
      const session = await supabase.auth.getSession();
      if (!session.data.session?.access_token) {
        throw new Error('No valid session');
      }

      const { data, error } = await supabase.functions.invoke('competition-management', {
        body: JSON.stringify({ 
          action: 'get_competition_details', 
          competition_id: competitionId 
        }),
        headers: {
          Authorization: `Bearer ${session.data.session.access_token}`
        }
      });

      if (error) throw error;
      if (data.success) {
        return data.data;
      } else {
        throw new Error(data.error);
      }
    } catch (err: any) {
      console.error('Error fetching competition details:', err);
      return null;
    }
  }, [user]);

  // Update leaderboard for a competition
  const updateLeaderboard = useCallback(async (competitionId: string) => {
    if (!user) return;

    try {
      const session = await supabase.auth.getSession();
      if (!session.data.session?.access_token) {
        throw new Error('No valid session');
      }

      const { data, error } = await supabase.functions.invoke('competition-management', {
        body: JSON.stringify({ 
          action: 'update_leaderboard', 
          competition_id: competitionId 
        }),
        headers: {
          Authorization: `Bearer ${session.data.session.access_token}`
        }
      });

      if (error) throw error;
      return data.success;
    } catch (err: any) {
      console.error('Error updating leaderboard:', err);
      return false;
    }
  }, [user]);

  // Initialize data on mount
  useEffect(() => {
    if (user) {
      fetchCompetitions();
      fetchUserCompetitions();
    }
  }, [user, fetchCompetitions, fetchUserCompetitions]);

  return {
    competitions,
    userCompetitions,
    loading,
    error,
    createCompetition,
    joinCompetition,
    getCompetitionDetails,
    updateLeaderboard,
    refreshCompetitions: fetchCompetitions,
    refreshUserCompetitions: fetchUserCompetitions
  };
};
