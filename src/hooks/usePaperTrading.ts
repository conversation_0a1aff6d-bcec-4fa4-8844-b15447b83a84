import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';

export interface PaperTradingAccount {
  id: string;
  user_id: string;
  account_name: string;
  starting_balance: number;
  current_balance: number;
  available_balance: number;
  portfolio_value: number;
  total_value: number;
  unrealized_pnl: number;
  realized_pnl: number;
  total_pnl: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface PaperTradingOrder {
  id: string;
  account_id: string;
  user_id: string;
  symbol: string;
  order_type: 'market' | 'limit' | 'stop' | 'stop_limit';
  side: 'buy' | 'sell';
  quantity: number;
  price?: number;
  stop_price?: number;
  time_in_force: 'DAY' | 'GTC' | 'IOC' | 'FOK';
  status: 'pending' | 'filled' | 'partially_filled' | 'cancelled' | 'rejected' | 'expired';
  filled_quantity: number;
  avg_fill_price?: number;
  total_value?: number;
  commission: number;
  created_at: string;
  updated_at: string;
  filled_at?: string;
  expires_at?: string;
}

export interface PaperTradingPosition {
  id: string;
  account_id: string;
  user_id: string;
  symbol: string;
  quantity: number;
  avg_cost: number;
  market_price?: number;
  market_value?: number;
  unrealized_pnl?: number;
  unrealized_pnl_percent?: number;
  day_change: number;
  day_change_percent: number;
  total_cost: number;
  created_at: string;
  updated_at: string;
  last_price_update?: string;
}

export interface PlaceOrderRequest {
  symbol: string;
  order_type: 'market' | 'limit' | 'stop' | 'stop_limit';
  side: 'buy' | 'sell';
  quantity: number;
  price?: number;
  stop_price?: number;
  time_in_force?: 'DAY' | 'GTC' | 'IOC' | 'FOK';
}

export const usePaperTrading = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [account, setAccount] = useState<PaperTradingAccount | null>(null);
  const [positions, setPositions] = useState<PaperTradingPosition[]>([]);
  const [orders, setOrders] = useState<PaperTradingOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch account data
  const fetchAccount = useCallback(async () => {
    if (!user) return;

    try {
      const session = await supabase.auth.getSession();
      if (!session.data.session?.access_token) {
        throw new Error('No valid session');
      }

      const { data, error } = await supabase.functions.invoke('paper-trading', {
        body: JSON.stringify({ action: 'get_account' }),
        headers: {
          Authorization: `Bearer ${session.data.session.access_token}`
        }
      });

      if (error) throw error;
      if (data.success) {
        setAccount(data.data);
      } else {
        throw new Error(data.error);
      }
    } catch (err) {
      console.error('Error fetching account:', err);
      setError(err.message || 'Failed to fetch account');
    }
  }, [user]);

  // Fetch portfolio data
  const fetchPortfolio = useCallback(async () => {
    if (!user) return;

    try {
      setLoading(true);
      const session = await supabase.auth.getSession();
      if (!session.data.session?.access_token) {
        throw new Error('No valid session');
      }

      const { data, error } = await supabase.functions.invoke('paper-trading', {
        body: JSON.stringify({ action: 'get_portfolio' }),
        headers: {
          Authorization: `Bearer ${session.data.session.access_token}`
        }
      });

      if (error) throw error;
      if (data.success) {
        setAccount(data.data.account);
        setPositions(data.data.positions);
        setOrders(data.data.recent_orders);
        setError(null); // Clear any previous errors
      } else {
        throw new Error(data.error);
      }
    } catch (err) {
      console.error('Error fetching portfolio:', err);
      setError(err.message || 'Failed to fetch portfolio');
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Place an order
  const placeOrder = useCallback(async (orderData: PlaceOrderRequest) => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    try {
      const session = await supabase.auth.getSession();
      if (!session.data.session?.access_token) {
        throw new Error('No valid session');
      }

      const { data, error } = await supabase.functions.invoke('paper-trading', {
        body: JSON.stringify({ action: 'place_order', ...orderData }),
        headers: {
          Authorization: `Bearer ${session.data.session.access_token}`
        }
      });

      if (error) throw error;
      if (data.success) {
        // Refresh portfolio data
        await fetchPortfolio();
        
        toast({
          title: "Order Placed Successfully",
          description: `${orderData.side.toUpperCase()} ${orderData.quantity} shares of ${orderData.symbol}`,
        });
        
        return data.data;
      } else {
        throw new Error(data.error);
      }
    } catch (err) {
      console.error('Error placing order:', err);
      const errorMessage = err.message || 'Failed to place order';
      
      toast({
        variant: "destructive",
        title: "Order Failed",
        description: errorMessage,
      });
      
      throw new Error(errorMessage);
    }
  }, [user, fetchPortfolio, toast]);

  // Cancel an order
  const cancelOrder = useCallback(async (orderId: string) => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    try {
      const { data, error } = await supabase.functions.invoke('paper-trading', {
        body: JSON.stringify({ action: 'cancel_order', order_id: orderId }),
        headers: {
          Authorization: `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
        }
      });

      if (error) throw error;
      if (data.success) {
        // Refresh portfolio data
        await fetchPortfolio();
        
        toast({
          title: "Order Cancelled",
          description: "Order has been successfully cancelled",
        });
        
        return data.data;
      } else {
        throw new Error(data.error);
      }
    } catch (err) {
      console.error('Error cancelling order:', err);
      const errorMessage = err.message || 'Failed to cancel order';
      
      toast({
        variant: "destructive",
        title: "Cancel Failed",
        description: errorMessage,
      });
      
      throw new Error(errorMessage);
    }
  }, [user, fetchPortfolio, toast]);

  // Update market prices
  const updatePrices = useCallback(async (symbols: string[]) => {
    if (!user || symbols.length === 0) return;

    try {
      const { data, error } = await supabase.functions.invoke('paper-trading', {
        body: JSON.stringify({ action: 'update_prices', symbols }),
        headers: {
          Authorization: `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
        }
      });

      if (error) throw error;
      if (data.success) {
        // Refresh portfolio to get updated prices
        await fetchPortfolio();
        return data.data;
      } else {
        throw new Error(data.error);
      }
    } catch (err) {
      console.error('Error updating prices:', err);
      // Don't show toast for price update errors as they happen frequently
    }
  }, [user, fetchPortfolio]);

  // Initialize data on mount
  useEffect(() => {
    if (user) {
      fetchPortfolio();
    }
  }, [user, fetchPortfolio]);

  // Auto-refresh prices every 30 seconds for positions
  useEffect(() => {
    if (positions.length === 0) return;

    const symbols = positions.map(p => p.symbol);
    const interval = setInterval(() => {
      updatePrices(symbols);
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [positions, updatePrices]);

  return {
    account,
    positions,
    orders,
    loading,
    error,
    placeOrder,
    cancelOrder,
    updatePrices,
    refreshPortfolio: fetchPortfolio,
    refreshAccount: fetchAccount
  };
};
