import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export const useSubscriptionType = () => {
  const [subscriptionType, setSubscriptionType] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch subscription type directly from profiles table
  useEffect(() => {
    const fetchSubscriptionType = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        
        if (!user) {
          setSubscriptionType('premium'); // Default to premium if no user
          setIsLoading(false);
          return;
        }

        const { data: profile, error } = await supabase
          .from('profiles')
          .select('subscription_type')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('Error fetching subscription type:', error);
          setSubscriptionType('premium'); // Default to premium on error
        } else {
          setSubscriptionType(profile?.subscription_type || 'premium');
        }
      } catch (error) {
        console.error('Error in fetchSubscriptionType:', error);
        setSubscriptionType('premium'); // Default to premium on error
      } finally {
        setIsLoading(false);
      }
    };

    fetchSubscriptionType();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(() => {
      fetchSubscriptionType();
    });

    return () => subscription.unsubscribe();
  }, []);

  // Function to refresh subscription type (useful after plan changes)
  const refreshSubscriptionType = async () => {
    setIsLoading(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        setSubscriptionType('premium');
        return;
      }

      const { data: profile, error } = await supabase
        .from('profiles')
        .select('subscription_type')
        .eq('id', user.id)
        .single();

      if (error) {
        console.error('Error refreshing subscription type:', error);
        setSubscriptionType('premium');
      } else {
        setSubscriptionType(profile?.subscription_type || 'premium');
      }
    } catch (error) {
      console.error('Error in refreshSubscriptionType:', error);
      setSubscriptionType('premium');
    } finally {
      setIsLoading(false);
    }
  };

  // No upgrades needed - everyone has premium
  const canUpgrade = (): boolean => {
    return false;
  };

  // No basic plan anymore
  const isBasicPlan = (): boolean => {
    return false;
  };

  // No pro plan anymore
  const isProPlan = (): boolean => {
    return false;
  };

  // Everyone is on premium plan
  const isPremiumPlan = (): boolean => {
    return true;
  };

  return {
    subscriptionType,
    isLoading,
    refreshSubscriptionType,
    canUpgrade,
    isBasicPlan,
    isProPlan,
    isPremiumPlan
  };
};
