import { useState, useEffect } from 'react';
import { useWhopUser } from '@/contexts/WhopContext';

// localStorage keys for persistence
const TRADING_ONBOARDING_KEY = 'trading_onboarding_completed';
const TRADING_PAYMENT_KEY = 'trading_payment_verified';

interface TradingOnboardingState {
  isOpen: boolean;
  hasCompletedOnboarding: boolean;
  hasPaymentAccess: boolean;
  paymentVerified: boolean;
}

interface UseTradingOnboardingResult extends TradingOnboardingState {
  completeOnboarding: () => void;
  closeOnboarding: () => void;
  resetOnboarding: () => void;
  verifyPayment: () => void;
}

/**
 * Hook for managing trading app onboarding state
 * Only shows onboarding for Whop users
 * Manages both onboarding completion and payment verification
 */
export const useTradingOnboarding = (): UseTradingOnboardingResult => {
  const { isWhopUser } = useWhopUser();
  
  // Initialize state from localStorage
  const [hasCompletedOnboarding, setHasCompletedOnboarding] = useState(() => {
    if (typeof window === 'undefined') return false;
    return localStorage.getItem(TRADING_ONBOARDING_KEY) === 'true';
  });

  const [paymentVerified, setPaymentVerified] = useState(() => {
    if (typeof window === 'undefined') return false;
    return localStorage.getItem(TRADING_PAYMENT_KEY) === 'true';
  });

  // Determine if onboarding modal should be open
  // Only show for Whop users who haven't completed onboarding
  const isOpen = isWhopUser && !hasCompletedOnboarding;

  // Determine if user has payment access
  // Non-Whop users have unrestricted access
  // Whop users need to complete payment verification
  const hasPaymentAccess = !isWhopUser || paymentVerified;

  // Debug logging for development
  useEffect(() => {
    if (import.meta.env?.DEV) {
      console.log('🎯 Trading Onboarding State:', {
        isWhopUser,
        hasCompletedOnboarding,
        paymentVerified,
        hasPaymentAccess,
        isOpen,
        timestamp: new Date().toISOString()
      });
    }
  }, [isWhopUser, hasCompletedOnboarding, paymentVerified, hasPaymentAccess, isOpen]);

  const completeOnboarding = () => {
    console.log('✅ Trading onboarding completed');
    setHasCompletedOnboarding(true);
    localStorage.setItem(TRADING_ONBOARDING_KEY, 'true');
  };

  const closeOnboarding = () => {
    console.log('❌ Trading onboarding closed without completion');
    setHasCompletedOnboarding(true);
    localStorage.setItem(TRADING_ONBOARDING_KEY, 'true');
  };

  const resetOnboarding = () => {
    console.log('🔄 Trading onboarding reset');
    setHasCompletedOnboarding(false);
    setPaymentVerified(false);
    localStorage.removeItem(TRADING_ONBOARDING_KEY);
    localStorage.removeItem(TRADING_PAYMENT_KEY);
  };

  const verifyPayment = () => {
    console.log('💳 Trading payment verified');
    setPaymentVerified(true);
    localStorage.setItem(TRADING_PAYMENT_KEY, 'true');
    // Also complete onboarding when payment is verified
    completeOnboarding();
  };

  return {
    isOpen,
    hasCompletedOnboarding,
    hasPaymentAccess,
    paymentVerified,
    completeOnboarding,
    closeOnboarding,
    resetOnboarding,
    verifyPayment
  };
};
