import { useState, useEffect } from 'react';
import { useWhopUser } from '@/contexts/WhopContext';

// Local storage key for tracking onboarding completion
const TRADING_ONBOARDING_KEY = 'trading_onboarding_completed';

interface TradingOnboardingState {
  isOpen: boolean;
  isCompleted: boolean;
  isLoading: boolean;
  error: string | null;
}

export const useTradingOnboarding = () => {
  const { isWhopUser, user: whopUser } = useWhopUser();
  const [state, setState] = useState<TradingOnboardingState>({
    isOpen: false,
    isCompleted: false,
    isLoading: false,
    error: null
  });

  // Check if onboarding has been completed
  useEffect(() => {
    const checkOnboardingStatus = () => {
      try {
        const completed = localStorage.getItem(TRADING_ONBOARDING_KEY) === 'true';
        
        // For Whop users, show onboarding if not completed
        // For non-Whop users, skip onboarding entirely
        if (isWhopUser && !completed) {
          setState(prev => ({ ...prev, isOpen: true, isCompleted: false }));
        } else {
          setState(prev => ({ ...prev, isOpen: false, isCompleted: true }));
        }
      } catch (error) {
        console.error('Error checking onboarding status:', error);
        setState(prev => ({ ...prev, error: 'Failed to check onboarding status' }));
      }
    };

    // Only check if we have determined the user's Whop status
    if (isWhopUser !== undefined) {
      checkOnboardingStatus();
    }
  }, [isWhopUser]);

  const completeOnboarding = () => {
    try {
      localStorage.setItem(TRADING_ONBOARDING_KEY, 'true');
      setState(prev => ({ 
        ...prev, 
        isOpen: false, 
        isCompleted: true,
        error: null 
      }));
      console.log('✅ Trading onboarding completed');
    } catch (error) {
      console.error('Error completing onboarding:', error);
      setState(prev => ({ ...prev, error: 'Failed to save onboarding status' }));
    }
  };

  const closeOnboarding = () => {
    setState(prev => ({ ...prev, isOpen: false }));
  };

  const setLoading = (loading: boolean) => {
    setState(prev => ({ ...prev, isLoading: loading }));
  };

  const setError = (error: string | null) => {
    setState(prev => ({ ...prev, error }));
  };

  const resetOnboarding = () => {
    try {
      localStorage.removeItem(TRADING_ONBOARDING_KEY);
      setState({
        isOpen: isWhopUser ? true : false,
        isCompleted: false,
        isLoading: false,
        error: null
      });
      console.log('🔄 Trading onboarding reset');
    } catch (error) {
      console.error('Error resetting onboarding:', error);
      setState(prev => ({ ...prev, error: 'Failed to reset onboarding' }));
    }
  };

  return {
    // State
    isOpen: state.isOpen,
    isCompleted: state.isCompleted,
    isLoading: state.isLoading,
    error: state.error,
    isWhopUser,
    whopUser,
    
    // Actions
    completeOnboarding,
    closeOnboarding,
    setLoading,
    setError,
    resetOnboarding
  };
};
