import { useState, useEffect } from 'react';
import { whopIntermediaryClient } from '@/lib/whopIntermediaryClient';

interface CompetitionPermissions {
  canCreateCompetitions: boolean;
  accessLevel: 'admin' | 'customer' | 'no_access';
  hasAccess: boolean;
  userId?: string;
  experienceId?: string;
  appKey?: string;
  appName?: string;
}

interface UseWhopCompetitionPermissionsResult {
  permissions: CompetitionPermissions | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

/**
 * React hook to check if the current user can create competitions in a Whop experience
 * 
 * @param experienceId - The Whop experience ID to check permissions for
 * @returns Object containing permissions, loading state, error, and refetch function
 * 
 * @example
 * ```tsx
 * function CompetitionCreator({ experienceId }: { experienceId: string }) {
 *   const { permissions, loading, error } = useWhopCompetitionPermissions(experienceId);
 * 
 *   if (loading) return <div>Checking permissions...</div>;
 *   if (error) return <div>Error: {error}</div>;
 *   if (!permissions?.canCreateCompetitions) {
 *     return <div>You need admin access to create competitions</div>;
 *   }
 * 
 *   return <CreateCompetitionForm />;
 * }
 * ```
 */
export function useWhopCompetitionPermissions(experienceId: string): UseWhopCompetitionPermissionsResult {
  const [permissions, setPermissions] = useState<CompetitionPermissions | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPermissions = async () => {
    if (!experienceId) {
      setError('Experience ID is required');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      console.log('🏆 Fetching competition permissions for experience:', experienceId);

      const response = await whopIntermediaryClient.checkCompetitionPermissions(experienceId);

      if (!response.success) {
        throw new Error(response.error || 'Failed to check permissions');
      }

      if (!response.permissions) {
        throw new Error('Invalid response format');
      }

      const perms = response.permissions;
      setPermissions(perms);

      console.log('✅ Competition permissions loaded:', {
        canCreateCompetitions: perms.canCreateCompetitions,
        accessLevel: perms.accessLevel,
        hasAccess: perms.hasAccess
      });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('❌ Error fetching competition permissions:', errorMessage);
      setError(errorMessage);
      setPermissions(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPermissions();
  }, [experienceId]);

  return {
    permissions,
    loading,
    error,
    refetch: fetchPermissions
  };
}

/**
 * Helper function to check if a user can create competitions (non-hook version)
 * Useful for one-off checks or in non-React contexts
 */
export async function checkCanCreateCompetitions(experienceId: string): Promise<{
  canCreate: boolean;
  reason?: string;
  accessLevel?: string;
}> {
  try {
    const response = await whopIntermediaryClient.checkCompetitionPermissions(experienceId);

    if (!response.success) {
      return {
        canCreate: false,
        reason: response.error || 'Failed to check permissions'
      };
    }

    const permissions = response.permissions;
    if (!permissions) {
      return {
        canCreate: false,
        reason: 'Invalid response format'
      };
    }

    if (!permissions.hasAccess) {
      return {
        canCreate: false,
        reason: 'You do not have access to this experience',
        accessLevel: permissions.accessLevel
      };
    }

    if (permissions.accessLevel !== 'admin') {
      return {
        canCreate: false,
        reason: 'Only admins can create competitions. You are a ' + permissions.accessLevel,
        accessLevel: permissions.accessLevel
      };
    }

    return {
      canCreate: true,
      accessLevel: permissions.accessLevel
    };

  } catch (error) {
    return {
      canCreate: false,
      reason: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

export default useWhopCompetitionPermissions;
