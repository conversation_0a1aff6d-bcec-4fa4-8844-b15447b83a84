import { renderHook, act } from '@testing-library/react';
import { useTradingOnboarding } from '../useTradingOnboarding';

// Mock the WhopContext
jest.mock('@/contexts/WhopContext', () => ({
  useWhopUser: () => ({
    isWhopUser: true,
    user: {
      id: 'test-user-id',
      username: 'testuser',
      email: '<EMAIL>'
    }
  })
}));

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

describe('useTradingOnboarding', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with onboarding open for Whop users when not completed', () => {
    localStorageMock.getItem.mockReturnValue(null);
    
    const { result } = renderHook(() => useTradingOnboarding());
    
    expect(result.current.isOpen).toBe(true);
    expect(result.current.isCompleted).toBe(false);
    expect(result.current.isWhopUser).toBe(true);
  });

  it('should initialize with onboarding closed when already completed', () => {
    localStorageMock.getItem.mockReturnValue('true');
    
    const { result } = renderHook(() => useTradingOnboarding());
    
    expect(result.current.isOpen).toBe(false);
    expect(result.current.isCompleted).toBe(true);
  });

  it('should complete onboarding and save to localStorage', () => {
    localStorageMock.getItem.mockReturnValue(null);
    
    const { result } = renderHook(() => useTradingOnboarding());
    
    act(() => {
      result.current.completeOnboarding();
    });
    
    expect(result.current.isOpen).toBe(false);
    expect(result.current.isCompleted).toBe(true);
    expect(localStorageMock.setItem).toHaveBeenCalledWith('trading_onboarding_completed', 'true');
  });

  it('should close onboarding without completing', () => {
    localStorageMock.getItem.mockReturnValue(null);
    
    const { result } = renderHook(() => useTradingOnboarding());
    
    act(() => {
      result.current.closeOnboarding();
    });
    
    expect(result.current.isOpen).toBe(false);
    expect(result.current.isCompleted).toBe(false);
    expect(localStorageMock.setItem).not.toHaveBeenCalled();
  });

  it('should reset onboarding and clear localStorage', () => {
    localStorageMock.getItem.mockReturnValue('true');
    
    const { result } = renderHook(() => useTradingOnboarding());
    
    act(() => {
      result.current.resetOnboarding();
    });
    
    expect(result.current.isOpen).toBe(true);
    expect(result.current.isCompleted).toBe(false);
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('trading_onboarding_completed');
  });

  it('should handle loading and error states', () => {
    const { result } = renderHook(() => useTradingOnboarding());
    
    act(() => {
      result.current.setLoading(true);
    });
    
    expect(result.current.isLoading).toBe(true);
    
    act(() => {
      result.current.setError('Test error');
    });
    
    expect(result.current.error).toBe('Test error');
    
    act(() => {
      result.current.setLoading(false);
      result.current.setError(null);
    });
    
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBe(null);
  });
});
