import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, TrendingUp, TrendingDown, BarChart3, Info } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { AdvancedStockChart } from '@/components/charts/AdvancedStockChart';
import StockPriceDisplay from '@/components/StockPriceDisplay';
import { 
  getCompanyInfo, 
  fetchStockDetail, 
  fetchMultiTimeframeData,
  addToRecentSearches,
  type StockDetailData,
  type MultiTimeframeData 
} from '@/services/stockDataService';

const StockDetail: React.FC = () => {
  const { symbol } = useParams<{ symbol: string }>();
  const navigate = useNavigate();
  const [stockData, setStockData] = useState<StockDetailData | null>(null);
  const [multiTimeframeData, setMultiTimeframeData] = useState<MultiTimeframeData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTimeframe, setSelectedTimeframe] = useState<'1D' | '5D' | '1M' | '3M' | '6M' | '1Y' | '5Y'>('1D');

  const companyInfo = symbol ? getCompanyInfo(symbol) : null;

  useEffect(() => {
    if (symbol) {
      addToRecentSearches(symbol);
      loadStockData();
    }
  }, [symbol]);

  const loadStockData = async () => {
    if (!symbol) return;

    setLoading(true);
    setError(null);

    try {
      // Load both stock detail and multi-timeframe data in parallel
      const [detailData, timeframeData] = await Promise.all([
        fetchStockDetail(symbol),
        fetchMultiTimeframeData(symbol)
      ]);

      setStockData(detailData);
      setMultiTimeframeData(timeframeData);
    } catch (err) {
      console.error('Error loading stock data:', err);
      setError('Failed to load stock data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num: number | undefined, prefix = '', suffix = '') => {
    if (num === undefined || num === null) return 'N/A';
    
    if (num >= 1e12) return `${prefix}${(num / 1e12).toFixed(2)}T${suffix}`;
    if (num >= 1e9) return `${prefix}${(num / 1e9).toFixed(2)}B${suffix}`;
    if (num >= 1e6) return `${prefix}${(num / 1e6).toFixed(2)}M${suffix}`;
    if (num >= 1e3) return `${prefix}${(num / 1e3).toFixed(2)}K${suffix}`;
    
    return `${prefix}${num.toLocaleString()}${suffix}`;
  };

  const formatPercent = (num: number | undefined) => {
    if (num === undefined || num === null) return 'N/A';
    const sign = num >= 0 ? '+' : '';
    return `${sign}${num.toFixed(2)}%`;
  };

  const getChangeColor = (change: number | undefined) => {
    if (change === undefined || change === null) return 'text-white/60';
    return change >= 0 ? 'text-green-400' : 'text-red-400';
  };

  const timeframes = [
    { key: '1D' as const, label: '1D' },
    { key: '5D' as const, label: '5D' },
    { key: '1M' as const, label: '1M' },
    { key: '3M' as const, label: '3M' },
    { key: '6M' as const, label: '6M' },
    { key: '1Y' as const, label: '1Y' },
    { key: '5Y' as const, label: '5Y' }
  ];

  if (!symbol) {
    return (
      <div className="min-h-screen bg-[#0A0A0A] text-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Stock not found</h1>
          <Button onClick={() => navigate('/stock-search')}>
            Back to Search
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#0A0A0A] text-white">
      <div className="max-w-7xl mx-auto px-6 py-6">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/stock-search')}
            className="text-white/60 hover:text-white"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Search
          </Button>
        </div>

        {/* Stock Header */}
        <div className="mb-8">
          <div className="flex items-start justify-between mb-4">
            <div>
              <div className="flex items-center gap-3 mb-2">
                <h1 className="text-3xl font-bold">{symbol}</h1>
                {companyInfo && (
                  <Badge variant="outline" className="text-white/60 border-white/20">
                    {companyInfo.name}
                  </Badge>
                )}
              </div>
              {companyInfo && (
                <p className="text-white/60 text-lg">{companyInfo.name}</p>
              )}
            </div>
          </div>

          {/* Price Display */}
          <div className="flex items-baseline gap-4">
            {loading ? (
              <div className="space-y-2">
                <Skeleton className="h-8 w-32 bg-white/10" />
                <Skeleton className="h-6 w-24 bg-white/10" />
              </div>
            ) : (
              <StockPriceDisplay 
                symbol={symbol} 
                timeframe="1D"
                render={(price, percentChange) => (
                  <div className="flex items-baseline gap-4">
                    <span className="text-4xl font-bold">${price}</span>
                    {stockData && (
                      <div className={`flex items-center gap-2 ${getChangeColor(stockData.changePercent)}`}>
                        {stockData.changePercent >= 0 ? (
                          <TrendingUp className="h-5 w-5" />
                        ) : (
                          <TrendingDown className="h-5 w-5" />
                        )}
                        <span className="text-lg font-medium">
                          {formatPercent(stockData.changePercent)} ({stockData.change >= 0 ? '+' : ''}${stockData.change.toFixed(2)})
                        </span>
                      </div>
                    )}
                  </div>
                )}
              />
            )}
          </div>
        </div>

        {/* Performance Metrics */}
        {multiTimeframeData && (
          <div className="mb-8">
            <div className="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-7 gap-4">
              {timeframes.map(({ key, label }) => {
                const data = multiTimeframeData[key];
                return (
                  <Card key={key} className="bg-[#1A1A1A] border-white/10">
                    <CardContent className="p-4 text-center">
                      <div className="text-sm text-white/60 mb-1">{label}</div>
                      <div className={`font-semibold ${getChangeColor(data?.changePercent)}`}>
                        {formatPercent(data?.changePercent)}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Chart Section */}
          <div className="lg:col-span-2">
            <div className="h-96">
              <AdvancedStockChart
                symbol={symbol}
                height={384}
                showVolume={false}
                showControls={true}
                showPriceDisplay={false}
                timeframe={selectedTimeframe}
              />
            </div>
          </div>

          {/* Key Statistics */}
          <div className="space-y-6">
            <Card className="bg-[#1A1A1A] border-white/10">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="h-5 w-5" />
                  Key Statistics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {loading ? (
                  <div className="space-y-3">
                    {[...Array(6)].map((_, i) => (
                      <div key={i} className="flex justify-between">
                        <Skeleton className="h-4 w-20 bg-white/10" />
                        <Skeleton className="h-4 w-16 bg-white/10" />
                      </div>
                    ))}
                  </div>
                ) : stockData ? (
                  <>
                    <div className="flex justify-between">
                      <span className="text-white/60">Market Cap</span>
                      <span>{formatNumber(stockData.marketCap, '$')}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/60">P/E Ratio</span>
                      <span>{stockData.peRatio?.toFixed(2) || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/60">Volume</span>
                      <span>{formatNumber(stockData.volume)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/60">Avg Volume</span>
                      <span>{formatNumber(stockData.avgVolume)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/60">52W High</span>
                      <span>${stockData.week52High?.toFixed(2) || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/60">52W Low</span>
                      <span>${stockData.week52Low?.toFixed(2) || 'N/A'}</span>
                    </div>
                  </>
                ) : (
                  <div className="text-center text-white/60">
                    Unable to load statistics
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Company Information */}
            {companyInfo && (
              <Card className="bg-[#1A1A1A] border-white/10">
                <CardHeader>
                  <CardTitle>About {companyInfo.name}</CardTitle>
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-full bg-white/10" />
                      <Skeleton className="h-4 w-3/4 bg-white/10" />
                      <Skeleton className="h-4 w-1/2 bg-white/10" />
                    </div>
                  ) : stockData?.description ? (
                    <p className="text-white/80 text-sm leading-relaxed">
                      {stockData.description}
                    </p>
                  ) : (
                    <p className="text-white/60 text-sm">
                      Company information will be displayed here when available.
                    </p>
                  )}
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default StockDetail;
