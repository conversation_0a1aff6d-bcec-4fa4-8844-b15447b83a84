import { useState } from 'react';
import { AnimatePresence } from "framer-motion";
import AuthModal from "@/components/auth/AuthModal";

const LandingPage = () => {
  const [showAuthModal, setShowAuthModal] = useState(true);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-[#0A0A0A] p-4">
      <AnimatePresence>
        {showAuthModal && (
          <AuthModal skipIntro={true} />
        )}
      </AnimatePresence>
    </div>
  );
};

export default LandingPage; 