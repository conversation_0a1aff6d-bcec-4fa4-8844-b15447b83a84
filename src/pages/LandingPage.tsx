import { useState, useEffect } from 'react';
import { AnimatePresence } from "framer-motion";
import AuthModal from "@/components/auth/AuthModal";

interface LandingPageProps {
  setShowOnboarding?: React.Dispatch<React.SetStateAction<boolean>>;
}

const LandingPage = ({ setShowOnboarding }: LandingPageProps) => {
  const [showAuthModal, setShowAuthModal] = useState(true);

  // If setShowOnboarding is provided, show onboarding immediately (in useEffect to avoid infinite loop)
  useEffect(() => {
    if (setShowOnboarding) {
      setShowOnboarding(true);
    }
  }, [setShowOnboarding]);

  // If setShowOnboarding is provided, don't render anything (onboarding will be shown)
  if (setShowOnboarding) {
    return null;
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-[#0A0A0A] p-4">
      <AnimatePresence>
        {showAuthModal && (
          <AuthModal skipIntro={true} />
        )}
      </AnimatePresence>
    </div>
  );
};

export default LandingPage; 