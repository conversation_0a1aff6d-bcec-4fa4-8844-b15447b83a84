import React, { useState, useEffect, useRef } from 'react';
import { Send, MessageSquare, Clock, Plus, Trash2, Bot, ChevronDown, Settings } from 'lucide-react';
import { useUnifiedAuth } from '@/hooks/useUnifiedAuth';
import { getAgents } from '@/services/agentService';

interface Message {
  role: 'user' | 'assistant';
  content: string;
  isLoading?: boolean;
  loadingStage?: 'loading' | 'fetching';
}

interface ChatSession {
  id: string;
  title: string;
  lastMessage: string;
  timestamp: string;
  messageCount: number;
}

interface Agent {
  id: string;
  name: string;
  description: string;
}

// Typing animation component for cycling prompts
const TypingPlaceholder: React.FC<{ examples: string[] }> = ({ examples }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [displayText, setDisplayText] = useState('');
  const [isTyping, setIsTyping] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    const currentExample = examples[currentIndex];
    const typeSpeed = 80;
    const deleteSpeed = 40;
    const pauseTime = 2000;

    let timeout: NodeJS.Timeout;

    if (isTyping && !isDeleting) {
      if (displayText.length < currentExample.length) {
        timeout = setTimeout(() => {
          setDisplayText(currentExample.slice(0, displayText.length + 1));
        }, typeSpeed);
      } else {
        timeout = setTimeout(() => {
          setIsDeleting(true);
          setIsTyping(false);
        }, pauseTime);
      }
    } else if (isDeleting && !isTyping) {
      if (displayText.length > 0) {
        timeout = setTimeout(() => {
          setDisplayText(displayText.slice(0, -1));
        }, deleteSpeed);
      } else {
        setIsDeleting(false);
        setCurrentIndex((prev) => (prev + 1) % examples.length);
        setTimeout(() => {
          setIsTyping(true);
        }, 300);
      }
    }

    return () => clearTimeout(timeout);
  }, [displayText, isTyping, isDeleting, currentIndex, examples]);

  return (
    <span className="text-white/40">
      {displayText}
      <span className="animate-pulse">|</span>
    </span>
  );
};

const ChatInterfacePage: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [currentSessionId, setCurrentSessionId] = useState<string>('default');
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const [agents, setAgents] = useState<Agent[]>([]);
  const [showAgentDropdown, setShowAgentDropdown] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { isAuthenticated, user } = useUnifiedAuth();

  // Mock chat history data
  const [chatSessions] = useState<ChatSession[]>([
    {
      id: 'session-1',
      title: 'PLTR Analysis',
      lastMessage: 'RSI shows oversold conditions',
      timestamp: '2 hours ago',
      messageCount: 12
    },
    {
      id: 'session-2',
      title: 'Portfolio Review',
      lastMessage: 'Your tech allocation looks good',
      timestamp: '1 day ago',
      messageCount: 8
    },
    {
      id: 'session-3',
      title: 'Market Trends',
      lastMessage: 'SPY showing bullish momentum',
      timestamp: '2 days ago',
      messageCount: 15
    },
    {
      id: 'session-4',
      title: 'Agent Building',
      lastMessage: 'RSI agent configured successfully',
      timestamp: '3 days ago',
      messageCount: 6
    }
  ]);

  const placeholderExamples = [
    "What are we analyzing, Cale?",
    "Scan PLTR using my RSI Agent",
    "Show me portfolio performance",
    "Analyze market trends today",
    "Build a new trading agent"
  ];

  // Load agents on component mount
  useEffect(() => {
    const loadAgents = async () => {
      try {
        const agentList = await getAgents();
        setAgents(agentList.map(agent => ({
          id: agent.id,
          name: agent.name,
          description: agent.description || 'Trading agent'
        })));
      } catch (error) {
        console.error('Error loading agents:', error);
      }
    };

    if (isAuthenticated) {
      loadAgents();
    }
  }, [isAuthenticated]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSend = async () => {
    if (input.trim() === '' || isLoading) return;

    const newMessage: Message = { role: 'user', content: input };
    setMessages((prevMessages) => [...prevMessages, newMessage]);
    setInput('');
    setIsLoading(true);

    // Add loading message
    setMessages((prevMessages) => [
      ...prevMessages,
      { role: 'assistant', content: '', isLoading: true, loadingStage: 'loading' }
    ]);

    try {
      if (!isAuthenticated || !user) {
        throw new Error('User not authenticated.');
      }

      // Simulate first stage delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update to fetching stage
      setMessages((prevMessages) =>
        prevMessages.map(msg =>
          msg.isLoading ? { ...msg, loadingStage: 'fetching' } : msg
        )
      );

      // Simulate second stage delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Remove loading message and add response
      setMessages((prevMessages) => {
        const withoutLoading = prevMessages.filter(msg => !msg.isLoading);
        return [
          ...withoutLoading,
          { role: 'assistant', content: 'This is a placeholder response. Chat functionality will be implemented.' }
        ];
      });
    } catch (error) {
      console.error('Error sending message:', error);
      setMessages((prevMessages) => {
        const withoutLoading = prevMessages.filter(msg => !msg.isLoading);
        return [
          ...withoutLoading,
          { role: 'assistant', content: 'Sorry, something went wrong.' }
        ];
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div className="flex h-full bg-[#0A0A0A]">
      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Detailed Header */}
        <div className="px-8 py-6 border-b border-white/[0.08]">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-light text-white font-sans tracking-tight mb-2">
                What are we analyzing, Cale?
              </h1>
              <p className="text-white/60 text-sm font-sans">
                Ask questions, analyze stocks, or build trading strategies
              </p>
            </div>
            <button
              onClick={() => {
                setMessages([]);
                setCurrentSessionId('new-' + Date.now());
              }}
              className="flex items-center gap-2 px-4 py-2.5 bg-white/[0.06] hover:bg-white/[0.08] border border-white/[0.08] rounded-xl text-white/80 hover:text-white transition-all duration-200 font-sans text-sm"
            >
              <Plus className="w-4 h-4" />
              New Chat
            </button>
          </div>
        </div>

        {/* Messages Area */}
        <div className="flex-1 overflow-y-auto">
          <div className="max-w-4xl mx-auto px-8 py-8">
            {messages.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full min-h-[400px] text-center">
                <div className="w-16 h-16 rounded-full bg-white/[0.04] border border-white/[0.08] flex items-center justify-center mb-6">
                  <MessageSquare className="w-8 h-8 text-white/40" />
                </div>
                <h2 className="text-xl font-medium text-white mb-3 font-sans">
                  Start a conversation
                </h2>
                <p className="text-white/60 text-sm font-sans max-w-md">
                  Ask me anything about trading, market analysis, or portfolio management. I'm here to help you make informed decisions.
                </p>
              </div>
            ) : (
              <div className="space-y-8">
                {messages.map((message, index) => (
                  <div key={index} className="space-y-3">
                    {message.role === 'user' ? (
                      <div className="text-left">
                        <div className="text-white text-lg leading-relaxed font-sans">
                          {message.content}
                        </div>
                      </div>
                    ) : (
                      <div className="text-left">
                        {message.isLoading ? (
                          <div className="flex items-center gap-3 text-white/60">
                            <div className="flex space-x-1">
                              <div className="w-2 h-2 bg-green-400/60 rounded-full animate-pulse"></div>
                              <div className="w-2 h-2 bg-green-400/60 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                              <div className="w-2 h-2 bg-green-400/60 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                            </div>
                            <span className="text-sm font-sans">
                              {message.loadingStage === 'loading' ? 'Analyzing...' : 'Processing...'}
                            </span>
                          </div>
                        ) : (
                          <div className="text-white/90 text-lg leading-relaxed font-sans">
                            {message.content}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>
            )}
          </div>
        </div>

        {/* Enhanced Input Area */}
        <div className="px-8 py-6 border-t border-white/[0.08]">
          <div className="max-w-4xl mx-auto">
            <div className="relative bg-white/[0.02] border border-white/[0.08] rounded-2xl p-4 focus-within:border-white/[0.12] transition-colors duration-200">
              <input
                type="text"
                className="w-full bg-transparent border-none outline-none text-white text-lg placeholder-white/40 resize-none font-sans pr-12"
                placeholder=""
                value={input}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setInput(e.target.value)}
                onKeyDown={handleKeyDown}
                disabled={isLoading}
              />

              {/* Animated placeholder when empty */}
              {!input && !isLoading && (
                <div className="absolute left-4 top-4 pointer-events-none">
                  <TypingPlaceholder examples={placeholderExamples} />
                </div>
              )}

              {/* Send button */}
              {input.trim() && (
                <button
                  onClick={handleSend}
                  disabled={isLoading}
                  className="absolute right-4 top-1/2 -translate-y-1/2 w-8 h-8 rounded-lg bg-green-500 hover:bg-green-600 text-white flex items-center justify-center transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Send className="w-4 h-4" />
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Chat History Sidebar */}
      <div className="w-80 border-l border-white/[0.08] bg-white/[0.02]">
        <div className="p-6 border-b border-white/[0.08]">
          <h3 className="text-lg font-medium text-white font-sans mb-2">
            Osis History
          </h3>
          <p className="text-white/60 text-sm font-sans">
            Your recent conversations
          </p>
        </div>

        <div className="p-4 space-y-3 overflow-y-auto h-full">
          {chatSessions.map((session) => (
            <button
              key={session.id}
              onClick={() => setCurrentSessionId(session.id)}
              className="w-full text-left p-4 rounded-xl bg-white/[0.02] hover:bg-white/[0.04] border border-white/[0.06] hover:border-white/[0.08] transition-all duration-200 group"
            >
              <div className="flex items-start justify-between mb-2">
                <h4 className="text-white font-medium text-sm font-sans group-hover:text-white/90 transition-colors">
                  {session.title}
                </h4>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    // Handle delete
                  }}
                  className="opacity-0 group-hover:opacity-100 text-white/40 hover:text-red-400 transition-all duration-200 p-1"
                >
                  <Trash2 className="w-3 h-3" />
                </button>
              </div>
              <p className="text-white/60 text-xs font-sans mb-3 line-clamp-2">
                {session.lastMessage}
              </p>
              <div className="flex items-center justify-between text-xs">
                <span className="text-white/40 font-sans flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  {session.timestamp}
                </span>
                <span className="text-white/40 font-sans">
                  {session.messageCount} messages
                </span>
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ChatInterfacePage;