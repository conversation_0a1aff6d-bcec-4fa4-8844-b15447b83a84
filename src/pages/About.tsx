import { Link } from 'react-router-dom';

export default function About() {
  return (
    <div className="min-h-screen bg-[#0A0A0A] text-white">
      <main className="container max-w-2xl mx-auto py-16 px-4">
        <h1 className="text-[2.5rem] font-[450] tracking-[-0.02em] mb-12">What is Osis?</h1>

        <div className="space-y-7 text-[1.0625rem] text-white/[0.82]">
          <p className="leading-[1.7]">
            Osis is a platform for building, testing, and analyzing your own trading tools and strategies. We empower users to create custom AI-driven agents and market analysis tools—no chat, no noise, just powerful features for serious traders and builders.
          </p>

          <p className="leading-[1.7]">
            Our mission is to make advanced trading technology accessible. With Osis, you can design, backtest, and deploy your own strategies, visualize market data, and learn from your results. You are in control of what you build and how you use it.
          </p>

          <p className="leading-[1.7]">
            <PERSON><PERSON> does not provide financial advice or recommendations. All tools and agents are user-created, and you are solely responsible for their use and any outcomes. We provide the platform and technology—what you build is up to you.
          </p>

          <p className="leading-[1.7]">
            We're committed to transparency, security, and empowering our users to innovate in the world of trading. If you have feedback or ideas, we want to hear from you as we continue to evolve Osis for the next generation of traders and builders.
          </p>

          <p className="leading-[1.7] text-white/[0.6] text-sm mt-8">
            <strong>Disclaimer:</strong> Osis is a technology platform only. All content, tools, and agents are for informational and educational purposes. You are solely responsible for your use of the platform and any tools you create. Osis does not provide investment advice or guarantee any outcomes.
          </p>
        </div>
      </main>
    </div>
  );
}