import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { ExternalLink, Calendar, Newspaper } from 'lucide-react';

interface NewsArticle {
  id: string;
  title: string;
  description: string;
  url: string;
  published_utc: string;
  ticker: string;
  source: string;
  image_url?: string;
  sentiment?: 'positive' | 'negative' | 'neutral';
}

interface PortfolioStock {
  ticker: string;
  allocation: number;
}

interface Portfolio {
  id: string;
  name: string;
  description: string;
  created_at: string;
  stocks?: PortfolioStock[];
}

const PortfolioNews = () => {
  const [portfolios, setPortfolios] = useState<Portfolio[]>([]);
  const [selectedPortfolioId, setSelectedPortfolioId] = useState<string | null>(null);
  const [selectedPortfolio, setSelectedPortfolio] = useState<Portfolio | null>(null);
  const [news, setNews] = useState<NewsArticle[]>([]);
  const [isLoadingPortfolios, setIsLoadingPortfolios] = useState(true);
  const [isLoadingNews, setIsLoadingNews] = useState(false);
  const [activeTab, setActiveTab] = useState("all");
  const { toast } = useToast();
  const navigate = useNavigate();

  // Load saved portfolios when the component mounts
  useEffect(() => {
    loadSavedPortfolios();
  }, []);

  // Load news when a portfolio is selected
  useEffect(() => {
    if (selectedPortfolioId) {
      loadPortfolioNews(selectedPortfolioId);
    }
  }, [selectedPortfolioId]);

  const loadSavedPortfolios = async () => {
    setIsLoadingPortfolios(true);
    try {
      // Get the current session for authentication
      const { data: sessionData } = await supabase.auth.getSession();

      // Call the Supabase edge function to list portfolios
      const { data, error } = await supabase.functions.invoke('portfolio-manager', {
        body: JSON.stringify({
          action: 'list'
        }),
        headers: sessionData?.session ? {
          Authorization: `Bearer ${sessionData.session.access_token}`
        } : undefined
      });

      if (error) throw error;

      setPortfolios(data?.portfolios || []);

      // If there are portfolios, select the first one by default
      if (data?.portfolios && data.portfolios.length > 0) {
        setSelectedPortfolioId(data.portfolios[0].id);
        setSelectedPortfolio(data.portfolios[0]);
      }
    } catch (error) {
      console.error('Error loading portfolios:', error);
      toast({
        title: "Loading Failed",
        description: "Failed to load saved portfolios.",
        variant: "destructive"
      });
    } finally {
      setIsLoadingPortfolios(false);
    }
  };

  const loadPortfolioNews = async (portfolioId: string) => {
    setIsLoadingNews(true);
    setNews([]);
    try {
      // Get the current session for authentication
      const { data: sessionData } = await supabase.auth.getSession();
      console.log('Session data:', sessionData?.session ? 'Authenticated' : 'Not authenticated');

      // Find the selected portfolio
      const portfolio = portfolios.find(p => p.id === portfolioId);
      if (portfolio) {
        console.log('Selected portfolio:', portfolio.name, portfolio.id);
        setSelectedPortfolio(portfolio);
      } else {
        console.warn('Portfolio not found in local state:', portfolioId);
      }

      // Call the Supabase edge function to get news for the portfolio
      console.log('Calling portfolio-news edge function with portfolioId:', portfolioId);

      // Prepare the request body
      const requestBody: any = {
        action: 'getPortfolioNews',
        portfolioId
      };

      // If this is a generated portfolio, we need to pass the stock tickers
      if (portfolioId === 'generated-portfolio' && portfolio) {
        // Extract tickers from the portfolio
        const stockTickers = portfolio.stocks?.map(stock => stock.ticker) || [];
        if (stockTickers.length > 0) {
          requestBody.stockTickers = stockTickers;
          console.log('Adding stock tickers to request:', stockTickers);
        }
      }

      console.log('Request body:', requestBody);

      const { data, error } = await supabase.functions.invoke('portfolio-news', {
        body: JSON.stringify(requestBody),
        headers: sessionData?.session ? {
          Authorization: `Bearer ${sessionData.session.access_token}`
        } : undefined
      });

      if (error) {
        console.error('Error from portfolio-news edge function:', error);
        throw error;
      }

      console.log('Response from portfolio-news edge function:', data);

      if (data?.news) {
        console.log('Setting news state with', data.news.length, 'articles');
        console.log('First article sample:', data.news[0]);
        setNews(data.news);
      } else {
        console.warn('No news data returned from edge function');
        console.log('Full response:', data);
      }
    } catch (error) {
      console.error('Error loading portfolio news:', error);
      toast({
        title: "Loading Failed",
        description: "Failed to load news for this portfolio.",
        variant: "destructive"
      });
    } finally {
      setIsLoadingNews(false);
    }
  };

  // Get unique tickers from news articles
  const uniqueTickers = [...new Set(news.map(article => article.ticker))];

  // Filter news based on active tab
  const filteredNews = activeTab === "all"
    ? news
    : news.filter(article => article.ticker === activeTab);

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get sentiment badge color
  const getSentimentColor = (sentiment?: string) => {
    if (!sentiment) return "bg-gray-500";

    switch(sentiment.toLowerCase()) {
      case 'positive':
        return "bg-green-500";
      case 'negative':
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  // Navigate to portfolio builder
  const goToPortfolioBuilder = () => {
    navigate('/portfolio-builder');
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Portfolio News</h1>
        <Button onClick={goToPortfolioBuilder}>
          Build Portfolios
        </Button>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Select Portfolio</CardTitle>
          <CardDescription>
            Choose a portfolio to see news related to its stocks
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoadingPortfolios ? (
            <Skeleton className="h-10 w-full" />
          ) : portfolios.length === 0 ? (
            <div className="text-center py-4">
              <p className="text-muted-foreground mb-4">
                You don't have any saved portfolios yet.
              </p>
              <Button onClick={goToPortfolioBuilder}>
                Create a Portfolio
              </Button>
            </div>
          ) : (
            <Select
              value={selectedPortfolioId || undefined}
              onValueChange={(value) => setSelectedPortfolioId(value)}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a portfolio" />
              </SelectTrigger>
              <SelectContent position="popper" side="bottom" align="start">
                {portfolios.map((portfolio) => (
                  <SelectItem key={portfolio.id} value={portfolio.id}>
                    {portfolio.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </CardContent>
      </Card>

      {selectedPortfolio && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>{selectedPortfolio.name}</CardTitle>
            <CardDescription>
              {selectedPortfolio.description}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="mb-4">
                <TabsTrigger value="all">All News</TabsTrigger>
                {uniqueTickers.map(ticker => (
                  <TabsTrigger key={ticker} value={ticker}>
                    {ticker}
                  </TabsTrigger>
                ))}
              </TabsList>

              <TabsContent value={activeTab} className="space-y-4">
                {isLoadingNews ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {[...Array(6)].map((_, index) => (
                      <Card key={index} className="overflow-hidden flex flex-col h-full">
                        <Skeleton className="h-48 w-full" />
                        <CardContent className="p-4">
                          <div className="flex justify-between mb-2">
                            <Skeleton className="h-4 w-20" />
                            <Skeleton className="h-4 w-24" />
                          </div>
                          <Skeleton className="h-6 w-full mb-2" />
                          <Skeleton className="h-4 w-full mb-1" />
                          <Skeleton className="h-4 w-full mb-1" />
                          <Skeleton className="h-4 w-3/4 mb-4" />
                          <Skeleton className="h-8 w-full" />
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : filteredNews.length === 0 ? (
                  <div className="text-center py-8">
                    <Newspaper className="mx-auto h-12 w-12 text-muted-foreground mb-2" />
                    <p className="text-muted-foreground">
                      No news found for this portfolio.
                    </p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {filteredNews.map((article) => (
                      <Card key={article.id} className="overflow-hidden flex flex-col h-full hover:shadow-lg transition-shadow duration-200">
                        <div className="relative">
                          {/* Image container with fixed height */}
                          <div className="w-full h-48 bg-muted/30 relative overflow-hidden">
                            {article.image_url ? (
                              <img
                                src={article.image_url}
                                key={article.id || article.title} // Force re-render when article changes
                                alt={article.title}
                                className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                                onError={(e) => {
                                  // Create a colored background with ticker if image fails
                                  const ticker = article.ticker;
                                  const element = e.target as HTMLImageElement;
                                  element.style.display = 'none';

                                  // Find the parent div and add the ticker as text
                                  const parent = element.parentElement;
                                  if (parent) {
                                    // Create a gradient based on the ticker (for visual variety)
                                    const hash = ticker.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
                                    const hue1 = hash % 360;
                                    const hue2 = (hash * 1.5) % 360;
                                    parent.style.background = `linear-gradient(135deg, hsl(${hue1}, 70%, 15%) 0%, hsl(${hue2}, 70%, 25%) 100%)`;
                                    parent.style.display = 'flex';
                                    parent.style.alignItems = 'center';
                                    parent.style.justifyContent = 'center';

                                    // Add ticker text if it doesn't exist
                                    if (!parent.querySelector('.ticker-placeholder')) {
                                      const tickerElement = document.createElement('span');
                                      tickerElement.className = 'ticker-placeholder';
                                      tickerElement.textContent = ticker;
                                      tickerElement.style.fontSize = '2.5rem';
                                      tickerElement.style.fontWeight = 'bold';
                                      tickerElement.style.color = 'rgba(255, 255, 255, 0.3)';
                                      parent.appendChild(tickerElement);
                                    }
                                  }
                                }}
                              />
                            ) : (
                              // Placeholder if no image
                              <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-800 to-gray-900 overflow-hidden relative">
                                <div className="absolute inset-0 opacity-20 bg-pattern-grid"></div>
                                <div className="z-10 flex flex-col items-center justify-center">
                                  <span className="text-4xl font-bold text-white/40 mb-1">{article.ticker}</span>
                                  <span className="text-xs text-white/30 px-2 text-center">{article.title.substring(0, 30)}...</span>
                                </div>
                              </div>
                            )}

                            {/* Ticker badge overlay */}
                            <div className="absolute top-2 left-2">
                              <Badge variant="secondary" className="font-bold">
                                {article.ticker}
                              </Badge>
                            </div>

                            {/* Sentiment badge if available */}
                            {article.sentiment && (
                              <div className="absolute top-2 right-2">
                                <Badge className={getSentimentColor(article.sentiment)}>
                                  {article.sentiment}
                                </Badge>
                              </div>
                            )}
                          </div>
                        </div>

                        <CardContent className="flex-1 flex flex-col p-4">
                          <div className="mb-2 flex items-center justify-between">
                            <span className="text-xs text-muted-foreground">{article.source}</span>
                            <div className="flex items-center text-xs text-muted-foreground">
                              <Calendar className="h-3 w-3 mr-1" />
                              <span>{formatDate(article.published_utc)}</span>
                            </div>
                          </div>

                          <h3 className="font-medium text-base mb-2 line-clamp-2">{article.title}</h3>

                          <p className="text-sm text-muted-foreground mb-4 line-clamp-3 flex-1">
                            {article.description}
                          </p>

                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full mt-auto"
                            onClick={() => window.open(article.url, '_blank')}
                          >
                            <ExternalLink className="h-4 w-4 mr-2" />
                            Read Full Article
                          </Button>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default PortfolioNews;
