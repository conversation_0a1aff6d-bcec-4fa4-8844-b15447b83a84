export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // ... existing validation ...

  try {
    const { priceId } = req.body;

    const paymentLink = await stripe.paymentLinks.create({
      _items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      allow_promotion_codes: true,
      after_completion: {
        type: 'redirect',
        redirect: {
          url: `${process.env.NEXTAUTH_URL}/dashboard?payment=success`
        }
      }
    });

    return res.status(200).json({ url: paymentLink.url });
  } catch (error) {
    // ... error handling ...
  }
} 