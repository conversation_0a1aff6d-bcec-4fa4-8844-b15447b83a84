import { useState, useEffect } from 'react';
import { MessageSquare, Check } from 'lucide-react';
import { useSubscription, SUBSCRIPTION_TYPES } from '@/hooks/useSubscription';
import { useUserLimits } from '@/hooks/useUserLimits';
import { useSubscriptionType } from '@/hooks/useSubscriptionType';
import { useToast } from '@/hooks/use-toast';
import { useLocation, useNavigate } from 'react-router-dom';
import { Progress } from '@/components/ui/progress';
import { supabase } from '@/integrations/supabase/client';

import { motion } from 'framer-motion';
import StockPriceDisplay from '@/components/StockPriceDisplay';
import { AdvancedStockChart } from '@/components/charts/AdvancedStockChart';
import { LoadingMessage } from '@/components/chat/LoadingStates';
import { loadStripe } from '@stripe/stripe-js';
import { getPriceIdForPlanType } from '@/utils/planUtils';
import { getAffiliateCode } from '@/utils/growiUtils';

// Stripe configuration - Using only legacy account for all users
const STRIPE_CONFIG = {
  publicKey: 'pk_live_51Qq1bADebmd1GpTvtrUIe0foCjJicP6lNCnMklfLEVs85JSDQqSEVQUmgp2OpR1oYwgcvSOV5oJOx4tzhv4TZZ5W00CXhlInPx',
};

// Paywall pricing configuration
const PAYWALL_VERSIONS = {
  premium: {
    weekly: 19.99
  }
};

// Plans configuration with single Premium plan
const plans = [
  {
    id: {
      weekly: 'price_premium_weekly_placeholder',
      yearly: 'price_premium_yearly_placeholder'
    },
    name: 'Premium',
    type: SUBSCRIPTION_TYPES.premium,
    price: {
      weekly: PAYWALL_VERSIONS.premium.weekly,
      yearly: 720 // Annual price
    },
    period: 'per week',
    description: 'Complete access to all features',
    features: [
      'All 7 AI Agents',
      'Stock and Crypto Analysis',
      'Instant Trading Information',
      'Unlimited messages',
      'Unlimited Portfolio Backtesting',
      'Unlimited Portfolios',
      'Agent Builder Access',
      'Discover Community Agents',
      'Advanced Stock Search',
      'Stock Scanner Tools',
      'Priority Support',
      'Advanced Analytics',
      'Custom Indicators'
    ]
  }
];

// Helper to map plan types to display names
const planTypeToDisplayName = {
  [SUBSCRIPTION_TYPES.premium]: 'Premium'
};

export default function ManageSubscription() {
  const { subscription, isLoadingSubscription, refetch } = useSubscription();
  const { messagesUsed, messagesLimit, messagesRemaining, isLoading: isLoadingLimits, handlePlanUpgrade } = useUserLimits();
  const { subscriptionType, canUpgrade } = useSubscriptionType();
  const [selectedPlan, setSelectedPlan] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const location = useLocation();
  const navigate = useNavigate();
  const [isAnnualPlan, setIsAnnualPlan] = useState(false);

  const [symbol, setSymbol] = useState('');
  const [showAIAnalysis, setShowAIAnalysis] = useState(false);
  const [analysisResults, setAnalysisResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [tradingStrategy, setTradingStrategy] = useState<{
    direction: 'LONG' | 'SHORT';
    confidence: 'HIGH' | 'MEDIUM' | 'LOW';
    entryPoints: Array<{ price: number; timestamp: number; label: string }>;
    exitPoints: Array<{ price: number; timestamp: number; label: string }>;
    stopLoss: number;
    supportLevels: number[];
    resistanceLevels: number[];
    takeProfitTargets: Array<{ price: number; timestamp: number; label: string }>;
    riskRewardRatio: string;
  }>({
    direction: 'LONG',
    confidence: 'MEDIUM',
    entryPoints: [],
    exitPoints: [],
    stopLoss: 0,
    supportLevels: [],
    resistanceLevels: [],
    takeProfitTargets: [],
    riskRewardRatio: '1:1'
  });
  const [symbolTypes, setSymbolTypes] = useState({});

  // Set initial annual/monthly state based on subscription
  useEffect(() => {
    if (subscription?.interval === 'year') {
      setIsAnnualPlan(true);
    } else {
      setIsAnnualPlan(false);
    }
  }, [subscription]);

  // No longer need to fetch user creation date since we use legacy account for all users

  // Handle Stripe redirect with query parameters
  useEffect(() => {
    const handleStripeRedirect = async () => {
      // Create a URL object to easily parse query parameters
      const url = new URL(window.location.href);
      const sessionId = url.searchParams.get('session_id');
      const success = url.searchParams.get('success');
      const customerId = url.searchParams.get('customer_id');

      // Only process if we have a session ID
      if (sessionId) {
        setIsLoading(true);

        try {
          // Handle the checkout redirect
          const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
            },
            body: JSON.stringify({
              action: 'handle-checkout-redirect',
              sessionId,
              customerId
            })
          });

          const { session, error } = await response.json();

          if (error) {
            throw new Error(error);
          }

          if (session?.status === 'complete') {
            toast({
              title: "Subscription Updated",
              description: "Your subscription has been updated successfully.",
            });

            // Check if user profile exists
            const { data: { session: authSession } } = await supabase.auth.getSession();
            if (authSession?.user) {
              const { data: profileExists, error: profileCheckError } = await supabase
                .from('profiles')
                .select('id')
                .eq('id', authSession.user.id)
                .maybeSingle();

              // If profile doesn't exist, create it
              if (!profileExists) {
                const { error: createError } = await supabase
                  .from('profiles')
                  .insert({
                    id: authSession.user.id,
                    subscription_type: null,
                    has_seen_onboarding: false,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                  });

                if (createError) {
                  console.error('Error creating profile:', createError);
                }
              }
            }

            // Refresh the onboarding status to hide the onboarding component
            // @ts-ignore - Accessing the function we added to window
            if (window.refreshOnboardingStatus) {
              try {
                // @ts-ignore
                window.refreshOnboardingStatus();
              } catch (error) {
                console.error('Error calling refreshOnboardingStatus:', error);
              }
            }
          }

          // Refresh subscription data and limits
          await refetch();
          await handlePlanUpgrade();

          // Remove query parameters from URL
          navigate('/subscription/manage', { replace: true });
        } catch (error) {
          console.error('Error handling Stripe redirect:', error);
          toast({
            title: "Update Failed",
            description: "There was an error processing your subscription update. Please try again.",
            variant: "destructive",
          });
        } finally {
          setIsLoading(false);
        }
      }
    };

    handleStripeRedirect();
  }, [location, navigate, toast, setIsLoading, refetch, handlePlanUpgrade]);

  // If subscription or limits data changes, update the UI
  useEffect(() => {
    if (!subscription || isLoadingSubscription || isLoadingLimits) return;

    // Use subscription_type to determine selected plan
    if (subscription.subscription_type) {
      // Find plan by type
      const plan = plans.find(p => p.type === subscription.subscription_type);
      if (plan) {
        setSelectedPlan(plan.id.weekly);
        return;
      }
    }

    // Fallback to free plan
    setSelectedPlan('price_free');
  }, [subscription, isLoadingSubscription, isLoadingLimits, plans]);

  // Refresh data function
  const refreshData = async () => {
    setIsLoading(true);

    try {
      // Refresh subscription and limits data
      await refetch();
      // Use handlePlanUpgrade for a more thorough refresh with forced reset
      await handlePlanUpgrade();

      toast({
        title: "Data Refreshed",
        description: "Your subscription information has been updated.",
      });
    } catch (error) {
      console.error('Error refreshing data:', error);
      toast({
        title: "Refresh Failed",
        description: "Could not refresh your subscription data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Get current plan display name
  const getCurrentPlanName = () => {
    if (!subscription || isLoadingSubscription) return 'Free';

    // Use subscription_type if available
    if (subscription.subscription_type) {
      return planTypeToDisplayName[subscription.subscription_type] || 'Free';
    }

    return 'Free';
  };

  // Get the Stripe billing portal URL - Always use legacy account
  const getStripeBillingPortalUrl = () => {
    return 'https://billing.stripe.com/p/login/fZeaGq9Iw2hAg929AA';
  };

  // Handle manage subscription button click
  const handleManageSubscription = () => {
    const billingPortalUrl = getStripeBillingPortalUrl();
    if (billingPortalUrl) {
      window.open(billingPortalUrl, '_blank');
    } else {
      toast({
        title: "Loading...",
        description: "Please wait while we prepare your billing portal.",
        variant: "default",
      });
    }
  };

  // Handle plan selection and update
  const handleSelectPlan = async (planType: string) => {
    // Don't do anything if this is already the current plan
    if (isCurrentPlan(planType)) {
      return;
    }

    setIsLoading(true);

    try {
      const planToSelect = plans.find(p => p.type === planType);
      if (!planToSelect) {
        throw new Error("Invalid plan selected");
      }

      // For any paid plan selection, use Stripe checkout
      if (planToSelect.name !== 'Free') {
        try {
          // Determine if this is an upgrade/downgrade or new subscription
          const isExistingSubscriber = getCurrentPlanName() !== 'Free';
          const actionToUse = isExistingSubscriber ? 'update-subscription' : 'create-checkout-session';

          console.log('🔄 ManageSubscription action:', {
            currentPlan: getCurrentPlanName(),
            targetPlan: planToSelect.name,
            isExistingSubscriber,
            actionToUse
          });

          // Check for no-trial parameters in the URL
          const urlParams = new URLSearchParams(window.location.search);
          const premiumNoTrial = urlParams.get('premiumnotrial') !== null;
          const annual = urlParams.get('annual') !== null;
          const skipTrial = premiumNoTrial;

          // Get affiliate code for Growi tracking
          const affiliateCode = getAffiliateCode();

          const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe-checkout`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
            },
            body: JSON.stringify({
              action: actionToUse,
              priceId: getPriceIdForPlanType('premium', annual ? 'yearly' : 'weekly'),
              returnUrl: window.location.origin + '/subscription/manage?success=true',
              skipTrial,
              affiliateCode
            })
          });

          if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Server returned ${response.status}: ${errorText}`);
          }

          const responseData = await response.json();

          const { sessionId, error } = responseData;

          if (error) {
            throw new Error(error);
          }

          if (!sessionId) {
            throw new Error('No session ID returned from server');
          }

          // ALL REDIRECT CODE REMOVED
          throw new Error('Redirect functionality removed');

          // Otherwise use the sessionId with Stripe.js
          if (sessionId) {
            // Redirect to Stripe checkout
            const stripe = await loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);
            if (!stripe) {
              throw new Error("Stripe failed to load");
            }

            const { error: redirectError } = await stripe.redirectToCheckout({ sessionId });
            if (redirectError) {
              throw new Error(`Stripe redirect failed: ${redirectError.message}`);
            }
          }
        } catch (error) {
          throw error;
        }
      }
      // Current plan is paid and downgrading to Free - show message to use Manage Subscription
      else if (getCurrentPlanName() !== 'Free' && planToSelect.name === 'Free') {
        // Instead of handling cancellation here, show guidance message
        toast({
          title: "Downgrade Not Available Here",
          description: "To downgrade to the Free plan, please click the 'Manage Subscription' button and cancel your subscription through Stripe.",
          variant: "destructive",
        });
      }

    } catch (error) {
      console.error('Error selecting plan:', error);
      toast({
        title: "Plan Change Failed",
        description: `Error: ${error.message || 'Unknown error occurred'}`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to check if a plan is the current plan
  const isCurrentPlan = (planType: string) => {
    if (!subscription || isLoadingSubscription) {
      return planType === SUBSCRIPTION_TYPES.premium;
    }

    return subscription.subscription_type === planType;
  };

  // Helper function to check if a plan is lower than current plan
  const isLowerTier = (planType: string) => {
    // No downgrades available with single Premium plan
    return false;
  };

  // Calculate message usage percentage
  const messageUsagePercentage = messagesLimit > 0 ? (messagesUsed / messagesLimit) * 100 : 0;
  const currentPlanName = getCurrentPlanName();

  const shouldShowChart = () => {
    return symbol && symbolTypes?.[symbol] !== 'CRYPTO';
  };

  return (
    <div className="h-full bg-[#0A0A0A] text-white flex flex-col font-hanken-grotesk relative">
      {/* Loading Overlay */}
      {isLoading && (
        <div className="fixed inset-0 bg-black bg-opacity-70 z-50 flex items-center justify-center backdrop-blur-sm animate-fade-in">
          <div className="bg-[#141414]/90 p-8 rounded-xl border border-[#303035]/60 text-center shadow-lg backdrop-blur-md">
            <div className="flex justify-center mb-4">
              <svg className="animate-spin h-10 w-10" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
            <h3 className="text-lg font-medium text-white mb-2">Processing Your Request</h3>
            <p className="text-white/60 text-sm">
              Please wait while we update your subscription...
            </p>
          </div>
        </div>
      )}

      <div className="flex-1 px-6 py-6 overflow-y-auto">
        {/* Header with Manage Button */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-xl font-medium text-white mb-1 font-sans">
              Subscription
            </h1>
            <p className="text-white/60 text-sm font-sans">
              Current plan: <span className="text-white">{currentPlanName}</span>
            </p>
          </div>
          <button
            onClick={() => window.open('https://billing.stripe.com/p/login/fZeaGq9Iw2hAg929AA', '_blank')}
            disabled={isLoading}
            className="px-4 py-2 bg-white/[0.03] hover:bg-white/[0.06] text-white/80 border border-white/[0.08] rounded-md transition-colors duration-200 font-sans disabled:opacity-50 text-sm"
          >
            {isLoading ? 'Processing...' : 'Manage Subscription'}
          </button>
        </div>

        {/* Message Usage - Compact */}
        {(
          <div className="max-w-md mx-auto mb-8">
            <div className="bg-white/[0.02] rounded-lg p-4 border border-white/[0.06]">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-white/70 font-sans">Messages</span>
                <span className="text-xs text-white/50 font-sans">{messagesUsed}/{messagesLimit}</span>
              </div>

              {isLoadingLimits ? (
                <div className="h-8 flex items-center justify-center">
                  <div className="w-3 h-3 border border-white/20 border-t-white/60 rounded-full animate-spin"></div>
                </div>
              ) : (
                <>
                  <Progress
                    value={messageUsagePercentage}
                    className="h-1.5 bg-white/10"
                    indicatorClassName={
                      messageUsagePercentage > 90 ? "bg-red-500" :
                      messageUsagePercentage > 75 ? "bg-amber-500" :
                      "bg-emerald-500"
                    }
                  />
                  <p className="mt-2 text-white/40 text-xs font-sans">
                    {messagesRemaining} remaining this cycle
                  </p>
                </>
              )}
            </div>
          </div>
        )}

        {/* Premium Plan Information */}
        <div className="max-w-2xl mx-auto mb-8">
          <div className="bg-white/[0.02] rounded-lg p-6 border border-white/[0.06]">
            <div className="text-center mb-4">
              <h3 className="text-lg font-medium text-white mb-2 font-sans">Premium Plan Active</h3>
              <p className="text-white/60 text-sm font-sans">
                You have full access to all features
              </p>
            </div>

            <div className="grid grid-cols-2 gap-3 text-xs">
              {plans[0].features.map((feature, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div className="w-1 h-1 bg-green-400 rounded-full flex-shrink-0"></div>
                  <span className="text-white/70 font-sans">{feature}</span>
                </div>
              ))}
            </div>
          </div>
        </div>



        {/* Manage Subscription Button */}
        {getCurrentPlanName() !== 'Free' && (
          <div className="max-w-2xl mx-auto text-center mb-12">
            <button
              onClick={handleManageSubscription}
              disabled={isLoading}
              className="px-6 py-2.5 bg-white/[0.03] hover:bg-white/[0.06] text-white/80 border border-white/[0.08] rounded-lg transition-colors duration-200 font-sans disabled:opacity-50"
            >
              {isLoading ? 'Processing...' : 'Manage Subscription'}
            </button>
            <p className="text-white/50 text-sm mt-3 font-sans">
              Cancel anytime • Manage billing and payment methods
            </p>
          </div>
        )}


      </div>

      {/* Global Styles - Adding consistent styling with ChatInterface */}
      <style>
        {`
          /* Define fade-in animation */
          @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
          }

          .animate-fade-in {
            animation: fadeIn 0.15s ease-out forwards;
          }

          /* Fix for flashing focus states */
          *:focus {
            outline: none !important;
          }

          /* Remove default focus rings and overlays */
          *:focus-visible {
            outline: none !important;
            box-shadow: none !important;
          }

          /* Fix transitions to prevent flashing */
          button, input, textarea, select {
            transition: border-color 0.15s, background-color 0.15s !important;
            outline: none !important;
          }

          /* Ensure no overlay appears on focus/blur */
          button:focus, input:focus, textarea:focus, select:focus,
          button:active, input:active, textarea:active, select:active {
            box-shadow: none !important;
            outline: none !important;
            border-color: #303035 !important;
          }

          /* Fix for flashing box when clicking outside */
          div[role="dialog"]:not(:focus-within),
          button:not(:focus-within),
          input:not(:focus-within),
          select:not(:focus-within) {
            transition: none !important;
          }

          /* Fix for button hover states */
          button:hover {
            transition: background-color 0.15s ease !important;
          }

          /* Prevent any flashing on blur */
          *:not(:focus-visible) {
            transition: border-color 0.15s ease, background-color 0.15s ease !important;
          }

          /* Prevent any animations or transitions on form controls when they lose focus */
          button:not(:focus):not(:hover),
          input:not(:focus),
          textarea:not(:focus),
          select:not(:focus) {
            transition: none !important;
          }

          /* Original styles from before */
          textarea:focus-visible {
            outline: none !important;
            border-color: #303035 !important;
            box-shadow: none !important;
            ring: 0 !important;
          }

          textarea:focus {
            outline: none !important;
            border-color: #303035 !important;
            box-shadow: none !important;
            ring: 0 !important;
          }

          /* Add subtle transition effects */
          textarea, button {
            transition: all 0.15s ease;
          }

          /* Darker placeholder text on focus */
          textarea:focus::placeholder {
            color: rgba(255, 255, 255, 0.2);
          }
        `}
      </style>
    </div>
  );
}