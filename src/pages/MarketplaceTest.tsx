import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { getPaidMarketplaceAgents } from '@/services/marketplaceService';
import { getFreeMarketplaceAgents } from '@/services/discoverService';
import { runMarketplaceSyncTests } from '@/tests/marketplace-sync-test';
import { runMarketplaceIsolationTests } from '@/tests/marketplace-isolation-test';
import { supabase } from '@/integrations/supabase/client';

interface MarketplaceAgent {
  id: string;
  name: string;
  description?: string;
  price?: number;
  is_paid?: boolean;
  marketplace_type?: string;
  seller_name?: string;
  tags?: string[];
  likes_count?: number;
  sales_count?: number;
}

interface TestResult {
  testName: string;
  passed: boolean;
  error?: string;
  details?: any;
}

export default function MarketplaceTest() {
  const [paidAgents, setPaidAgents] = useState<MarketplaceAgent[]>([]);
  const [freeAgents, setFreeAgents] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isolationResults, setIsolationResults] = useState<TestResult[]>([]);
  const [testRunning, setTestRunning] = useState(false);
  const [dataConsistency, setDataConsistency] = useState<any>(null);

  const loadMarketplaceAgents = async () => {
    setLoading(true);
    try {
      // Load paid agents with strict isolation
      const paidResult = await getPaidMarketplaceAgents({ limit: 20 });
      if (paidResult.success) {
        setPaidAgents(paidResult.agents);
        console.log('Loaded paid agents:', paidResult.agents);
      } else {
        console.error('Failed to load paid agents:', paidResult.error);
      }

      // Load free agents with strict isolation
      const freeResult = await getFreeMarketplaceAgents({ limit: 20 });
      if (freeResult.success) {
        setFreeAgents(freeResult.agents);
        console.log('Loaded free agents:', freeResult.agents);
      } else {
        console.error('Failed to load free agents:', freeResult.error);
      }
    } catch (error) {
      console.error('Error loading agents:', error);
    } finally {
      setLoading(false);
    }
  };

  const runSyncTests = async () => {
    setTestRunning(true);
    try {
      const results = await runMarketplaceSyncTests();
      setTestResults(results);
    } catch (error) {
      console.error('Error running sync tests:', error);
    } finally {
      setTestRunning(false);
    }
  };

  const runIsolationTests = async () => {
    setTestRunning(true);
    try {
      const results = await runMarketplaceIsolationTests();
      setIsolationResults(results);
    } catch (error) {
      console.error('Error running isolation tests:', error);
    } finally {
      setTestRunning(false);
    }
  };

  const checkDataConsistency = async () => {
    try {
      // Check for inconsistent states
      const { data: inconsistentAgents, error } = await supabase
        .rpc('check_marketplace_consistency');

      if (error) {
        console.error('Error checking consistency:', error);
        return;
      }

      // Manual query for consistency check
      const { data: agents, error: agentsError } = await supabase
        .from('agents')
        .select(`
          id, name, is_public, is_for_sale, price,
          published_agents(id, is_active)
        `);

      if (agentsError) {
        console.error('Error fetching agents:', agentsError);
        return;
      }

      const inconsistent = agents?.filter(agent => {
        const hasPublishedEntry = agent.published_agents && agent.published_agents.length > 0 && agent.published_agents[0].is_active;
        return (
          (agent.is_public && !hasPublishedEntry && !agent.is_for_sale) ||
          (!agent.is_public && hasPublishedEntry)
        );
      }) || [];

      setDataConsistency({
        totalAgents: agents?.length || 0,
        inconsistentAgents: inconsistent.length,
        details: inconsistent
      });
    } catch (error) {
      console.error('Error checking data consistency:', error);
    }
  };

  useEffect(() => {
    loadMarketplaceAgents();
    checkDataConsistency();
  }, []);

  return (
    <div className="min-h-screen bg-black text-white p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-2">Marketplace Synchronization Test</h1>
          <p className="text-white/60">Testing agent marketplace visibility and status consistency</p>
        </div>

        {/* Test Controls */}
        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle>Test Controls</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <Button
                onClick={loadMarketplaceAgents}
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {loading ? 'Loading...' : 'Reload Agents'}
              </Button>
              <Button
                onClick={runSyncTests}
                disabled={testRunning}
                className="bg-green-600 hover:bg-green-700"
              >
                {testRunning ? 'Running...' : 'Sync Tests'}
              </Button>
              <Button
                onClick={runIsolationTests}
                disabled={testRunning}
                className="bg-red-600 hover:bg-red-700"
              >
                {testRunning ? 'Running...' : 'Isolation Tests'}
              </Button>
              <Button
                onClick={checkDataConsistency}
                className="bg-purple-600 hover:bg-purple-700"
              >
                Data Consistency
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Data Consistency Results */}
        {dataConsistency && (
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle>Data Consistency Check</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">{dataConsistency.totalAgents}</div>
                  <div className="text-sm text-white/60">Total Agents</div>
                </div>
                <div className="text-center">
                  <div className={`text-2xl font-bold ${dataConsistency.inconsistentAgents === 0 ? 'text-green-400' : 'text-red-400'}`}>
                    {dataConsistency.inconsistentAgents}
                  </div>
                  <div className="text-sm text-white/60">Inconsistent</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">
                    {dataConsistency.totalAgents - dataConsistency.inconsistentAgents}
                  </div>
                  <div className="text-sm text-white/60">Consistent</div>
                </div>
              </div>
              {dataConsistency.inconsistentAgents > 0 && (
                <div className="mt-4">
                  <h4 className="font-semibold text-red-400 mb-2">Inconsistent Agents:</h4>
                  <div className="space-y-2">
                    {dataConsistency.details.map((agent: any) => (
                      <div key={agent.id} className="bg-red-900/20 p-2 rounded text-sm">
                        <strong>{agent.name}</strong> - Public: {agent.is_public ? 'Yes' : 'No'}, 
                        For Sale: {agent.is_for_sale ? 'Yes' : 'No'}, 
                        Published: {agent.published_agents?.length > 0 ? 'Yes' : 'No'}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Sync Test Results */}
        {testResults.length > 0 && (
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle>Sync Test Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {testResults.map((result, index) => (
                  <div key={index} className={`p-3 rounded flex items-center justify-between ${
                    result.passed ? 'bg-green-900/20 border border-green-800' : 'bg-red-900/20 border border-red-800'
                  }`}>
                    <div>
                      <span className={`font-semibold ${result.passed ? 'text-green-400' : 'text-red-400'}`}>
                        {result.passed ? '✅' : '❌'} {result.testName}
                      </span>
                      {result.error && (
                        <div className="text-sm text-red-300 mt-1">{result.error}</div>
                      )}
                      {result.details && (
                        <div className="text-sm text-white/60 mt-1">
                          {JSON.stringify(result.details)}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Isolation Test Results */}
        {isolationResults.length > 0 && (
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle>🔒 Marketplace Isolation Test Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {isolationResults.map((result, index) => (
                  <div key={index} className={`p-3 rounded flex items-center justify-between ${
                    result.passed ? 'bg-green-900/20 border border-green-800' : 'bg-red-900/20 border border-red-800'
                  }`}>
                    <div>
                      <span className={`font-semibold ${result.passed ? 'text-green-400' : 'text-red-400'}`}>
                        {result.passed ? '✅' : '❌'} {result.testName}
                      </span>
                      {result.error && (
                        <div className="text-sm text-red-300 mt-1">{result.error}</div>
                      )}
                      {result.details && (
                        <div className="text-sm text-white/60 mt-1">
                          {JSON.stringify(result.details)}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Paid Marketplace Agents */}
        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle>💰 Paid Marketplace Agents ({paidAgents.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {paidAgents.length === 0 ? (
              <div className="text-center py-8 text-white/60">
                {loading ? 'Loading paid agents...' : 'No paid marketplace agents found'}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {paidAgents.map((agent) => (
                  <div key={agent.id} className="bg-gray-800 p-4 rounded-lg border border-green-700">
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="font-semibold text-white truncate">{agent.name}</h3>
                      <Badge variant="default" className="bg-green-600">
                        ${agent.price}
                      </Badge>
                    </div>
                    <p className="text-sm text-white/60 mb-3 line-clamp-2">
                      {agent.description || 'No description'}
                    </p>
                    <div className="flex items-center justify-between text-xs text-white/50">
                      <span>By {agent.seller_name || 'Unknown'}</span>
                      <div className="flex gap-2">
                        <span>❤️ {agent.likes_count || 0}</span>
                        <span>📊 {agent.sales_count || 0}</span>
                      </div>
                    </div>
                    <div className="mt-2">
                      <Badge variant="outline" className="text-xs border-green-600 text-green-400">
                        PAID
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Free Marketplace Agents */}
        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle>🆓 Free Marketplace Agents ({freeAgents.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {freeAgents.length === 0 ? (
              <div className="text-center py-8 text-white/60">
                {loading ? 'Loading free agents...' : 'No free marketplace agents found'}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {freeAgents.map((agent) => (
                  <div key={agent.id || agent.agentId} className="bg-gray-800 p-4 rounded-lg border border-blue-700">
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="font-semibold text-white truncate">{agent.name}</h3>
                      <Badge variant="secondary" className="bg-blue-600">
                        FREE
                      </Badge>
                    </div>
                    <p className="text-sm text-white/60 mb-3 line-clamp-2">
                      {agent.description || 'No description'}
                    </p>
                    <div className="flex items-center justify-between text-xs text-white/50">
                      <span>By {agent.publisherName || 'Unknown'}</span>
                      <div className="flex gap-2">
                        <span>⭐ {agent.averageRating || 0}</span>
                        <span>📥 {agent.downloadCount || 0}</span>
                      </div>
                    </div>
                    <div className="mt-2">
                      <Badge variant="outline" className="text-xs border-blue-600 text-blue-400">
                        FREE
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
