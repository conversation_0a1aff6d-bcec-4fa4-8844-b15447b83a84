import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Loader2, Clock } from 'lucide-react';
import CleanCandlestickChart from '@/components/charts/CleanCandlestickChart';
import { useToast } from '@/components/ui/use-toast';
import { useUnifiedAuth } from '@/hooks/useUnifiedAuth';
import { useResponsive } from '@/hooks/useResponsive';
import { getAgentsByUserId } from '@/services/agentService';
import { supabase } from '@/integrations/supabase/client';
import { useGamification } from '@/contexts/GamificationContext';
import TradeAnalysisModal from '@/components/modals/TradeAnalysisModal';
import { useLocation, useNavigate } from 'react-router-dom';
import { fetchTradeChartData, adjustTradeToMarketOpen } from '@/services/cleanChartService';

interface Agent {
  id?: string; // Changed to optional based on agentService definition
  name: string;
  description?: string; // Changed to optional based on agentService definition
  configuration: any;
  isMarketplaceAgent?: boolean; // Flag to identify marketplace agents
}

interface BacktestResult {
  symbol: string;
  startDate: string;
  endDate: string;
  totalReturn: number;
  buyAndHoldReturn: number;
  numberOfTrades: number;
  winRate: number;
  maxDrawdown: number;
  sharpeRatio: number;
  trades: Array<{
    date: string;
    type: 'buy' | 'sell';
    price: number;
    signal: string;
    confidence: number;
    agentMetrics?: {
      chartOverlays?: Record<string, any>;
      indicators?: Record<string, any>;
      [key: string]: any;
    };
  }>;
  performanceChart: Array<{
    date: string;
    agentValue: number;
    buyHoldValue: number;
  }>;
  agentConfiguration?: any; // Agent configuration for extracting indicator info
}

// Helper function to extract chart overlay data from agent metrics
const extractChartOverlays = (agentMetrics: any, agentConfiguration: any) => {
  const indicators: any[] = [];
  const supportResistanceLevels: any[] = [];
  const trendLines: any[] = [];
  const breakoutZones: any[] = [];

  if (!agentMetrics?.chartOverlays) {
    return { indicators, supportResistanceLevels, trendLines, breakoutZones };
  }

  Object.entries(agentMetrics.chartOverlays).forEach(([key, overlay]: [string, any]) => {
    switch (overlay.type) {
      case 'moving_average':
      case 'momentum_indicator':
      case 'trend_indicator':
      case 'volume_indicator':
      case 'volatility_indicator':
        indicators.push({
          type: overlay.type,
          indicator: overlay.indicator || overlay.averageType,
          data: overlay.data,
          parameters: overlay.parameters,
          color: getIndicatorColor(overlay.type, overlay.indicator || overlay.averageType)
        });
        break;

      case 'support_resistance':
        if (overlay.data?.targetLevel) {
          supportResistanceLevels.push({
            price: overlay.data.targetLevel,
            type: overlay.data.levelType || 'support',
            strength: overlay.data.confidence || 50,
            touches: overlay.parameters?.minTouches || 2,
            label: `${overlay.data.levelType?.toUpperCase()} $${overlay.data.targetLevel.toFixed(2)}`
          });
        }
        break;

      case 'trend_line':
        if (overlay.data?.startPrice && overlay.data?.endPrice) {
          trendLines.push({
            startTimestamp: overlay.data.startTimestamp || Date.now() - 86400000,
            startPrice: overlay.data.startPrice,
            endTimestamp: overlay.data.endTimestamp || Date.now(),
            endPrice: overlay.data.endPrice,
            type: 'trend'
          });
        }
        break;

      case 'breakout_zone':
        if (overlay.data?.currentPrice && overlay.data?.resistanceLevel) {
          breakoutZones.push({
            timestamp: Date.now(),
            price: overlay.data.currentPrice,
            type: overlay.data.breakoutDirection === 'bullish' ? 'bullish' : 'bearish',
            level: overlay.data.resistanceLevel || overlay.data.supportLevel
          });
        }
        break;
    }
  });

  return { indicators, supportResistanceLevels, trendLines, breakoutZones };
};

// Helper function to get indicator colors
const getIndicatorColor = (type: string, indicator: string): string => {
  const colorMap: Record<string, string> = {
    'sma': '#3b82f6',
    'ema': '#ef4444',
    'vwap': '#10b981',
    'rsi': '#f59e0b',
    'macd': '#8b5cf6',
    'stochastic': '#06b6d4',
    'bollinger_bands': '#ec4899',
    'atr': '#84cc16'
  };

  return colorMap[indicator] || colorMap[type] || '#6b7280';
};

const AgentBacktesting: React.FC = () => {
  const { user } = useUnifiedAuth();
  const { toast } = useToast();
  const { trackAction } = useGamification();
  const { isMobile, classes } = useResponsive();
  const location = useLocation();
  const navigate = useNavigate();

  const [agents, setAgents] = useState<Agent[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<string>('');
  const [selectedSymbol, setSelectedSymbol] = useState<string>('AAPL');
  const [selectedTimeframe, setSelectedTimeframe] = useState<string>('1Y');
  const [selectedInterval, setSelectedInterval] = useState<string>('1D');
  const [isBacktesting, setIsBacktesting] = useState(false);
  const [backtestResult, setBacktestResult] = useState<BacktestResult | null>(null);
  const [loadingAgents, setLoadingAgents] = useState(true);
  const [isTradeModalOpen, setIsTradeModalOpen] = useState(false);
  const [selectedTradeIndex, setSelectedTradeIndex] = useState<number | undefined>(undefined);

  // Main chart state
  const [mainChartData, setMainChartData] = useState<any[]>([]);
  const [isLoadingMainChart, setIsLoadingMainChart] = useState(false);
  const [mainChartTimeframe, setMainChartTimeframe] = useState<'1min' | '5min' | '15min' | '30min' | '1hour' | '4hour' | 'daily'>('1hour');
  const [currentTradeIndex, setCurrentTradeIndex] = useState<number | undefined>(undefined);

  // Chart timeframe options
  const chartTimeframeOptions = [
    { value: '1min' as const, label: '1m', description: '1 minute' },
    { value: '5min' as const, label: '5m', description: '5 minutes' },
    { value: '15min' as const, label: '15m', description: '15 minutes' },
    { value: '30min' as const, label: '30m', description: '30 minutes' },
    { value: '1hour' as const, label: '1h', description: '1 hour' },
    { value: '4hour' as const, label: '4h', description: '4 hours' },
    { value: 'daily' as const, label: '1D', description: '1 day' }
  ];

  // Function to handle trade click - now loads chart data for main view
  const handleTradeClick = async (tradeIndex: number) => {
    if (!backtestResult?.trades || !backtestResult.trades[tradeIndex]) return;

    setCurrentTradeIndex(tradeIndex);
    setIsLoadingMainChart(true);

    try {
      const selectedTrade = backtestResult.trades[tradeIndex];
      const tradeTimestamp = new Date(selectedTrade.date).getTime();
      const adjustedTimestamp = adjustTradeToMarketOpen(tradeTimestamp);

      const chartData = await fetchTradeChartData(
        selectedSymbol,
        adjustedTimestamp,
        mainChartTimeframe
      );

      setMainChartData(chartData);
    } catch (error) {
      console.error('Error loading chart data:', error);
      toast({
        title: 'Chart Error',
        description: 'Failed to load chart data for this trade',
        variant: 'destructive'
      });
    } finally {
      setIsLoadingMainChart(false);
    }
  };

  // Load chart data when timeframe changes
  useEffect(() => {
    if (currentTradeIndex !== undefined && backtestResult?.trades) {
      handleTradeClick(currentTradeIndex);
    }
  }, [mainChartTimeframe]);

  // Function to close trade modal
  const handleCloseTradeModal = () => {
    setIsTradeModalOpen(false);
    setSelectedTradeIndex(undefined);
  };

  // Get current date for reference
  const currentDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

  // Timeframe options (how much historical data to test)
  const timeframes = [
    { value: '3M', label: '3 Months' },
    { value: '6M', label: '6 Months' },
    { value: '1Y', label: '1 Year' },
    { value: '2Y', label: '2 Years' },
    { value: '5Y', label: '5 Years' }
  ];

  // Interval options (how often the agent runs)
  const intervals = [
    { value: '1D', label: 'Daily' },
    { value: '1W', label: 'Weekly' },
    { value: '1M', label: 'Monthly' }
  ];

  // Load user's agents and handle marketplace agent from navigation state
  useEffect(() => {
    const loadAgents = async () => {
      if (!user?.id) return;

      try {
        const userAgents: Agent[] = await getAgentsByUserId(user.id); // Explicitly cast to local Agent type

        // Check if we have a marketplace agent from navigation state
        const marketplaceAgent = location.state?.marketplaceAgent;
        if (marketplaceAgent) {
          // Add the marketplace agent to the list and auto-select it
          const agentWithMarketplaceFlag = {
            ...marketplaceAgent,
            isMarketplaceAgent: true
          };
          setAgents([agentWithMarketplaceFlag, ...userAgents]);
          setSelectedAgent(marketplaceAgent.id);
        } else {
          setAgents(userAgents);
        }
      } catch (error) {
        console.error('Error loading agents:', error);
        toast({
          title: 'Error',
          description: 'Failed to load your agents',
          variant: 'destructive'
        });
      } finally {
        setLoadingAgents(false);
      }
    };

    loadAgents();
  }, [user?.id, toast, location.state]);

  // Run backtest
  const handleBacktest = async () => {
    if (!selectedAgent || !selectedSymbol) {
      toast({
        title: 'Missing Information',
        description: 'Please select an agent and enter a stock symbol',
        variant: 'destructive'
      });
      return;
    }

    console.log('🚀 Starting backtest navigation...', {
      selectedAgent,
      selectedSymbol,
      user: user?.id,
      currentPath: location.pathname
    });

    // Immediately navigate to results page with loading state
    navigate('/backtest-results', {
      state: {
        isRunningBacktest: true,
        agentId: selectedAgent,
        agentName: agents.find(a => a.id === selectedAgent)?.name || 'Unknown Agent',
        selectedSymbol: selectedSymbol.toUpperCase(),
        selectedTimeframe,
        selectedInterval,
        userId: user?.id,
        currentDate: currentDate,
        isMarketplaceAgent: agents.find(a => a.id === selectedAgent)?.isMarketplaceAgent || false,
        agentConfiguration: agents.find(a => a.id === selectedAgent)?.configuration
      }
    });

    console.log('✅ Navigation to /backtest-results initiated');
  };

  if (loadingAgents) {
    return (
      <div className="h-full bg-[#0A0A0A] text-white flex items-center justify-center font-hanken-grotesk">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-white/60" />
          <p className="mt-2 text-sm text-muted-foreground">Loading your agents...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`h-full bg-[#0A0A0A] text-white flex flex-col font-hanken-grotesk ${isMobile ? 'px-3 py-3' : classes.container}`}>
      {/* Header & Subtle Configuration */}
      <div className={`${isMobile ? 'px-3 py-3' : classes.container} py-4 border-b border-white/[0.04]`}>
        <div className={`flex ${isMobile ? 'flex-col gap-4' : 'items-center justify-between'}`}>
          <div>
            <h1 className={`${isMobile ? 'text-2xl' : 'text-4xl'} font-normal text-white mb-4 font-sans tracking-tight leading-tight`}>
              Agent Backtesting
            </h1>
            <p className="text-white/50 text-sm">
              Test your trading agents against historical data
            </p>
          </div>

          {/* Enhanced Configuration - Mobile Responsive */}
          <div className={`flex ${isMobile ? 'flex-col gap-2' : 'items-center gap-4'}`}>
            {/* Agent Selection */}
            <div className={`${isMobile ? 'w-full' : 'w-48'}`}>
              <Select value={selectedAgent} onValueChange={setSelectedAgent}>
                <SelectTrigger className="bg-white/[0.04] border border-white/[0.08] text-white h-10 text-sm w-full rounded-md">
                  <SelectValue placeholder="Select Agent" />
                </SelectTrigger>
                <SelectContent className="bg-[#1A1A1A] border-white/[0.08]">
                  {agents.map((agent) => (
                    <SelectItem key={agent.id} value={agent.id} className="text-white hover:bg-white/[0.05] text-sm">
                      {agent.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Ticker Input */}
            <div className={`${isMobile ? 'w-full' : 'w-32'}`}>
              <Input
                value={selectedSymbol}
                onChange={(e) => setSelectedSymbol(e.target.value.toUpperCase())}
                placeholder="AAPL"
                className="bg-white/[0.04] border border-white/[0.08] text-white h-10 text-sm uppercase placeholder-white/40 w-full rounded-md"
              />
            </div>

            {/* Timeframe Selection */}
            <div className={`${isMobile ? 'w-full' : 'w-32'}`}>
              <Select value={selectedTimeframe} onValueChange={setSelectedTimeframe}>
                <SelectTrigger className="bg-white/[0.04] border border-white/[0.08] text-white h-10 text-sm w-full rounded-md">
                  <SelectValue placeholder="Period" />
                </SelectTrigger>
                <SelectContent className="bg-[#1A1A1A] border-white/[0.08]">
                  {timeframes.map((timeframe) => (
                    <SelectItem key={timeframe.value} value={timeframe.value} className="text-white hover:bg-white/[0.05] text-sm">
                      {timeframe.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Test Button */}
            <div className={`${isMobile ? 'w-full' : 'w-auto'}`}>
              <Button
                onClick={handleBacktest}
                disabled={!selectedAgent || !selectedSymbol || isBacktesting}
                className={`bg-white text-black hover:bg-gray-200 shadow-[0_2px_4px_rgba(0,0,0,0.2)] h-10 text-sm font-medium rounded-md transition-all duration-200 ${isMobile ? 'w-full' : 'px-6'} ${isBacktesting ? 'px-8' : ''}`}
              >
                <span className="flex items-center justify-center">
                  {isBacktesting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Testing...
                    </>
                  ) : (
                    'Test'
                  )}
                </span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Empty State - Configure Backtest */}
      {!isBacktesting && !backtestResult && (
        <div className={`flex-1 ${classes.container} pb-6`}>
          <div className="backdrop-blur-sm bg-[#0D0D0D]/80 border border-white/[0.06] rounded-xl h-full flex flex-col shadow-[0_8px_32px_rgba(0,0,0,0.4),inset_0_1px_0_rgba(255,255,255,0.08)]">
            <div className="flex-1 flex items-center justify-center p-8">
              <div className="text-center max-w-lg">
                {/* Backtest Icon Visual */}
                <div className="relative mb-8">
                  <div className="w-32 h-32 mx-auto flex items-center justify-center">
                    <img
                      src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/icons//Bar%20chart.svg"
                      alt="Bar Chart"
                      className="w-24 h-24 opacity-40"
                    />
                  </div>
                </div>

                {selectedAgent && selectedSymbol ? (
                  <div>
                    <h3 className="text-xl font-medium text-white mb-3">Ready to Backtest</h3>
                    <p className="text-white/60 text-sm mb-6 leading-relaxed">
                      Your agent <span className="text-emerald-400 font-medium">{agents.find(a => a.id === selectedAgent)?.name}</span> is ready to test against <span className="text-emerald-400 font-medium">{selectedSymbol}</span> over the past <span className="text-emerald-400 font-medium">{timeframes.find(t => t.value === selectedTimeframe)?.label}</span>.
                    </p>
                    <div className="flex items-center justify-center gap-2 text-white/40 text-xs">
                      <div className="w-2 h-2 bg-emerald-500/60 rounded-full animate-pulse"></div>
                      <span>Click "Test" to begin backtesting</span>
                    </div>
                  </div>
                ) : (
                  <div>
                    <h3 className="text-xl font-medium text-white mb-3">Configure Backtest</h3>
                    <p className="text-white/60 text-sm mb-6 leading-relaxed">
                      Select an agent and stock symbol to test your trading strategy against historical data.
                    </p>
                    <div className="space-y-2 text-white/40 text-xs">
                      <div className="flex items-center justify-center gap-2">
                        <div className={`w-2 h-2 rounded-full ${selectedAgent ? 'bg-emerald-500' : 'bg-white/20'}`}></div>
                        <span>Choose your trading agent</span>
                      </div>
                      <div className="flex items-center justify-center gap-2">
                        <div className={`w-2 h-2 rounded-full ${selectedSymbol ? 'bg-emerald-500' : 'bg-white/20'}`}></div>
                        <span>Enter stock symbol (e.g., AAPL)</span>
                      </div>
                      <div className="flex items-center justify-center gap-2">
                        <div className={`w-2 h-2 rounded-full ${selectedTimeframe ? 'bg-emerald-500' : 'bg-white/20'}`}></div>
                        <span>Select time period</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Backtest Results - Scrollable Layout */}
      {backtestResult && (
        <div className="flex-1 overflow-y-auto">
          <div className={`${classes.container} py-6 space-y-6`}>

            {/* Performance Metrics - Top Section */}
            <div>
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-white">Performance Metrics</h3>
                <p className="text-sm text-white/60">Key statistics from your backtest results</p>
              </div>
              <div className={`grid ${isMobile ? 'grid-cols-2 gap-3' : 'grid-cols-4 gap-4'}`}>
                <div className="bg-[#0D0D0D] border border-white/[0.06] rounded-lg p-4 text-center shadow-[inset_0_1px_0_rgba(255,255,255,0.1)]">
                  <div className={`text-2xl font-bold ${
                    backtestResult.totalReturn > 0 ? 'text-emerald-400' : 'text-red-400'
                  }`}>
                    {backtestResult.totalReturn > 0 ? '+' : ''}{backtestResult.totalReturn.toFixed(2)}%
                  </div>
                  <div className="text-sm text-white/70 mt-2">Total Return</div>
                  <div className="text-xs text-white/50 mt-1">Agent Performance</div>
                </div>

                <div className="bg-[#0D0D0D] border border-white/[0.06] rounded-lg p-4 text-center shadow-[inset_0_1px_0_rgba(255,255,255,0.1)]">
                  <div className="text-2xl font-bold text-white">{backtestResult.numberOfTrades}</div>
                  <div className="text-sm text-white/70 mt-2">Total Trades</div>
                  <div className="text-xs text-white/50 mt-1">Executed Orders</div>
                </div>

                <div className="bg-[#0D0D0D] border border-white/[0.06] rounded-lg p-4 text-center shadow-[inset_0_1px_0_rgba(255,255,255,0.1)]">
                  <div className="text-2xl font-bold text-emerald-400">
                    {backtestResult.winRate.toFixed(1)}%
                  </div>
                  <div className="text-sm text-white/70 mt-2">Win Rate</div>
                  <div className="text-xs text-white/50 mt-1">Successful Trades</div>
                </div>

                <div className="bg-[#0D0D0D] border border-white/[0.06] rounded-lg p-4 text-center shadow-[inset_0_1px_0_rgba(255,255,255,0.1)]">
                  <div className="text-2xl font-bold text-red-400">
                    -{backtestResult.maxDrawdown.toFixed(2)}%
                  </div>
                  <div className="text-sm text-white/70 mt-2">Max Drawdown</div>
                  <div className="text-xs text-white/50 mt-1">Largest Loss</div>
                </div>
              </div>
            </div>

            {/* Main Chart Section - Fixed Height */}
            <div className={`${isMobile ? 'space-y-6' : 'flex gap-6'}`}>

              {/* Main Trading Chart - Enhanced with Glow */}
              <div className={`${isMobile ? 'w-full' : 'flex-1'} bg-[#0D0D0D] border border-white/[0.08] rounded-lg overflow-hidden shadow-[0_0_20px_rgba(59,130,246,0.15),inset_0_1px_0_rgba(255,255,255,0.1)] ring-1 ring-blue-500/20`}>

                {/* Chart Header with Controls */}
                <div className={`flex ${isMobile ? 'flex-col gap-3' : 'items-center justify-between'} p-4 border-b border-white/[0.06] bg-gradient-to-r from-[#0D0D0D] to-[#111111]`}>
                  <div>
                    <h3 className="text-lg font-semibold text-white flex items-center gap-2">
                      📊 Trading Chart
                      {currentTradeIndex !== undefined && (
                        <span className="text-xs bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full">
                          Trade {currentTradeIndex + 1}/{backtestResult.trades.length}
                        </span>
                      )}
                    </h3>
                    <p className="text-sm text-white/60">
                      {currentTradeIndex !== undefined
                        ? `${selectedSymbol} - ${mainChartTimeframe} intervals with trade markers`
                        : 'Select a trade from the history to view detailed price action with zoom controls'
                      }
                    </p>
                  </div>

                  {/* Timeframe Controls */}
                  <div className="flex items-center gap-3">
                    <Clock className="h-4 w-4 text-white/60" />
                    <span className="text-white/60 text-sm">Interval:</span>
                    <div className="flex items-center gap-1 bg-[#0F0F11] rounded-lg p-1 border border-[#1A1A1C] shadow-inner">
                      {chartTimeframeOptions.map((option) => (
                        <button
                          key={option.value}
                          onClick={() => setMainChartTimeframe(option.value)}
                          disabled={isLoadingMainChart}
                          className={`px-3 py-2 text-xs font-medium rounded transition-all duration-200 ${
                            mainChartTimeframe === option.value
                              ? 'bg-blue-500 text-white shadow-sm shadow-blue-500/30'
                              : 'text-white/60 hover:text-white hover:bg-white/5'
                          } ${isLoadingMainChart ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                          title={option.description}
                        >
                          {option.label}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Chart Content - Enhanced Height */}
                <div className="relative bg-[#0A0A0A]" style={{ height: '650px' }}>
                  {currentTradeIndex !== undefined && mainChartData.length > 0 ? (() => {
                    // Extract chart overlays from current trade's agent metrics
                    const currentTrade = backtestResult.trades[currentTradeIndex];
                    const chartOverlays = extractChartOverlays(
                      currentTrade.agentMetrics,
                      backtestResult.agentConfiguration
                    );

                    return (
                      <CleanCandlestickChart
                        symbol={selectedSymbol}
                        data={mainChartData}
                        tradeMarkers={[
                          {
                            timestamp: adjustTradeToMarketOpen(new Date(backtestResult.trades[currentTradeIndex].date).getTime()),
                            price: backtestResult.trades[currentTradeIndex].price,
                            type: backtestResult.trades[currentTradeIndex].type === 'buy' ? 'entry' : 'exit',
                            label: `${backtestResult.trades[currentTradeIndex].type.toUpperCase()} $${backtestResult.trades[currentTradeIndex].price.toFixed(2)}`,
                            color: backtestResult.trades[currentTradeIndex].type === 'buy' ? '#10b981' : '#ef4444',
                            tradeIndex: currentTradeIndex
                          }
                        ]}
                        height={650}
                        className="w-full h-full"
                        currentTradeIndex={currentTradeIndex}
                        allTrades={backtestResult.trades}
                        indicators={chartOverlays.indicators}
                        supportResistanceLevels={chartOverlays.supportResistanceLevels}
                        trendLines={chartOverlays.trendLines}
                        breakoutZones={chartOverlays.breakoutZones}
                      />
                    );
                  })() : isLoadingMainChart ? (
                    <div className="w-full h-full flex items-center justify-center">
                      <div className="text-center">
                        <div className="h-10 w-10 border-2 border-white/10 border-t-blue-400 rounded-full animate-spin mb-4 mx-auto"></div>
                        <div className="text-white/70 text-lg mb-2">Loading {mainChartTimeframe} chart data...</div>
                        <div className="text-white/50 text-sm">Fetching market data for selected trade</div>
                        <div className="text-white/30 text-xs mt-2">Chart will include zoom controls and detailed OHLC data</div>
                      </div>
                    </div>
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <div className="text-center max-w-lg">
                        <div className="text-white/60 text-2xl mb-4">📊 Interactive Trading Chart</div>
                        <div className="text-white/40 text-lg mb-3">Select a trade from the history to view detailed price action</div>
                        <div className="text-white/30 text-sm mb-2">Features include:</div>
                        <div className="text-white/30 text-xs space-y-1">
                          <div>• Zoom in/out with mouse wheel or touch gestures</div>
                          <div>• Pan across time periods by dragging</div>
                          <div>• Detailed OHLC data on hover</div>
                          <div>• Precise trade entry/exit markers</div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Trade History Sidebar - Desktop Only */}
              {!isMobile && (
                <div className="w-80 bg-[#0D0D0D] border border-white/[0.06] rounded-lg overflow-hidden shadow-[inset_0_1px_0_rgba(255,255,255,0.1)]" style={{ height: '723px' }}>

                  {/* Sidebar Header */}
                  <div className="p-4 border-b border-white/[0.04]">
                    <h3 className="text-lg font-semibold text-white">Trade History</h3>
                    <p className="text-sm text-white/60">Click to view on chart</p>
                  </div>

                  {/* Trade List */}
                  <div className="h-[calc(100%-73px)] overflow-y-auto">
                    <div className="p-3">
                      <div className="space-y-2">
                        {(backtestResult.trades ?? []).slice().reverse().map((trade, reversedIndex) => {
                          const originalIndex = (backtestResult.trades?.length || 0) - 1 - reversedIndex;
                          const isSelected = currentTradeIndex === originalIndex;
                          return (
                            <div
                              key={reversedIndex}
                              className={`p-3 rounded-lg transition-all duration-200 cursor-pointer border ${
                                isSelected
                                  ? 'bg-blue-500/20 border-blue-500/40 shadow-[0_0_0_1px_rgba(59,130,246,0.3)]'
                                  : 'hover:bg-white/[0.02] border-white/[0.04] hover:border-white/[0.08]'
                              }`}
                              onClick={() => handleTradeClick(originalIndex)}
                            >
                              <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center gap-2">
                                  <span className={`w-2.5 h-2.5 rounded-full ${
                                    trade.type === 'buy' ? 'bg-emerald-400' : 'bg-red-400'
                                  }`} />
                                  <span className={`text-sm font-semibold ${
                                    trade.type === 'buy' ? 'text-emerald-400' : 'text-red-400'
                                  }`}>
                                    {trade.type.toUpperCase()}
                                  </span>
                                </div>
                                <span className="text-sm text-white font-mono">${trade.price.toFixed(2)}</span>
                              </div>
                              <div className="flex items-center justify-between text-sm text-white/60 mb-1">
                                <span className="font-mono">{trade.date}</span>
                                <span className="font-medium">{trade.confidence}%</span>
                              </div>
                              <div className="text-xs text-white/50 truncate">{trade.signal}</div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Trade History - Mobile Section */}
            {isMobile && (
              <div className="bg-[#0D0D0D] border border-white/[0.06] rounded-lg overflow-hidden shadow-[inset_0_1px_0_rgba(255,255,255,0.1)]">
                <div className="p-4 border-b border-white/[0.04]">
                  <h3 className="text-lg font-semibold text-white">Trade History</h3>
                  <p className="text-sm text-white/60">Tap to view on chart</p>
                </div>
                <div className="max-h-80 overflow-y-auto p-3">
                  <div className="space-y-2">
                    {(backtestResult.trades ?? []).slice().reverse().map((trade, reversedIndex) => {
                      const originalIndex = (backtestResult.trades?.length || 0) - 1 - reversedIndex;
                      const isSelected = currentTradeIndex === originalIndex;
                      return (
                        <div
                          key={reversedIndex}
                          className={`flex items-center justify-between p-3 rounded-lg transition-all duration-200 cursor-pointer border ${
                            isSelected
                              ? 'bg-blue-500/20 border-blue-500/40'
                              : 'hover:bg-white/[0.02] border-white/[0.04]'
                          }`}
                          onClick={() => handleTradeClick(originalIndex)}
                        >
                          <div className="flex items-center gap-3">
                            <span className={`w-2.5 h-2.5 rounded-full ${
                              trade.type === 'buy' ? 'bg-emerald-400' : 'bg-red-400'
                            }`} />
                            <div>
                              <div className="flex items-center gap-2">
                                <span className={`text-sm font-semibold ${
                                  trade.type === 'buy' ? 'text-emerald-400' : 'text-red-400'
                                }`}>
                                  {trade.type.toUpperCase()}
                                </span>
                                <span className="text-sm text-white font-mono">${trade.price.toFixed(2)}</span>
                              </div>
                              <div className="text-xs text-white/60 font-mono">{trade.date}</div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm text-white/70">{trade.confidence}%</div>
                            <div className="text-xs text-white/50">Confidence</div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Trade Analysis Modal */}
      {backtestResult && (
        <TradeAnalysisModal
          symbol={selectedSymbol}
          trades={backtestResult.trades}
          startDate={backtestResult.startDate}
          endDate={backtestResult.endDate}
          isOpen={isTradeModalOpen}
          onClose={handleCloseTradeModal}
          selectedTradeIndex={selectedTradeIndex}
        />
      )}
    </div>
  );
};

export default AgentBacktesting;

