import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, User, Crown, Info, FileText, Shield } from 'lucide-react';
import { useResponsive } from '@/hooks/useResponsive';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import Settings from './Settings';
import About from './About';
import TermsOfService from './TermsOfService';
import PrivacyPolicy from './PrivacyPolicy';
import ManageSubscription from './ManageSubscription';

const UnifiedSettings: React.FC = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('profile');
  const { isMobile, classes } = useResponsive();

  return (
    <div className={`h-full bg-[#0A0A0A] text-white flex flex-col ${isMobile ? 'px-3 py-3' : classes.container}`}>
      {/* Header */}
      <div className={`flex items-center justify-between ${isMobile ? 'px-3 py-2' : classes.container} py-2 border-b border-[#1A1A1A]/30`}>
        <div className="flex items-center gap-3">
          <button
            onClick={() => navigate(-1)}
            className="text-white/90 hover:text-white transition-colors duration-200"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <h1 className={`${isMobile ? 'text-xl' : 'text-2xl'} font-semibold text-white`}>Settings</h1>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          {/* Tab Navigation - Mobile Responsive */}
          <div className={`border-b border-[#1A1A1A]/30 ${isMobile ? 'px-3' : classes.container}`}>
            <TabsList className={`bg-transparent border-none p-0 h-auto ${isMobile ? 'flex-wrap' : ''}`}>
              <TabsTrigger
                value="profile"
                className={`flex items-center gap-2 ${isMobile ? 'px-2 py-1.5 text-xs' : 'px-4 py-2'} text-white/70 hover:text-white data-[state=active]:text-white data-[state=active]:border-b-2 data-[state=active]:border-white rounded-none bg-transparent`}
              >
                <User className="w-4 h-4" />
                <span className={isMobile ? 'hidden' : 'inline'}>Profile</span>
              </TabsTrigger>
              <TabsTrigger
                value="subscription"
                className={`flex items-center gap-2 ${isMobile ? 'px-2 py-1.5 text-xs' : 'px-4 py-2'} text-white/70 hover:text-white data-[state=active]:text-white data-[state=active]:border-b-2 data-[state=active]:border-white rounded-none bg-transparent`}
              >
                <Crown className="w-4 h-4" />
                <span className={isMobile ? 'hidden' : 'inline'}>Subscription</span>
              </TabsTrigger>
              <TabsTrigger
                value="about"
                className={`flex items-center gap-2 ${isMobile ? 'px-2 py-1.5 text-xs' : 'px-4 py-2'} text-white/70 hover:text-white data-[state=active]:text-white data-[state=active]:border-b-2 data-[state=active]:border-white rounded-none bg-transparent`}
              >
                <Info className="w-4 h-4" />
                <span className={isMobile ? 'hidden' : 'inline'}>About</span>
              </TabsTrigger>
              <TabsTrigger
                value="terms"
                className={`flex items-center gap-2 ${isMobile ? 'px-2 py-1.5 text-xs' : 'px-4 py-2'} text-white/70 hover:text-white data-[state=active]:text-white data-[state=active]:border-b-2 data-[state=active]:border-white rounded-none bg-transparent`}
              >
                <FileText className="w-4 h-4" />
                <span className={isMobile ? 'hidden' : 'inline'}>Terms</span>
              </TabsTrigger>
              <TabsTrigger
                value="privacy"
                className={`flex items-center gap-2 ${isMobile ? 'px-2 py-1.5 text-xs' : 'px-4 py-2'} text-white/70 hover:text-white data-[state=active]:text-white data-[state=active]:border-b-2 data-[state=active]:border-white rounded-none bg-transparent`}
              >
                <Shield className="w-4 h-4" />
                <span className={isMobile ? 'hidden' : 'inline'}>Privacy</span>
              </TabsTrigger>
            </TabsList>
          </div>

          {/* Tab Content */}
          <div className="flex-1 overflow-y-auto">
            <TabsContent value="profile" className="h-full m-0 p-0">
              <Settings />
            </TabsContent>
            <TabsContent value="subscription" className="h-full m-0 p-0">
              <ManageSubscription />
            </TabsContent>
            <TabsContent value="about" className="h-full m-0 p-0">
              <About />
            </TabsContent>
            <TabsContent value="terms" className="h-full m-0 p-0">
              <TermsOfService />
            </TabsContent>
            <TabsContent value="privacy" className="h-full m-0 p-0">
              <PrivacyPolicy />
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
};

export default UnifiedSettings;
