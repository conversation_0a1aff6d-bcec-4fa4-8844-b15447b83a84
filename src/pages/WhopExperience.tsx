import React, { useEffect, useState } from 'react';
import { useParams, Navigate } from 'react-router-dom';
import WhopExperiencePage from '@/components/whop/WhopExperiencePage';
import WhopAuthGuard from '@/components/whop/WhopAuthGuard';
import WhopPageLayout from '@/components/whop/WhopPageLayout';
import Home from './Home';
import Trading from './Trading';
import { useWhopUser, useWhop } from '@/contexts/WhopContext';
import { Loader2 } from 'lucide-react';
import {
  getExperienceInfo,
  getRouteFromExperienceName,
  type ExperienceInfo
} from '@/utils/experienceUtils';

const WhopExperience: React.FC = () => {
  const { experienceId } = useParams<{ experienceId: string }>();
  const { isWhopUser } = useWhopUser();
  const { setExperienceIdAndSave } = useWhop();
  const [experienceInfo, setExperienceInfo] = useState<ExperienceInfo | null>(null);
  const [isLoadingExperience, setIsLoadingExperience] = useState(true);
  const [experienceError, setExperienceError] = useState<string | null>(null);

  console.log('🎯 Whop Experience Page:', { experienceId, isWhopUser });

  // Immediately save the experience ID when we have it
  useEffect(() => {
    if (experienceId) {
      console.log('💾 Saving experience ID from URL params:', experienceId);
      setExperienceIdAndSave(experienceId);
    }
  }, [experienceId, setExperienceIdAndSave]);

  // Fetch experience information to determine routing
  useEffect(() => {
    const fetchExperienceInfo = async () => {
      if (!experienceId) {
        setExperienceError('No experience ID provided');
        setIsLoadingExperience(false);
        return;
      }

      try {
        setIsLoadingExperience(true);

        // Use the utility function to get experience info
        const experienceInfo = await getExperienceInfo(experienceId);

        if (experienceInfo) {
          setExperienceInfo(experienceInfo);
          console.log('✅ Experience info loaded:', experienceInfo);
        } else {
          throw new Error('Failed to fetch experience info');
        }
      } catch (error) {
        console.error('❌ Error fetching experience info:', error);
        setExperienceError(error instanceof Error ? error.message : 'Unknown error');
      } finally {
        setIsLoadingExperience(false);
      }
    };

    fetchExperienceInfo();
  }, [experienceId]);

  // Show loading state while fetching experience info
  if (isLoadingExperience) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-[#0A0A0A]">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
          <p className="text-gray-400">Loading experience...</p>
        </div>
      </div>
    );
  }

  // Handle experience loading error or use fallback logic
  if (experienceError || !experienceInfo) {
    console.warn('⚠️ Experience info not available, falling back to ID-based detection');
    // Fallback to the old ID-based detection
    const isTradingExperience = experienceId === 'exp_ThljdpAF70d4Af';

    if (isTradingExperience) {
      return (
        <WhopExperiencePage>
          <WhopAuthGuard requireAccess={true}>
            <Trading />
          </WhopAuthGuard>
        </WhopExperiencePage>
      );
    }
  } else {
    // Use experience name and app name for routing decisions
    const experienceName = experienceInfo.name;

    // Check if we have app information from the API response
    const appName = (experienceInfo as any).app?.name || '';
    const detectedApp = (experienceInfo as any).detectedApp || '';

    console.log('🎯 Enhanced experience routing:', {
      experienceName,
      appName,
      detectedApp,
      experienceId
    });

    // Determine target route based on app name first, then experience name
    let targetRoute: string;

    if (appName.toLowerCase().includes('trade') || detectedApp.toLowerCase() === 'trading') {
      targetRoute = '/trading';
      console.log('🚀 Routing to /trading based on app name or detected app');
    } else if (appName.toLowerCase().includes('osis') || detectedApp.toLowerCase() === 'osis') {
      targetRoute = '/';
      console.log('🚀 Routing to / based on app name or detected app');
    } else {
      // Fallback to experience name-based routing
      targetRoute = getRouteFromExperienceName(experienceName);
      console.log('🚀 Routing based on experience name fallback:', targetRoute);
    }

    console.log(`🚀 Final redirect to ${targetRoute}`);
    return <Navigate to={targetRoute} replace />;
  }

  // Fallback: For OSIS experiences or unknown cases, render the Home component with layout
  return (
    <WhopExperiencePage>
      <WhopAuthGuard requireAccess={true}>
        <WhopPageLayout>
          {/*
            For OSIS Whop users or fallback cases, we render the main Home component
            but within the Whop experience context with mobile-style navigation.
            This ensures they get the same functionality as regular users
            but with a mobile-optimized interface.
          */}
          <Home />
        </WhopPageLayout>
      </WhopAuthGuard>
    </WhopExperiencePage>
  );
};

export default WhopExperience;
