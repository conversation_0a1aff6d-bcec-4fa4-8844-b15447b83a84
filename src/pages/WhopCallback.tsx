import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useWhop } from '@/contexts/WhopContext';

const WhopCallback: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { signIn } = useAuth();
  const { initializeWhopAuth } = useWhop();
  const [status, setStatus] = useState<'processing' | 'success' | 'error'>('processing');
  const [errorMessage, setErrorMessage] = useState<string>('');

  useEffect(() => {
    const handleCallback = async () => {
      try {
        setStatus('processing');
        
        // Get OAuth parameters from URL
        const code = searchParams.get('code');
        const state = searchParams.get('state');
        const error = searchParams.get('error');
        const errorDescription = searchParams.get('error_description');

        // Handle OAuth errors
        if (error) {
          console.error('❌ OAuth error:', error, errorDescription);
          setErrorMessage(`Authentication failed: ${errorDescription || error}`);
          setStatus('error');
          return;
        }

        // Validate required parameters
        if (!code) {
          console.error('❌ Missing authorization code');
          setErrorMessage('Missing authorization code from Whop');
          setStatus('error');
          return;
        }

        if (!state) {
          console.error('❌ Missing state parameter');
          setErrorMessage('Missing state parameter from Whop');
          setStatus('error');
          return;
        }

        console.log('🔄 Processing Whop OAuth callback...', {
          hasCode: !!code,
          hasState: !!state,
          state: state
        });

        // Initialize Whop authentication with the authorization code
        await initializeWhopAuth();

        console.log('✅ Whop authentication successful');
        setStatus('success');

        // Get the redirect URL from state or default to home
        let redirectTo = '/';
        try {
          // State might contain the original URL the user was trying to access
          const decodedState = decodeURIComponent(state);
          if (decodedState && decodedState.startsWith('/')) {
            redirectTo = decodedState;
          }
        } catch (e) {
          console.warn('⚠️ Could not decode state parameter, using default redirect');
        }

        // Small delay to show success state
        setTimeout(() => {
          navigate(redirectTo, { replace: true });
        }, 1500);

      } catch (error) {
        console.error('❌ Error processing Whop callback:', error);
        setErrorMessage(error instanceof Error ? error.message : 'Unknown error occurred');
        setStatus('error');
      }
    };

    handleCallback();
  }, [searchParams, navigate, signIn, initializeWhopAuth]);

  const handleRetry = () => {
    // Redirect to home and let the auth flow restart
    navigate('/', { replace: true });
  };

  const handleGoHome = () => {
    navigate('/', { replace: true });
  };

  return (
    <div className="min-h-screen bg-[#0A0A0A] text-white flex items-center justify-center">
      <div className="max-w-md w-full mx-4">
        <div className="bg-white/[0.02] border border-white/[0.05] rounded-xl p-8 text-center">
          {status === 'processing' && (
            <>
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto mb-6"></div>
              <h1 className="text-2xl font-bold text-white mb-2">
                Processing Authentication
              </h1>
              <p className="text-white/60 text-sm">
                Completing your Whop authentication...
              </p>
            </>
          )}

          {status === 'success' && (
            <>
              <div className="w-12 h-12 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h1 className="text-2xl font-bold text-white mb-2">
                Authentication Successful!
              </h1>
              <p className="text-white/60 text-sm mb-4">
                Redirecting you to Trade Sensei...
              </p>
              <div className="w-full bg-white/[0.08] rounded-full h-2 overflow-hidden">
                <div className="h-full bg-green-400 rounded-full animate-pulse"></div>
              </div>
            </>
          )}

          {status === 'error' && (
            <>
              <div className="w-12 h-12 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-6 h-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <h1 className="text-2xl font-bold text-white mb-2">
                Authentication Failed
              </h1>
              <p className="text-white/60 text-sm mb-6">
                {errorMessage || 'An error occurred during authentication'}
              </p>
              <div className="space-y-3">
                <button
                  onClick={handleRetry}
                  className="w-full bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                >
                  Try Again
                </button>
                <button
                  onClick={handleGoHome}
                  className="w-full bg-white/[0.05] hover:bg-white/[0.08] text-white font-medium py-2 px-4 rounded-lg border border-white/[0.10] transition-colors"
                >
                  Go to Home
                </button>
              </div>
            </>
          )}
        </div>

        <div className="mt-6 text-center">
          <p className="text-white/40 text-xs">
            Powered by Whop Authentication
          </p>
        </div>
      </div>
    </div>
  );
};

export default WhopCallback;
