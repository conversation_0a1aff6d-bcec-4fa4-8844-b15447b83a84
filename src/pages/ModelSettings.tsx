
import { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowLeft, Save } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import AuthButton from '@/components/auth/AuthButton';

export default function ModelSettings() {
  const [selectedModel, setSelectedModel] = useState('Osis-default');
  
  // Hide the model selector when this component mounts
  useEffect(() => {
    // Find the model selector in the header
    const modelSelector = document.querySelector('header .flex.items-center.gap-4');
    
    if (modelSelector) {
      // Store the original display state using a class approach
      modelSelector.classList.add('hidden');
      
      // Restore the model selector when component unmounts
      return () => {
        modelSelector.classList.remove('hidden');
      };
    }
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
  };

  return (
    <div className="min-h-screen bg-[#0A0A0A] text-white">
      <header className="sticky top-0 z-50 flex items-center justify-between bg-[#0A0A0A] px-4 py-2">
        <div className="flex items-center gap-2">
          <Link to="/" className="flex items-center justify-center h-8 w-8 rounded-md hover:bg-white/5">
            <ArrowLeft className="h-4 w-4 text-white/70" />
          </Link>
          <h1 className="text-lg font-medium">Model Settings</h1>
        </div>
        <AuthButton />
      </header>

      <main className="container max-w-3xl mx-auto py-6 px-4">
        <form onSubmit={handleSubmit}>
          <Card className="bg-[#0F0F0F] border-white/5 shadow-md">
            <div className="p-3 border-b border-white/5">
              <h2 className="text-xs font-medium text-white/90">AI Model Preferences</h2>
            </div>
            <CardContent className="p-3 space-y-4">
              <div>
                <Label className="text-xs font-medium text-white/70 mb-2 block">
                  Default Model
                </Label>
                <RadioGroup 
                  value={selectedModel} 
                  onValueChange={setSelectedModel}
                  className="space-y-2"
                >
                  <div className="flex items-center space-x-2 rounded-md border border-white/10 p-2 hover:bg-white/5">
                    <RadioGroupItem value="Osis-default" id="Osis-default" className="border-white/20" />
                    <Label htmlFor="Osis-default" className="flex flex-col cursor-pointer">
                      <span className="text-sm">Osis</span>
                      <span className="text-xs text-white/50">Balanced performance for everyday tasks</span>
                    </Label>
                  </div>
                  
                  <div className="flex items-center space-x-2 rounded-md border border-white/10 p-2 hover:bg-white/5">
                    <RadioGroupItem value="Osis-plus" id="Osis-plus" className="border-white/20" />
                    <Label htmlFor="Osis-plus" className="flex flex-col cursor-pointer">
                      <span className="text-sm">Osis Plus</span>
                      <span className="text-xs text-white/50">Enhanced capabilities for complex analysis</span>
                    </Label>
                  </div>
                  
                  <div className="flex items-center space-x-2 rounded-md border border-white/10 p-2 hover:bg-white/5">
                    <RadioGroupItem value="Osis-fast" id="Osis-fast" className="border-white/20" />
                    <Label htmlFor="Osis-fast" className="flex flex-col cursor-pointer">
                      <span className="text-sm">Osis Fast</span>
                      <span className="text-xs text-white/50">Optimized for speed and efficiency</span>
                    </Label>
                  </div>
                </RadioGroup>
              </div>
              
              <div className="pt-3 border-t border-white/5">
                <Label className="text-xs font-medium text-white/70 mb-2 block">
                  Advanced Settings
                </Label>
                <div className="space-y-2">
                  <div className="flex items-center justify-between rounded-md border border-white/10 p-2">
                    <div>
                      <span className="text-sm">Temperature</span>
                      <p className="text-xs text-white/50">Controls randomness in responses</p>
                    </div>
                    <input 
                      type="range" 
                      min="0" 
                      max="100" 
                      className="w-24 accent-sage-light" 
                      defaultValue="70"
                    />
                  </div>
                  
                  <div className="flex items-center justify-between rounded-md border border-white/10 p-2">
                    <div>
                      <span className="text-sm">Context Length</span>
                      <p className="text-xs text-white/50">How much conversation history to include</p>
                    </div>
                    <select className="bg-[#141414] border border-white/10 rounded text-xs p-1">
                      <option>Default</option>
                      <option>Extended</option>
                      <option>Maximum</option>
                    </select>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <div className="flex justify-end mt-4">
            <Button
              type="submit"
              className="flex items-center gap-1.5 px-3 py-1.5 h-8 text-xs bg-[#1A1A1A] hover:bg-[#222222] text-white border border-white/10"
            >
              <Save className="h-3.5 w-3.5" />
              <span>Save Preferences</span>
            </Button>
          </div>
        </form>
      </main>
    </div>
  );
}
