import { useState, useEffect } from 'react';
import { Check, TrendingUp } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useSubscriptionType } from '@/hooks/useSubscriptionType';
import { loadStripe } from '@stripe/stripe-js';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';
import { getAffiliateCode } from '@/utils/growiUtils';

// Stripe configuration - Using only legacy account for all users
const STRIPE_CONFIG = {
  publicKey: 'pk_live_51Qq1bADebmd1GpTvtrUIe0foCjJicP6lNCnMklfLEVs85JSDQqSEVQUmgp2OpR1oYwgcvSOV5oJOx4tzhv4TZZ5W00CXhlInPx',
};

const Subscription = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { subscriptionType, isLoading, canUpgrade } = useSubscriptionType();
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);
  // Get current user
  useEffect(() => {
    const getCurrentUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setCurrentUser(user);
    };

    getCurrentUser();
  }, []);

  // Plan configurations with single Premium plan - both weekly and annual
  const getPlans = () => {
    // All users use legacy account now

    return [
      {
        id: 'premium-weekly',
        name: 'Premium',
        price: '$9.99',
        period: '/week',
        priceId: 'price_1ROYLKDebmd1GpTvct491Kw6', // Always use legacy account now
        billingPeriod: 'weekly',
        description: 'Complete access to all features',
        icon: TrendingUp,
        popular: true,
        spotlight: true,
        features: [
          'All 7 AI Agents',
          'Stock and Crypto Analysis',
          'Instant Trading Information',
          'Unlimited messages',
          'Unlimited Portfolio Backtesting',
          'Unlimited Portfolios',
          'Agent Builder Access',
          'Discover Community Agents',
          'Advanced Stock Search',
          'Stock Scanner Tools',
          'Priority Support',
          'Advanced Analytics',
          'Custom Indicators'
        ],
        benefits: [
          'Build custom trading agents',
          'Access community strategies',
          'Advanced market analysis',
          'Professional trading tools',
          'Unlimited everything'
        ]
      },
      {
        id: 'premium-annual',
        name: 'Premium Annual',
        price: '$360',
        period: '/year',
        priceId: 'price_1RVyr7Debmd1GpTvgWmmS7R1', // Annual Premium
        billingPeriod: 'yearly',
        description: 'Complete access to all features - Best Value!',
        icon: TrendingUp,
        popular: false,
        spotlight: false,
        savings: 'Save $318/year',
        features: [
          'All 7 AI Agents',
          'Stock and Crypto Analysis',
          'Instant Trading Information',
          'Unlimited messages',
          'Unlimited Portfolio Backtesting',
          'Unlimited Portfolios',
          'Agent Builder Access',
          'Discover Community Agents',
          'Advanced Stock Search',
          'Stock Scanner Tools',
          'Priority Support',
          'Advanced Analytics',
          'Custom Indicators'
        ],
        benefits: [
          'Build custom trading agents',
          'Access community strategies',
          'Advanced market analysis',
          'Professional trading tools',
          'Unlimited everything'
        ]
      }
    ];
  };

  const plans = getPlans();

  // Handle plan selection and Stripe checkout using correct auth method
  const handleSelectPlan = async (plan: any) => {
    if (isProcessing || !currentUser) return;

    setIsProcessing(true);

    try {
      // Get the session token for authorization
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        throw new Error("No authentication token found");
      }

      // Determine if this is an upgrade/downgrade or new subscription
      const isExistingSubscriber = subscriptionType && subscriptionType !== 'basic';
      const action = isExistingSubscriber ? 'update-subscription' : 'create-checkout-session';

      console.log('🔄 Subscription action:', {
        currentSubscriptionType: subscriptionType,
        targetPlan: plan.id,
        isExistingSubscriber,
        action
      });

      // Get affiliate code for Growi tracking
      const affiliateCode = getAffiliateCode();

      // Create checkout session using the correct endpoint
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe-checkout`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          action,
          priceId: plan.priceId,
          returnUrl: window.location.origin + '/subscription/manage?success=true',
          affiliateCode
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Server returned ${response.status}: ${errorText}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      const { sessionId } = data;

      if (!sessionId) {
        throw new Error('No session ID returned from server');
      }

      // Use legacy Stripe account for all users
      const stripe = await loadStripe(STRIPE_CONFIG.publicKey);
      if (!stripe) throw new Error("Stripe failed to load");

      const { error } = await stripe.redirectToCheckout({ sessionId });
      if (error) throw new Error(`Stripe redirect failed: ${error.message}`);

    } catch (error) {
      console.error('Error creating checkout session:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to start checkout process. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Determine current plan
  const getCurrentPlan = () => {
    if (isLoading) return null;
    return plans.find(p => p.id === subscriptionType) || plans[0];
  };

  const currentPlan = getCurrentPlan();

  // If user is not on basic plan, show a different message
  if (!isLoading && !canUpgrade()) {
    return (
      <div className="h-full bg-[#0A0A0A] text-white flex flex-col overflow-y-auto">
        <div className="flex-1 flex items-center justify-center">
          <div className="max-w-2xl mx-auto text-center px-8">
            <div className="w-16 h-16 mx-auto mb-6 bg-green-500/20 rounded-full flex items-center justify-center">
              <Check className="w-8 h-8 text-green-400" />
            </div>
            <h1 className="text-3xl font-normal text-white mb-4 font-sans">
              You're All Set!
            </h1>
            <p className="text-white/60 text-base font-sans leading-relaxed mb-8">
              You're currently on the <span className="text-white font-medium">{currentPlan?.name || 'Pro'}</span> plan with full access to all features.
            </p>
            <div className="flex gap-4 justify-center">
              <button
                onClick={() => navigate('/subscription/manage')}
                className="px-6 py-3 bg-white/[0.03] hover:bg-white/[0.06] text-white/80 border border-white/[0.08] rounded-lg transition-colors duration-200 font-sans"
              >
                View Plans
              </button>
              <button
                onClick={() => {
                  // Always use legacy billing portal for all users
                  window.open('https://billing.stripe.com/p/login/fZeaGq9Iw2hAg929AA', '_blank');
                }}
                className="px-6 py-3 bg-white hover:bg-white/95 text-black rounded-lg transition-colors duration-200 font-sans"
              >
                Manage Billing
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show loading state if user is not yet loaded
  if (!currentUser) {
    return (
      <div className="h-full bg-[#0A0A0A] text-white flex flex-col overflow-y-auto">
        <div className="flex-1 flex items-center justify-center">
          <div className="max-w-2xl mx-auto text-center px-8">
            <div className="w-16 h-16 mx-auto mb-6 bg-white/[0.02] rounded-full flex items-center justify-center">
              <div className="w-8 h-8 border-2 border-white/20 border-t-white/60 rounded-full animate-spin"></div>
            </div>
            <h1 className="text-3xl font-normal text-white mb-4 font-sans">
              Loading Plans...
            </h1>
            <p className="text-white/60 text-base font-sans leading-relaxed">
              Please wait while we prepare your subscription options
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-[#0A0A0A] text-white flex flex-col overflow-y-auto">
      {/* Clean Header */}
      <div className="px-8 py-12">
        <div className="max-w-5xl mx-auto text-center">
          <h1 className="text-3xl font-normal text-white mb-4 font-sans">
            Choose Your Plan
          </h1>
          <p className="text-white/60 text-base font-sans leading-relaxed">
            Unlock advanced AI trading capabilities with our weekly plans
          </p>
        </div>
      </div>

      {/* Plans Section */}
      <div className="flex-1 px-8">
        <div className="max-w-5xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {plans.map((plan) => {
              const Icon = plan.icon;
              const isSpotlight = plan.spotlight;
              const isCurrent = currentPlan?.id === plan.id;

              return (
                <div
                  key={plan.id}
                  className={`relative rounded-xl border transition-all duration-200 ${
                    isSpotlight
                      ? 'bg-white/[0.02] border-white/[0.12] scale-105'
                      : isCurrent
                      ? 'bg-white/[0.04] border-white/[0.12]'
                      : 'bg-white/[0.01] border-white/[0.06] hover:border-white/[0.10]'
                  }`}
                >
                  {/* Popular Badge */}
                  {isSpotlight && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <div className="px-3 py-1 bg-white text-black rounded-full text-xs font-medium">
                        Most Popular
                      </div>
                    </div>
                  )}

                  {/* Current Plan Badge */}
                  {isCurrent && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <div className="px-3 py-1 bg-green-500 text-white rounded-full text-xs font-medium">
                        Current Plan
                      </div>
                    </div>
                  )}

                  <div className="p-6">
                    {/* Plan Header */}
                    <div className="text-center mb-6">
                      <div className="flex items-center justify-center gap-2 mb-2">
                        <Icon className="w-5 h-5 text-white/80" />
                        <h3 className="text-xl font-medium text-white font-sans">{plan.name}</h3>
                      </div>
                      <p className="text-white/50 text-sm font-sans mb-4">{plan.description}</p>

                      <div className="flex items-baseline justify-center gap-1">
                        <span className="text-3xl font-normal text-white font-sans">
                          {plan.price}
                        </span>
                        <span className="text-white/50 text-sm font-sans">{plan.period}</span>
                      </div>

                      {/* Savings Badge for Annual Plan */}
                      {plan.savings && (
                        <div className="mt-2">
                          <span className="inline-block px-2 py-1 bg-green-500/20 text-green-400 text-xs font-medium rounded-full">
                            {plan.savings}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Features List */}
                    <div className="space-y-3 mb-6">
                      {plan.features.map((feature, index) => (
                        <div key={index} className="flex items-center gap-3">
                          <Check className="w-4 h-4 text-green-400 flex-shrink-0" />
                          <span className="text-white/70 text-sm font-sans">{feature}</span>
                        </div>
                      ))}
                    </div>

                    {/* No limitations for Premium plan */}

                    {/* Action Button */}
                    <button
                      onClick={() => handleSelectPlan(plan)}
                      disabled={isCurrent || isProcessing}
                      className={`w-full py-2.5 text-sm font-medium rounded-lg transition-all duration-200 font-sans ${
                        isCurrent
                          ? 'bg-white/[0.03] text-white/40 cursor-not-allowed border border-white/[0.08]'
                          : isSpotlight
                          ? 'bg-white hover:bg-white/95 text-black shadow-[inset_0_1px_2px_rgba(0,0,0,0.03),0_1px_3px_rgba(0,0,0,0.08)]'
                          : 'bg-white/[0.03] hover:bg-white/[0.06] text-white/80 border border-white/[0.08]'
                      }`}
                    >
                      {isProcessing ? (
                        <div className="flex items-center justify-center gap-2">
                          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                          Processing...
                        </div>
                      ) : isCurrent ? (
                        'Current Plan'
                      ) : (
                        `Upgrade to ${plan.name}`
                      )}
                    </button>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Simple Footer */}
          <div className="mt-12 text-center">
            <p className="text-white/50 text-sm font-sans">
              Cancel anytime • Secure payments by Stripe
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Subscription;
