import React, { useEffect, useRef, useState } from 'react';
import TradingChart from '@/components/TradingChart/TradingChart';
import { useWhopUser } from '@/contexts/WhopContext';
import { useTradingOnboarding } from '@/hooks/useTradingOnboarding';
import TradingOnboardingModal from '@/components/trading/TradingOnboardingModal';
import CompetitionCountdownModal from '@/components/trading/CompetitionCountdownModal';
import { Button } from '@/components/ui/button';

const Trading: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { isWhopUser } = useWhopUser();

  // Trading onboarding state
  const {
    isOpen: isOnboardingOpen,
    hasPaymentAccess,
    verifyPayment,
    resetOnboarding
  } = useTradingOnboarding();

  // Competition countdown state
  const [showCountdown, setShowCountdown] = useState(false);
  const [competitionHasStarted, setCompetitionHasStarted] = useState(false);

  // Set competition start date (2 weeks from now as requested)
  const competitionStartDate = new Date();
  competitionStartDate.setDate(competitionStartDate.getDate() + 14);

  // Check if we should show countdown modal
  useEffect(() => {
    if (isWhopUser && hasPaymentAccess && !competitionHasStarted) {
      const now = new Date();
      if (now < competitionStartDate) {
        setShowCountdown(true);
      } else {
        setCompetitionHasStarted(true);
      }
    }
  }, [isWhopUser, hasPaymentAccess, competitionHasStarted, competitionStartDate]);


  // Handle competition start
  const handleCompetitionStart = () => {
    console.log('🏁 Competition started, hiding countdown modal');
    setCompetitionHasStarted(true);
    setShowCountdown(false);
  };

  // Handle onboarding completion
  const handleOnboardingComplete = () => {
    console.log('✅ Onboarding completed, verifying payment');
    verifyPayment();
  };

  // Ensure the page takes full viewport height and removes any default margins
  useEffect(() => {
    // Set body styles for full-screen trading experience
    document.body.style.margin = '0';
    document.body.style.padding = '0';
    document.body.style.overflow = 'hidden';
    document.documentElement.style.height = '100%';
    document.body.style.height = '100%';

    // Trigger layout recalculation after mount to ensure proper chart display
    // This mimics the behavior that happens when dropdown menus are activated
    const triggerLayoutRecalculation = () => {
      if (containerRef.current) {
        // Force a reflow by reading layout properties
        const container = containerRef.current;
        container.offsetHeight; // Force reflow

        // Dispatch a resize event to ensure chart resizes properly
        window.dispatchEvent(new Event('resize'));

        console.log('✅ Trading page layout recalculation triggered');
      }
    };

    // Multiple triggers to ensure proper initialization
    const timers = [
      setTimeout(triggerLayoutRecalculation, 100),
      setTimeout(triggerLayoutRecalculation, 300),
      setTimeout(triggerLayoutRecalculation, 600)
    ];

    // Cleanup on unmount
    return () => {
      timers.forEach(timer => clearTimeout(timer));
      document.body.style.margin = '';
      document.body.style.padding = '';
      document.body.style.overflow = '';
      document.documentElement.style.height = '';
      document.body.style.height = '';
    };
  }, []);

  // Determine if trading should be blocked
  const shouldBlockTrading = isWhopUser && (!hasPaymentAccess || showCountdown);

  return (
    <div
      ref={containerRef}
      className={`fixed inset-0 bg-[#0A0A0A] text-white ${
        isWhopUser ? 'whop-trading-fullscreen' : ''
      }`}
    >
      {/* Trading Onboarding Modal */}
      <TradingOnboardingModal
        isOpen={isOnboardingOpen}
        onComplete={handleOnboardingComplete}
      />

      {/* Competition Countdown Modal */}
      <CompetitionCountdownModal
        isOpen={showCountdown}
        competitionStartDate={competitionStartDate}
        onCompetitionStart={handleCompetitionStart}
      />

      {/* Main Trading Interface */}
      {!shouldBlockTrading ? (
        <div className="w-full h-full">
          <TradingChart />
        </div>
      ) : (
        // Show loading state while modals are handling access
        <div className="w-full h-full flex items-center justify-center">
          <div className="text-center space-y-4">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white/30 mx-auto"></div>
            <p className="text-white/60">Preparing trading environment...</p>
          </div>
        </div>
      )}

      {/* Debug Controls - Development Only */}
      {import.meta.env.DEV && (
        <div className="fixed top-4 right-4 space-y-2 z-50">
          <Button
            onClick={resetOnboarding}
            variant="outline"
            size="sm"
            className="bg-red-500/20 border-red-500/30 text-red-400 hover:bg-red-500/30"
          >
            Reset Onboarding
          </Button>
        </div>
      )}

      {/* Custom styles for Whop full-screen trading */}
      {isWhopUser && (
        <style>{`
          .whop-trading-fullscreen {
            /* Ensure full-screen experience for Whop users */
            z-index: 1000;
          }

          /* Hide any potential Whop bottom navigation for full-screen trading */
          .whop-trading-fullscreen ~ * {
            display: none !important;
          }
        `}</style>
      )}
    </div>
  );
};

export default Trading;
