import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, TrendingUp, Clock, ArrowRight, Star } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useWatchlist } from '@/contexts/WatchlistContext';
import { useToast } from '@/components/ui/use-toast';
import {
  searchStocks,
  getPopularStocks,
  getRecentSearches,
  addToRecentSearches,
  type StockInfo,
  type StockSearchResult
} from '@/services/stockDataService';

const StockSearch: React.FC = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { isInWatchlist, addToWatchlist, removeFromWatchlist } = useWatchlist();

  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<StockSearchResult[]>([]);
  const [showResults, setShowResults] = useState(false);
  const [popularStocks] = useState<StockInfo[]>(getPopularStocks());
  const [recentSearches, setRecentSearches] = useState<StockInfo[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const searchInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setRecentSearches(getRecentSearches());
  }, []);

  useEffect(() => {
    if (searchQuery.length > 0) {
      const results = searchStocks(searchQuery, 8);
      setSearchResults(results);
      setShowResults(true);
      setSelectedIndex(-1);
    } else {
      setSearchResults([]);
      setShowResults(false);
      setSelectedIndex(-1);
    }
  }, [searchQuery]);

  const handleStockSelect = (ticker: string) => {
    addToRecentSearches(ticker);
    setRecentSearches(getRecentSearches());
    navigate(`/stock/${ticker}`);
  };

  const handleStarToggle = async (e: React.MouseEvent, stock: StockInfo | StockSearchResult) => {
    e.stopPropagation(); // Prevent navigation when clicking star

    const ticker = 'ticker' in stock ? stock.ticker : stock.symbol;
    const name = 'name' in stock ? stock.name : stock.companyName;

    try {
      if (isInWatchlist(ticker)) {
        const success = await removeFromWatchlist(ticker);
        if (success) {
          toast({
            title: 'Removed from Watchlist',
            description: `${ticker} has been removed from your watchlist.`,
          });
        }
      } else {
        const success = await addToWatchlist({ symbol: ticker, name });
        if (success) {
          toast({
            title: 'Added to Watchlist',
            description: `${ticker} has been added to your watchlist.`,
          });
        }
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update watchlist. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showResults || searchResults.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < searchResults.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < searchResults.length) {
          handleStockSelect(searchResults[selectedIndex].ticker);
        } else if (searchResults.length > 0) {
          handleStockSelect(searchResults[0].ticker);
        }
        break;
      case 'Escape':
        setShowResults(false);
        setSelectedIndex(-1);
        break;
    }
  };

  const handleInputFocus = () => {
    if (searchQuery.length > 0) {
      setShowResults(true);
    }
  };

  const handleInputBlur = () => {
    // Delay hiding results to allow for clicks
    setTimeout(() => setShowResults(false), 150);
  };

  return (
    <div className="min-h-screen bg-[#0A0A0A] text-white">
      <div className="max-w-4xl mx-auto px-6 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Stock Search</h1>
          <p className="text-white/60">Search for stocks by ticker symbol or company name</p>
        </div>

        {/* Search Bar */}
        <div className="relative mb-8">
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/40" />
            <Input
              ref={searchInputRef}
              type="text"
              placeholder="Search stocks (e.g., AAPL, Apple, Tesla)..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyDown={handleKeyDown}
              onFocus={handleInputFocus}
              onBlur={handleInputBlur}
              className="pl-12 h-14 text-lg bg-[#1A1A1A] border-white/10 focus:border-[#33C3F0] focus:ring-[#33C3F0]"
            />
          </div>

          {/* Search Results Dropdown */}
          {showResults && searchResults.length > 0 && (
            <Card className="absolute top-full left-0 right-0 mt-2 z-50 bg-[#1A1A1A] border-white/10 shadow-xl">
              <CardContent className="p-0">
                {searchResults.map((stock, index) => (
                  <div
                    key={stock.ticker}
                    className={`flex items-center justify-between p-4 cursor-pointer transition-colors border-b border-white/5 last:border-b-0 ${
                      index === selectedIndex ? 'bg-white/5' : 'hover:bg-white/5'
                    }`}
                    onClick={() => handleStockSelect(stock.ticker)}
                  >
                    <div className="flex-1">
                      <div className="flex items-center gap-3">
                        <span className="font-semibold text-white">{stock.ticker}</span>
                        <span className="text-white/60 text-sm truncate">{stock.name}</span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={(e) => handleStarToggle(e, stock)}
                        className={`w-6 h-6 rounded flex items-center justify-center transition-all duration-200 ${
                          isInWatchlist(stock.ticker)
                            ? 'bg-yellow-500/20 text-yellow-400 hover:bg-yellow-500/30'
                            : 'bg-white/[0.05] text-white/40 hover:bg-white/[0.1] hover:text-white/60'
                        }`}
                        title={isInWatchlist(stock.ticker) ? 'Remove from watchlist' : 'Add to watchlist'}
                      >
                        <Star className={`w-3 h-3 ${isInWatchlist(stock.ticker) ? 'fill-current' : ''}`} />
                      </button>
                      <ArrowRight className="h-4 w-4 text-white/40" />
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Recent Searches */}
        {recentSearches.length > 0 && (
          <div className="mb-8">
            <div className="flex items-center gap-2 mb-4">
              <Clock className="h-5 w-5 text-white/60" />
              <h2 className="text-lg font-semibold">Recent Searches</h2>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              {recentSearches.map((stock) => (
                <Card
                  key={stock.ticker}
                  className="bg-[#1A1A1A] border-white/10 hover:border-white/20 cursor-pointer transition-all hover:bg-[#252525]"
                  onClick={() => handleStockSelect(stock.ticker)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-semibold text-white">{stock.ticker}</div>
                        <div className="text-sm text-white/60 truncate">{stock.name}</div>
                      </div>
                      <div className="flex items-center gap-2">
                        <button
                          onClick={(e) => handleStarToggle(e, stock)}
                          className={`w-6 h-6 rounded flex items-center justify-center transition-all duration-200 ${
                            isInWatchlist(stock.ticker)
                              ? 'bg-yellow-500/20 text-yellow-400 hover:bg-yellow-500/30'
                              : 'bg-white/[0.05] text-white/40 hover:bg-white/[0.1] hover:text-white/60'
                          }`}
                          title={isInWatchlist(stock.ticker) ? 'Remove from watchlist' : 'Add to watchlist'}
                        >
                          <Star className={`w-3 h-3 ${isInWatchlist(stock.ticker) ? 'fill-current' : ''}`} />
                        </button>
                        <ArrowRight className="h-4 w-4 text-white/40" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Popular Stocks */}
        <div>
          <div className="flex items-center gap-2 mb-4">
            <TrendingUp className="h-5 w-5 text-white/60" />
            <h2 className="text-lg font-semibold">Popular Stocks</h2>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {popularStocks.map((stock) => (
              <Card
                key={stock.ticker}
                className="bg-[#1A1A1A] border-white/10 hover:border-white/20 cursor-pointer transition-all hover:bg-[#252525] group"
                onClick={() => handleStockSelect(stock.ticker)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="font-semibold text-white text-lg">{stock.ticker}</div>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={(e) => handleStarToggle(e, stock)}
                        className={`w-6 h-6 rounded flex items-center justify-center transition-all duration-200 ${
                          isInWatchlist(stock.ticker)
                            ? 'bg-yellow-500/20 text-yellow-400 hover:bg-yellow-500/30'
                            : 'bg-white/[0.05] text-white/40 hover:bg-white/[0.1] hover:text-white/60'
                        }`}
                        title={isInWatchlist(stock.ticker) ? 'Remove from watchlist' : 'Add to watchlist'}
                      >
                        <Star className={`w-3 h-3 ${isInWatchlist(stock.ticker) ? 'fill-current' : ''}`} />
                      </button>
                      <ArrowRight className="h-4 w-4 text-white/40 group-hover:text-white/60 transition-colors" />
                    </div>
                  </div>
                  <div className="text-sm text-white/60 truncate">{stock.name}</div>
                  {/* Add popular badge for certain stocks */}
                  {['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META', 'TSLA', 'NVDA'].includes(stock.ticker) && (
                    <Badge variant="secondary" className="mt-2 bg-blue-500/20 text-blue-400 border-blue-500/30">
                      <TrendingUp className="h-3 w-3 mr-1" />
                      Popular
                    </Badge>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Search Tips */}
        <div className="mt-12 p-6 bg-[#1A1A1A] rounded-xl border border-white/10">
          <h3 className="text-lg font-semibold mb-3">Search Tips</h3>
          <ul className="space-y-2 text-white/60">
            <li>• Search by ticker symbol (e.g., "AAPL" for Apple Inc.)</li>
            <li>• Search by company name (e.g., "Apple" or "Microsoft")</li>
            <li>• Use keyboard arrows to navigate results and Enter to select</li>
            <li>• Your recent searches are saved for quick access</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default StockSearch;
