import React, { useState, useEffect } from 'react';
import CleanCandlestickChart from '@/components/charts/CleanCandlestickChart';
import { Button } from '@/components/ui/button';
import { fetchTradeChartData } from '@/services/cleanChartService';

const ChartTest: React.FC = () => {
  const [chartData, setChartData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [symbol, setSymbol] = useState('AAPL');

  // Generate test trade markers
  const tradeMarkers = React.useMemo(() => {
    if (chartData.length === 0) return [];

    const midIndex = Math.floor(chartData.length / 2);
    const entryCandle = chartData[midIndex];
    const exitCandle = chartData[Math.min(midIndex + 10, chartData.length - 1)];

    return [
      {
        timestamp: entryCandle.timestamp,
        price: entryCandle.close,
        type: 'entry' as const,
        label: `BUY $${entryCandle.close.toFixed(2)}`,
        color: '#10b981'
      },
      {
        timestamp: exitCandle.timestamp,
        price: exitCandle.close,
        type: 'exit' as const,
        label: `SELL $${exitCandle.close.toFixed(2)}`,
        color: '#ef4444'
      }
    ];
  }, [chartData]);

  const loadTestData = async () => {
    setIsLoading(true);
    setError(null);
    try {
      // Use current time as trade timestamp
      const tradeTimestamp = Date.now() - (12 * 60 * 60 * 1000); // 12 hours ago
      const data = await fetchTradeChartData(symbol, tradeTimestamp, 24);
      setChartData(data);
    } catch (error) {
      console.error('Error loading test data:', error);
      setError(`Failed to load data for ${symbol}: ${error.message || 'Unknown error'}`);
      setChartData([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadTestData();
  }, [symbol]);

  return (
    <div className="h-full bg-[#0A0A0A] text-white p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-white mb-4">Clean Chart Test</h1>
          <p className="text-white/60 mb-4">
            Testing the new clean candlestick chart implementation
          </p>
          
          <div className="flex gap-4 items-center">
            <input
              type="text"
              value={symbol}
              onChange={(e) => setSymbol(e.target.value.toUpperCase())}
              className="px-3 py-2 bg-white/10 border border-white/20 rounded text-white"
              placeholder="Symbol"
            />
            <Button onClick={loadTestData} disabled={isLoading}>
              {isLoading ? 'Loading...' : 'Load Data'}
            </Button>
          </div>
        </div>

        <div className="bg-[#0F0F11] border border-white/10 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-4">Chart Preview</h2>

          {error && (
            <div className="mb-4 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
              <div className="text-red-400 text-sm font-medium mb-1">Error Loading Data</div>
              <div className="text-white/70 text-sm">{error}</div>
            </div>
          )}

          {isLoading ? (
            <div className="flex items-center justify-center h-96">
              <div className="text-center">
                <div className="h-8 w-8 border-2 border-white/10 border-t-white/30 rounded-full animate-spin mb-3 mx-auto"></div>
                <div className="text-white/70 text-sm">Loading real chart data...</div>
              </div>
            </div>
          ) : (
            <CleanCandlestickChart
              symbol={symbol}
              data={chartData}
              tradeMarkers={tradeMarkers}
              height={500}
              className="w-full"
            />
          )}

          <div className="mt-4 grid grid-cols-3 gap-4 text-sm">
            <div className="bg-white/5 p-3 rounded">
              <div className="text-white/60">Data Points</div>
              <div className="text-white font-mono">{chartData.length} candles</div>
            </div>
            <div className="bg-white/5 p-3 rounded">
              <div className="text-white/60">Timeframe</div>
              <div className="text-white">15-minute intervals</div>
            </div>
            <div className="bg-white/5 p-3 rounded">
              <div className="text-white/60">Trade Markers</div>
              <div className="text-white">{tradeMarkers.length} markers</div>
            </div>
          </div>
        </div>

        <div className="mt-6 bg-[#0F0F11] border border-white/10 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-3">Chart Features</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="text-white font-medium mb-2">✅ Implemented</h4>
              <ul className="text-white/60 space-y-1">
                <li>• Clean candlestick rendering</li>
                <li>• OHLC data display</li>
                <li>• Trade entry/exit markers</li>
                <li>• Interactive tooltips</li>
                <li>• Responsive design</li>
                <li>• Professional styling</li>
              </ul>
            </div>
            <div>
              <h4 className="text-white font-medium mb-2">🎯 Design Goals</h4>
              <ul className="text-white/60 space-y-1">
                <li>• Minimal visual clutter</li>
                <li>• Standard trading platform look</li>
                <li>• Clear price and time axes</li>
                <li>• Easy-to-read candlesticks</li>
                <li>• Fits within modal properly</li>
                <li>• No excessive decorations</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChartTest;
