import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

const Index = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 animate-fade-in">
      <div className="text-center space-y-6 max-w-2xl">
        <h1 className="text-4xl font-bold tracking-tight">
          Welcome to <span className="text-sage">Osis</span>
        </h1>
        <p className="text-xl text-muted-foreground">
          Enhance your trading decisions with Grok-powered insights and organized trade management.
        </p>
        <div className="flex gap-4 justify-center">
          <Button
            onClick={() => navigate("/chat")}
            className="bg-sage hover:bg-sage-light"
          >
            Start Analysis
            <ArrowRight className="ml-2 w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Index;
