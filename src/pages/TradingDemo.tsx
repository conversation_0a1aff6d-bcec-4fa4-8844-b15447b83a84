import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import TradingInterface, { OrdersPanel, Order, Position } from '@/components/TradingInterface/TradingInterface';

const TradingDemo: React.FC = () => {
  const [showTradingInterface, setShowTradingInterface] = useState(false);

  // Orders panel state - managed independently of trading interface
  const [showOrdersPanel, setShowOrdersPanel] = useState<boolean>(false);
  const [orders, setOrders] = useState<Order[]>([]);
  const [positions, setPositions] = useState<Position[]>([
    {
      symbol: 'AAPL',
      quantity: 10,
      averagePrice: 170.50,
      currentPrice: 175.43,
      unrealizedPnL: 49.30,
      unrealizedPnLPercent: 2.89
    },
    {
      symbol: 'TSLA',
      quantity: 5,
      averagePrice: 245.00,
      currentPrice: 238.50,
      unrealizedPnL: -32.50,
      unrealizedPnLPercent: -2.65
    },
    {
      symbol: 'MSFT',
      quantity: 15,
      averagePrice: 380.00,
      currentPrice: 395.25,
      unrealizedPnL: 228.75,
      unrealizedPnLPercent: 4.01
    }
  ]);

  // Mock data for demonstration
  const [mockSymbol, setMockSymbol] = useState('AAPL');
  const mockPrice = 175.43;

  // State for pre-populating trading interface when selling from positions
  const [sellModeData, setSellModeData] = useState<{
    initialTab: 'buy' | 'sell';
    initialQuantity: string;
  }>({
    initialTab: 'buy',
    initialQuantity: '1'
  });

  // Handler for selling positions from the positions menu
  const handleSellPosition = (symbol: string, quantity: number, currentPrice: number) => {
    setMockSymbol(symbol);
    setSellModeData({
      initialTab: 'sell',
      initialQuantity: quantity.toString()
    });
    setShowTradingInterface(true);
  };

  return (
    <div className="min-h-screen bg-[#0A0A0A] text-white p-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-4">Trading Interface Demo</h1>
          <p className="text-white/60 mb-6">
            This demo showcases the new trading interface UI component that has been integrated 
            into the stock screener page. Click the button below to open the trading sidebar.
          </p>
        </div>

        {/* Demo Controls */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Demo Controls</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold">Mock Stock: {mockSymbol}</h3>
                  <p className="text-white/60">Current Price: ${mockPrice}</p>
                </div>
                <Button
                  onClick={() => setShowTradingInterface(true)}
                  className="bg-[#00C853] hover:bg-[#00D85A] text-white"
                >
                  Open Trading Interface
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Features List */}
        <Card>
          <CardHeader>
            <CardTitle>Trading Interface Features</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-2 text-[#00C853]">UI Components</h4>
                <ul className="space-y-1 text-white/80">
                  <li>• Toggleable sidebar with smooth animations</li>
                  <li>• Buy/Sell tabs with color-coded styling</li>
                  <li>• Order type selector (Market, Limit, Stop)</li>
                  <li>• Quantity and price input fields</li>
                  <li>• Account balance display</li>
                  <li>• Order summary with estimated total</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2 text-[#00C853]">Design Features</h4>
                <ul className="space-y-1 text-white/80">
                  <li>• Consistent with existing design system</li>
                  <li>• Glass morphism effects and backdrop blur</li>
                  <li>• Responsive design for mobile devices</li>
                  <li>• Green/red color scheme for buy/sell actions</li>
                  <li>• Professional trading interface styling</li>
                  <li>• Smooth slide-in/out animations</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Integration Notes */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Integration Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 text-white/80">
              <p>
                <strong className="text-white">Chart Integration:</strong> The trading interface 
                is integrated into the TradingChart component with a "Trade" button alongside 
                the existing Tools, Favorite, and Settings buttons.
              </p>
              <p>
                <strong className="text-white">Non-functional:</strong> This is a UI-only 
                implementation. The submit order functionality is not connected to any trading 
                API and will only log the order details to the console.
              </p>
              <p>
                <strong className="text-white">Responsive:</strong> The interface adapts to 
                different screen sizes, showing as a sidebar on desktop and a full-screen 
                overlay on mobile devices.
              </p>
              <p>
                <strong className="text-white">Accessibility:</strong> Includes proper ARIA 
                labels, keyboard navigation support, and focus management.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Trading Interface Component */}
      <TradingInterface
        isOpen={showTradingInterface}
        onClose={() => {
          setShowTradingInterface(false);
          // Reset sell mode data when closing
          setSellModeData({
            initialTab: 'buy',
            initialQuantity: '1'
          });
        }}
        symbol={mockSymbol}
        currentPrice={mockPrice}
        showOrdersPanel={showOrdersPanel}
        setShowOrdersPanel={setShowOrdersPanel}
        orders={orders}
        setOrders={setOrders}
        positions={positions}
        setPositions={setPositions}
        initialTab={sellModeData.initialTab}
        initialQuantity={sellModeData.initialQuantity}
      />

      {/* Orders Panel - Independent of Trading Interface */}
      <OrdersPanel
        isOpen={showOrdersPanel}
        onClose={() => setShowOrdersPanel(false)}
        orders={orders}
        positions={positions}
        onSellPosition={handleSellPosition}
      />
    </div>
  );
};

export default TradingDemo;
