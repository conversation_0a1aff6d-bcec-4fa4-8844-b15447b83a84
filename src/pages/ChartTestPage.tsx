import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';





const ChartTestPage: React.FC = () => {

  return (
    <div className="min-h-screen bg-[#0A0A0C] p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-white mb-2">Chart Testing Page</h1>
          <p className="text-white/70">
            Test candlestick chart components
          </p>
        </div>

        {/* Test Controls */}
        <Card className="bg-[#0F0F11] border-[#1A1A1C]">
          <CardHeader>
            <CardTitle className="text-white">Chart Display Tests</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button
                onClick={() => openModal()}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                Test Modal - Overview
              </Button>
              
              <Button
                onClick={() => openModal(0)}
                className="bg-purple-600 hover:bg-purple-700 text-white"
              >
                Test Modal - First Trade
              </Button>
              
              <Button
                onClick={() => openModal(2)}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                Test Modal - Third Trade
              </Button>
            </div>

            <div className="mt-6">
              <h4 className="text-white font-medium mb-3">Expected Features:</h4>
              <ul className="text-white/70 space-y-1 text-sm list-disc list-inside">
                <li>✅ Candlestick chart should display with proper OHLC bars</li>
                <li>✅ Green candles for bullish periods, red for bearish</li>
                <li>✅ Zoom controls (Focus/Full view, Zoom In/Out/Reset)</li>
                <li>✅ Trade annotations with entry/exit markers</li>
                <li>✅ Interactive trade selection in the trade list</li>
                <li>✅ Trade setup analysis with confidence indicators</li>
                <li>✅ Responsive design for different screen sizes</li>
                <li>✅ Proper chart tooltips showing OHLC data</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Chart Test Placeholder */}
        <Card className="bg-[#0F0F11] border-[#1A1A1C]">
          <CardHeader>
            <CardTitle className="text-white">Chart Testing</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <p className="text-white/60">Chart testing components have been removed</p>
              <p className="text-white/40 text-sm mt-2">Trade click functionality has been disabled</p>
            </div>
          </CardContent>
        </Card>

        {/* Debug Information */}
        <Card className="bg-[#0F0F11] border-[#1A1A1C]">
          <CardHeader>
            <CardTitle className="text-white">Debug Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="text-white font-medium mb-2">Chart Implementation:</h4>
                <div className="text-white/70 text-sm space-y-1">
                  <p>• Using SimpleCandlestickChart component</p>
                  <p>• Recharts library with ComposedChart</p>
                  <p>• Custom Bar components for wicks and bodies</p>
                  <p>• ReferenceLine components for trade annotations</p>
                </div>
              </div>

              <div>
                <h4 className="text-white font-medium mb-2">Data Flow:</h4>
                <div className="text-white/70 text-sm space-y-1">
                  <p>• TradeChart → SimpleCandlestickChart</p>
                  <p>• Chart data service provides OHLCV data</p>
                  <p>• Smart date range calculation based on zoom level</p>
                  <p>• Trade annotations generated from trade data</p>
                </div>
              </div>

              <div>
                <h4 className="text-white font-medium mb-2">Test Data:</h4>
                <div className="text-white/70 text-sm space-y-1">
                  <p>• Generated test candlestick data</p>
                  <p>• Sample OHLCV data points</p>
                  <p>• Date Range: 30 days</p>
                  <p>• Price Range: $140 - $160</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

      </div>
    </div>
  );
};

export default ChartTestPage;
