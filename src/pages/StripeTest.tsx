import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { validateStripeSession, createValidatedCheckoutSession } from '@/utils/stripeUtils';
import { Loader2, CheckCircle, XCircle } from 'lucide-react';

const StripeTest = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<any[]>([]);
  const { toast } = useToast();

  const addTestResult = (test: string, success: boolean, details?: any) => {
    setTestResults(prev => [...prev, {
      test,
      success,
      details,
      timestamp: new Date().toISOString()
    }]);
  };

  const testBasicCheckout = async () => {
    setIsLoading(true);
    try {
      // Test with a basic plan price ID
      const data = await createValidatedCheckoutSession('price_1RVyDZFagIdq3bE61UyNGiAj', {
        returnUrl: window.location.origin + '/stripe-test?success=true',
        skipTrial: true
      });

      if (data.error) {
        addTestResult('Basic Checkout Creation', false, data.error);
        throw new Error(data.error);
      }

      addTestResult('Basic Checkout Creation', true, {
        hasUrl: !!data.url,
        hasSessionId: !!data.sessionId,
        metadata: data.metadata
      });

      toast({
        title: 'Test Successful',
        description: 'Basic checkout session created successfully',
      });
    } catch (error) {
      console.error('Basic checkout test failed:', error);
      toast({
        title: 'Test Failed',
        description: error.message || 'Basic checkout test failed',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const testSessionValidation = async () => {
    setIsLoading(true);
    try {
      // First create a session
      const data = await createValidatedCheckoutSession('price_1RVyDZFagIdq3bE61UyNGiAj', {
        returnUrl: window.location.origin + '/stripe-test?success=true',
        skipTrial: true
      });

      if (data.error) {
        addTestResult('Session Validation Test', false, 'Failed to create session for validation test');
        throw new Error(data.error);
      }

      // Extract session ID
      let sessionId = data.sessionId;
      if (!sessionId && data.url) {
        const urlMatch = data.url.match(/\/pay\/(cs_[^#?]+)/);
        sessionId = urlMatch ? urlMatch[1] : null;
      }

      if (!sessionId) {
        addTestResult('Session Validation Test', false, 'No session ID found');
        throw new Error('No session ID found');
      }

      // Test validation
      const validation = await validateStripeSession(sessionId);
      
      addTestResult('Session Validation Test', validation.valid, {
        sessionId,
        validationResult: validation
      });

      if (validation.valid) {
        toast({
          title: 'Validation Successful',
          description: 'Session validation test passed',
        });
      } else {
        throw new Error(validation.error || 'Session validation failed');
      }
    } catch (error) {
      console.error('Session validation test failed:', error);
      toast({
        title: 'Validation Failed',
        description: error.message || 'Session validation test failed',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const testInvalidSession = async () => {
    setIsLoading(true);
    try {
      // Test with an invalid session ID
      const validation = await validateStripeSession('cs_invalid_session_id_12345');
      
      addTestResult('Invalid Session Test', !validation.valid, {
        expectedInvalid: true,
        actualResult: validation
      });

      if (!validation.valid) {
        toast({
          title: 'Test Successful',
          description: 'Invalid session correctly identified as invalid',
        });
      } else {
        throw new Error('Invalid session was incorrectly validated as valid');
      }
    } catch (error) {
      console.error('Invalid session test failed:', error);
      toast({
        title: 'Test Failed',
        description: error.message || 'Invalid session test failed',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="min-h-screen bg-[#0A0A0A] text-white p-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-4">Stripe Integration Test</h1>
          <p className="text-white/60">
            Test the Stripe checkout and validation functionality
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <Card className="bg-white/[0.02] border-white/[0.06]">
            <CardHeader>
              <CardTitle className="text-white">Checkout Tests</CardTitle>
              <CardDescription className="text-white/60">
                Test checkout session creation and validation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button
                onClick={testBasicCheckout}
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                Test Basic Checkout
              </Button>
              
              <Button
                onClick={testSessionValidation}
                disabled={isLoading}
                variant="outline"
                className="w-full"
              >
                {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                Test Session Validation
              </Button>
              
              <Button
                onClick={testInvalidSession}
                disabled={isLoading}
                variant="secondary"
                className="w-full"
              >
                {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                Test Invalid Session
              </Button>
            </CardContent>
          </Card>

          <Card className="bg-white/[0.02] border-white/[0.06]">
            <CardHeader>
              <CardTitle className="text-white">Test Results</CardTitle>
              <CardDescription className="text-white/60">
                Results from the latest tests
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {testResults.length === 0 ? (
                  <p className="text-white/40 text-sm">No tests run yet</p>
                ) : (
                  testResults.map((result, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-2 p-2 rounded bg-white/[0.02] border border-white/[0.04]"
                    >
                      {result.success ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-500" />
                      )}
                      <div className="flex-1">
                        <p className="text-sm font-medium text-white">{result.test}</p>
                        {result.details && (
                          <p className="text-xs text-white/60">
                            {typeof result.details === 'string' 
                              ? result.details 
                              : JSON.stringify(result.details, null, 2).substring(0, 100) + '...'
                            }
                          </p>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
              {testResults.length > 0 && (
                <Button
                  onClick={clearResults}
                  variant="outline"
                  size="sm"
                  className="w-full mt-4"
                >
                  Clear Results
                </Button>
              )}
            </CardContent>
          </Card>
        </div>

        <Card className="bg-white/[0.02] border-white/[0.06]">
          <CardHeader>
            <CardTitle className="text-white">Debug Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <p><span className="text-white/60">Supabase URL:</span> {import.meta.env.VITE_SUPABASE_URL}</p>
              <p><span className="text-white/60">Stripe Publishable Key:</span> {import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY?.substring(0, 20)}...</p>
              <p><span className="text-white/60">Current URL:</span> {window.location.href}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default StripeTest;
