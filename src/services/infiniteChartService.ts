import { format, addDays, subDays } from 'date-fns';
import { supabase } from '@/integrations/supabase/client';
import { FocusedOHLCVData, DataSegment, InfiniteChartData } from './tradeAnalysisService';

// Each segment represents 7 days of 15-minute data (~672 candles) for better API efficiency
const SEGMENT_DURATION_DAYS = 7;
const SEGMENT_DURATION_MS = SEGMENT_DURATION_DAYS * 24 * 60 * 60 * 1000;

// Configuration for unlimited data loading
const UNLIMITED_CONFIG = {
  MAX_SEGMENTS_IN_MEMORY: 100,       // Keep up to 100 segments (700 days) in memory
  PRELOAD_SEGMENTS_AHEAD: 10,        // Preload 10 segments ahead of viewport
  PRELOAD_SEGMENTS_BEHIND: 10,       // Preload 10 segments behind viewport
  CLEANUP_THRESHOLD: 200,            // Clean up when we have more than 200 segments
  MAX_RETRIES_PER_SEGMENT: 5,        // More aggressive retries for real data
  SEGMENT_CACHE_SIZE: 500,           // Cache up to 500 segments (10+ years of data)
  INITIAL_LOAD_SEGMENTS: 20          // Load 20 segments initially (140 days)
};

/**
 * Generate a unique key for a data segment based on start time
 */
function getSegmentKey(startTime: number): string {
  return format(new Date(startTime), 'yyyy-MM-dd');
}

/**
 * Calculate which segments are needed for a given time range
 * Now handles unlimited time ranges efficiently
 */
function getRequiredSegments(startTime: number, endTime: number): string[] {
  const segments: string[] = [];

  // Start from the beginning of the week containing startTime
  let currentTime = new Date(startTime);
  const dayOfWeek = currentTime.getDay();
  const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Monday = 0
  currentTime.setDate(currentTime.getDate() - daysToSubtract);
  currentTime.setHours(0, 0, 0, 0);

  const endDate = new Date(endTime);

  while (currentTime <= endDate) {
    segments.push(getSegmentKey(currentTime.getTime()));
    currentTime = addDays(currentTime, SEGMENT_DURATION_DAYS);
  }

  return segments;
}

/**
 * Get segments for unlimited expansion around a viewport
 */
function getExpandedSegments(centerTime: number, viewportRange: number, expansionFactor: number = 3): string[] {
  const expandedRange = viewportRange * expansionFactor;
  const startTime = centerTime - expandedRange / 2;
  const endTime = centerTime + expandedRange / 2;

  return getRequiredSegments(startTime, endTime);
}



/**
 * Fetch real data for a segment from the API (now fetches 7 days of data)
 */
async function fetchSegmentData(
  symbol: string,
  startTime: number
): Promise<FocusedOHLCVData[]> {
  const startDate = format(new Date(startTime), 'yyyy-MM-dd');
  const endDate = format(addDays(new Date(startTime), SEGMENT_DURATION_DAYS), 'yyyy-MM-dd');

  console.log(`[InfiniteChart] Fetching REAL data for ${symbol} from ${startDate} to ${endDate}`);

  try {
    const requestBody = {
      action: 'focused-trade-data',
      symbol,
      startDate,
      endDate,
      timeframe: '15minute'
    };

    console.log(`[InfiniteChart] Request body:`, JSON.stringify(requestBody, null, 2));

    const { data, error } = await supabase.functions.invoke('chart-processor', {
      body: JSON.stringify(requestBody)
    });

    console.log(`[InfiniteChart] API Response:`, { data, error });

    if (error) {
      console.error(`[InfiniteChart] Supabase function error:`, error);
      throw new Error(`Supabase function error: ${JSON.stringify(error)}`);
    }

    if (!data) {
      console.error(`[InfiniteChart] No data returned from API`);
      throw new Error('No data returned from API');
    }

    if (!data.results) {
      console.error(`[InfiniteChart] No results field in response:`, data);
      throw new Error(`No results field in response: ${JSON.stringify(data)}`);
    }

    if (!Array.isArray(data.results)) {
      console.error(`[InfiniteChart] Results is not an array:`, data.results);
      throw new Error(`Results is not an array: ${typeof data.results}`);
    }

    if (data.results.length === 0) {
      console.warn(`[InfiniteChart] Empty results array for ${symbol} on ${startDate}`);
      // For empty results, still try to use real data structure but fall back to mock
      throw new Error(`Empty results for ${symbol} on ${startDate}`);
    }

    console.log(`[InfiniteChart] SUCCESS: Retrieved ${data.results.length} real data points for ${symbol}`);

    // Transform API data to our format
    const transformedData = data.results.map((item: any) => {
      if (!item.t || !item.o || !item.h || !item.l || !item.c) {
        console.warn(`[InfiniteChart] Invalid data point:`, item);
        return null;
      }

      return {
        timestamp: item.t,
        date: format(new Date(item.t), 'yyyy-MM-dd HH:mm:ss'),
        open: Number(item.o),
        high: Number(item.h),
        low: Number(item.l),
        close: Number(item.c),
        volume: Number(item.v || 0)
      };
    }).filter(Boolean) as FocusedOHLCVData[];

    if (transformedData.length === 0) {
      throw new Error(`All data points were invalid for ${symbol} on ${startDate}`);
    }

    console.log(`[InfiniteChart] Transformed ${transformedData.length} valid data points`);
    console.log(`[InfiniteChart] Sample data point:`, transformedData[0]);

    return transformedData;

  } catch (error) {
    console.error(`[InfiniteChart] Failed to fetch real data for ${symbol} on ${startDate}:`, error);
    throw new Error(`Failed to fetch chart data for ${symbol}: ${error.message || 'Unknown error'}`);
  }
}

/**
 * Get a realistic base price for mock data based on symbol
 */
function getRealisticBasePrice(symbol: string): number {
  const priceMap: Record<string, number> = {
    'AAPL': 175,
    'GOOGL': 140,
    'MSFT': 380,
    'AMZN': 145,
    'TSLA': 240,
    'NVDA': 450,
    'META': 320,
    'NFLX': 450,
    'SPY': 450,
    'QQQ': 380
  };

  return priceMap[symbol.toUpperCase()] || 150;
}

/**
 * Infinite Chart Data Manager
 */
export class InfiniteChartManager {
  private data: InfiniteChartData;
  private loadingPromises: Map<string, Promise<void>> = new Map();
  private onDataUpdate?: (data: InfiniteChartData) => void;

  constructor(
    symbol: string,
    tradeTimestamp: number,
    entryPrice: number,
    onDataUpdate?: (data: InfiniteChartData) => void
  ) {
    this.data = {
      symbol,
      segments: new Map(),
      tradeTimestamp,
      entryPrice,
      direction: 'LONG'
    };
    this.onDataUpdate = onDataUpdate;
  }

  /**
   * Get all data within a time range, loading segments as needed
   * Now supports unlimited time ranges
   */
  async getDataInRange(startTime: number, endTime: number): Promise<FocusedOHLCVData[]> {
    console.log(`[InfiniteChart] Getting data for unlimited range: ${format(new Date(startTime), 'yyyy-MM-dd')} to ${format(new Date(endTime), 'yyyy-MM-dd')}`);

    const requiredSegments = getRequiredSegments(startTime, endTime);
    console.log(`[InfiniteChart] Required segments: ${requiredSegments.length} segments`);

    // Load any missing segments (no limits)
    await this.loadSegments(requiredSegments);

    // Combine data from all segments in range
    const allData: FocusedOHLCVData[] = [];
    let loadedSegments = 0;

    for (const segmentKey of requiredSegments) {
      const segment = this.data.segments.get(segmentKey);
      if (segment && segment.isLoaded) {
        // Filter data to exact time range
        const segmentData = segment.data.filter(
          candle => candle.timestamp >= startTime && candle.timestamp <= endTime
        );
        allData.push(...segmentData);
        loadedSegments++;
      }
    }

    console.log(`[InfiniteChart] Loaded ${loadedSegments}/${requiredSegments.length} segments, ${allData.length} total candles`);

    // Sort by timestamp for proper chronological order
    allData.sort((a, b) => a.timestamp - b.timestamp);

    // Trigger aggressive preloading for smooth infinite scrolling
    this.aggressivePreload(startTime, endTime);

    return allData;
  }

  /**
   * Aggressive preloading for unlimited scrolling
   */
  private aggressivePreload(viewportStart: number, viewportEnd: number): void {
    const viewportRange = viewportEnd - viewportStart;
    const centerTime = (viewportStart + viewportEnd) / 2;

    // Preload much larger ranges for smooth infinite scrolling
    const preloadSegments = getExpandedSegments(centerTime, viewportRange, 15); // 15x expansion for truly unlimited feel

    console.log(`[InfiniteChart] Aggressive preloading ${preloadSegments.length} segments for unlimited scrolling`);

    // Load in background without waiting
    this.loadSegments(preloadSegments).catch(error => {
      console.warn('[InfiniteChart] Background preloading failed:', error);
    });
  }

  /**
   * Load segments that aren't already loaded or loading
   * Now supports unlimited concurrent loading
   */
  private async loadSegments(segmentKeys: string[]): Promise<void> {
    const loadPromises: Promise<void>[] = [];

    console.log(`[InfiniteChart] Loading ${segmentKeys.length} segments for unlimited data`);

    for (const segmentKey of segmentKeys) {
      if (!this.data.segments.has(segmentKey) && !this.loadingPromises.has(segmentKey)) {
        loadPromises.push(this.loadSegment(segmentKey));
      }
    }

    if (loadPromises.length > 0) {
      console.log(`[InfiniteChart] Starting ${loadPromises.length} concurrent segment loads`);

      // Load all segments concurrently for maximum speed
      const results = await Promise.allSettled(loadPromises);

      // Log results
      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;
      console.log(`[InfiniteChart] Segment loading complete: ${successful} successful, ${failed} failed`);

      // Clean up memory if we have too many segments
      this.intelligentCleanup();
    }
  }

  /**
   * Load a single segment
   */
  private async loadSegment(segmentKey: string): Promise<void> {
    // Create loading promise to prevent duplicate requests
    const loadPromise = this.doLoadSegment(segmentKey);
    this.loadingPromises.set(segmentKey, loadPromise);
    
    try {
      await loadPromise;
    } finally {
      this.loadingPromises.delete(segmentKey);
    }
  }

  /**
   * Actually perform the segment loading
   */
  private async doLoadSegment(segmentKey: string): Promise<void> {
    const segmentDate = new Date(segmentKey + 'T00:00:00');
    const startTime = segmentDate.getTime();
    
    // Create segment with loading state
    const segment: DataSegment = {
      startTime,
      endTime: startTime + SEGMENT_DURATION_MS,
      data: [],
      isLoading: true,
      isLoaded: false
    };
    
    this.data.segments.set(segmentKey, segment);
    this.notifyDataUpdate();
    
    try {
      console.log(`[InfiniteChart] Loading segment ${segmentKey} for ${this.data.symbol}`);

      // Fetch the data with retry logic
      const segmentData = await this.fetchSegmentDataWithRetry(segmentKey, startTime);

      // Update segment with loaded data
      segment.data = segmentData;
      segment.isLoading = false;
      segment.isLoaded = true;

      console.log(`[InfiniteChart] Successfully loaded segment ${segmentKey} with ${segmentData.length} data points`);

      this.notifyDataUpdate();

    } catch (error) {
      console.error(`[InfiniteChart] Failed to load segment ${segmentKey} after retries:`, error);

      // Mark as failed with empty data
      segment.data = [];
      segment.isLoading = false;
      segment.isLoaded = false; // Mark as failed, not loaded

      console.error(`[InfiniteChart] Segment ${segmentKey} failed to load - no data available`);

      this.notifyDataUpdate();
    }
  }

  /**
   * Fetch segment data with aggressive retry logic for unlimited real data
   */
  private async fetchSegmentDataWithRetry(segmentKey: string, startTime: number, maxRetries: number = UNLIMITED_CONFIG.MAX_RETRIES_PER_SEGMENT): Promise<FocusedOHLCVData[]> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`[InfiniteChart] Attempt ${attempt}/${maxRetries} to fetch segment ${segmentKey}`);

        const data = await fetchSegmentData(this.data.symbol, startTime);

        // Validate that we got real data (not mock)
        if (data.length > 0 && data[0].date.includes(':')) {
          console.log(`[InfiniteChart] Got real data on attempt ${attempt} for segment ${segmentKey}`);
          return data;
        } else if (data.length > 0) {
          console.log(`[InfiniteChart] Got mock data on attempt ${attempt} for segment ${segmentKey}, retrying...`);
          throw new Error('Received mock data instead of real data');
        } else {
          throw new Error('Received empty data');
        }

      } catch (error) {
        console.warn(`[InfiniteChart] Attempt ${attempt} failed for segment ${segmentKey}:`, error);
        lastError = error instanceof Error ? error : new Error(String(error));

        if (attempt < maxRetries) {
          // Wait before retrying (exponential backoff)
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
          console.log(`[InfiniteChart] Waiting ${delay}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    console.error(`[InfiniteChart] All ${maxRetries} attempts failed for segment ${segmentKey}`);
    throw lastError || new Error(`Failed to fetch segment ${segmentKey} after ${maxRetries} attempts`);
  }

  /**
   * Preload segments around a given time range for unlimited smooth scrolling
   */
  async preloadAroundRange(startTime: number, endTime: number, bufferWeeks: number = 4): Promise<void> {
    const bufferMs = bufferWeeks * 7 * 24 * 60 * 60 * 1000; // Buffer in weeks now
    const expandedStart = startTime - bufferMs;
    const expandedEnd = endTime + bufferMs;

    console.log(`[InfiniteChart] Preloading ${bufferWeeks} weeks of data around viewport`);

    const segmentsToPreload = getRequiredSegments(expandedStart, expandedEnd);

    // Load in background without waiting - unlimited preloading
    this.loadSegments(segmentsToPreload).catch(error => {
      console.warn('[InfiniteChart] Background preloading failed:', error);
    });
  }

  /**
   * Intelligent cleanup to manage memory with unlimited data
   */
  private intelligentCleanup(): void {
    const segmentCount = this.data.segments.size;

    if (segmentCount <= UNLIMITED_CONFIG.CLEANUP_THRESHOLD) {
      return; // No cleanup needed yet
    }

    console.log(`[InfiniteChart] Starting intelligent cleanup: ${segmentCount} segments in memory`);

    // Get all segments sorted by last access time (if we tracked it) or by distance from current time
    const currentTime = Date.now();
    const segmentEntries = Array.from(this.data.segments.entries());

    // Sort by distance from current time (keep recent data)
    segmentEntries.sort(([keyA, segmentA], [keyB, segmentB]) => {
      const timeA = Math.abs(segmentA.startTime - currentTime);
      const timeB = Math.abs(segmentB.startTime - currentTime);
      return timeB - timeA; // Furthest first (for removal)
    });

    // Remove the furthest segments, keeping the most recent ones
    const segmentsToRemove = segmentEntries.slice(UNLIMITED_CONFIG.MAX_SEGMENTS_IN_MEMORY);

    for (const [segmentKey] of segmentsToRemove) {
      this.data.segments.delete(segmentKey);
    }

    console.log(`[InfiniteChart] Cleanup complete: Removed ${segmentsToRemove.length} segments, ${this.data.segments.size} remaining`);
  }

  /**
   * Get the current data state
   */
  getData(): InfiniteChartData {
    return this.data;
  }

  /**
   * Notify listeners of data updates
   */
  private notifyDataUpdate(): void {
    if (this.onDataUpdate) {
      this.onDataUpdate(this.data);
    }
  }

  /**
   * Clean up old segments to prevent memory leaks (now uses intelligent cleanup)
   */
  cleanupOldSegments(currentViewStart: number, currentViewEnd: number, keepWeeks: number = 20): void {
    // Use intelligent cleanup instead of simple distance-based cleanup
    this.intelligentCleanup();

    console.log(`[InfiniteChart] Memory cleanup: ${this.data.segments.size} segments remaining in cache`);
  }
}
