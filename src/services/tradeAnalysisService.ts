import { format, subHours, addHours } from 'date-fns';
import { supabase } from '@/integrations/supabase/client';

export interface FocusedOHLCVData {
  timestamp: number;
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface TradeAnalysisData {
  symbol: string;
  data: FocusedOHLCVData[];
  tradeTimestamp: number;
  entryPrice: number;
  exitPrice?: number;
  direction: 'LONG' | 'SHORT';
  contextHours: number;
}

export interface DataSegment {
  startTime: number;
  endTime: number;
  data: FocusedOHLCVData[];
  isLoading: boolean;
  isLoaded: boolean;
}

export interface InfiniteChartData {
  symbol: string;
  segments: Map<string, DataSegment>;
  tradeTimestamp: number;
  entryPrice: number;
  exitPrice?: number;
  direction: 'LONG' | 'SHORT';
}

/**
 * Calculate extended timeframe around trade execution
 * Returns full trading day data for comprehensive analysis
 */
export function calculateExtendedTimeframe(tradeDate: string): {
  startDate: string;
  endDate: string;
} {
  // Parse the trade date - it might be just "YYYY-MM-DD" format from backtest
  let tradeTime: Date;

  if (tradeDate.includes('T') || tradeDate.includes(' ')) {
    // Full datetime format
    tradeTime = new Date(tradeDate);
  } else {
    // Just date format (YYYY-MM-DD) - assume market open time (9:30 AM ET)
    tradeTime = new Date(`${tradeDate}T09:30:00-05:00`);
  }

  // Get the full trading week around the trade
  // Start from 3 days before the trade date
  const startTime = new Date(tradeTime);
  startTime.setDate(startTime.getDate() - 3);

  // End 3 days after the trade date
  const endTime = new Date(tradeTime);
  endTime.setDate(endTime.getDate() + 3);

  return {
    startDate: format(startTime, 'yyyy-MM-dd'),
    endDate: format(endTime, 'yyyy-MM-dd')
  };
}

/**
 * Fetch focused OHLCV data around trade execution time
 * Uses specified timeframe for detailed market context analysis
 */
export async function fetchFocusedTradeData(
  symbol: string,
  tradeDate: string,
  timeframe: string = '15min'
): Promise<TradeAnalysisData> {
  try {
    const { startDate, endDate } = calculateExtendedTimeframe(tradeDate);

    console.log(`Fetching extended trade data for ${symbol} from ${startDate} to ${endDate} with timeframe ${timeframe}`);
    console.log(`Original trade date: ${tradeDate}`);

    const requestBody = {
      action: 'focused-trade-data',
      symbol,
      startDate,
      endDate,
      timeframe
    };

    console.log('Request body:', JSON.stringify(requestBody, null, 2));

    // Call Supabase edge function with specified timeframe
    const { data, error } = await supabase.functions.invoke('chart-processor', {
      body: JSON.stringify(requestBody)
    });

    console.log('Edge function response:', { data, error });

    if (error) {
      console.error('Error fetching focused trade data:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));
      throw new Error(`Failed to fetch trade data for ${symbol}: ${error.message || 'Unknown error'}`);
    }

    if (!data || !data.results || data.results.length === 0) {
      console.error(`No extended data available for ${symbol} around ${tradeDate}`);
      throw new Error(`No trade data available for ${symbol} in the specified time range`);
    }

    // Transform Polygon data to our format
    const transformedData: FocusedOHLCVData[] = data.results.map((item: any) => ({
      timestamp: item.t,
      date: format(new Date(item.t), 'yyyy-MM-dd HH:mm:ss'),
      open: item.o,
      high: item.h,
      low: item.l,
      close: item.c,
      volume: item.v
    }));

    return {
      symbol,
      data: transformedData,
      tradeTimestamp: new Date(tradeDate).getTime(),
      entryPrice: 0, // Will be set by caller
      direction: 'LONG',
      contextHours: 0 // Not applicable for extended data
    };

  } catch (error) {
    console.error('Error in fetchFocusedTradeData:', error);
    throw error;
  }
}


