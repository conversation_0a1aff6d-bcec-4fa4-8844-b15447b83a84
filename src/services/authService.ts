import { supabase } from '@/integrations/supabase/client';
import { getCurrentAuthenticatedUser } from '@/utils/authUtils';

export interface AuthenticatedUser {
  id: string;
  email: string;
  user_metadata?: {
    username?: string;
    full_name?: string;
    avatar_url?: string;
    isWhopUser?: boolean;
    whop_user_id?: string;
    [key: string]: any;
  };
  isWhopUser: boolean;
}

/**
 * Universal authentication function that works for both regular and Whop users
 * This should be used by ALL services instead of supabase.auth.getUser()
 */
export const getAuthenticatedUser = async (): Promise<AuthenticatedUser> => {
  // First try regular Supabase authentication
  const { data: { user: supabaseUser }, error: supabaseError } = await supabase.auth.getUser();

  if (supabaseUser && !supabaseError) {
    console.log('✅ Found authenticated Supabase user:', supabaseUser.id, supabaseUser.user_metadata?.isWhopUser ? '(Whop)' : '(Regular)');
    return {
      id: supabaseUser.id,
      email: supabaseUser.email!,
      user_metadata: supabaseUser.user_metadata,
      isWhopUser: supabaseUser.user_metadata?.isWhopUser === true
    };
  }

  // If no Supabase user but we have Whop user data, try to re-authenticate
  if (typeof window !== 'undefined') {
    const whopUserData = localStorage.getItem('whop_user_data');
    if (whopUserData) {
      try {
        const whopUser = JSON.parse(whopUserData);
        console.log('🔄 No Supabase session found, attempting to re-authenticate Whop user:', whopUser.username);

        const { signInWhopUser } = await import('./whopAuthService');
        const authResult = await signInWhopUser(whopUser);

        if (authResult.success && authResult.session) {
          console.log('✅ Successfully re-authenticated Whop user');

          // Get the user again after re-authentication
          const { data: { user: reAuthUser } } = await supabase.auth.getUser();
          if (reAuthUser) {
            return {
              id: reAuthUser.id,
              email: reAuthUser.email!,
              user_metadata: reAuthUser.user_metadata,
              isWhopUser: true
            };
          }
        } else {
          console.error('❌ Re-authentication failed for Whop user - session creation is required for API access');
          console.error('❌ Whop user exists but cannot create session - this will cause 401 errors');

          // Don't fall back to unified auth for Whop users without sessions
          // This prevents the false positive where the app thinks they're authenticated
          // but edge functions will reject them
          throw new Error('Whop user authentication failed - session required for API access');
        }
      } catch (reAuthError) {
        console.error('❌ Critical: Failed to re-authenticate Whop user:', reAuthError);
        throw new Error('Whop user authentication failed - please refresh the page');
      }
    }
  }

  // Enhanced error message for debugging
  console.error('❌ No authenticated user found. This may indicate:');
  console.error('  - User is not logged in');
  console.error('  - Whop user authentication failed');
  console.error('  - Session expired or invalid');

  throw new Error('User not authenticated');
};

/**
 * Check if user is authenticated without throwing an error
 */
export const isAuthenticated = async (): Promise<boolean> => {
  try {
    await getAuthenticatedUser();
    return true;
  } catch {
    return false;
  }
};

/**
 * Get user ID for database operations
 */
export const getCurrentUserId = async (): Promise<string> => {
  const user = await getAuthenticatedUser();
  return user.id;
};

/**
 * Ensure user has a valid Supabase session, refreshing if necessary
 * This is useful for services that require authentication tokens
 */
export const ensureAuthenticated = async (): Promise<{ user: AuthenticatedUser; session: any }> => {
  // Get authenticated user (this will try to re-authenticate if needed)
  const user = await getAuthenticatedUser();

  // Get current session
  const { data: { session }, error } = await supabase.auth.getSession();

  console.log('🔍 Session check after authentication:', {
    hasSession: !!session,
    hasAccessToken: !!session?.access_token,
    sessionError: error,
    userId: user.id,
    isWhopUser: user.isWhopUser
  });

  if (!session?.access_token) {
    console.error('❌ No valid session found after authentication');

    // For Whop users, provide specific guidance
    if (user.isWhopUser) {
      console.error('💡 Whop user session issue - this may indicate:');
      console.error('  - Session creation failed in whop-user-auth function');
      console.error('  - Credentials-based sign-in failed');
      console.error('  - User exists but needs manual authentication');
      throw new Error('Whop user authentication session failed - please refresh the page');
    }

    throw new Error('Failed to establish authenticated session');
  }

  console.log('✅ User authenticated with valid session');
  return { user, session };
};
