import { supabase } from '@/integrations/supabase/client';

// Types for the discover service
export interface PublishedAgent {
  id: string;
  agentId: string;
  name: string;
  description?: string;
  category: string;
  tags: string[];
  publisherName: string;
  publisherId: string;
  downloadCount: number;
  averageRating: number;
  totalReviews: number;
  isFeatured: boolean;
  createdAt: string;
  updatedAt: string;
  configuration?: any;
}

export interface AgentCategory {
  id: string;
  name: string;
  description?: string;
  icon_url?: string;
  sort_order: number;
  is_active: boolean;
}

export interface AgentReview {
  id: string;
  rating: number;
  reviewText?: string;
  reviewerName: string;
  reviewerId: string;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface DiscoverFilters {
  search?: string;
  category?: string;
  tags?: string[];
  sortBy?: 'newest' | 'popular' | 'rating' | 'downloads';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
  featured?: boolean;
}

export interface DiscoverResponse {
  success: boolean;
  agents: PublishedAgent[];
  categories: AgentCategory[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
  error?: string;
}

export interface PublishAgentRequest {
  agentId: string;
  name: string;
  description?: string;
  category: string;
  tags?: string[];
}

export interface ImportAgentRequest {
  publishedAgentId: string;
  customName?: string;
}

export interface CreateReviewRequest {
  publishedAgentId: string;
  rating: number;
  reviewText?: string;
}

export interface UpdateReviewRequest {
  reviewId: string;
  rating: number;
  reviewText?: string;
}

/**
 * Get FREE marketplace agents ONLY (strict isolation)
 * This function ONLY returns agents from published_agents table with is_active=true
 * It will NEVER return paid agents that are for sale
 */
export async function getFreeMarketplaceAgents(filters: DiscoverFilters = {}): Promise<DiscoverResponse> {
  try {
    console.log('🆓 Getting FREE marketplace agents with filters:', filters);

    // STRICT ISOLATION: Only get free agents from published_agents table
    // Must have: is_active = true in published_agents
    // Must NOT have: is_for_sale = true in agents table (to prevent cross-contamination)
    const { data, error } = await supabase.functions.invoke('discover-agents', {
      body: {
        ...filters,
        strict_free_only: true // Flag to enforce strict isolation in edge function
      }
    });

    if (error) {
      console.error('❌ Error fetching free agents:', error);
      return {
        success: false,
        agents: [],
        categories: [],
        pagination: { total: 0, limit: 0, offset: 0, hasMore: false },
        error: error.message || 'Failed to get free marketplace agents'
      };
    }

    console.log(`🆓 Found ${data.agents?.length || 0} free agents`);

    return data;
  } catch (error) {
    console.error('❌ Error getting free marketplace agents:', error);
    return {
      success: false,
      agents: [],
      categories: [],
      pagination: { total: 0, limit: 0, offset: 0, hasMore: false },
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Discover published agents with filtering and search
 * DEPRECATED: Use getFreeMarketplaceAgents() for strict isolation
 */
export async function discoverAgents(filters: DiscoverFilters = {}): Promise<DiscoverResponse> {
  try {
    console.log('⚠️ discoverAgents() called - consider using getFreeMarketplaceAgents() for strict isolation');
    console.log('Discovering agents with filters:', filters);

    const { data, error } = await supabase.functions.invoke('discover-agents', {
      body: filters
    });

    if (error) {
      console.error('Discover agents error:', error);
      return {
        success: false,
        agents: [],
        categories: [],
        pagination: { total: 0, limit: 0, offset: 0, hasMore: false },
        error: error.message || 'Failed to discover agents'
      };
    }

    return data;
  } catch (error) {
    console.error('Discover agents service error:', error);
    return {
      success: false,
      agents: [],
      categories: [],
      pagination: { total: 0, limit: 0, offset: 0, hasMore: false },
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Publish an agent to the marketplace
 */
export async function publishAgent(request: PublishAgentRequest): Promise<{ success: boolean; publishedAgent?: any; message?: string; error?: string }> {
  try {
    console.log('Publishing agent:', request);

    const { data, error } = await supabase.functions.invoke('publish-agent', {
      body: request
    });

    if (error) {
      console.error('Publish agent error:', error);
      return {
        success: false,
        error: error.message || 'Failed to publish agent'
      };
    }

    return data;
  } catch (error) {
    console.error('Publish agent service error:', error);
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Unpublish an agent from the free marketplace
 */
export async function unpublishAgent(agentId: string): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('🔄 Unpublishing agent:', agentId);

    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.error('❌ User not authenticated');
      throw new Error('User not authenticated');
    }

    console.log('👤 User ID:', user.id);

    // Set is_active to false for the published agent
    const { data, error } = await supabase
      .from('published_agents')
      .update({
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('agent_id', agentId)
      .eq('publisher_id', user.id)
      .select();

    console.log('📊 Update result:', { data, error });

    if (error) {
      console.error('❌ Database error:', error);
      throw error;
    }

    if (!data || data.length === 0) {
      console.warn('⚠️ No rows updated - agent may not exist or user may not own it');
      return {
        success: false,
        error: 'Agent not found or you do not have permission to unpublish it'
      };
    }

    // SYNCHRONIZATION FIX: Check if agent should remain public (if it's for sale)
    console.log('🔄 Checking if agent should remain public...');
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('is_for_sale, is_public')
      .eq('id', agentId)
      .single();

    if (!agentError && agent && !agent.is_for_sale && agent.is_public) {
      // Agent is not for sale and is currently public, so make it private
      console.log('🔄 Making agent private since it\'s not for sale...');
      const { error: updateError } = await supabase
        .from('agents')
        .update({
          is_public: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', agentId)
        .eq('user_id', user.id);

      if (updateError) {
        console.warn('⚠️ Failed to update agent public status:', updateError);
        // Don't fail the whole operation
      } else {
        console.log('✅ Agent made private successfully');
      }
    } else if (!agentError && agent && agent.is_for_sale) {
      console.log('ℹ️ Agent remains public because it\'s for sale');
    }

    console.log('✅ Successfully unpublished agent');
    return { success: true };
  } catch (error) {
    console.error('❌ Unpublish agent service error:', error);
    return {
      success: false,
      error: error.message || 'Failed to unpublish agent'
    };
  }
}

/**
 * Import a published agent to user's library
 */
export async function importAgent(request: ImportAgentRequest): Promise<{ success: boolean; agent?: any; message?: string; alreadyImported?: boolean; error?: string }> {
  try {
    console.log('Importing agent:', request);

    const { data, error } = await supabase.functions.invoke('import-agent', {
      body: request
    });

    if (error) {
      console.error('Import agent error:', error);
      return {
        success: false,
        error: error.message || 'Failed to import agent'
      };
    }

    return data;
  } catch (error) {
    console.error('Import agent service error:', error);
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Get reviews for a published agent
 */
export async function getAgentReviews(publishedAgentId: string): Promise<{ success: boolean; reviews: AgentReview[]; error?: string }> {
  try {
    console.log('Getting reviews for agent:', publishedAgentId);

    const { data, error } = await supabase.functions.invoke('agent-reviews', {
      method: 'GET',
      body: { publishedAgentId }
    });

    if (error) {
      console.error('Get agent reviews error:', error);
      return {
        success: false,
        reviews: [],
        error: error.message || 'Failed to get agent reviews'
      };
    }

    return data;
  } catch (error) {
    console.error('Get agent reviews service error:', error);
    return {
      success: false,
      reviews: [],
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Create a review for a published agent
 */
export async function createAgentReview(request: CreateReviewRequest): Promise<{ success: boolean; review?: any; message?: string; error?: string }> {
  try {
    console.log('Creating agent review:', request);

    const { data, error } = await supabase.functions.invoke('agent-reviews', {
      method: 'POST',
      body: request
    });

    if (error) {
      console.error('Create agent review error:', error);
      return {
        success: false,
        error: error.message || 'Failed to create review'
      };
    }

    return data;
  } catch (error) {
    console.error('Create agent review service error:', error);
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Update an existing review
 */
export async function updateAgentReview(request: UpdateReviewRequest): Promise<{ success: boolean; review?: any; message?: string; error?: string }> {
  try {
    console.log('Updating agent review:', request);

    const { data, error } = await supabase.functions.invoke('agent-reviews', {
      method: 'PUT',
      body: request
    });

    if (error) {
      console.error('Update agent review error:', error);
      return {
        success: false,
        error: error.message || 'Failed to update review'
      };
    }

    return data;
  } catch (error) {
    console.error('Update agent review service error:', error);
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Delete a review
 */
export async function deleteAgentReview(reviewId: string): Promise<{ success: boolean; message?: string; error?: string }> {
  try {
    console.log('Deleting agent review:', reviewId);

    const { data, error } = await supabase.functions.invoke('agent-reviews', {
      method: 'DELETE',
      body: { reviewId }
    });

    if (error) {
      console.error('Delete agent review error:', error);
      return {
        success: false,
        error: error.message || 'Failed to delete review'
      };
    }

    return data;
  } catch (error) {
    console.error('Delete agent review service error:', error);
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Check if an agent is currently in the marketplace
 * This checks both the published_agents table (for free agents) and agents table (for paid agents)
 */
export async function checkAgentMarketplaceStatus(agentId: string): Promise<{
  isInMarketplace: boolean;
  isPublished: boolean;
  isForSale: boolean;
  error?: string;
}> {
  try {
    console.log('Checking marketplace status for agent:', agentId);

    // Check if agent is published in the free marketplace (published_agents table)
    const { data: publishedAgent, error: publishedError } = await supabase
      .from('published_agents')
      .select('id, is_active')
      .eq('agent_id', agentId)
      .eq('is_active', true)
      .single();

    const isPublished = !publishedError && !!publishedAgent;

    // Check if agent is for sale in the paid marketplace (agents table)
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('is_for_sale, is_public')
      .eq('id', agentId)
      .single();

    const isForSale = !agentError && agent?.is_for_sale === true && agent?.is_public === true;

    // Agent is in marketplace if it's either published (free) or for sale (paid)
    const isInMarketplace = isPublished || isForSale;

    console.log('Marketplace status result:', {
      agentId,
      isInMarketplace,
      isPublished: !!isPublished,
      isForSale: !!isForSale
    });

    return {
      isInMarketplace,
      isPublished: !!isPublished,
      isForSale: !!isForSale
    };
  } catch (error) {
    console.error('Check agent marketplace status error:', error);
    return {
      isInMarketplace: false,
      isPublished: false,
      isForSale: false,
      error: error.message || 'Failed to check marketplace status'
    };
  }
}
