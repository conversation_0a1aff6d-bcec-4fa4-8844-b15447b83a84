import { supabase } from '@/integrations/supabase/client';
import { unpublishAgent, checkAgentMarketplaceStatus } from './discoverService';
import { getAuthenticatedUser } from './authService';

// Types for the agent service
export interface AgentBlock {
  id: string;
  type: string;
  position: { x: number; y: number };
  // Connection properties
  outputConnections?: string[];
  inputConnections?: string[];
  trueConnection?: string;
  falseConnection?: string;
  bullishConnection?: string;
  bearishConnection?: string;
  neutralConnection?: string;
  atLevelConnection?: string;
  notAtLevelConnection?: string;
  [key: string]: any;
}

export interface Agent {
  id?: string;
  name: string;
  description?: string;
  configuration: {
    blocks: AgentBlock[];
    entryBlockId: string;
  };
  is_public?: boolean;
  public_description?: string;
  tags?: string[];
  likes_count?: number;
  usage_count?: number;
  created_at?: string;
  updated_at?: string;
  user_id?: string;
  // Ownership metadata
  is_owned?: boolean;
  is_licensed?: boolean;
  is_purchased?: boolean;
  // Marketplace metadata
  is_for_sale?: boolean;
  price?: number;
  sales_count?: number;
}

export interface AgentRun {
  id?: string;
  agent_id: string;
  symbol: string;
  result: {
    signal: 'bullish' | 'bearish' | 'neutral';
    confidence: number;
    reasoning: string;
    metrics: Record<string, any>;
    executionPath: string[];
    executionTime: number;
    timestamp: string;
  };
  created_at?: string;
}

/**
 * Check if an agent name already exists
 * @param name - The name to check
 * @param excludeId - Optional ID to exclude from the check
 * @returns True if the name exists, false otherwise
 */
export async function checkAgentNameExists(name: string, excludeId?: string): Promise<boolean> {
  try {
    let query = supabase
      .from('agents')
      .select('id')
      .eq('name', name);

    if (excludeId) {
      query = query.neq('id', excludeId);
    }

    const { data, error } = await query;

    if (error) throw error;
    return data && data.length > 0;
  } catch (error) {
    console.error('Error checking agent name:', error);
    return false;
  }
}

/**
 * Generate a unique name for an agent
 * @param baseName - The base name to use
 * @param excludeId - Optional ID to exclude from the check
 * @returns A unique name
 */
export async function generateUniqueName(baseName: string, excludeId?: string): Promise<string> {
  // Check if the base name is already unique
  const exists = await checkAgentNameExists(baseName, excludeId);
  if (!exists) return baseName;

  // Try adding numbers until we find a unique name
  let counter = 1;
  let uniqueName = `${baseName} (${counter})`;

  while (await checkAgentNameExists(uniqueName, excludeId)) {
    counter++;
    uniqueName = `${baseName} (${counter})`;

    // Safety check to prevent infinite loops
    if (counter > 100) {
      uniqueName = `${baseName} (${Date.now()})`;
      break;
    }
  }

  return uniqueName;
}

/**
 * Handle marketplace synchronization for an agent
 * @param agentId - The agent ID
 * @param isInMarketplace - Whether the agent should be in the marketplace
 * @param wasInMarketplace - Whether the agent was previously in the marketplace
 */
async function handleMarketplaceSync(agentId: string, isInMarketplace: boolean, wasInMarketplace: boolean): Promise<void> {
  try {
    // If agent should be removed from marketplace but was previously in it
    if (!isInMarketplace && wasInMarketplace) {
      console.log(`Removing agent ${agentId} from marketplace`);
      await unpublishAgent(agentId);
    }
    // Note: Adding to marketplace requires explicit publishing through the publish flow
    // We don't automatically publish agents just because they're marked as "in marketplace"
  } catch (error) {
    console.error('Error handling marketplace sync:', error);
    // Don't throw here - marketplace sync failures shouldn't prevent agent saving
  }
}

/**
 * Save an agent to Supabase
 * @param agent - The agent to save
 * @param isInMarketplace - Whether the agent should be in the marketplace (optional)
 * @returns The saved agent
 */
export async function saveAgent(agent: Agent, isInMarketplace?: boolean): Promise<Agent> {
  try {
    // Get the current authenticated user (works for both regular and Whop users)
    const user = await getAuthenticatedUser();

    console.log('💾 Saving agent for user:', {
      userId: user.id,
      agentName: agent.name,
      isUpdate: !!agent.id,
      isWhopUser: user.isWhopUser
    });

    // Check if we need to use the agent-operations function (for Whop users or users without active sessions)
    const { data: { user: supabaseUser } } = await supabase.auth.getUser();
    const useAgentOperationsFunction = !supabaseUser || user.isWhopUser;

    if (useAgentOperationsFunction) {
      console.log('🔧 Using agent-operations function to bypass RLS for user:', user.id);

      // Prepare agent data for the function
      const agentData = {
        name: agent.name,
        description: agent.description,
        configuration: agent.configuration,
        is_public: agent.is_public,
        public_description: agent.public_description,
        tags: agent.tags,
        updated_at: new Date().toISOString()
      };

      // For new agents, generate unique name and add creation timestamp
      if (!agent.id) {
        const uniqueName = await generateUniqueName(agent.name);
        agentData.name = uniqueName;
      }

      const operation = agent.id ? 'update' : 'create';
      const { data: result, error } = await supabase.functions.invoke('agent-operations', {
        body: {
          operation,
          agentData,
          userId: user.id,
          agentId: agent.id
        }
      });

      if (error) throw error;
      if (!result.success) throw new Error(result.error || 'Agent operation failed');

      console.log('✅ Agent saved via function:', result.agent.id);

      // Handle marketplace synchronization if specified
      if (isInMarketplace !== undefined && agent.id) {
        const marketplaceStatus = await checkAgentMarketplaceStatus(agent.id);
        await handleMarketplaceSync(agent.id, isInMarketplace, marketplaceStatus.isInMarketplace);
      }

      return result.agent;
    } else {
      // Regular users with active sessions can use direct database access
      console.log('🔧 Using direct database access for user:', user.id);

      // Check if this is an update or a new agent
      if (agent.id) {
        // Check current marketplace status if isInMarketplace is provided
        let wasInMarketplace = false;
        if (isInMarketplace !== undefined) {
          const marketplaceStatus = await checkAgentMarketplaceStatus(agent.id);
          wasInMarketplace = marketplaceStatus.isInMarketplace;
        }

        // Update existing agent
        const { data, error } = await supabase
          .from('agents')
          .update({
            name: agent.name,
            description: agent.description,
            configuration: agent.configuration,
            is_public: agent.is_public,
            public_description: agent.public_description,
            tags: agent.tags,
            updated_at: new Date().toISOString()
          })
          .eq('id', agent.id)
          .select()
          .single();

        if (error) throw error;

        // Handle marketplace synchronization if specified
        if (isInMarketplace !== undefined) {
          await handleMarketplaceSync(agent.id, isInMarketplace, wasInMarketplace);
        }

        return data;
      } else {
        // Generate a unique name if needed
        const uniqueName = await generateUniqueName(agent.name);

        // Create new agent
        const { data, error } = await supabase
          .from('agents')
          .insert({
            name: uniqueName,
            description: agent.description,
            configuration: agent.configuration,
            is_public: agent.is_public || false,
            public_description: agent.public_description,
            tags: agent.tags || [],
            user_id: user.id
          })
          .select()
          .single();

        if (error) throw error;

        // For new agents, marketplace sync is not needed since they can't be in marketplace yet

        return data;
      }
    }
  } catch (error) {
    console.error('❌ Error saving agent:', error);
    throw error;
  }
}

/**
 * Get all agents for the current user with ownership metadata
 * @returns Array of agents with ownership information
 */
export async function getAgents(): Promise<Agent[]> {
  try {
    // Get the current authenticated user (works for both regular and Whop users)
    const user = await getAuthenticatedUser();

    console.log('🔐 Getting agents for user:', {
      userId: user.id,
      isWhopUser: user.isWhopUser,
      username: user.user_metadata?.username,
      userIdFormat: user.id.includes('whop_') ? 'INVALID_TEMP_FORMAT' : 'VALID_UUID'
    });

    // Validate that we have a proper UUID (not a temporary whop_ format)
    if (user.id.startsWith('whop_')) {
      console.error('🚨 CRITICAL: User ID is in temporary format, this will cause UUID errors!');
      console.error('🔧 This indicates the Whop user authentication system is not working properly.');
      console.error('🔧 The user should have been created in Supabase with a proper UUID.');
      throw new Error('Invalid user authentication - temporary ID format detected. Please refresh and try again.');
    }

    // Additional validation for UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(user.id)) {
      console.error('🚨 CRITICAL: User ID is not a valid UUID format:', user.id);
      throw new Error('Invalid user ID format - not a valid UUID. Please refresh and try again.');
    }

    // Check if we need to use the agent-operations function (for Whop users or users without active sessions)
    const { data: { user: supabaseUser } } = await supabase.auth.getUser();
    const useAgentOperationsFunction = !supabaseUser || user.isWhopUser;

    let ownedAgents;

    if (useAgentOperationsFunction) {
      console.log('🔧 Using agent-operations function to list agents for user:', user.id);

      const { data: result, error } = await supabase.functions.invoke('agent-operations', {
        body: {
          operation: 'list',
          userId: user.id
        }
      });

      if (error) throw error;
      if (!result.success) throw new Error(result.error || 'Failed to get agents');

      ownedAgents = result.agents;
      console.log('✅ Agents retrieved via function:', ownedAgents.length);
    } else {
      // Regular users with active sessions can use direct database access
      console.log('🔧 Using direct database access to list agents for user:', user.id);

      const { data, error } = await supabase
        .from('agents')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      ownedAgents = data;
      console.log('✅ Agents retrieved directly:', ownedAgents?.length || 0);
    }

    if (!ownedAgents || ownedAgents.length === 0) {
      return [];
    }

    // Get agent IDs for license and purchase checks
    const agentIds = ownedAgents.map(agent => agent.id);

    // Check for licenses (agents user purchased)
    const { data: licenses } = await supabase
      .from('agent_licenses')
      .select('agent_id')
      .eq('buyer_id', user.id)
      .in('agent_id', agentIds);

    // Check for purchases (old system) - check if any of the user's agents are purchased copies
    const { data: purchases } = await supabase
      .from('purchased_agents')
      .select('purchased_agent_id')
      .eq('buyer_id', user.id)
      .in('purchased_agent_id', agentIds);

    // Create sets for quick lookup
    const licensedAgentIds = new Set(licenses?.map(l => l.agent_id) || []);
    const purchasedAgentIds = new Set(purchases?.map(p => p.purchased_agent_id) || []);

    // Add ownership metadata to each agent
    const agentsWithMetadata = ownedAgents.map(agent => {
      const isLicensed = licensedAgentIds.has(agent.id);
      const isPurchased = purchasedAgentIds.has(agent.id);

      // Additional check: if agent name contains "(Purchased)" or "(Imported)", it's likely purchased
      const nameIndicatesPurchase = agent.name && (
        agent.name.includes('(Purchased)') ||
        agent.name.includes('(Imported)') ||
        agent.name.includes('(Licensed)')
      );

      const isActuallyPurchased = isPurchased || nameIndicatesPurchase;
      const isOwned = !isLicensed && !isActuallyPurchased; // Only true owners, not license holders

      return {
        ...agent,
        is_owned: isOwned,
        is_licensed: isLicensed,
        is_purchased: isActuallyPurchased
      };
    });

    console.log('Agents with metadata:', agentsWithMetadata.map(a => ({
      name: a.name,
      is_owned: a.is_owned,
      is_licensed: a.is_licensed,
      is_purchased: a.is_purchased
    })));

    return agentsWithMetadata;
  } catch (error) {
    console.error('Error getting agents:', error);
    throw error;
  }
}

/**
 * Get all agents for a specific user ID
 * @param userId - The user ID to get agents for
 * @returns Array of agents
 */
export async function getAgentsByUserId(userId: string): Promise<Agent[]> {
  try {
    console.log('🔍 Getting agents for user ID:', userId);

    // Check if we need to use the agent-operations function (for Whop users or users without active sessions)
    const { data: { user: supabaseUser } } = await supabase.auth.getUser();

    // Determine if this is likely a Whop user by checking the current session user or user ID format
    let isWhopUser = false;
    if (supabaseUser) {
      // Check if the current session user is a Whop user
      isWhopUser = supabaseUser.user_metadata?.isWhopUser === true;
    } else {
      // No active session - check if the user ID looks like a Whop user ID
      // Whop user IDs typically start with "user_" followed by alphanumeric characters
      isWhopUser = userId.startsWith('user_') && userId.length > 5;
    }

    const useAgentOperationsFunction = !supabaseUser || isWhopUser;

    // Only validate UUID format for regular users (not Whop users)
    if (!isWhopUser) {
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(userId)) {
        console.error('🚨 CRITICAL: User ID is not a valid UUID format:', userId);
        throw new Error('Invalid user ID format - not a valid UUID. Please refresh and try again.');
      }
    }

    if (useAgentOperationsFunction) {
      console.log('🔧 Using agent-operations function to list agents for user:', userId);

      let supabaseUserId = userId;

      // If this is a Whop user ID, we need to look up the corresponding Supabase UUID
      if (isWhopUser && userId.startsWith('user_')) {
        console.log('🔍 Looking up Supabase UUID for Whop user:', userId);

        try {
          const { data: lookupResult, error: lookupError } = await supabase.functions.invoke('whop-user-lookup', {
            body: { whopUserId: userId }
          });

          if (lookupError || !lookupResult?.success || !lookupResult?.user?.id) {
            console.error('❌ Failed to lookup Whop user in Supabase:', lookupError || 'User not found');
            throw new Error('Whop user not found in Supabase. Please refresh and try again.');
          }

          supabaseUserId = lookupResult.user.id;
          console.log('✅ Found Supabase UUID for Whop user:', supabaseUserId);
        } catch (lookupError) {
          console.error('❌ Error looking up Whop user:', lookupError);
          throw new Error('Failed to authenticate Whop user. Please refresh and try again.');
        }
      }

      const { data: result, error } = await supabase.functions.invoke('agent-operations', {
        body: {
          operation: 'list',
          userId: supabaseUserId
        }
      });

      if (error) throw error;
      if (!result.success) throw new Error(result.error || 'Failed to get agents');

      console.log('✅ Agents retrieved via function:', result.agents.length);
      return result.agents || [];
    } else {
      // Regular users with active sessions can use direct database access
      console.log('🔧 Using direct database access to list agents for user:', userId);

      const { data, error } = await supabase
        .from('agents')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      console.log('✅ Agents retrieved directly:', data?.length || 0);
      return data || [];
    }
  } catch (error) {
    console.error('❌ Error getting agents by user ID:', error);
    throw error;
  }
}

/**
 * Get an agent by ID
 * @param id - The agent ID
 * @returns The agent
 */
export async function getAgentById(id: string): Promise<Agent | null> {
  try {
    // Get the current authenticated user (works for both regular and Whop users)
    const user = await getAuthenticatedUser();

    console.log('🔍 Getting agent by ID for user:', {
      userId: user.id,
      agentId: id,
      isWhopUser: user.isWhopUser
    });

    // Check if we need to use the agent-operations function (for Whop users or users without active sessions)
    const { data: { user: supabaseUser } } = await supabase.auth.getUser();
    const useAgentOperationsFunction = !supabaseUser || user.isWhopUser;

    if (useAgentOperationsFunction) {
      console.log('🔧 Using agent-operations function to get agent for user:', user.id);

      const { data: result, error } = await supabase.functions.invoke('agent-operations', {
        body: {
          operation: 'get',
          userId: user.id,
          agentId: id
        }
      });

      if (error) throw error;
      if (!result.success) {
        if (result.error === 'Agent not found or access denied') {
          return null;
        }
        throw new Error(result.error || 'Failed to get agent');
      }

      console.log('✅ Agent retrieved via function:', result.agent.id);
      return result.agent;
    } else {
      // Regular users with active sessions can use direct database access
      console.log('🔧 Using direct database access to get agent for user:', user.id);

      const { data, error } = await supabase
        .from('agents')
        .select('*')
        .eq('id', id)
        .eq('user_id', user.id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned
          return null;
        }
        throw error;
      }

      console.log('✅ Agent retrieved directly:', data.id);
      return data;
    }
  } catch (error) {
    console.error(`❌ Error getting agent with ID ${id}:`, error);
    throw error;
  }
}

/**
 * Delete an agent
 * @param id - The agent ID
 * @returns Success status
 */
export async function deleteAgent(id: string): Promise<boolean> {
  try {
    // Get the current authenticated user (works for both regular and Whop users)
    const user = await getAuthenticatedUser();

    console.log('🗑️ Deleting agent for user:', {
      userId: user.id,
      agentId: id,
      isWhopUser: user.isWhopUser
    });

    // Check if we need to use the agent-operations function (for Whop users or users without active sessions)
    const { data: { user: supabaseUser } } = await supabase.auth.getUser();
    const useAgentOperationsFunction = !supabaseUser || user.isWhopUser;

    if (useAgentOperationsFunction) {
      console.log('🔧 Using agent-operations function to delete agent for user:', user.id);

      const { data: result, error } = await supabase.functions.invoke('agent-operations', {
        body: {
          operation: 'delete',
          userId: user.id,
          agentId: id
        }
      });

      if (error) throw error;
      if (!result.success) throw new Error(result.error || 'Agent deletion failed');

      console.log('✅ Agent deleted via function:', id);
      return true;
    } else {
      // Regular users with active sessions can use direct database access
      console.log('🔧 Using direct database access to delete agent for user:', user.id);

      const { error } = await supabase
        .from('agents')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id);

      if (error) throw error;
      console.log('✅ Agent deleted directly:', id);
      return true;
    }
  } catch (error) {
    console.error(`❌ Error deleting agent with ID ${id}:`, error);
    throw error;
  }
}

/**
 * Run an agent
 * @param agentId - The agent ID
 * @param symbol - The symbol to analyze
 * @param timeframe - The timeframe to analyze
 * @returns The agent run result
 */


/**
 * Ensure all block types in a payload are uppercase
 * @param payload - The payload to check
 * @returns The modified payload with uppercase block types
 */
function ensureUppercaseBlockTypes(payload: any): any {
  // Create a deep copy of the payload
  const modifiedPayload = JSON.parse(JSON.stringify(payload));

  // Check if the payload has an agentConfig property
  if (modifiedPayload.agentConfig && modifiedPayload.agentConfig.blocks) {
    // Convert all block types to uppercase
    modifiedPayload.agentConfig.blocks = modifiedPayload.agentConfig.blocks.map((block: any) => {
      if (block.type) {
        // First, handle specific conversions
        if (block.type.toLowerCase() === 'when_run') {
          block.type = 'WHEN_RUN';
        } else if (block.type.toLowerCase() === 'indicator') {
          block.type = 'INDICATOR';
        } else if (block.type.toLowerCase() === 'price') {
          block.type = 'PRICE';
        } else if (block.type.toLowerCase() === 'fundamental') {
          block.type = 'FUNDAMENTAL';
        } else if (block.type.toLowerCase() === 'condition') {
          block.type = 'CONDITION';
        } else if (block.type.toLowerCase() === 'trigger') {
          block.type = 'TRIGGER';
        } else if (block.type.toLowerCase() === 'operator') {
          block.type = 'OPERATOR';
        } else if (block.type.toLowerCase() === 'bullish_confidence_boost') {
          block.type = 'BULLISH_CONFIDENCE_BOOST';
        } else if (block.type.toLowerCase() === 'bearish_confidence_boost') {
          block.type = 'BEARISH_CONFIDENCE_BOOST';
        } else {
          // For any other types, convert to uppercase
          block.type = block.type.toUpperCase();
        }

        // Remove any underscores if they exist
        block.type = block.type.replace(/_/g, '');
      }
      return block;
    });
  }

  // Also convert the stringified payload to catch any nested types
  const stringifiedPayload = JSON.stringify(modifiedPayload);
  const modifiedString = stringifiedPayload
    .replace(/"type":"when_run"/gi, '"type":"WHENRUN"')
    .replace(/"type":"indicator"/gi, '"type":"INDICATOR"')
    .replace(/"type":"price"/gi, '"type":"PRICE"')
    .replace(/"type":"fundamental"/gi, '"type":"FUNDAMENTAL"')
    .replace(/"type":"condition"/gi, '"type":"CONDITION"')
    .replace(/"type":"trigger"/gi, '"type":"TRIGGER"')
    .replace(/"type":"operator"/gi, '"type":"OPERATOR"')
    .replace(/"type":"bullish_confidence_boost"/gi, '"type":"BULLISHCONFIDENCEBOOST"')
    .replace(/"type":"bearish_confidence_boost"/gi, '"type":"BEARISHCONFIDENCEBOOST"');

  // Parse the modified string back to an object
  return JSON.parse(modifiedString);
}

/**
 * Convert block types to the format expected by the edge function
 * @param config - The agent configuration to convert
 * @returns The converted agent configuration
 */
function convertBlockTypes(config: Agent['configuration']): Agent['configuration'] {
  if (!config || !config.blocks) {
    return config;
  }

  // Create a deep copy of the configuration to avoid modifying the original
  const configCopy = JSON.parse(JSON.stringify(config));

  // Convert block types to uppercase with underscores
  configCopy.blocks = configCopy.blocks.map((block: any) => {
    // Create a new block object with the type converted to uppercase
    const newBlock = { ...block };

    // Convert the type to uppercase
    if (typeof block.type === 'string') {
      // Replace all occurrences of when_run with WHEN_RUN
      if (block.type.toLowerCase() === 'when_run') {
        newBlock.type = 'WHEN_RUN';
      } else if (block.type.toLowerCase() === 'indicator') {
        newBlock.type = 'INDICATOR';
      } else if (block.type.toLowerCase() === 'price') {
        newBlock.type = 'PRICE';
      } else if (block.type.toLowerCase() === 'fundamental') {
        newBlock.type = 'FUNDAMENTAL';
      } else if (block.type.toLowerCase() === 'condition') {
        newBlock.type = 'CONDITION';
      } else if (block.type.toLowerCase() === 'trigger') {
        newBlock.type = 'TRIGGER';
      } else if (block.type.toLowerCase() === 'operator') {
        newBlock.type = 'OPERATOR';
      } else if (block.type.toLowerCase() === 'bullish_confidence_boost') {
        newBlock.type = 'BULLISH_CONFIDENCE_BOOST';
      } else if (block.type.toLowerCase() === 'bearish_confidence_boost') {
        newBlock.type = 'BEARISH_CONFIDENCE_BOOST';
      }
    }

    return newBlock;
  });

  return configCopy;
}

/**
 * Validate an agent configuration
 * @param config - The agent configuration to validate
 * @returns An error message if invalid, or null if valid
 */
function validateAgentConfig(config: Agent['configuration']): string | null {
  if (!config) {
    return 'Agent configuration is missing';
  }

  if (!config.blocks || !Array.isArray(config.blocks) || config.blocks.length === 0) {
    return 'Agent configuration must have at least one block';
  }

  if (!config.entryBlockId) {
    return 'Agent configuration must have an entry block ID';
  }

  // Check if the entry block exists
  const entryBlockExists = config.blocks.some(block => block.id === config.entryBlockId);
  if (!entryBlockExists) {
    return 'Entry block ID does not match any block in the configuration';
  }

  // Check that all blocks have required properties
  for (const block of config.blocks) {
    if (!block.id) {
      return 'All blocks must have an ID';
    }

    if (!block.type) {
      return `Block ${block.id} is missing a type`;
    }

    if (!block.position || typeof block.position.x !== 'number' || typeof block.position.y !== 'number') {
      return `Block ${block.id} has an invalid position`;
    }
  }

  return null;
}

/**
 * Extract the response body from an edge function error
 * @param error - The error object
 * @returns The response body or null if not available
 */
function extractErrorResponseBody(error: any): any {
  try {
    // Check if the error has a response property
    if (error.response) {
      return error.response;
    }

    // Check if the error message contains a JSON string
    const jsonMatch = error.message.match(/\{.*\}/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }

    // Check for specific error patterns
    const agentRunnerMatch = error.message.match(/Error in agent-runner: (.*)/);
    if (agentRunnerMatch && agentRunnerMatch[1]) {
      console.error('Extracted agent-runner error:', agentRunnerMatch[1]);
      return { error: agentRunnerMatch[1] };
    }

    // Check for HTTP error codes
    const httpErrorMatch = error.message.match(/(\d{3}) .*$/);
    if (httpErrorMatch && httpErrorMatch[1]) {
      const statusCode = httpErrorMatch[1];
      console.error('Extracted HTTP status code:', statusCode);
      return { statusCode, error: `HTTP error ${statusCode}` };
    }

    return null;
  } catch (e) {
    console.error('Error extracting response body:', e);
    return null;
  }
}

export async function runAgent(
  agentId: string,
  symbol: string,
  timeframe: string = 'day'
): Promise<AgentRun['result']> {
  try {
    // First, get the agent to ensure it exists and belongs to the user
    const agent = await getAgentById(agentId);

    if (!agent) {
      throw new Error(`Agent with ID ${agentId} not found`);
    }

    // Get the current authenticated user (works for both regular and Whop users)
    const user = await getAuthenticatedUser();

    // Validate the agent configuration
    const validationError = validateAgentConfig(agent.configuration);
    if (validationError) {
      throw new Error(`Invalid agent configuration: ${validationError}`);
    }

    // Convert block types to the format expected by the edge function
    const convertedConfig = convertBlockTypes(agent.configuration);

    console.log('Original block types:', agent.configuration.blocks.map(b => b.type));
    console.log('Converted block types:', convertedConfig.blocks.map(b => b.type));

    // Prepare the request payload with detailed information
    const requestPayload = {
      agentId,
      agentConfig: convertedConfig,
      symbol: symbol.toUpperCase(),
      timeframe,
      saveRun: true,
      userId: user.id,
      // Enable debug mode for development
      debug: process.env.NODE_ENV === 'development',
      // Include additional debugging information
      requestTime: new Date().toISOString(),
      clientInfo: {
        environment: process.env.NODE_ENV,
        userAgent: navigator.userAgent,
        language: navigator.language
      }
    };

    console.log('Agent runner request payload:', JSON.stringify(requestPayload, null, 2));

    // Clean up orphaned connections before sending to edge function
    const cleanupOrphanedConnections = (blocks: any[]) => {
      const blockIds = new Set(blocks.map(block => block.id));

      return blocks.map(block => {
        const cleanedBlock = { ...block };

        // Clean up inputConnections
        if (cleanedBlock.inputConnections) {
          cleanedBlock.inputConnections = cleanedBlock.inputConnections.filter((id: string) => blockIds.has(id));
        }

        // Clean up outputConnections
        if (cleanedBlock.outputConnections) {
          cleanedBlock.outputConnections = cleanedBlock.outputConnections.filter((id: string) => blockIds.has(id));
        }

        // Clean up trueConnection
        if (cleanedBlock.trueConnection && !blockIds.has(cleanedBlock.trueConnection)) {
          cleanedBlock.trueConnection = undefined;
        }

        // Clean up falseConnection
        if (cleanedBlock.falseConnection && !blockIds.has(cleanedBlock.falseConnection)) {
          cleanedBlock.falseConnection = undefined;
        }

        return cleanedBlock;
      });
    };

    // Clean up the agent configuration
    requestPayload.agentConfig.blocks = cleanupOrphanedConnections(requestPayload.agentConfig.blocks);

    // Ensure all block types are uppercase
    const modifiedPayload = ensureUppercaseBlockTypes(requestPayload);

    // Double-check that all block types are uppercase
    console.log('Modified payload block types:', modifiedPayload.agentConfig.blocks.map((b: any) => b.type));

    // Verify that all block types are uppercase
    const blockTypes = modifiedPayload.agentConfig.blocks.map((b: any) => b.type);
    const allUppercase = blockTypes.every((type: string) =>
      type === type.toUpperCase()
    );

    if (!allUppercase) {
      console.warn('Warning: Not all block types are uppercase. This may cause issues with the edge function.');
    }

    try {
      // Invoke the edge function with the modified configuration
      const { data, error } = await supabase.functions.invoke('agent-runner', {
        body: modifiedPayload
      });

      if (error) {
        // Try to extract the response body from the error
        const responseBody = extractErrorResponseBody(error);

        console.error('Edge function error details:', {
          message: error.message,
          name: error.name,
          stack: error.stack,
          code: (error as any).code,
          statusCode: (error as any).statusCode,
          details: (error as any).details,
          responseBody
        });

        // If we have a response body with an error message, use that
        if (responseBody && responseBody.error) {
          // Check for specific indicator-related errors
          if (responseBody.error.includes('indicator') || responseBody.error.includes('RSI') ||
              responseBody.error.includes('MACD') || responseBody.error.includes('calculation')) {
            throw new Error(`Indicator calculation error: ${responseBody.error}. Please check your agent configuration and try again.`);
          }
          throw new Error(`Edge function error: ${responseBody.error}`);
        }

        // Provide more specific error messages based on the error
        if (error.message.includes('not found')) {
          throw new Error(`Agent with ID ${agentId} not found or you don't have permission to access it`);
        } else if (error.message.includes('authentication')) {
          throw new Error('Authentication error: Please log in again and try once more');
        } else if (error.message.includes('400')) {
          throw new Error('Invalid request: The agent configuration may be malformed. Check that all required fields are present and valid.');
        } else if (error.message.includes('500')) {
          throw new Error('Server error: The agent runner service encountered an internal error. Please try again later.');
        } else if (error.message.includes('indicator') || error.message.includes('calculation')) {
          throw new Error('Indicator calculation error: There was an issue calculating technical indicators. Please check your agent configuration and try again.');
        } else {
          throw new Error(`Failed to run agent: ${error.message}`);
        }
      }

      if (!data) {
        console.error('No data returned from agent runner');
        throw new Error('No data returned from agent runner');
      }

      // Validate the response data
      console.log('Agent runner response data:', data);

      // Check if data is an object
      if (typeof data !== 'object' || data === null) {
        console.error('Invalid response format (not an object):', data);
        throw new Error('Invalid response format from agent runner: not an object');
      }

      // Check if data has the required fields
      if (data.signal === undefined) {
        console.error('Invalid response format (missing signal):', data);
        throw new Error('Invalid response format from agent runner: missing signal');
      }

      if (data.confidence === undefined) {
        console.error('Invalid response format (missing confidence):', data);
        throw new Error('Invalid response format from agent runner: missing confidence');
      }

      // Convert the response to the expected format if needed
      const result = {
        signal: data.signal,
        confidence: data.confidence,
        reasoning: data.reasoning || '',
        metrics: data.metrics || {},
        executionPath: data.executionPath || [],
        executionTime: data.executionTime || 0,
        timestamp: data.timestamp || new Date().toISOString(),
        debugLogs: data.debugLogs || []
      };

      console.log('Agent runner response:', JSON.stringify(result, null, 2));
      return result;
    } catch (edgeError) {
      console.error('Error invoking edge function:', edgeError);

      // Rethrow with more context
      if (edgeError instanceof Error) {
        throw new Error(`Agent runner error: ${edgeError.message}`);
      } else {
        throw new Error('Unknown error occurred while running the agent');
      }
    }
  } catch (error) {
    console.error(`Error running agent with ID ${agentId}:`, error);
    throw error;
  }
}

/**
 * Run an agent with a custom configuration (without saving)
 * @param agentConfig - The agent configuration
 * @param symbol - The symbol to analyze
 * @param timeframe - The timeframe to analyze
 * @returns The agent run result
 */
export async function runCustomAgent(
  agentConfig: Agent['configuration'],
  symbol: string,
  timeframe: string = 'day'
): Promise<AgentRun['result']> {
  try {
    // Validate the agent configuration
    const validationError = validateAgentConfig(agentConfig);
    if (validationError) {
      throw new Error(`Invalid agent configuration: ${validationError}`);
    }

    // Get the current authenticated user (works for both regular and Whop users)
    const user = await getAuthenticatedUser();

    // Convert block types to the format expected by the edge function
    const convertedConfig = convertBlockTypes(agentConfig);

    console.log('Original block types:', agentConfig.blocks.map(b => b.type));
    console.log('Converted block types:', convertedConfig.blocks.map(b => b.type));

    // Prepare the request payload with detailed information
    const requestPayload = {
      agentConfig: convertedConfig,
      symbol: symbol.toUpperCase(),
      timeframe,
      saveRun: false,
      userId: user.id,
      // Enable debug mode for development
      debug: process.env.NODE_ENV === 'development',
      // Include additional debugging information
      requestTime: new Date().toISOString(),
      clientInfo: {
        environment: process.env.NODE_ENV,
        userAgent: navigator.userAgent,
        language: navigator.language
      }
    };

    console.log('Custom agent runner request payload:', JSON.stringify(requestPayload, null, 2));

    // Clean up orphaned connections before sending to edge function
    const cleanupOrphanedConnections = (blocks: any[]) => {
      const blockIds = new Set(blocks.map(block => block.id));

      return blocks.map(block => {
        const cleanedBlock = { ...block };

        // Clean up inputConnections
        if (cleanedBlock.inputConnections) {
          cleanedBlock.inputConnections = cleanedBlock.inputConnections.filter((id: string) => blockIds.has(id));
        }

        // Clean up outputConnections
        if (cleanedBlock.outputConnections) {
          cleanedBlock.outputConnections = cleanedBlock.outputConnections.filter((id: string) => blockIds.has(id));
        }

        // Clean up trueConnection
        if (cleanedBlock.trueConnection && !blockIds.has(cleanedBlock.trueConnection)) {
          cleanedBlock.trueConnection = undefined;
        }

        // Clean up falseConnection
        if (cleanedBlock.falseConnection && !blockIds.has(cleanedBlock.falseConnection)) {
          cleanedBlock.falseConnection = undefined;
        }

        return cleanedBlock;
      });
    };

    // Clean up the agent configuration
    requestPayload.agentConfig.blocks = cleanupOrphanedConnections(requestPayload.agentConfig.blocks);

    // Ensure all block types are uppercase
    const modifiedPayload = ensureUppercaseBlockTypes(requestPayload);

    // Double-check that all block types are uppercase
    console.log('Modified payload block types:', modifiedPayload.agentConfig.blocks.map((b: any) => b.type));

    // Verify that all block types are uppercase
    const blockTypes = modifiedPayload.agentConfig.blocks.map((b: any) => b.type);
    const allUppercase = blockTypes.every((type: string) =>
      type === type.toUpperCase()
    );

    if (!allUppercase) {
      console.warn('Warning: Not all block types are uppercase. This may cause issues with the edge function.');
    }

    try {
      // Invoke the edge function with the modified configuration
      const { data, error } = await supabase.functions.invoke('agent-runner', {
        body: modifiedPayload
      });

      if (error) {
        // Try to extract the response body from the error
        const responseBody = extractErrorResponseBody(error);

        console.error('Edge function error details:', {
          message: error.message,
          name: error.name,
          stack: error.stack,
          code: (error as any).code,
          statusCode: (error as any).statusCode,
          details: (error as any).details,
          responseBody
        });

        // If we have a response body with an error message, use that
        if (responseBody && responseBody.error) {
          throw new Error(`Edge function error: ${responseBody.error}`);
        }

        // Provide more specific error messages based on the error
        if (error.message.includes('authentication')) {
          throw new Error('Authentication error: Please log in again and try once more');
        } else if (error.message.includes('400')) {
          throw new Error('Invalid request: The agent configuration may be malformed. Check that all required fields are present and valid.');
        } else if (error.message.includes('500')) {
          throw new Error('Server error: The agent runner service encountered an internal error. Please try again later.');
        } else {
          throw new Error(`Failed to run agent: ${error.message}`);
        }
      }

      if (!data) {
        console.error('No data returned from agent runner');
        throw new Error('No data returned from agent runner');
      }

      // Validate the response data
      console.log('Custom agent runner response data:', data);

      // Check if data is an object
      if (typeof data !== 'object' || data === null) {
        console.error('Invalid response format (not an object):', data);
        throw new Error('Invalid response format from agent runner: not an object');
      }

      // Check if data has the required fields
      if (data.signal === undefined) {
        console.error('Invalid response format (missing signal):', data);
        throw new Error('Invalid response format from agent runner: missing signal');
      }

      if (data.confidence === undefined) {
        console.error('Invalid response format (missing confidence):', data);
        throw new Error('Invalid response format from agent runner: missing confidence');
      }

      // Convert the response to the expected format if needed
      const result = {
        signal: data.signal,
        confidence: data.confidence,
        reasoning: data.reasoning || '',
        metrics: data.metrics || {},
        executionPath: data.executionPath || [],
        executionTime: data.executionTime || 0,
        timestamp: data.timestamp || new Date().toISOString(),
        debugLogs: data.debugLogs || []
      };

      console.log('Custom agent runner response:', JSON.stringify(result, null, 2));
      return result;
    } catch (edgeError) {
      console.error('Error invoking edge function:', edgeError);

      // Rethrow with more context
      if (edgeError instanceof Error) {
        throw new Error(`Agent runner error: ${edgeError.message}`);
      } else {
        throw new Error('Unknown error occurred while running the agent');
      }
    }
  } catch (error) {
    console.error('Error running custom agent:', error);
    throw error;
  }
}

/**
 * Get agent runs for an agent
 * @param agentId - The agent ID
 * @param limit - Maximum number of runs to return
 * @returns Array of agent runs
 */
export async function getAgentRuns(
  agentId: string,
  limit: number = 10
): Promise<AgentRun[]> {
  try {
    const { data, error } = await supabase
      .from('agent_runs')
      .select('*')
      .eq('agent_id', agentId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error(`Error getting runs for agent with ID ${agentId}:`, error);
    throw error;
  }
}

/**
 * Get the latest agent run for a symbol
 * @param symbol - The symbol
 * @returns The latest agent run for the symbol
 */
export async function getLatestAgentRunForSymbol(
  symbol: string
): Promise<AgentRun | null> {
  try {
    const { data, error } = await supabase
      .from('agent_runs')
      .select('*')
      .eq('symbol', symbol)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error(`Error getting latest run for symbol ${symbol}:`, error);
    return null;
  }
}

/**
 * Get all public agents
 * @returns Array of public agents
 */
export async function getPublicAgents(): Promise<Agent[]> {
  try {
    // First get the agents
    const { data: agents, error: agentsError } = await supabase
      .from('agents')
      .select('*')
      .eq('is_public', true)
      .order('likes_count', { ascending: false });

    if (agentsError) throw agentsError;
    if (!agents || agents.length === 0) return [];

    // Get unique user IDs
    const userIds = [...new Set(agents.map(agent => agent.user_id))];

    // Get profiles for these users
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, full_name')
      .in('id', userIds);

    if (profilesError) throw profilesError;

    // Create a map for quick lookup
    const profileMap = new Map(profiles?.map(p => [p.id, p]) || []);

    // Combine the data
    const agentsWithProfiles = agents.map(agent => ({
      ...agent,
      profiles: profileMap.get(agent.user_id) || null
    }));

    return agentsWithProfiles;
  } catch (error) {
    console.error('Error getting public agents:', error);
    throw error;
  }
}

/**
 * Like or unlike an agent
 * @param agentId - The agent ID
 * @returns Success status
 */
export async function toggleAgentLike(agentId: string): Promise<{ liked: boolean }> {
  try {
    const user = await getAuthenticatedUser();

    // Check if already liked
    const { data: existingLike } = await supabase
      .from('agent_likes')
      .select('id')
      .eq('agent_id', agentId)
      .eq('user_id', user.id)
      .single();

    if (existingLike) {
      // Unlike
      const { error } = await supabase
        .from('agent_likes')
        .delete()
        .eq('agent_id', agentId)
        .eq('user_id', user.id);

      if (error) throw error;
      return { liked: false };
    } else {
      // Like
      const { error } = await supabase
        .from('agent_likes')
        .insert({
          agent_id: agentId,
          user_id: user.id
        });

      if (error) throw error;
      return { liked: true };
    }
  } catch (error) {
    console.error('Error toggling agent like:', error);
    throw error;
  }
}

/**
 * Check if user has liked an agent
 * @param agentId - The agent ID
 * @returns Whether the user has liked the agent
 */
export async function hasUserLikedAgent(agentId: string): Promise<boolean> {
  try {
    const user = await getAuthenticatedUser();
    if (!user) return false;

    const { data, error } = await supabase
      .from('agent_likes')
      .select('id')
      .eq('agent_id', agentId)
      .eq('user_id', user.id)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return !!data;
  } catch (error) {
    console.error('Error checking agent like:', error);
    return false;
  }
}

/**
 * Increment usage count for an agent
 * @param agentId - The agent ID
 */
export async function incrementAgentUsage(agentId: string): Promise<void> {
  try {
    // First get the current usage count
    const { data: agent, error: fetchError } = await supabase
      .from('agents')
      .select('usage_count')
      .eq('id', agentId)
      .single();

    if (fetchError) throw fetchError;

    // Increment the usage count
    const newUsageCount = (agent?.usage_count || 0) + 1;

    const { error } = await supabase
      .from('agents')
      .update({ usage_count: newUsageCount })
      .eq('id', agentId);

    if (error) throw error;
  } catch (error) {
    console.error('Error incrementing agent usage:', error);
    // Don't throw error as this is not critical
  }
}

/**
 * Check if the current user owns an agent (not just has a license)
 * @param agentId - The agent ID to check
 * @returns True if the user owns the agent, false otherwise
 */
export async function checkAgentOwnership(agentId: string): Promise<boolean> {
  try {
    const user = await getAuthenticatedUser();
    if (!user) {
      return false;
    }

    // Check if user owns the agent
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('user_id')
      .eq('id', agentId)
      .eq('user_id', user.id)
      .single();

    if (agentError || !agent) {
      return false;
    }

    // SECURITY: Double-check that this is not a licensed agent
    // Licensed agents should NEVER be considered "owned" by the buyer
    const { data: licenseCheck } = await supabase
      .from('agent_licenses')
      .select('id')
      .eq('agent_id', agentId)
      .eq('buyer_id', user.id)
      .single();

    if (licenseCheck) {
      return false; // User has a license, but doesn't own the agent
    }

    // SECURITY: Additional check - ensure user is not trying to claim ownership of a purchased agent
    const { data: purchaseCheck } = await supabase
      .from('purchased_agents')
      .select('id')
      .eq('original_agent_id', agentId)
      .eq('buyer_id', user.id)
      .single();

    if (purchaseCheck) {
      return false; // User purchased the agent, but doesn't own it
    }

    return true;
  } catch (error) {
    console.error('Error checking agent ownership:', error);
    return false;
  }
}

/**
 * Get the access level for an agent
 * @param agentId - The agent ID to check
 * @returns Access level information
 */
export async function getAgentAccessLevel(agentId: string): Promise<{
  isOwner: boolean;
  isLicensed: boolean;
  isPurchased: boolean;
  canEdit: boolean;
  canSetPrice: boolean;
  canDelete: boolean;
  error?: string;
}> {
  try {
    const user = await getAuthenticatedUser();

    // Check if user owns the agent
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('user_id')
      .eq('id', agentId)
      .eq('user_id', user.id)
      .single();

    const isOwner = !agentError && !!agent;

    // Check if user has a license for this agent
    const { data: licenseCheck } = await supabase
      .from('agent_licenses')
      .select('id')
      .eq('agent_id', agentId)
      .eq('buyer_id', user.id)
      .single();

    const isLicensed = !!licenseCheck;

    // Check if user purchased this agent (old system)
    const { data: purchaseCheck } = await supabase
      .from('purchased_agents')
      .select('id')
      .eq('original_agent_id', agentId)
      .eq('buyer_id', user.id)
      .single();

    const isPurchased = !!purchaseCheck;

    // Determine permissions based on ownership status
    const canEdit = isOwner && !isLicensed && !isPurchased;
    const canSetPrice = isOwner && !isLicensed && !isPurchased;
    const canDelete = isOwner && !isLicensed && !isPurchased;

    return {
      isOwner,
      isLicensed,
      isPurchased,
      canEdit,
      canSetPrice,
      canDelete
    };
  } catch (error) {
    console.error('Error getting agent access level:', error);
    return {
      isOwner: false,
      isLicensed: false,
      isPurchased: false,
      canEdit: false,
      canSetPrice: false,
      canDelete: false,
      error: error.message || 'Failed to get access level'
    };
  }
}

/**
 * Run auto-backtest for agent builder
 * @param agentConfig - The agent configuration
 * @param symbol - The symbol to analyze (defaults to SPY)
 * @returns The backtest result
 */
export async function runAutoBacktest(
  agentConfig: any,
  symbol: string = 'SPY'
): Promise<any> {
  try {
    // Get the current authenticated user (works for both regular and Whop users)
    const user = await getAuthenticatedUser();

    // Validate the agent configuration
    const validationError = validateAgentConfig(agentConfig);
    if (validationError) {
      throw new Error(`Invalid agent configuration: ${validationError}`);
    }

    console.log('Running auto-backtest for agent with symbol:', symbol);

    // Prepare the request payload for backtesting
    const requestPayload = {
      agentId: 'temp-' + Date.now(), // Temporary ID for unsaved agents
      symbol: symbol.toUpperCase(),
      timeframe: '1Y', // Use 1 year timeframe format expected by edge function
      interval: '1D', // Use daily interval format expected by edge function
      userId: user.id,
      currentDate: new Date().toISOString(),
      isMarketplaceAgent: true, // Set to true so it uses provided configuration
      agentConfiguration: agentConfig
    };

    console.log('Auto-backtest request payload:', JSON.stringify(requestPayload, null, 2));

    // Call the backtesting edge function
    const { data, error } = await supabase.functions.invoke('agent-backtesting', {
      body: requestPayload
    });

    if (error) {
      console.error('Auto-backtest error:', error);
      throw new Error(`Backtest failed: ${error.message}`);
    }

    if (!data) {
      throw new Error('No backtest data returned');
    }

    console.log('Auto-backtest completed successfully:', data);
    return data;

  } catch (error: any) {
    console.error('Auto-backtest error:', error);
    throw error;
  }
}
