import axios from 'axios';

const POLYGON_API_KEY = import.meta.env.VITE_POLYGON_API_KEY || 'JAWMu4zOUA4W7HrLCOx2GU2Cpml4_Faz';
const BASE_URL = 'https://api.polygon.io';

// Interface for the stock data
export interface StockCandle {
  c: number; // close price
  h: number; // highest price
  l: number; // lowest price
  o: number; // open price
  t: number; // timestamp
  v: number; // volume
}

// Interface for the API response
interface PolygonAggregatesResponse {
  ticker: string;
  status: string;
  queryCount: number;
  resultsCount: number;
  adjusted: boolean;
  results: StockCandle[];
  request_id: string;
  count: number;
}

// Function to fetch stock data from Polygon API
export const fetchStockData = async (
  ticker: string,
  multiplier: number = 1,
  timespan: string = 'hour',
  from: string,
  to: string
): Promise<StockCandle[]> => {
  try {
    // Add timestamp to ensure we get fresh data and avoid caching
    const timestamp = Date.now();
    // Use descending sort to get the most recent data first, then we'll reverse it
    // Increased limit to 50000 to support 5-year data ranges
    const url = `${BASE_URL}/v2/aggs/ticker/${ticker}/range/${multiplier}/${timespan}/${from}/${to}?adjusted=true&sort=desc&limit=50000&timestamp=${timestamp}&apiKey=${POLYGON_API_KEY}`;

    console.log(`🔗 Polygon API call:`, {
      ticker,
      multiplier,
      timespan,
      from,
      to,
      timestamp,
      currentDate: new Date().toISOString(),
      url: url.replace(POLYGON_API_KEY, 'API_KEY_HIDDEN')
    });

    const response = await axios.get<PolygonAggregatesResponse>(url);

    // Accept both OK and DELAYED status as valid responses
    if (response.data.status === 'OK' || response.data.status === 'DELAYED') {
      // Check if we have results
      if (response.data.results && response.data.results.length > 0) {
        // Since we used descending sort, reverse the data to get chronological order
        const sortedResults = response.data.results.reverse();

        // Debug logging for returned data
        const firstResult = sortedResults[0];
        const lastResult = sortedResults[sortedResults.length - 1];
        console.log(`📊 Polygon API response:`, {
          status: response.data.status,
          resultsCount: sortedResults.length,
          requestedRange: `${from} to ${to}`,
          firstCandle: {
            timestamp: firstResult.t,
            date: new Date(firstResult.t).toISOString(),
            price: firstResult.c
          },
          lastCandle: {
            timestamp: lastResult.t,
            date: new Date(lastResult.t).toISOString(),
            price: lastResult.c
          },
          note: 'Data sorted desc then reversed to get most recent available data'
        });

        return sortedResults;
      } else {
        throw new Error('No data available for the selected timeframe');
      }
    } else {
      throw new Error(`Failed to fetch data: ${response.data.status}`);
    }
  } catch (error) {
    console.error('Error fetching stock data:', error);
    throw error;
  }
};

// Function to format date to YYYY-MM-DD
export const formatDate = (date: Date): string => {
  return date.toISOString().split('T')[0];
};

// Function to get date range for the last N days
export const getDateRangeForLastDays = (days: number): { from: string; to: string } => {
  // Use current time to ensure we get the latest data
  const now = new Date();

  // For the "to" date, use tomorrow to ensure we capture today's data
  const to = new Date(now);
  to.setDate(to.getDate() + 1); // Add 1 day to ensure we get today's data

  // For very large day ranges (like 5 years), use a more reliable method
  const from = new Date(now);
  if (days > 365) {
    // For ranges over a year, calculate by years and days to avoid month overflow issues
    const years = Math.floor(days / 365);
    const remainingDays = days % 365;
    from.setFullYear(from.getFullYear() - years);
    from.setDate(from.getDate() - remainingDays);
  } else {
    from.setDate(from.getDate() - days);
  }

  const result = {
    from: formatDate(from),
    to: formatDate(to)
  };

  // Debug logging to check date ranges
  console.log(`📅 getDateRangeForLastDays(${days}):`, {
    currentDate: now.toISOString(),
    fromDate: from.toISOString(),
    toDate: to.toISOString(),
    result,
    daysDifference: Math.round((to.getTime() - from.getTime()) / (1000 * 60 * 60 * 24)),
    yearsDifference: Math.round((to.getTime() - from.getTime()) / (1000 * 60 * 60 * 24 * 365) * 10) / 10,
    requestedDays: days,
    note: 'Using improved date calculation for large ranges'
  });

  return result;
};

// Function to convert Polygon data to Lightweight Charts format
export const convertToChartData = (data: StockCandle[]) => {
  return data.map(candle => ({
    time: candle.t / 1000, // Convert milliseconds to seconds for Lightweight Charts
    open: candle.o,
    high: candle.h,
    low: candle.l,
    close: candle.c,
    volume: candle.v
  }));
};

// Function to fetch 24-hour intraday data for mini charts
export const fetch24HourData = async (ticker: string): Promise<{ time: number; price: number }[]> => {
  try {
    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    const from = formatDate(yesterday);
    const to = formatDate(now);

    // Fetch 5-minute intervals for 24 hours
    const data = await fetchStockData(ticker, 5, 'minute', from, to);

    // Convert to simple price points for line chart
    return data.map(candle => ({
      time: candle.t,
      price: candle.c
    })).slice(-288); // Last 288 points (24 hours * 12 five-minute intervals per hour)
  } catch (error) {
    console.error(`Error fetching 24h data for ${ticker}:`, error);
    // Return empty array on error to prevent breaking the UI
    return [];
  }
};
