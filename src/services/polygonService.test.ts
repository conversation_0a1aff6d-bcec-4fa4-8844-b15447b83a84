import { fetchLatestPriceData, LatestPriceData } from './polygonService';
import { formatPolygonSymbol } from '@/utils/symbolUtils';

// Mock the symbolUtils module
jest.mock('@/utils/symbolUtils', () => ({
  formatPolygonSymbol: jest.fn((symbol) => Promise.resolve(`MOCK_${symbol}`))
}));

// Mock fetch
global.fetch = jest.fn();

describe('fetchLatestPriceData', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (fetch as jest.Mock).mockClear();
  });

  it('should fetch and transform intraday data for a single symbol', async () => {
    // Mock successful API response with intraday data
    const mockResponse = {
      results: [
        {
          t: 1617221700000, // latest data point
          o: 102,
          h: 103,
          l: 101,
          c: 103,
          v: 2000
        },
        {
          t: 1617221640000,
          o: 101,
          h: 102,
          l: 100,
          c: 102,
          v: 3000
        },
        {
          t: 1617221580000, // earliest data point
          o: 100,
          h: 101,
          l: 99,
          c: 101,
          v: 5000
        }
      ],
      ticker: 'MOCK_AAPL'
    };

    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse
    });

    const result = await fetchLatestPriceData('AAPL');

    // Check formatPolygonSymbol was called
    expect(formatPolygonSymbol).toHaveBeenCalledWith('AAPL');

    // Check fetch was called with correct URL for intraday data
    expect(fetch).toHaveBeenCalledWith(
      expect.stringContaining('https://api.polygon.io/v2/aggs/ticker/MOCK_AAPL/range/1/minute')
    );

    // Check the transformed result
    expect(result).toHaveLength(1);
    expect(result[0]).toMatchObject({
      symbol: 'AAPL',
      formattedSymbol: 'MOCK_AAPL',
      price: 103,
      change: 3, // 103 - 100
      percentChange: 3, // (103-100)/100 * 100
      volume: 10000, // sum of all volumes
      dataType: 'intraday_15min',
      dataPoints: 3
    });
  });

  it('should fall back to previous close data if intraday data is not available', async () => {
    // Mock empty intraday data response
    const emptyIntradayResponse = {
      results: [],
      ticker: 'MOCK_AAPL'
    };

    // Mock successful previous data response
    const mockPrevResponse = {
      results: [{
        t: 1617221400000,
        o: 100,
        h: 105,
        l: 98,
        c: 102,
        v: 10000
      }],
      ticker: 'MOCK_AAPL'
    };

    (fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => emptyIntradayResponse
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockPrevResponse
      });

    const result = await fetchLatestPriceData('AAPL');

    // Check formatPolygonSymbol was called
    expect(formatPolygonSymbol).toHaveBeenCalledWith('AAPL');

    // Check fetch was called twice - first for intraday, then for prev
    expect(fetch).toHaveBeenCalledTimes(2);
    expect(fetch).toHaveBeenNthCalledWith(
      1,
      expect.stringContaining('https://api.polygon.io/v2/aggs/ticker/MOCK_AAPL/range/1/minute')
    );
    expect(fetch).toHaveBeenNthCalledWith(
      2,
      expect.stringContaining('https://api.polygon.io/v2/aggs/ticker/MOCK_AAPL/prev')
    );

    // Check the transformed result
    expect(result).toHaveLength(1);
    expect(result[0]).toMatchObject({
      symbol: 'AAPL',
      formattedSymbol: 'MOCK_AAPL',
      price: 102,
      dataType: 'previous_day'
    });
  });

  it('should process multiple symbols with intraday data', async () => {
    // Mock successful API responses
    const mockAaplResponse = {
      results: [
        {
          t: 1617221700000,
          o: 102,
          h: 103,
          l: 101,
          c: 103,
          v: 5000
        },
        {
          t: 1617221580000,
          o: 100,
          h: 101,
          l: 99,
          c: 101,
          v: 5000
        }
      ],
      ticker: 'MOCK_AAPL'
    };

    const mockMsftResponse = {
      results: [
        {
          t: 1617221700000,
          o: 202,
          h: 203,
          l: 201,
          c: 203,
          v: 8000
        },
        {
          t: 1617221580000,
          o: 200,
          h: 201,
          l: 199,
          c: 200,
          v: 7000
        }
      ],
      ticker: 'MOCK_MSFT'
    };

    (fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockAaplResponse
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockMsftResponse
      });

    const result = await fetchLatestPriceData(['AAPL', 'MSFT']);

    // Check formatPolygonSymbol was called for both symbols
    expect(formatPolygonSymbol).toHaveBeenCalledWith('AAPL');
    expect(formatPolygonSymbol).toHaveBeenCalledWith('MSFT');

    // Check fetch was called with correct URLs
    expect(fetch).toHaveBeenCalledTimes(2);
    
    // Check results
    expect(result).toHaveLength(2);
    expect(result[0].symbol).toBe('AAPL');
    expect(result[1].symbol).toBe('MSFT');
    expect(result[0].price).toBe(103);
    expect(result[1].price).toBe(203);
    expect(result[0].dataType).toBe('intraday_15min');
    expect(result[1].dataType).toBe('intraday_15min');
  });

  it('should handle API errors gracefully', async () => {
    // Mock a failed API response
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
      status: 404
    });

    const result = await fetchLatestPriceData('BADTICKER');

    // Should return an empty array instead of throwing
    expect(result).toEqual([]);
    
    // Console error should have been called
    expect(console.error).toHaveBeenCalled;
  });
}); 