import { format, subDays, addDays } from 'date-fns';

export interface OHLCVData {
  timestamp: number;
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface ChartDataResponse {
  data: OHLCVData[];
  symbol: string;
  from: string;
  to: string;
}

/**
 * Fetch OHLCV data from Polygon API for chart display
 */
export async function fetchChartData(
  symbol: string,
  fromDate: string,
  toDate: string
): Promise<ChartDataResponse> {
  try {
    // Format dates for Polygon API (YYYY-MM-DD)
    const from = format(new Date(fromDate), 'yyyy-MM-dd');
    const to = format(new Date(toDate), 'yyyy-MM-dd');
    
    console.log(`Fetching chart data for ${symbol} from ${from} to ${to}`);
    
    // Use Polygon aggregates endpoint for daily OHLCV data
    const apiKey = import.meta.env.VITE_POLYGON_API_KEY;
    if (!apiKey) {
      throw new Error('Polygon API key not configured');
    }
    
    const url = `https://api.polygon.io/v2/aggs/ticker/${symbol}/range/1/day/${from}/${to}?adjusted=true&sort=asc&apikey=${apiKey}`;
    
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`Polygon API error: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    
    if (!data.results || data.results.length === 0) {
      console.warn(`No data returned for ${symbol} from ${from} to ${to}`);
      return {
        data: [],
        symbol,
        from,
        to
      };
    }
    
    // Transform Polygon data to our format
    const chartData: OHLCVData[] = data.results.map((item: any) => ({
      timestamp: item.t,
      date: format(new Date(item.t), 'yyyy-MM-dd'),
      open: item.o,
      high: item.h,
      low: item.l,
      close: item.c,
      volume: item.v
    }));
    
    console.log(`Fetched ${chartData.length} data points for ${symbol}`);
    
    return {
      data: chartData,
      symbol,
      from,
      to
    };
    
  } catch (error) {
    console.error('Error fetching chart data:', error);
    throw error;
  }
}

/**
 * Calculate precise date range for trade chart - exactly 5 trading days before and after
 */
export function calculateTradeChartDateRange(
  entryDate: string,
  exitDate?: string
): { from: string; to: string } {
  const entry = new Date(entryDate);
  const exit = exitDate ? new Date(exitDate) : entry;

  // Calculate trade duration
  const tradeDuration = Math.abs(exit.getTime() - entry.getTime());
  const tradeDays = Math.ceil(tradeDuration / (1000 * 60 * 60 * 24));

  // Fixed context: exactly 5 trading days before entry and 5 trading days after exit
  // This gives us approximately 10-15 total candlesticks for optimal pattern analysis
  const contextBefore = 7; // 7 calendar days to ensure 5 trading days
  const contextAfter = 7;   // 7 calendar days to ensure 5 trading days

  const from = format(subDays(entry, contextBefore), 'yyyy-MM-dd');
  const to = format(addDays(exit, contextAfter), 'yyyy-MM-dd');

  console.log(`Trade chart range: ${from} to ${to} (${tradeDays} trade days, ${contextBefore} days before, ${contextAfter} days after)`);
  console.log(`Entry: ${entryDate}, Exit: ${exitDate || 'N/A'}`);

  return { from, to };
}

/**
 * Calculate smart date range for trade analysis with zoom support
 */
export function calculateTradeAnalysisDateRange(
  entryDate: string,
  exitDate?: string,
  zoomLevel: number = 1,
  viewMode: 'focused' | 'full' = 'focused',
  backtestStartDate?: string,
  backtestEndDate?: string
): { from: string; to: string } {
  if (viewMode === 'full' && backtestStartDate && backtestEndDate) {
    return { from: backtestStartDate, to: backtestEndDate };
  }

  const entry = new Date(entryDate);
  const exit = exitDate ? new Date(exitDate) : new Date();

  // Calculate trade duration
  const tradeDuration = Math.abs(exit.getTime() - entry.getTime());
  const tradeDays = Math.ceil(tradeDuration / (1000 * 60 * 60 * 24));

  // Smart context calculation based on zoom level and trade duration
  let contextDays = Math.max(5, Math.ceil(tradeDays * 0.8)); // Base context
  contextDays = Math.ceil(contextDays / zoomLevel); // Adjust for zoom

  // Ensure minimum context for pattern analysis
  contextDays = Math.max(contextDays, 3);

  // Calculate date range
  const fromDate = new Date(entry.getTime() - (contextDays * 24 * 60 * 60 * 1000));
  const toDate = new Date(exit.getTime() + (contextDays * 24 * 60 * 60 * 1000));

  const from = format(fromDate, 'yyyy-MM-dd');
  const to = format(toDate, 'yyyy-MM-dd');

  console.log(`Trade analysis range: ${from} to ${to} (zoom: ${zoomLevel}x, context: ${contextDays} days)`);

  return { from, to };
}



/**
 * Fetch chart data with proper error handling
 */
export async function fetchChartDataWithFallback(
  symbol: string,
  fromDate: string,
  toDate: string
): Promise<ChartDataResponse> {
  try {
    return await fetchChartData(symbol, fromDate, toDate);
  } catch (error) {
    console.error('Failed to fetch chart data:', error);
    throw new Error(`Failed to fetch chart data for ${symbol}: ${error.message || 'Unknown error'}`);
  }
}
