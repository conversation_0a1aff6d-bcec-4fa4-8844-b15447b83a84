import { formatPolygonSymbol } from '@/utils/symbolUtils';
import { supabase } from '@/integrations/supabase/client';

interface PolygonChartParams {
  symbol: string;
  timeframe: string;
}

// New interface for fetching latest price data
export interface LatestPriceData {
  symbol: string;
  price: number;
  change: number;
  percentChange: number;
  volume: number;
  timestamp: number;
  formattedSymbol: string;
  dataType?: string;
  dataPoints?: number;
  open?: number;  // Add open price field
  timeframe?: string; // Add timeframe for percentage calculation context
}

// Function to fetch latest price data for one or more symbols
export async function fetchLatestPriceData(symbols: string | string[], requestedTimeframe: string = '1D'): Promise<LatestPriceData[]> {
  try {
    // Call the Supabase edge function
    const { data, error } = await supabase.functions.invoke('chart-processor', {
      body: JSON.stringify({
        action: 'latest-price',
        symbol: symbols,
        timeframe: requestedTimeframe
      })
    });

    if (error) {
      console.error('Error fetching latest price data from edge function:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error(`Error processing latest price data:`, error);
    return [];
  }
}

export async function fetchPolygonChartData({ symbol, timeframe }: PolygonChartParams) {
  try {
    // Call the Supabase edge function
    const { data, error } = await supabase.functions.invoke('chart-data', {
      body: JSON.stringify({
        symbol,
        timeframe
      })
    });

    if (error) {
      console.error('Error fetching chart data:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error(`Error fetching chart data for ${symbol}:`, error);
    throw error;
  }
}

// Function to fetch percentage changes for multiple timeframes at once
export async function fetchMultiTimeframePriceData(symbol: string, timeframes: string[] = ['1D', '1W', '1M', '1Y', '5Y']): Promise<Record<string, LatestPriceData>> {
  try {
    // Call the Supabase edge function
    const { data, error } = await supabase.functions.invoke('chart-processor', {
      body: JSON.stringify({
        action: 'multi-timeframe',
        symbol,
        timeframes
      })
    });

    if (error) {
      console.error('Error fetching multi-timeframe data:', error);
      return {};
    }

    return data || {};
  } catch (error) {
    console.error(`Error in multi-timeframe fetch for ${symbol}:`, error);
    return {};
  }
}
