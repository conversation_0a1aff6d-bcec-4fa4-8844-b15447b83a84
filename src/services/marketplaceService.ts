import { supabase } from '@/integrations/supabase/client';
import { getAffiliateCode } from '@/utils/growiUtils';
import { getCurrentAuthenticatedUser } from '@/utils/authUtils';

// Types for the marketplace service
export interface MarketplaceAgent {
  id: string;
  name: string;
  description?: string;
  price: number;
  is_for_sale: boolean;
  sales_count: number;
  tags: string[];
  likes_count: number;
  usage_count: number;
  created_at: string;
  updated_at: string;
  user_id: string;
  seller_name?: string;
  seller_email?: string;
  is_verified?: boolean;
  verified_at?: string;
  verified_by?: string;
  verification_notes?: string;
}

export interface PurchasedAgent {
  id: string;
  buyer_id: string;
  seller_id: string;
  original_agent_id: string;
  purchased_agent_id: string;
  transaction_id: string;
  purchase_price: number;
  custom_name?: string;
  is_active: boolean;
  created_at: string;
  original_agent?: {
    name: string;
    description?: string;
    tags: string[];
    created_at: string;
  };
  purchased_agent?: {
    id: string;
    name: string;
    description?: string;
    configuration: any;
    created_at: string;
  };
  seller?: {
    full_name?: string;
    email?: string;
  };
  transaction?: {
    amount_total: number;
    created_at: string;
  };
}

export interface SellerAccount {
  id: string;
  user_id: string;
  stripe_account_id: string;
  account_status: string;
  charges_enabled: boolean;
  payouts_enabled: boolean;
  details_submitted: boolean;
  requirements_due: string[];
  created_at: string;
  updated_at: string;
}

export interface SellerEarning {
  id: string;
  seller_id: string;
  transaction_id: string;
  amount: number;
  currency: string;
  status: string;
  stripe_transfer_id?: string;
  payout_date?: string;
  created_at: string;
  transaction?: {
    agent_id: string;
    buyer_id: string;
    amount_total: number;
    created_at: string;
    agents?: {
      name: string;
    };
    buyer?: {
      full_name?: string;
      email?: string;
    };
  };
}

export interface EarningsSummary {
  total_earnings: number;
  available_earnings: number;
  paid_out_earnings: number;
  total_sales: number;
}

export interface MarketplaceFilters {
  search?: string;
  min_price?: number;
  max_price?: number;
  tags?: string[];
  sort_by?: 'newest' | 'price_low' | 'price_high' | 'popular' | 'sales';
  limit?: number;
  offset?: number;
  verified_only?: boolean;
}

/**
 * Get PAID marketplace agents ONLY (strict isolation)
 * This function ONLY returns agents that are for sale with price > 0
 * It will NEVER return free agents from published_agents table
 */
export async function getPaidMarketplaceAgents(filters: MarketplaceFilters = {}): Promise<{
  success: boolean;
  agents: MarketplaceAgent[];
  total: number;
  error?: string;
}> {
  try {
    console.log('💰 Getting PAID marketplace agents with filters:', filters);

    // STRICT ISOLATION: Only get paid agents from agents table
    // Must have: is_for_sale = true, is_public = true, price > 0
    // Must NOT be in published_agents table (to prevent cross-contamination)
    let query = supabase
      .from('agents')
      .select('*', { count: 'exact' })
      .eq('is_for_sale', true)
      .eq('is_public', true)
      .not('price', 'is', null)
      .gt('price', 0);

    // Apply search filters
    if (filters.search) {
      const searchFilter = `name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`;
      query = query.or(searchFilter);
    }

    // Apply price filters
    if (filters.min_price !== undefined) {
      query = query.gte('price', filters.min_price);
    }

    if (filters.max_price !== undefined) {
      query = query.lte('price', filters.max_price);
    }

    // Apply tag filters
    if (filters.tags && filters.tags.length > 0) {
      query = query.overlaps('tags', filters.tags);
    }

    // Apply verification filter
    if (filters.verified_only) {
      query = query.eq('is_verified', true);
    }

    // Apply sorting
    switch (filters.sort_by) {
      case 'price_low':
        query = query.order('price', { ascending: true });
        break;
      case 'price_high':
        query = query.order('price', { ascending: false });
        break;
      case 'popular':
        query = query.order('likes_count', { ascending: false });
        break;
      case 'sales':
        query = query.order('sales_count', { ascending: false });
        break;
      case 'newest':
      default:
        query = query.order('created_at', { ascending: false });
        break;
    }

    // Apply pagination
    if (filters.limit) {
      query = query.limit(filters.limit);
    }
    if (filters.offset) {
      query = query.range(filters.offset, filters.offset + (filters.limit || 20) - 1);
    }

    const { data, error, count } = await query;

    if (error) {
      console.error('❌ Error fetching paid agents:', error);
      throw error;
    }

    console.log(`💰 Found ${data?.length || 0} paid agents`);

    // STRICT ISOLATION: Filter out any agents that are also in published_agents
    // This prevents cross-contamination between free and paid sections
    const agentIds = (data || []).map(agent => agent.id);
    const { data: publishedAgents } = await supabase
      .from('published_agents')
      .select('agent_id')
      .in('agent_id', agentIds)
      .eq('is_active', true);

    const publishedAgentIds = new Set(publishedAgents?.map(pa => pa.agent_id) || []);

    // Remove any agents that are also published as free
    const strictlyPaidAgents = (data || []).filter(agent => !publishedAgentIds.has(agent.id));

    console.log(`🔒 After strict isolation: ${strictlyPaidAgents.length} paid agents (removed ${(data?.length || 0) - strictlyPaidAgents.length} cross-contaminated)`);

    // Get unique user IDs to fetch profile data
    const userIds = [...new Set(strictlyPaidAgents.map(agent => agent.user_id))];

    // Fetch profile data for all users
    const { data: profiles } = await supabase
      .from('profiles')
      .select('id, full_name, email')
      .in('id', userIds);

    // Create a map of user profiles for quick lookup
    const profileMap = new Map(profiles?.map(profile => [profile.id, profile]) || []);

    const agents: MarketplaceAgent[] = strictlyPaidAgents.map(agent => ({
      ...agent,
      is_paid: true,
      marketplace_type: 'paid' as const,
      seller_name: profileMap.get(agent.user_id)?.full_name,
      seller_email: profileMap.get(agent.user_id)?.email
    }));

    return {
      success: true,
      agents,
      total: strictlyPaidAgents.length
    };
  } catch (error) {
    console.error('❌ Error getting paid marketplace agents:', error);
    return {
      success: false,
      agents: [],
      total: 0,
      error: error.message || 'Failed to get paid marketplace agents'
    };
  }
}

/**
 * DEPRECATED: Use getPaidMarketplaceAgents() instead
 * This function violated isolation requirements by mixing free and paid agents
 */
export async function getMarketplaceAgents(filters: MarketplaceFilters = {}): Promise<{
  success: boolean;
  agents: MarketplaceAgent[];
  total: number;
  error?: string;
}> {
  console.warn('⚠️ getMarketplaceAgents() is deprecated. Use getPaidMarketplaceAgents() for paid agents or discoverAgents() for free agents.');
  return getPaidMarketplaceAgents(filters);
}

/**
 * COMPLETE MARKETPLACE REMOVAL
 * Removes an agent from ALL marketplace sections (both free and paid)
 * This ensures the agent does not appear in any marketplace queries
 */
export async function removeAgentFromAllMarketplaces(agentId: string): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    console.log('🗑️ Removing agent from ALL marketplaces:', agentId);

    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    // SECURITY: Verify agent ownership first
    const { data: agent, error: ownershipError } = await supabase
      .from('agents')
      .select('user_id, name')
      .eq('id', agentId)
      .single();

    if (ownershipError || !agent) {
      throw new Error('Agent not found');
    }

    if (agent.user_id !== user.id) {
      throw new Error('You do not have permission to remove this agent from the marketplace');
    }

    console.log(`🔐 Ownership verified for agent: ${agent.name}`);

    // STEP 1: Remove from paid marketplace (agents table)
    console.log('🔄 Removing from paid marketplace...');
    const { error: paidError } = await supabase
      .from('agents')
      .update({
        is_for_sale: false,
        price: null,
        is_public: false, // Make completely private
        updated_at: new Date().toISOString()
      })
      .eq('id', agentId)
      .eq('user_id', user.id);

    if (paidError) {
      console.error('❌ Error removing from paid marketplace:', paidError);
      throw new Error(`Failed to remove from paid marketplace: ${paidError.message}`);
    }

    // STEP 2: Remove from free marketplace (published_agents table)
    console.log('🔄 Removing from free marketplace...');

    // First check if the user owns this published agent
    const { data: publishedAgent, error: checkError } = await supabase
      .from('published_agents')
      .select('id, publisher_id')
      .eq('agent_id', agentId)
      .single();

    if (checkError) {
      console.log('ℹ️ Agent not in free marketplace (no published_agents entry)');
    } else if (publishedAgent.publisher_id !== user.id) {
      console.error('❌ User does not own this published agent');
      throw new Error('You do not have permission to remove this agent from the marketplace');
    } else {
      // User owns the published agent, proceed with removal
      const { error: freeError } = await supabase
        .from('published_agents')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('agent_id', agentId)
        .eq('publisher_id', user.id);

      if (freeError) {
        console.error('❌ Error removing from free marketplace:', freeError);
        throw new Error(`Failed to remove from free marketplace: ${freeError.message}`);
      }
    }

    // STEP 3: Verify complete removal
    console.log('🔍 Verifying complete removal...');
    const { data: verifyAgent } = await supabase
      .from('agents')
      .select('is_for_sale, is_public, price')
      .eq('id', agentId)
      .single();

    const { data: verifyPublished } = await supabase
      .from('published_agents')
      .select('is_active')
      .eq('agent_id', agentId)
      .eq('is_active', true)
      .single();

    if (verifyAgent?.is_for_sale || verifyAgent?.is_public || verifyPublished) {
      console.error('❌ Agent still appears in marketplace after removal attempt');
      throw new Error('Failed to completely remove agent from marketplace');
    }

    console.log('✅ Agent successfully removed from ALL marketplaces');
    return { success: true };

  } catch (error) {
    console.error('❌ Error removing agent from all marketplaces:', error);
    return {
      success: false,
      error: error.message || 'Failed to remove agent from marketplace'
    };
  }
}

/**
 * Update agent pricing (alias for setAgentPrice)
 */
export async function updateAgentPricing(agentId: string, pricing: { price: number | null; is_for_sale: boolean }): Promise<{
  success: boolean;
  error?: string;
}> {
  return setAgentPrice(agentId, pricing.price, pricing.is_for_sale);
}

/**
 * Set agent price and enable/disable sale (OWNERS ONLY)
 */
export async function setAgentPrice(agentId: string, price: number | null, isForSale: boolean): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Check if Whop user has valid email before allowing marketplace listing
    if (isForSale) {
      const emailValidation = await validateWhopUserEmail();
      if (!emailValidation.isValid) {
        throw new Error(emailValidation.error || 'Please add your email address in Settings to list agents for sale');
      }
    }

    // SECURITY: Verify agent ownership (not just license)
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('user_id')
      .eq('id', agentId)
      .eq('user_id', user.id) // SECURITY: Only allow actual owners
      .single();

    if (agentError || !agent) {
      throw new Error('Agent not found or you do not own this agent');
    }

    // SECURITY: Triple-check that this is not a licensed agent
    // Licensed agents should NEVER be allowed to have prices set by buyers
    const { data: licenseCheck } = await supabase
      .from('agent_licenses')
      .select('id')
      .eq('agent_id', agentId)
      .eq('buyer_id', user.id)
      .single();

    if (licenseCheck) {
      throw new Error('SECURITY VIOLATION: You cannot set prices for licensed agents. You can only set prices for agents you created.');
    }

    // SECURITY: Additional check - ensure user is not trying to modify an agent they purchased
    const { data: purchaseCheck } = await supabase
      .from('purchased_agents')
      .select('id')
      .eq('original_agent_id', agentId)
      .eq('buyer_id', user.id)
      .single();

    if (purchaseCheck) {
      throw new Error('SECURITY VIOLATION: You cannot set prices for agents you purchased. You can only set prices for agents you created.');
    }

    // Update agent pricing and auto-publish when listing for sale
    const updateData: any = {
      price: isForSale ? price : null,
      is_for_sale: isForSale,
      updated_at: new Date().toISOString()
    };

    // When listing for sale, automatically make it public
    if (isForSale) {
      updateData.is_public = true;
    }

    const { error } = await supabase
      .from('agents')
      .update(updateData)
      .eq('id', agentId)
      .eq('user_id', user.id); // SECURITY: Double-check ownership

    if (error) {
      throw error;
    }

    // SYNCHRONIZATION FIX: Handle published_agents table consistency
    if (!isForSale) {
      // If removing from sale, also remove from published_agents table to maintain consistency
      console.log('🔄 Agent removed from paid marketplace, checking published_agents table...');
      try {
        const { error: unpublishError } = await supabase
          .from('published_agents')
          .update({ is_active: false })
          .eq('agent_id', agentId)
          .eq('publisher_id', user.id);

        if (unpublishError) {
          console.warn('⚠️ Failed to update published_agents table:', unpublishError);
          // Don't fail the whole operation
        } else {
          console.log('✅ Successfully synchronized published_agents table');
        }
      } catch (syncError) {
        console.warn('⚠️ Exception while synchronizing published_agents table:', syncError);
        // Don't fail the whole operation
      }
    } else {
      // MARKETPLACE SYNC FIX: When listing for paid sale, ensure no conflicting published_agents entry
      console.log('🔄 Agent listed for paid sale, cleaning up any inactive published_agents entries...');
      try {
        const { error: cleanupError } = await supabase
          .from('published_agents')
          .update({ is_active: false })
          .eq('agent_id', agentId)
          .eq('publisher_id', user.id)
          .eq('is_active', false); // Only update already inactive entries to avoid conflicts

        if (cleanupError) {
          console.warn('⚠️ Failed to cleanup published_agents table:', cleanupError);
          // Don't fail the whole operation
        } else {
          console.log('✅ Successfully cleaned up inactive published_agents entries');
        }
      } catch (syncError) {
        console.warn('⚠️ Exception while cleaning up published_agents table:', syncError);
        // Don't fail the whole operation
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Error setting agent price:', error);
    return {
      success: false,
      error: error.message || 'Failed to set agent price'
    };
  }
}

/**
 * Check if Whop user has provided a valid email for marketplace features
 */
export async function validateWhopUserEmail(): Promise<{
  isValid: boolean;
  email?: string;
  error?: string;
}> {
  try {
    const user = await getCurrentAuthenticatedUser();
    if (!user) {
      return { isValid: false, error: 'User not authenticated' };
    }

    // Only check for Whop users
    if (!user.isWhopUser) {
      return { isValid: true }; // Regular users are always valid
    }

    const userEmail = user.email || '';

    // Check if it's a whop.app or whop.user email (invalid for marketplace)
    const isWhopAppEmail = userEmail.includes('@whop.user') || userEmail.includes('@whop.app');

    if (!userEmail || isWhopAppEmail) {
      return {
        isValid: false,
        error: 'Please add your email address in Settings to access marketplace features'
      };
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(userEmail)) {
      return {
        isValid: false,
        error: 'Please provide a valid email address in Settings'
      };
    }

    return { isValid: true, email: userEmail };
  } catch (error) {
    console.error('Error validating Whop user email:', error);
    return {
      isValid: false,
      error: 'Failed to validate email'
    };
  }
}

/**
 * Check if user can access marketplace features (for UI display)
 */
export async function canAccessMarketplaceFeatures(): Promise<{
  canAccess: boolean;
  reason?: string;
}> {
  try {
    const emailValidation = await validateWhopUserEmail();
    return {
      canAccess: emailValidation.isValid,
      reason: emailValidation.error
    };
  } catch (error) {
    return {
      canAccess: false,
      reason: 'Failed to validate marketplace access'
    };
  }
}

/**
 * Create Stripe Connect account for seller
 */
export async function createSellerAccount(email: string, country: string = 'US'): Promise<{
  success: boolean;
  account_id?: string;
  status?: string;
  error?: string;
}> {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) {
      throw new Error('No authentication token found');
    }

    const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/marketplace-stripe-connect`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      },
      body: JSON.stringify({
        action: 'create-account',
        email,
        country
      })
    });

    if (!response.ok) {
      throw new Error(`Server returned ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error creating seller account:', error);
    return {
      success: false,
      error: error.message || 'Failed to create seller account'
    };
  }
}

/**
 * Create account link for Stripe Connect onboarding
 */
export async function createAccountLink(accountId: string, refreshUrl: string, returnUrl: string): Promise<{
  success: boolean;
  url?: string;
  error?: string;
}> {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) {
      throw new Error('No authentication token found');
    }

    const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/marketplace-stripe-connect`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      },
      body: JSON.stringify({
        action: 'create-account-link',
        account_id: accountId,
        refresh_url: refreshUrl,
        return_url: returnUrl
      })
    });

    if (!response.ok) {
      throw new Error(`Server returned ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error creating account link:', error);
    return {
      success: false,
      error: error.message || 'Failed to create account link'
    };
  }
}

/**
 * Get seller account status
 */
export async function getSellerAccount(): Promise<{
  success: boolean;
  account?: any;
  error?: string;
}> {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) {
      throw new Error('No authentication token found');
    }

    const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/marketplace-stripe-connect`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      },
      body: JSON.stringify({
        action: 'get-account'
      })
    });

    if (!response.ok) {
      throw new Error(`Server returned ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error getting seller account:', error);
    return {
      success: false,
      error: error.message || 'Failed to get seller account'
    };
  }
}

/**
 * Clean up marketplace inconsistencies for a specific agent
 * This fixes cases where agents are stuck between free and paid marketplaces
 */
export async function cleanupAgentMarketplaceState(agentId: string): Promise<{
  success: boolean;
  changes: string[];
  error?: string;
}> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    const changes: string[] = [];

    // Get current agent state
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('id, name, user_id, is_for_sale, price, is_public')
      .eq('id', agentId)
      .eq('user_id', user.id)
      .single();

    if (agentError || !agent) {
      throw new Error('Agent not found or you do not own this agent');
    }

    // Get published_agents state
    const { data: publishedAgent } = await supabase
      .from('published_agents')
      .select('id, is_active')
      .eq('agent_id', agentId)
      .eq('publisher_id', user.id)
      .single();

    console.log(`🔍 Cleaning up marketplace state for agent: ${agent.name}`);
    console.log(`📊 Current state: is_for_sale=${agent.is_for_sale}, price=${agent.price}, is_public=${agent.is_public}`);
    console.log(`📋 Published state: ${publishedAgent ? `active=${publishedAgent.is_active}` : 'not published'}`);

    // CASE 1: Agent is for paid sale but has conflicting published_agents entry
    if (agent.is_for_sale && agent.price && agent.price > 0) {
      if (publishedAgent && publishedAgent.is_active) {
        // Deactivate published_agents entry to avoid conflict
        const { error: deactivateError } = await supabase
          .from('published_agents')
          .update({ is_active: false })
          .eq('agent_id', agentId)
          .eq('publisher_id', user.id);

        if (deactivateError) {
          console.warn('⚠️ Failed to deactivate published_agents entry:', deactivateError);
        } else {
          changes.push('Deactivated conflicting free marketplace entry');
          console.log('✅ Deactivated conflicting published_agents entry');
        }
      }

      // Ensure agent is public for paid marketplace
      if (!agent.is_public) {
        const { error: publicError } = await supabase
          .from('agents')
          .update({ is_public: true })
          .eq('id', agentId)
          .eq('user_id', user.id);

        if (publicError) {
          console.warn('⚠️ Failed to make agent public:', publicError);
        } else {
          changes.push('Made agent public for paid marketplace');
          console.log('✅ Made agent public for paid marketplace');
        }
      }
    }

    // CASE 2: Agent is not for sale but has active published_agents entry
    if (!agent.is_for_sale && publishedAgent && publishedAgent.is_active) {
      // This is fine - agent is in free marketplace only
      console.log('ℹ️ Agent is correctly in free marketplace only');
    }

    // CASE 3: Agent is not for sale and not published - make it private
    if (!agent.is_for_sale && (!publishedAgent || !publishedAgent.is_active) && agent.is_public) {
      const { error: privateError } = await supabase
        .from('agents')
        .update({ is_public: false })
        .eq('id', agentId)
        .eq('user_id', user.id);

      if (privateError) {
        console.warn('⚠️ Failed to make agent private:', privateError);
      } else {
        changes.push('Made agent private (not in any marketplace)');
        console.log('✅ Made agent private since it\'s not in any marketplace');
      }
    }

    console.log(`🎯 Cleanup completed with ${changes.length} changes`);
    return { success: true, changes };

  } catch (error) {
    console.error('❌ Error cleaning up agent marketplace state:', error);
    return {
      success: false,
      changes: [],
      error: error.message || 'Failed to cleanup agent marketplace state'
    };
  }
}

/**
 * SECURITY: Validate if user can edit an agent (OWNERS ONLY)
 */
export async function validateAgentEditPermission(agentId: string): Promise<{
  canEdit: boolean;
  error?: string;
}> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return { canEdit: false, error: 'User not authenticated' };
    }

    // Check if user owns the agent
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('user_id')
      .eq('id', agentId)
      .eq('user_id', user.id)
      .single();

    if (agentError || !agent) {
      return { canEdit: false, error: 'Agent not found or you do not own this agent' };
    }

    // SECURITY: Check if user has a license for this agent (meaning they purchased it)
    const { data: licenseCheck } = await supabase
      .from('agent_licenses')
      .select('id')
      .eq('agent_id', agentId)
      .eq('buyer_id', user.id)
      .single();

    if (licenseCheck) {
      return { canEdit: false, error: 'You cannot edit licensed agents. You can only edit agents you created.' };
    }

    // SECURITY: Check if user purchased this agent
    const { data: purchaseCheck } = await supabase
      .from('purchased_agents')
      .select('id')
      .eq('original_agent_id', agentId)
      .eq('buyer_id', user.id)
      .single();

    if (purchaseCheck) {
      return { canEdit: false, error: 'You cannot edit purchased agents. You can only edit agents you created.' };
    }

    return { canEdit: true };
  } catch (error) {
    console.error('Error validating agent edit permission:', error);
    return { canEdit: false, error: 'Failed to validate permissions' };
  }
}

/**
 * Purchase an agent
 */
export async function purchaseAgent(agentId: string, customName?: string): Promise<{
  success: boolean;
  client_secret?: string;
  payment_intent_id?: string;
  amount?: number;
  agent_name?: string;
  seller_name?: string;
  error?: string;
}> {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) {
      throw new Error('No authentication token found');
    }

    // Get affiliate code for Growi tracking
    const affiliateCode = getAffiliateCode();

    const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/marketplace-purchase`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      },
      body: JSON.stringify({
        action: 'purchase-agent',
        agent_id: agentId,
        custom_name: customName,
        affiliateCode
      })
    });

    if (!response.ok) {
      throw new Error(`Server returned ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error purchasing agent:', error);
    return {
      success: false,
      error: error.message || 'Failed to purchase agent'
    };
  }
}

/**
 * Confirm payment after successful Stripe payment
 */
export async function confirmPayment(paymentIntentId: string, customName?: string): Promise<{
  success: boolean;
  purchased_agent_id?: string;
  error?: string;
}> {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) {
      throw new Error('No authentication token found');
    }

    const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/marketplace-purchase`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      },
      body: JSON.stringify({
        action: 'confirm-payment',
        payment_intent_id: paymentIntentId,
        custom_name: customName
      })
    });

    if (!response.ok) {
      throw new Error(`Server returned ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error confirming payment:', error);
    return {
      success: false,
      error: error.message || 'Failed to confirm payment'
    };
  }
}

/**
 * Get user's purchased agents
 */
export async function getPurchasedAgents(): Promise<{
  success: boolean;
  purchased_agents: PurchasedAgent[];
  error?: string;
}> {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) {
      throw new Error('No authentication token found');
    }

    const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/marketplace-purchase`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      },
      body: JSON.stringify({
        action: 'get-purchased-agents'
      })
    });

    if (!response.ok) {
      throw new Error(`Server returned ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error getting purchased agents:', error);
    return {
      success: false,
      purchased_agents: [],
      error: error.message || 'Failed to get purchased agents'
    };
  }
}

/**
 * Get seller earnings and summary
 */
export async function getSellerEarnings(): Promise<{
  success: boolean;
  earnings: SellerEarning[];
  summary: EarningsSummary;
  error?: string;
}> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    // For now, return demo data since we don't have real transactions yet
    const demoEarnings: SellerEarning[] = [];
    const demoSummary: EarningsSummary = {
      total_earnings: 0,
      available_earnings: 0,
      paid_out_earnings: 0,
      total_sales: 0
    };

    return {
      success: true,
      earnings: demoEarnings,
      summary: demoSummary
    };
  } catch (error) {
    console.error('Error getting seller earnings:', error);
    return {
      success: false,
      earnings: [],
      summary: {
        total_earnings: 0,
        available_earnings: 0,
        paid_out_earnings: 0,
        total_sales: 0
      },
      error: error.message || 'Failed to get seller earnings'
    };
  }
}




