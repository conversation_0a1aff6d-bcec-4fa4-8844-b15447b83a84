import { supabase } from '@/integrations/supabase/client';
import { getAuthenticatedUser } from './authService';

// Types for secure agent service
export interface AgentLicense {
  id: string;
  license_key: string;
  buyer_id: string;
  seller_id: string;
  agent_id: string;
  custom_name?: string;
  purchase_price: number;
  usage_count: number;
  max_usage_limit?: number;
  license_expires_at?: string;
  is_active: boolean;
  is_suspended: boolean;
  created_at: string;
  updated_at: string;
  agent?: {
    id: string;
    name: string;
    description?: string;
    tags?: string[];
    is_encrypted: boolean;
    security_level: number;
  };
}

export interface SecureAgentExecutionResult {
  signal: 'bullish' | 'bearish' | 'neutral';
  confidence: number;
  reasoning: string;
  timestamp: string;
  agent_name: string;
  usage_count: number;
  max_usage_limit?: number;
}

/**
 * SECURITY: Execute a licensed agent securely without exposing configuration
 */
export async function executeSecureAgent(
  licenseKey: string,
  symbol: string,
  timeframe: string = 'day'
): Promise<SecureAgentExecutionResult> {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) {
      throw new Error('No authentication token found');
    }

    // Generate request fingerprint for security tracking
    const requestFingerprint = await generateRequestFingerprint();

    const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/secure-agent-executor`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      },
      body: JSON.stringify({
        license_key: licenseKey,
        symbol: symbol,
        timeframe: timeframe,
        request_fingerprint: requestFingerprint
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Secure agent execution failed');
    }

    const result = await response.json();
    
    if (!result.success) {
      throw new Error(result.error || 'Secure agent execution failed');
    }

    return result.result;

  } catch (error) {
    console.error('Secure agent execution error:', error);
    throw error;
  }
}

/**
 * Get user's purchased agent licenses
 */
export async function getPurchasedAgentLicenses(): Promise<{
  success: boolean;
  licenses?: AgentLicense[];
  error?: string;
}> {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) {
      throw new Error('No authentication token found');
    }

    const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/marketplace-purchase`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      },
      body: JSON.stringify({
        action: 'get-purchased-agents'
      })
    });

    if (!response.ok) {
      throw new Error('Failed to fetch purchased agents');
    }

    const result = await response.json();
    
    if (!result.success) {
      throw new Error(result.error || 'Failed to fetch purchased agents');
    }

    return {
      success: true,
      licenses: result.purchased_agents
    };

  } catch (error) {
    console.error('Error fetching purchased agent licenses:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * SECURITY: Validate license access without exposing agent configuration
 */
export async function validateLicenseAccess(licenseKey: string): Promise<{
  valid: boolean;
  agent_name?: string;
  usage_count?: number;
  max_usage_limit?: number;
  error?: string;
}> {
  try {
    const user = await getAuthenticatedUser();

    const { data: validation, error } = await supabase
      .rpc('validate_agent_license', {
        p_license_key: licenseKey,
        p_user_id: user.id,
        p_request_fingerprint: await generateRequestFingerprint()
      });

    if (error) {
      throw new Error(error.message);
    }

    return validation;

  } catch (error) {
    console.error('License validation error:', error);
    return {
      valid: false,
      error: error instanceof Error ? error.message : 'Validation failed'
    };
  }
}

/**
 * Get secure agent execution history (without exposing configurations)
 */
export async function getSecureExecutionHistory(licenseKey?: string): Promise<{
  success: boolean;
  executions?: any[];
  error?: string;
}> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    let query = supabase
      .from('secure_agent_executions')
      .select(`
        id,
        symbol,
        timeframe,
        execution_result,
        execution_time_ms,
        created_at,
        agent_licenses!inner(
          custom_name,
          agent:agent_id(name)
        )
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (licenseKey) {
      query = query.eq('license_key', licenseKey);
    }

    const { data: executions, error } = await query;

    if (error) {
      throw new Error(error.message);
    }

    return {
      success: true,
      executions: executions || []
    };

  } catch (error) {
    console.error('Error fetching execution history:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * SECURITY: Generate request fingerprint for tracking and anti-sharing
 */
async function generateRequestFingerprint(): Promise<string> {
  try {
    // Collect browser/device information for fingerprinting
    const fingerprint = {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      screen: `${screen.width}x${screen.height}`,
      timestamp: Date.now()
    };

    // Create hash of fingerprint data
    const encoder = new TextEncoder();
    const data = encoder.encode(JSON.stringify(fingerprint));
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');

    return hashHex;

  } catch (error) {
    console.error('Error generating request fingerprint:', error);
    return 'unknown';
  }
}

/**
 * SECURITY: Check if user has valid license for an agent
 */
export async function hasValidLicense(agentId: string): Promise<boolean> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return false;
    }

    const { data: license, error } = await supabase
      .from('agent_licenses')
      .select('id')
      .eq('buyer_id', user.id)
      .eq('agent_id', agentId)
      .eq('is_active', true)
      .eq('is_suspended', false)
      .single();

    return !error && !!license;

  } catch (error) {
    console.error('Error checking license validity:', error);
    return false;
  }
}

/**
 * SECURITY: Report suspicious activity
 */
export async function reportSecurityViolation(
  violationType: string,
  details: any
): Promise<void> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    await supabase
      .from('agent_access_violations')
      .insert({
        user_id: user?.id,
        violation_type: violationType,
        violation_details: details,
        ip_address: 'client-side', // Will be overridden by server
        user_agent: navigator.userAgent
      });

  } catch (error) {
    console.error('Error reporting security violation:', error);
    // Don't throw - security reporting should be silent
  }
}
