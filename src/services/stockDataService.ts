import { supabase } from '@/integrations/supabase/client';

// We'll fetch company data dynamically instead of importing the large JSON file
// This avoids bundle size issues and import path problems

export interface StockInfo {
  ticker: string;
  name: string;
  cik?: number;
}

export interface StockSearchResult {
  ticker: string;
  name: string;
  sector?: string;
  marketCap?: number;
  price?: number;
  change?: number;
  changePercent?: number;
}

export interface StockDetailData {
  ticker: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap?: number;
  peRatio?: number;
  week52High?: number;
  week52Low?: number;
  avgVolume?: number;
  sector?: string;
  description?: string;
  employees?: number;
  headquarters?: string;
  website?: string;
  ceo?: string;
  founded?: string;
}

export interface MultiTimeframeData {
  '1D': { change: number; changePercent: number };
  '5D': { change: number; changePercent: number };
  '1M': { change: number; changePercent: number };
  '3M': { change: number; changePercent: number };
  '6M': { change: number; changePercent: number };
  '1Y': { change: number; changePercent: number };
  '5Y': { change: number; changePercent: number };
}

// Curated list of popular stocks with company names
const STOCK_COMPANIES: Record<string, string> = {
  'AAPL': 'Apple Inc.',
  'MSFT': 'Microsoft Corporation',
  'GOOGL': 'Alphabet Inc.',
  'GOOG': 'Alphabet Inc.',
  'AMZN': 'Amazon.com Inc.',
  'META': 'Meta Platforms Inc.',
  'TSLA': 'Tesla Inc.',
  'NVDA': 'NVIDIA Corporation',
  'NFLX': 'Netflix Inc.',
  'UBER': 'Uber Technologies Inc.',
  'SPOT': 'Spotify Technology S.A.',
  'PLTR': 'Palantir Technologies Inc.',
  'COIN': 'Coinbase Global Inc.',
  'RBLX': 'Roblox Corporation',
  'HOOD': 'Robinhood Markets Inc.',
  'SOFI': 'SoFi Technologies Inc.',
  'RIVN': 'Rivian Automotive Inc.',
  'JPM': 'JPMorgan Chase & Co.',
  'BAC': 'Bank of America Corporation',
  'WFC': 'Wells Fargo & Company',
  'GS': 'The Goldman Sachs Group Inc.',
  'MS': 'Morgan Stanley',
  'V': 'Visa Inc.',
  'MA': 'Mastercard Incorporated',
  'PYPL': 'PayPal Holdings Inc.',
  'SQ': 'Block Inc.',
  'ADBE': 'Adobe Inc.',
  'CRM': 'Salesforce Inc.',
  'ORCL': 'Oracle Corporation',
  'IBM': 'International Business Machines Corporation',
  'CSCO': 'Cisco Systems Inc.',
  'INTC': 'Intel Corporation',
  'AMD': 'Advanced Micro Devices Inc.',
  'QCOM': 'QUALCOMM Incorporated',
  'AVGO': 'Broadcom Inc.',
  'TXN': 'Texas Instruments Incorporated',
  'MU': 'Micron Technology Inc.',
  'LRCX': 'Lam Research Corporation',
  'KLAC': 'KLA Corporation',
  'AMAT': 'Applied Materials Inc.',
  'JNJ': 'Johnson & Johnson',
  'PFE': 'Pfizer Inc.',
  'ABBV': 'AbbVie Inc.',
  'MRK': 'Merck & Co. Inc.',
  'LLY': 'Eli Lilly and Company',
  'UNH': 'UnitedHealth Group Incorporated',
  'CVS': 'CVS Health Corporation',
  'WMT': 'Walmart Inc.',
  'COST': 'Costco Wholesale Corporation',
  'HD': 'The Home Depot Inc.',
  'LOW': 'Lowe\'s Companies Inc.',
  'TGT': 'Target Corporation',
  'AMGN': 'Amgen Inc.',
  'GILD': 'Gilead Sciences Inc.',
  'VRTX': 'Vertex Pharmaceuticals Incorporated',
  'BIIB': 'Biogen Inc.',
  'REGN': 'Regeneron Pharmaceuticals Inc.',
  'KO': 'The Coca-Cola Company',
  'PEP': 'PepsiCo Inc.',
  'MCD': 'McDonald\'s Corporation',
  'SBUX': 'Starbucks Corporation',
  'NKE': 'NIKE Inc.',
  'DIS': 'The Walt Disney Company',
  'T': 'AT&T Inc.',
  'VZ': 'Verizon Communications Inc.',
  'CMCSA': 'Comcast Corporation',
  'XOM': 'Exxon Mobil Corporation',
  'CVX': 'Chevron Corporation',
  'COP': 'ConocoPhillips',
  'SLB': 'Schlumberger Limited',
  'EOG': 'EOG Resources Inc.',
  'BA': 'The Boeing Company',
  'CAT': 'Caterpillar Inc.',
  'DE': 'Deere & Company',
  'GE': 'General Electric Company',
  'MMM': '3M Company',
  'HON': 'Honeywell International Inc.',
  'UNP': 'Union Pacific Corporation',
  'UPS': 'United Parcel Service Inc.',
  'FDX': 'FedEx Corporation',
  'LMT': 'Lockheed Martin Corporation',
  'RTX': 'Raytheon Technologies Corporation',
  'NOC': 'Northrop Grumman Corporation',
  'GD': 'General Dynamics Corporation'
};

// Create a map of ticker to company info for fast lookup
const createCompanyMap = (): Map<string, StockInfo> => {
  const companyMap = new Map<string, StockInfo>();

  // Add our curated stock list
  for (const [ticker, name] of Object.entries(STOCK_COMPANIES)) {
    companyMap.set(ticker, {
      ticker,
      name
    });
  }

  return companyMap;
};

// Initialize the company map
const COMPANY_MAP = createCompanyMap();

/**
 * Get company information for a ticker
 */
export const getCompanyInfo = (ticker: string): StockInfo | null => {
  const upperTicker = ticker.toUpperCase();
  return COMPANY_MAP.get(upperTicker) || null;
};

/**
 * Search for stocks by ticker or company name
 */
export const searchStocks = (query: string, limit: number = 10): StockSearchResult[] => {
  if (!query || query.length < 1) return [];
  
  const upperQuery = query.toUpperCase();
  const results: StockSearchResult[] = [];
  
  // First, search by ticker (exact matches and starts with)
  for (const [ticker, info] of COMPANY_MAP) {
    if (ticker === upperQuery) {
      // Exact ticker match - highest priority
      results.unshift({
        ticker: info.ticker,
        name: info.name
      });
    } else if (ticker.startsWith(upperQuery)) {
      // Ticker starts with query
      results.push({
        ticker: info.ticker,
        name: info.name
      });
    }
  }
  
  // Then search by company name
  for (const [ticker, info] of COMPANY_MAP) {
    const upperName = info.name.toUpperCase();
    if (upperName.includes(upperQuery) && !results.some(r => r.ticker === ticker)) {
      results.push({
        ticker: info.ticker,
        name: info.name
      });
    }
  }
  
  return results.slice(0, limit);
};

/**
 * Get all available stocks for autocomplete
 */
export const getAllStocks = (): StockInfo[] => {
  return Array.from(COMPANY_MAP.values()).sort((a, b) => a.ticker.localeCompare(b.ticker));
};

/**
 * Get popular/trending stocks
 */
export const getPopularStocks = (): StockInfo[] => {
  const popularTickers = [
    'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META', 'TSLA', 'NVDA', 'NFLX', 
    'UBER', 'SPOT', 'PLTR', 'COIN', 'RBLX', 'HOOD', 'SOFI', 'RIVN'
  ];
  
  return popularTickers
    .map(ticker => getCompanyInfo(ticker))
    .filter((info): info is StockInfo => info !== null);
};

/**
 * Fetch detailed stock data from Polygon API via Supabase edge function
 */
export const fetchStockDetail = async (ticker: string): Promise<StockDetailData | null> => {
  try {
    const { data, error } = await supabase.functions.invoke('stock-detail', {
      body: JSON.stringify({ symbol: ticker })
    });

    if (error) {
      console.error('Error fetching stock detail:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error in fetchStockDetail:', error);
    return null;
  }
};

/**
 * Fetch multi-timeframe performance data
 */
export const fetchMultiTimeframeData = async (ticker: string): Promise<MultiTimeframeData | null> => {
  try {
    const { data, error } = await supabase.functions.invoke('chart-processor', {
      body: JSON.stringify({
        action: 'multi-timeframe',
        symbol: ticker,
        timeframes: ['1D', '5D', '1M', '3M', '6M', '1Y', '5Y']
      })
    });

    if (error) {
      console.error('Error fetching multi-timeframe data:', error);
      return null;
    }

    // Transform the API response to match our expected format
    if (data && typeof data === 'object') {
      const transformedData: MultiTimeframeData = {} as MultiTimeframeData;

      for (const [timeframe, priceData] of Object.entries(data)) {
        if (priceData && typeof priceData === 'object' && 'timeframePercentChange' in priceData) {
          const pd = priceData as any;
          transformedData[timeframe as keyof MultiTimeframeData] = {
            change: pd.change || 0,
            changePercent: pd.timeframePercentChange || pd.percentChange || 0
          };
        }
      }

      return transformedData;
    }

    return null;
  } catch (error) {
    console.error('Error in fetchMultiTimeframeData:', error);
    return null;
  }
};

/**
 * Get recent searches from localStorage
 */
export const getRecentSearches = (): StockInfo[] => {
  try {
    const recent = localStorage.getItem('recentStockSearches');
    if (recent) {
      const tickers = JSON.parse(recent) as string[];
      return tickers
        .map(ticker => getCompanyInfo(ticker))
        .filter((info): info is StockInfo => info !== null)
        .slice(0, 5);
    }
  } catch (error) {
    console.error('Error getting recent searches:', error);
  }
  return [];
};

/**
 * Add a stock to recent searches
 */
export const addToRecentSearches = (ticker: string): void => {
  try {
    const recent = getRecentSearches().map(s => s.ticker);
    const updated = [ticker, ...recent.filter(t => t !== ticker)].slice(0, 5);
    localStorage.setItem('recentStockSearches', JSON.stringify(updated));
  } catch (error) {
    console.error('Error adding to recent searches:', error);
  }
};
