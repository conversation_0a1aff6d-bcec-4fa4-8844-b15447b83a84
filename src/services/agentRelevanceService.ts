import { Agent } from '@/services/agentService';

export interface AgentRelevanceScore {
  agentId: string;
  agentName: string;
  relevanceScore: number;
  matchedKeywords: string[];
  matchedCapabilities: string[];
  reasoning: string;
}

// Keywords that indicate different types of trading analysis
const TRADING_KEYWORDS = {
  technical: [
    'rsi', 'macd', 'moving average', 'bollinger bands', 'stochastic', 'momentum',
    'support', 'resistance', 'trend', 'breakout', 'oversold', 'overbought',
    'technical analysis', 'chart', 'pattern', 'indicator', 'signal'
  ],
  candlestick: [
    'candle', 'candlestick', 'doji', 'hammer', 'engulfing', 'shooting star',
    'hanging man', 'marubozu', 'spinning top', 'harami', 'piercing', 'dark cloud'
  ],
  fundamental: [
    'earnings', 'revenue', 'profit', 'pe ratio', 'debt', 'cash flow',
    'balance sheet', 'income statement', 'fundamental analysis', 'valuation'
  ],
  sentiment: [
    'news', 'sentiment', 'social', 'buzz', 'opinion', 'analyst', 'rating',
    'upgrade', 'downgrade', 'recommendation'
  ],
  risk: [
    'risk', 'volatility', 'drawdown', 'stop loss', 'position size',
    'portfolio', 'diversification', 'correlation'
  ],
  market: [
    'market', 'sector', 'industry', 'economy', 'fed', 'interest rate',
    'inflation', 'gdp', 'unemployment'
  ]
};

// Block type capabilities mapping
const BLOCK_CAPABILITIES = {
  'INDICATOR': ['technical analysis', 'momentum', 'trend analysis'],
  'CONDITION': ['rule-based analysis', 'threshold monitoring'],
  'TRIGGER': ['signal generation', 'trade alerts'],
  'CANDLE_PATTERN': ['candlestick analysis', 'pattern recognition'],
  'SUPPORT_RESISTANCE': ['support and resistance', 'key levels'],
  'AND': ['complex logic', 'multi-condition analysis'],
  'OR': ['alternative scenarios', 'flexible conditions'],
  'WHEN_RUN': ['execution control', 'timing']
};

/**
 * Analyze user message and determine which agents are most relevant
 */
export function analyzeAgentRelevance(
  userMessage: string,
  availableAgents: Agent[],
  symbols: string[] = []
): AgentRelevanceScore[] {
  const messageLower = userMessage.toLowerCase();
  const agentScores: AgentRelevanceScore[] = [];

  for (const agent of availableAgents) {
    const score = calculateAgentRelevance(messageLower, agent, symbols);
    if (score.relevanceScore > 0) {
      agentScores.push(score);
    }
  }

  // Sort by relevance score (highest first)
  return agentScores.sort((a, b) => b.relevanceScore - a.relevanceScore);
}

/**
 * Calculate relevance score for a specific agent
 */
function calculateAgentRelevance(
  messageLower: string,
  agent: Agent,
  symbols: string[]
): AgentRelevanceScore {
  let score = 0;
  const matchedKeywords: string[] = [];
  const matchedCapabilities: string[] = [];
  const reasoningParts: string[] = [];

  // 1. Check agent name and description for keyword matches
  const agentText = `${agent.name} ${agent.description || ''}`.toLowerCase();
  
  // Check for direct mentions of agent name
  if (messageLower.includes(agent.name.toLowerCase())) {
    score += 50;
    matchedKeywords.push(agent.name);
    reasoningParts.push(`Agent name mentioned directly`);
  }

  // 2. Analyze agent capabilities based on block types
  const agentCapabilities = extractAgentCapabilities(agent);
  
  for (const capability of agentCapabilities) {
    if (messageLower.includes(capability.toLowerCase()) || 
        agentText.includes(capability.toLowerCase())) {
      score += 20;
      matchedCapabilities.push(capability);
      reasoningParts.push(`Matches ${capability} capability`);
    }
  }

  // 3. Check for trading keyword matches
  for (const [category, keywords] of Object.entries(TRADING_KEYWORDS)) {
    for (const keyword of keywords) {
      if (messageLower.includes(keyword)) {
        // Check if agent has relevant capabilities for this keyword category
        const relevantCapabilities = getRelevantCapabilitiesForCategory(category, agentCapabilities);
        if (relevantCapabilities.length > 0) {
          score += 15;
          matchedKeywords.push(keyword);
          reasoningParts.push(`Keyword "${keyword}" matches agent's ${category} capabilities`);
        }
      }
    }
  }

  // 4. Check for indicator-specific matches
  if (hasIndicatorBlocks(agent)) {
    const indicatorKeywords = ['indicator', 'signal', 'analysis', 'technical'];
    for (const keyword of indicatorKeywords) {
      if (messageLower.includes(keyword)) {
        score += 10;
        matchedKeywords.push(keyword);
        reasoningParts.push(`Technical indicator agent matches "${keyword}"`);
      }
    }
  }

  // 5. Check for pattern recognition matches
  if (hasCandlePatternBlocks(agent)) {
    const patternKeywords = ['pattern', 'candle', 'chart', 'formation'];
    for (const keyword of patternKeywords) {
      if (messageLower.includes(keyword)) {
        score += 15;
        matchedKeywords.push(keyword);
        reasoningParts.push(`Pattern recognition agent matches "${keyword}"`);
      }
    }
  }

  // 6. Symbol-specific relevance
  if (symbols.length > 0) {
    // If message mentions specific symbols, all agents get base relevance
    score += 5;
    reasoningParts.push(`Applicable to symbol analysis`);
  }

  // 7. General trading question boost
  const generalTradingTerms = ['trade', 'buy', 'sell', 'invest', 'stock', 'market'];
  const hasGeneralTerms = generalTradingTerms.some(term => messageLower.includes(term));
  if (hasGeneralTerms && score === 0) {
    score += 5; // Minimal relevance for general trading questions
    reasoningParts.push(`General trading relevance`);
  }

  return {
    agentId: agent.id!,
    agentName: agent.name,
    relevanceScore: Math.min(score, 100), // Cap at 100
    matchedKeywords: [...new Set(matchedKeywords)], // Remove duplicates
    matchedCapabilities: [...new Set(matchedCapabilities)],
    reasoning: reasoningParts.join('; ') || 'Low relevance match'
  };
}

/**
 * Extract capabilities from agent configuration
 */
function extractAgentCapabilities(agent: Agent): string[] {
  const capabilities = new Set<string>();
  
  for (const block of agent.configuration.blocks) {
    const blockCapabilities = BLOCK_CAPABILITIES[block.type as keyof typeof BLOCK_CAPABILITIES];
    if (blockCapabilities) {
      blockCapabilities.forEach(cap => capabilities.add(cap));
    }

    // Add specific indicator names
    if (block.type === 'INDICATOR' && block.indicator) {
      capabilities.add(block.indicator.toLowerCase());
    }

    // Add specific pattern types
    if (block.type === 'CANDLE_PATTERN' && block.pattern) {
      capabilities.add(`${block.pattern} pattern`);
    }
  }

  return Array.from(capabilities);
}

/**
 * Get relevant capabilities for a keyword category
 */
function getRelevantCapabilitiesForCategory(
  category: string,
  agentCapabilities: string[]
): string[] {
  const categoryMappings: Record<string, string[]> = {
    technical: ['technical analysis', 'momentum', 'trend analysis'],
    candlestick: ['candlestick analysis', 'pattern recognition'],
    fundamental: ['fundamental analysis'],
    sentiment: ['sentiment analysis'],
    risk: ['risk management'],
    market: ['market analysis']
  };

  const relevantCaps = categoryMappings[category] || [];
  return agentCapabilities.filter(cap => 
    relevantCaps.some(relevant => cap.includes(relevant))
  );
}

/**
 * Check if agent has indicator blocks
 */
function hasIndicatorBlocks(agent: Agent): boolean {
  return agent.configuration.blocks.some(block => block.type === 'INDICATOR');
}

/**
 * Check if agent has candle pattern blocks
 */
function hasCandlePatternBlocks(agent: Agent): boolean {
  return agent.configuration.blocks.some(block => block.type === 'CANDLE_PATTERN');
}

/**
 * Filter agents by minimum relevance threshold
 */
export function filterRelevantAgents(
  agentScores: AgentRelevanceScore[],
  minScore: number = 10,
  maxAgents: number = 5
): AgentRelevanceScore[] {
  return agentScores
    .filter(score => score.relevanceScore >= minScore)
    .slice(0, maxAgents);
}

/**
 * Get default agents when no specific relevance is found
 */
export function getDefaultRelevantAgents(
  availableAgents: Agent[],
  maxAgents: number = 3
): AgentRelevanceScore[] {
  // Prefer agents with diverse capabilities
  const diverseAgents = availableAgents
    .slice(0, maxAgents)
    .map(agent => ({
      agentId: agent.id!,
      agentName: agent.name,
      relevanceScore: 5,
      matchedKeywords: [],
      matchedCapabilities: [],
      reasoning: 'Default selection for general analysis'
    }));

  return diverseAgents;
}
