import { supabase } from '@/integrations/supabase/client';

export interface AgentReview {
  id: string;
  agent_id: string;
  user_id: string;
  user_email?: string;
  user_name?: string;
  rating: number;
  title?: string;
  review_text?: string;
  is_verified_purchase: boolean;
  backtest_performance?: any;
  helpful_count: number;
  created_at: string;
  updated_at: string;
  user_helpfulness_vote?: boolean | null; // Current user's helpfulness vote
}

export interface ReviewSummary {
  average_rating: number;
  total_reviews: number;
  rating_distribution: {
    [key: string]: number;
  };
}

export interface PublicBacktest {
  id: string;
  agent_id: string;
  user_id: string;
  user_email?: string;
  user_name?: string;
  backtest_config: any;
  results: any;
  is_public: boolean;
  created_at: string;
}

/**
 * Get reviews for an agent
 */
export async function getAgentReviews(agentId: string, limit = 20, offset = 0): Promise<{
  reviews: AgentReview[];
  total: number;
}> {
  try {
    const { data: { user } } = await supabase.auth.getUser();

    // Get reviews from the new marketplace_reviews table
    const { data: reviews, error, count } = await supabase
      .from('marketplace_reviews')
      .select('*', { count: 'exact' })
      .eq('agent_id', agentId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;

    // Get current user's helpfulness votes for these reviews
    let reviewsWithVotes = reviews || [];
    if (user && reviews && reviews.length > 0) {
      const reviewIds = reviews.map(r => r.id);
      const { data: votes } = await supabase
        .from('marketplace_review_helpfulness')
        .select('review_id, is_helpful')
        .eq('user_id', user.id)
        .in('review_id', reviewIds);

      const voteMap = new Map(votes?.map(v => [v.review_id, v.is_helpful]) || []);

      reviewsWithVotes = reviews.map(review => ({
        ...review,
        user_helpfulness_vote: voteMap.get(review.id) || null
      }));
    }

    return {
      reviews: reviewsWithVotes,
      total: count || 0
    };
  } catch (error) {
    console.error('Error getting agent reviews:', error);
    throw error;
  }
}

/**
 * Get review summary for an agent
 */
export async function getAgentReviewSummary(agentId: string): Promise<ReviewSummary> {
  try {
    const { data, error } = await supabase
      .from('agents')
      .select('marketplace_rating, marketplace_review_count, marketplace_rating_dist')
      .eq('id', agentId)
      .single();

    if (error) throw error;

    return {
      average_rating: data.marketplace_rating || 0,
      total_reviews: data.marketplace_review_count || 0,
      rating_distribution: data.marketplace_rating_dist || { "1": 0, "2": 0, "3": 0, "4": 0, "5": 0 }
    };
  } catch (error) {
    console.error('Error getting agent review summary:', error);
    throw error;
  }
}

/**
 * Create a new review
 */
export async function createAgentReview(review: {
  agent_id: string;
  rating: number;
  title?: string;
  review_text?: string;
  backtest_performance?: any;
}): Promise<AgentReview> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('User not authenticated');

    // Get user display info
    const userEmail = user.email || '';
    const userName = user.user_metadata?.full_name || user.email?.split('@')[0] || 'Anonymous';

    // Check if user has already reviewed this agent
    const { data: existingReview } = await supabase
      .from('marketplace_reviews')
      .select('id')
      .eq('agent_id', review.agent_id)
      .eq('user_id', user.id)
      .single();

    if (existingReview) {
      throw new Error('You have already reviewed this agent');
    }

    // Check if user has purchased this agent (for verified purchase flag)
    const { data: purchase } = await supabase
      .from('agent_licenses')
      .select('id')
      .eq('agent_id', review.agent_id)
      .eq('buyer_id', user.id)
      .eq('is_active', true)
      .single();

    const { data, error } = await supabase
      .from('marketplace_reviews')
      .insert({
        ...review,
        user_id: user.id,
        user_email: userEmail,
        user_name: userName,
        is_verified_purchase: !!purchase
      })
      .select()
      .single();

    if (error) throw error;

    return data;
  } catch (error) {
    console.error('Error creating agent review:', error);
    throw error;
  }
}

/**
 * Update a review
 */
export async function updateAgentReview(reviewId: string, updates: {
  rating?: number;
  title?: string;
  review_text?: string;
  backtest_performance?: any;
}): Promise<AgentReview> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from('marketplace_reviews')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', reviewId)
      .eq('user_id', user.id)
      .select()
      .single();

    if (error) throw error;

    return data;
  } catch (error) {
    console.error('Error updating agent review:', error);
    throw error;
  }
}

/**
 * Delete a review
 */
export async function deleteAgentReview(reviewId: string): Promise<void> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('User not authenticated');

    const { error } = await supabase
      .from('marketplace_reviews')
      .delete()
      .eq('id', reviewId)
      .eq('user_id', user.id);

    if (error) throw error;
  } catch (error) {
    console.error('Error deleting agent review:', error);
    throw error;
  }
}

/**
 * Vote on review helpfulness
 */
export async function voteOnReviewHelpfulness(reviewId: string, isHelpful: boolean): Promise<void> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('User not authenticated');

    const userEmail = user.email || '';

    // Upsert the vote
    const { error } = await supabase
      .from('marketplace_review_helpfulness')
      .upsert({
        review_id: reviewId,
        user_id: user.id,
        user_email: userEmail,
        is_helpful: isHelpful
      });

    if (error) throw error;
  } catch (error) {
    console.error('Error voting on review helpfulness:', error);
    throw error;
  }
}

/**
 * Get public backtests for an agent
 */
export async function getAgentPublicBacktests(agentId: string, limit = 10): Promise<PublicBacktest[]> {
  try {
    const { data, error } = await supabase
      .from('marketplace_backtests')
      .select('*')
      .eq('agent_id', agentId)
      .eq('is_public', true)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;

    return data || [];
  } catch (error) {
    console.error('Error getting agent public backtests:', error);
    throw error;
  }
}

/**
 * Create a public backtest
 */
export async function createPublicBacktest(backtest: {
  agent_id: string;
  backtest_config: any;
  results: any;
  is_public?: boolean;
}): Promise<PublicBacktest> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('User not authenticated');

    const userEmail = user.email || '';
    const userName = user.user_metadata?.full_name || user.email?.split('@')[0] || 'Anonymous';

    const { data, error } = await supabase
      .from('marketplace_backtests')
      .insert({
        ...backtest,
        user_id: user.id,
        user_email: userEmail,
        user_name: userName,
        is_public: backtest.is_public !== false // Default to true
      })
      .select()
      .single();

    if (error) throw error;

    return data;
  } catch (error) {
    console.error('Error creating public backtest:', error);
    throw error;
  }
}
