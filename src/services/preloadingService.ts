/**
 * Preloading Service - Handles resource preloading and caching for optimal performance
 */

interface PreloadOptions {
  priority?: 'high' | 'low';
  crossOrigin?: 'anonymous' | 'use-credentials';
  timeout?: number;
}

interface CacheEntry {
  data: any;
  timestamp: number;
  ttl: number;
}

class PreloadingService {
  private cache = new Map<string, CacheEntry>();
  private preloadedResources = new Set<string>();
  private loadingPromises = new Map<string, Promise<any>>();

  /**
   * Preload critical CSS and fonts
   */
  preloadCriticalResources(): void {
    // Preload fonts
    this.preloadFont('/fonts/sf-pro-display.woff2', 'font/woff2');
    this.preloadFont('/fonts/sf-pro-text.woff2', 'font/woff2');
    
    // Preload critical images
    this.preloadImage('/images/logo.svg');
    this.preloadImage('/images/placeholder.svg');
    
    // Preload critical API endpoints
    this.preloadApiData();
  }

  /**
   * Preload font resources
   */
  private preloadFont(href: string, type: string): void {
    if (this.preloadedResources.has(href)) return;

    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = href;
    link.as = 'font';
    link.type = type;
    link.crossOrigin = 'anonymous';
    
    document.head.appendChild(link);
    this.preloadedResources.add(href);
  }

  /**
   * Preload image resources
   */
  private preloadImage(src: string, options: PreloadOptions = {}): Promise<void> {
    if (this.preloadedResources.has(src)) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        this.preloadedResources.add(src);
        resolve();
      };
      img.onerror = reject;
      img.src = src;

      // Timeout handling
      if (options.timeout) {
        setTimeout(() => reject(new Error('Image preload timeout')), options.timeout);
      }
    });
  }

  /**
   * Preload JavaScript modules
   */
  preloadModule(moduleId: string): void {
    if (this.preloadedResources.has(moduleId)) return;

    const link = document.createElement('link');
    link.rel = 'modulepreload';
    link.href = moduleId;
    
    document.head.appendChild(link);
    this.preloadedResources.add(moduleId);
  }

  /**
   * Preload critical API data
   */
  private async preloadApiData(): Promise<void> {
    try {
      // Preload user session data
      this.preloadUserData();
      
      // Preload market data for popular symbols
      this.preloadMarketData(['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'NVDA']);
      
      // Preload watchlist data
      this.preloadWatchlistData();
    } catch (error) {
      console.warn('API preloading failed:', error);
    }
  }

  /**
   * Preload user data with caching
   */
  async preloadUserData(): Promise<any> {
    const cacheKey = 'user-data';
    
    // Check cache first
    const cached = this.getFromCache(cacheKey);
    if (cached) return cached;

    // Check if already loading
    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey);
    }

    // Start loading
    const loadingPromise = this.fetchUserData();
    this.loadingPromises.set(cacheKey, loadingPromise);

    try {
      const data = await loadingPromise;
      this.setCache(cacheKey, data, 5 * 60 * 1000); // 5 minutes TTL
      return data;
    } finally {
      this.loadingPromises.delete(cacheKey);
    }
  }

  /**
   * Preload market data for popular symbols
   */
  async preloadMarketData(symbols: string[]): Promise<void> {
    const promises = symbols.map(symbol => this.preloadSymbolData(symbol));
    await Promise.allSettled(promises);
  }

  /**
   * Preload data for a specific symbol
   */
  async preloadSymbolData(symbol: string): Promise<any> {
    const cacheKey = `market-data-${symbol}`;
    
    // Check cache first
    const cached = this.getFromCache(cacheKey);
    if (cached) return cached;

    // Check if already loading
    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey);
    }

    // Start loading
    const loadingPromise = this.fetchMarketData(symbol);
    this.loadingPromises.set(cacheKey, loadingPromise);

    try {
      const data = await loadingPromise;
      this.setCache(cacheKey, data, 2 * 60 * 1000); // 2 minutes TTL for market data
      return data;
    } catch (error) {
      console.warn(`Failed to preload market data for ${symbol}:`, error);
      return null;
    } finally {
      this.loadingPromises.delete(cacheKey);
    }
  }

  /**
   * Preload watchlist data
   */
  async preloadWatchlistData(): Promise<any> {
    const cacheKey = 'watchlist-data';
    
    // Check cache first
    const cached = this.getFromCache(cacheKey);
    if (cached) return cached;

    try {
      const data = await this.fetchWatchlistData();
      this.setCache(cacheKey, data, 3 * 60 * 1000); // 3 minutes TTL
      return data;
    } catch (error) {
      console.warn('Failed to preload watchlist data:', error);
      return null;
    }
  }

  /**
   * Cache management
   */
  private setCache(key: string, data: any, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  private getFromCache(key: string): any | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    const now = Date.now();
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  /**
   * Clear expired cache entries
   */
  clearExpiredCache(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Prefetch next page data based on current route
   */
  prefetchRouteData(currentRoute: string): void {
    const routePrefetchMap: Record<string, string[]> = {
      '/': ['/portfolio-manager', '/trades'],
      '/portfolio-manager': ['/trades', '/stock-search'],
      '/trades': ['/portfolio-manager', '/agent-management'],
      '/agent-management': ['/agent-builder', '/discover'],
      '/stock-search': ['/portfolio-manager', '/trades'],
    };

    const routesToPrefetch = routePrefetchMap[currentRoute];
    if (routesToPrefetch) {
      routesToPrefetch.forEach(route => {
        this.prefetchRouteAssets(route);
      });
    }
  }

  /**
   * Prefetch assets for a specific route
   */
  private prefetchRouteAssets(route: string): void {
    // This would be implemented based on your routing structure
    // For now, we'll just log the intent
    console.log(`Prefetching assets for route: ${route}`);
  }

  /**
   * Placeholder methods for actual API calls
   */
  private async fetchUserData(): Promise<any> {
    // This would be replaced with actual Supabase call
    return new Promise(resolve => setTimeout(() => resolve({}), 100));
  }

  private async fetchMarketData(symbol: string): Promise<any> {
    // This would be replaced with actual market data API call
    return new Promise(resolve => setTimeout(() => resolve({ symbol, price: 100 }), 100));
  }

  private async fetchWatchlistData(): Promise<any> {
    // This would be replaced with actual watchlist API call
    return new Promise(resolve => setTimeout(() => resolve([]), 100));
  }

  /**
   * Initialize service worker for caching
   */
  initServiceWorker(): void {
    // Temporarily disabled due to fetch errors
    // TODO: Re-enable when service worker is properly configured for all environments
    console.log('Service Worker registration disabled to prevent fetch errors');

    // if ('serviceWorker' in navigator) {
    //   navigator.serviceWorker.register('/sw.js')
    //     .then(registration => {
    //       console.log('Service Worker registered:', registration);
    //     })
    //     .catch(error => {
    //       console.log('Service Worker registration failed:', error);
    //     });
    // }
  }
}

// Export singleton instance
export const preloadingService = new PreloadingService();

// Auto-initialize critical resources
if (typeof window !== 'undefined') {
  // Start preloading immediately
  preloadingService.preloadCriticalResources();
  
  // Initialize service worker
  preloadingService.initServiceWorker();
  
  // Clean up cache periodically
  setInterval(() => {
    preloadingService.clearExpiredCache();
  }, 5 * 60 * 1000); // Every 5 minutes
}
