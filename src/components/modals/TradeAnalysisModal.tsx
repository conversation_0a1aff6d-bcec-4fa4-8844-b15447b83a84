import React, { useState, useEffect } from 'react';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  TrendingUp,
  TrendingDown,
  Clock,
  Target,
  BarChart3,
  Calendar,
  DollarSign
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import CleanCandlestickChart from '@/components/charts/CleanCandlestickChart';
import { fetchTradeChartData, adjustTradeToMarketOpen, TradeTimeframe } from '@/services/cleanChartService';
import { format } from 'date-fns';

interface BacktestTrade {
  date: string;
  type: 'buy' | 'sell';
  price: number;
  signal: string;
  confidence: number;
}

interface TradeAnalysisModalProps {
  symbol: string;
  trades: BacktestTrade[];
  startDate: string;
  endDate: string;
  isOpen: boolean;
  onClose: () => void;
  selectedTradeIndex?: number;
}

interface TradePair {
  entry: BacktestTrade;
  exit?: BacktestTrade;
  profitLoss?: number;
  profitLossPercent?: number;
  duration?: string;
}

const TradeAnalysisModal: React.FC<TradeAnalysisModalProps> = ({
  symbol,
  trades,
  startDate,
  endDate,
  isOpen,
  onClose,
  selectedTradeIndex
}) => {
  const [error, setError] = useState<string | null>(null);
  const [chartData, setChartData] = useState<any[]>([]);
  const [isLoadingChart, setIsLoadingChart] = useState(false);
  const [currentTradeIndex, setCurrentTradeIndex] = useState<number | undefined>(selectedTradeIndex);
  const [selectedTimeframe, setSelectedTimeframe] = useState<TradeTimeframe>('15min');

  // Candle interval options for the selector
  const timeframeOptions: { value: TradeTimeframe; label: string; description: string }[] = [
    { value: '1min', label: '1m', description: '1-minute candles' },
    { value: '5min', label: '5m', description: '5-minute candles' },
    { value: '15min', label: '15m', description: '15-minute candles' },
    { value: '30min', label: '30m', description: '30-minute candles' },
    { value: '1hour', label: '1h', description: '1-hour candles' },
    { value: '4hour', label: '4h', description: '4-hour candles' },
    { value: 'daily', label: '1D', description: 'Daily candles' }
  ];

  // Update current trade index when selectedTradeIndex changes
  useEffect(() => {
    setCurrentTradeIndex(selectedTradeIndex);
  }, [selectedTradeIndex]);

  // Keyboard shortcuts for timeframe switching
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (!isOpen) return;

      // Only handle if no input is focused
      if (document.activeElement?.tagName === 'INPUT' || document.activeElement?.tagName === 'TEXTAREA') {
        return;
      }

      const timeframes: TradeTimeframe[] = ['1min', '5min', '15min', '30min', '1hour', '4hour', 'daily'];
      const currentIndex = timeframes.indexOf(selectedTimeframe);

      switch (e.key) {
        case '1':
          setSelectedTimeframe('1min');
          break;
        case '2':
          setSelectedTimeframe('5min');
          break;
        case '3':
          setSelectedTimeframe('15min');
          break;
        case '4':
          setSelectedTimeframe('30min');
          break;
        case '5':
          setSelectedTimeframe('1hour');
          break;
        case '6':
          setSelectedTimeframe('4hour');
          break;
        case '7':
          setSelectedTimeframe('daily');
          break;
        case 'ArrowLeft':
          if (currentIndex > 0) {
            setSelectedTimeframe(timeframes[currentIndex - 1]);
          }
          break;
        case 'ArrowRight':
          if (currentIndex < timeframes.length - 1) {
            setSelectedTimeframe(timeframes[currentIndex + 1]);
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [isOpen, selectedTimeframe]);

  // Fetch chart data when modal opens and trade is selected
  useEffect(() => {
    const fetchChartData = async () => {
      if (!isOpen || currentTradeIndex === undefined || !trades[currentTradeIndex]) {
        return;
      }

      setIsLoadingChart(true);
      try {
        const selectedTrade = trades[currentTradeIndex];

        // Robust date parsing - handle different date formats
        let tradeTimestamp: number;
        try {
          // Try parsing the date directly
          tradeTimestamp = new Date(selectedTrade.date).getTime();

          // If that fails, try parsing as ISO string or other common formats
          if (isNaN(tradeTimestamp)) {
            // Try adding time if it's just a date
            if (selectedTrade.date.length === 10) { // YYYY-MM-DD format
              tradeTimestamp = new Date(selectedTrade.date + 'T09:30:00').getTime(); // Market open time
            } else {
              throw new Error('Unable to parse date');
            }
          }
        } catch (dateError) {
          console.error(`[TradeAnalysis] Date parsing error:`, dateError);
          throw new Error(`Invalid trade date format: ${selectedTrade.date}`);
        }

        console.log(`[TradeAnalysis] Fetching chart data for trade ${currentTradeIndex}:`, {
          date: selectedTrade.date,
          type: selectedTrade.type,
          price: selectedTrade.price,
          timestamp: tradeTimestamp,
          dateObject: new Date(tradeTimestamp),
          isValidDate: !isNaN(tradeTimestamp),
          formattedDate: new Date(tradeTimestamp).toISOString()
        });

        // Final validation
        if (isNaN(tradeTimestamp)) {
          throw new Error(`Invalid trade date: ${selectedTrade.date}`);
        }

        // Always adjust trade timestamp to market open for consistent positioning
        // This ensures all trades appear at the start of the trading day
        const adjustedTimestamp = adjustTradeToMarketOpen(tradeTimestamp);

        const data = await fetchTradeChartData(
          symbol,
          adjustedTimestamp,
          selectedTimeframe
        );
        setChartData(data);
        setError(null); // Clear any previous errors
      } catch (error) {
        console.error('Error fetching chart data:', error);
        setError(`Failed to load chart data: ${error.message || 'Unknown error'}`);
        setChartData([]); // Clear chart data on error
      } finally {
        setIsLoadingChart(false);
      }
    };

    fetchChartData();
  }, [isOpen, currentTradeIndex, symbol, trades, selectedTimeframe]);

  // Handle timeline navigation
  const handleNavigateToTrade = (tradeIndex: number) => {
    setCurrentTradeIndex(tradeIndex);
  };

  // Create trade pairs from individual trades
  const tradePairs: TradePair[] = React.useMemo(() => {
    const pairs: TradePair[] = [];
    let i = 0;
    
    while (i < trades.length) {
      const currentTrade = trades[i];
      
      if (currentTrade.type === 'buy') {
        // Look for the next sell trade
        let sellTrade = undefined;
        for (let j = i + 1; j < trades.length; j++) {
          if (trades[j].type === 'sell') {
            sellTrade = trades[j];
            break;
          }
        }
        
        const pair: TradePair = {
          entry: currentTrade,
          exit: sellTrade
        };
        
        // Calculate P&L if we have both entry and exit
        if (sellTrade) {
          pair.profitLoss = sellTrade.price - currentTrade.price;
          pair.profitLossPercent = ((sellTrade.price - currentTrade.price) / currentTrade.price) * 100;
          
          // Calculate duration
          const entryTime = new Date(currentTrade.date);
          const exitTime = new Date(sellTrade.date);
          const durationMs = exitTime.getTime() - entryTime.getTime();
          const durationDays = Math.floor(durationMs / (1000 * 60 * 60 * 24));
          const durationHours = Math.floor((durationMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
          
          if (durationDays > 0) {
            pair.duration = `${durationDays}d ${durationHours}h`;
          } else {
            pair.duration = `${durationHours}h`;
          }
        }
        
        pairs.push(pair);
      }
      i++;
    }
    
    return pairs;
  }, [trades]);

  // Get selected trade and pair
  const selectedTrade = currentTradeIndex !== undefined ? trades[currentTradeIndex] : null;
  const selectedPair = selectedTrade ? tradePairs.find(pair =>
    pair.entry.date === selectedTrade.date && pair.entry.price === selectedTrade.price
  ) : null;

  // Clear error when trade changes
  useEffect(() => {
    if (selectedTrade && isOpen) {
      setError(null);
    }
  }, [selectedTrade, isOpen]);

  // Prepare trade markers for chart - show all trades in timeline
  const tradeMarkers = React.useMemo(() => {
    if (!chartData || chartData.length === 0 || !selectedTrade) return [];

    const markers = [];
    const chartStartTime = chartData[0]?.timestamp;
    const chartEndTime = chartData[chartData.length - 1]?.timestamp;

    console.log(`[TradeMarkers] Chart timeframe: ${new Date(chartStartTime).toISOString()} to ${new Date(chartEndTime).toISOString()}`);

    // ALWAYS add the current trade marker, adjusted to market open
    const rawTradeTimestamp = new Date(selectedTrade.date).getTime();
    const currentTradeTimestamp = adjustTradeToMarketOpen(rawTradeTimestamp);

    console.log(`[TradeMarkers] Raw trade timestamp: ${new Date(rawTradeTimestamp).toISOString()}`);
    console.log(`[TradeMarkers] Adjusted trade timestamp: ${new Date(currentTradeTimestamp).toISOString()}`);
    console.log(`[TradeMarkers] Chart timeframe: ${new Date(chartStartTime).toISOString()} to ${new Date(chartEndTime).toISOString()}`);

    // Validate timestamp is reasonable
    if (isNaN(currentTradeTimestamp)) {
      console.error(`[TradeMarkers] Invalid trade timestamp for date: ${selectedTrade.date}`);
      return [];
    }

    // Check if trade is within reasonable range of chart data
    const maxTimeDistance = 7 * 24 * 60 * 60 * 1000; // 7 days
    const isWithinReasonableRange =
      Math.abs(currentTradeTimestamp - chartStartTime) <= maxTimeDistance ||
      Math.abs(currentTradeTimestamp - chartEndTime) <= maxTimeDistance ||
      (currentTradeTimestamp >= chartStartTime && currentTradeTimestamp <= chartEndTime);

    if (!isWithinReasonableRange) {
      console.warn(`[TradeMarkers] Trade timestamp is far from chart data`);
      console.warn(`[TradeMarkers] Trade: ${new Date(currentTradeTimestamp).toISOString()}`);
      console.warn(`[TradeMarkers] Chart: ${new Date(chartStartTime).toISOString()} to ${new Date(chartEndTime).toISOString()}`);
    }

    // Use a timestamp that's more likely to be within the chart data range
    let markerTimestamp = currentTradeTimestamp;

    // If the trade is way outside the chart range, try to find a better timestamp
    if (!isWithinReasonableRange && chartData.length > 0) {
      // Find the closest chart data point to the trade time
      const closestDataPoint = chartData.reduce((closest, current) => {
        const currentDiff = Math.abs(current.timestamp - currentTradeTimestamp);
        const closestDiff = Math.abs(closest.timestamp - currentTradeTimestamp);
        return currentDiff < closestDiff ? current : closest;
      });

      console.log(`[TradeMarkers] Using closest chart data point timestamp instead`);
      console.log(`[TradeMarkers] Original: ${new Date(currentTradeTimestamp).toISOString()}`);
      console.log(`[TradeMarkers] Adjusted: ${new Date(closestDataPoint.timestamp).toISOString()}`);

      markerTimestamp = closestDataPoint.timestamp;
    }

    markers.push({
      timestamp: markerTimestamp,
      price: selectedTrade.price,
      type: selectedTrade.type === 'buy' ? 'entry' as const : 'exit' as const,
      label: `${selectedTrade.type.toUpperCase()} $${selectedTrade.price.toFixed(2)} (Current)`,
      color: selectedTrade.type === 'buy' ? '#10b981' : '#ef4444',
      tradeIndex: currentTradeIndex
    });

    // Add other trades that fall within the chart timeframe
    trades.forEach((trade, index) => {
      if (index === currentTradeIndex) return; // Skip current trade (already added)

      const rawTimestamp = new Date(trade.date).getTime();
      const adjustedTimestamp = adjustTradeToMarketOpen(rawTimestamp);

      // Include trades within a wider range around the chart timeframe
      const bufferTime = 2 * 60 * 60 * 1000; // 2 hours buffer
      if (adjustedTimestamp >= (chartStartTime - bufferTime) && adjustedTimestamp <= (chartEndTime + bufferTime)) {
        markers.push({
          timestamp: adjustedTimestamp,
          price: trade.price,
          type: trade.type === 'buy' ? 'entry' as const : 'exit' as const,
          label: `${trade.type.toUpperCase()} $${trade.price.toFixed(2)}`,
          color: trade.type === 'buy' ? '#10b981' : '#ef4444',
          tradeIndex: index
        });
      }
    });

    console.log(`[TradeMarkers] Generated ${markers.length} markers:`, markers.map(m => ({
      timestamp: new Date(m.timestamp).toISOString(),
      price: m.price,
      type: m.type,
      label: m.label
    })));

    return markers;
  }, [trades, chartData, currentTradeIndex, selectedTrade]);

  // Calculate summary statistics
  const summaryStats = React.useMemo(() => {
    const completedPairs = tradePairs.filter(pair => pair.exit);
    const totalTrades = completedPairs.length;
    const winningTrades = completedPairs.filter(pair => (pair.profitLoss || 0) > 0).length;
    const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;
    const totalPnL = completedPairs.reduce((sum, pair) => sum + (pair.profitLoss || 0), 0);
    
    return {
      totalTrades,
      winningTrades,
      losingTrades: totalTrades - winningTrades,
      winRate,
      totalPnL
    };
  }, [tradePairs]);

  if (!selectedTrade) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-[#0A0A0C] border-[#1A1A1C]">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold text-white">
              Trade Analysis
            </DialogTitle>
          </DialogHeader>
          <div className="flex items-center justify-center py-12">
            <p className="text-white/60">No trade selected for analysis</p>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto bg-[#0A0A0C] border-[#1A1A1C] p-4 lg:p-6">
        <DialogHeader>
          <DialogTitle className="text-lg lg:text-xl font-bold text-white flex flex-col sm:flex-row items-start sm:items-center gap-2 lg:gap-3">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-blue-400" />
              <span>{symbol} Trade Analysis</span>
            </div>
            <Badge className="bg-blue-500/20 text-blue-300 border-blue-500/30 text-xs">
              {format(new Date(selectedTrade.date), 'MMM dd, yyyy HH:mm')}
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 xl:grid-cols-4 gap-4 lg:gap-6">
          {/* Chart Section - Takes up 3 columns */}
          <div className="xl:col-span-3 order-2 xl:order-1">
            {/* Candle Interval Selector */}
            <div className="mb-4 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-white/60" />
                <span className="text-white/60 text-sm">Candle Interval:</span>
                <span className="text-white/40 text-xs hidden sm:inline">
                  (Keys: 1-7 or ←→)
                </span>
              </div>
              <div className="flex items-center gap-1 bg-[#0F0F11] rounded-lg p-1 border border-[#1A1A1C]">
                {timeframeOptions.map((option) => (
                  <button
                    key={option.value}
                    onClick={() => setSelectedTimeframe(option.value)}
                    disabled={isLoadingChart}
                    className={`px-3 py-1.5 text-xs font-medium rounded transition-all duration-200 relative ${
                      selectedTimeframe === option.value
                        ? 'bg-blue-500 text-white shadow-sm'
                        : 'text-white/60 hover:text-white hover:bg-white/5'
                    } ${isLoadingChart ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                    title={option.description}
                  >
                    {option.label}
                    {isLoadingChart && selectedTimeframe === option.value && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="h-3 w-3 border border-white/30 border-t-white rounded-full animate-spin"></div>
                      </div>
                    )}
                  </button>
                ))}
              </div>
            </div>
            {isLoadingChart ? (
              <div className="w-full bg-[#0A0A0C] rounded-lg border border-[#1A1A1C] flex items-center justify-center" style={{ height: 500 }}>
                <div className="text-center">
                  <div className="h-8 w-8 border-2 border-white/10 border-t-white/30 rounded-full animate-spin mb-3 mx-auto"></div>
                  <div className="text-white/70 text-sm">Loading {selectedTimeframe} chart data...</div>
                  <div className="text-white/50 text-xs mt-1">
                    Each candle represents {
                      selectedTimeframe === '1min' ? '1 minute' :
                      selectedTimeframe === '5min' ? '5 minutes' :
                      selectedTimeframe === '15min' ? '15 minutes' :
                      selectedTimeframe === '30min' ? '30 minutes' :
                      selectedTimeframe === '1hour' ? '1 hour' :
                      selectedTimeframe === '4hour' ? '4 hours' :
                      '1 day'
                    } of price action
                  </div>
                </div>
              </div>
            ) : error ? (
              <div className="w-full bg-[#0A0A0C] rounded-lg border border-red-500/20 flex items-center justify-center" style={{ height: 500 }}>
                <div className="text-center p-6">
                  <div className="text-red-400 text-lg mb-2">⚠️ Chart Data Error</div>
                  <div className="text-white/70 text-sm mb-4">{error}</div>
                  <div className="text-white/50 text-xs">
                    Unable to load real market data for this trade analysis.
                    <br />
                    Please check your connection and try again.
                  </div>
                </div>
              </div>
            ) : (
              <CleanCandlestickChart
                symbol={symbol}
                data={chartData}
                tradeMarkers={tradeMarkers}
                height={500}
                className="w-full"
                currentTradeIndex={currentTradeIndex}
                onNavigateToTrade={handleNavigateToTrade}
                allTrades={trades}
              />
            )}
          </div>

          {/* Trade Details Section - Takes up 1 column */}
          <div className="space-y-4 order-1 xl:order-2">
            {/* Chart Info */}
            <Card className="bg-[#0F0F11] border-[#1A1A1C]">
              <CardHeader>
                <CardTitle className="text-white text-sm flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  <span>Chart Analysis</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-xs text-white/60">
                  <div className="flex justify-between mb-1">
                    <span>Candle Interval:</span>
                    <span className="text-white">
                      {timeframeOptions.find(opt => opt.value === selectedTimeframe)?.description || selectedTimeframe}
                    </span>
                  </div>
                  <div className="flex justify-between mb-1">
                    <span>Data Points:</span>
                    <span className="text-white">{chartData.length} candles</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Chart Context:</span>
                    <span className="text-white">
                      {selectedTimeframe === '1min' ? '12 hours (~240 candles)' :
                       selectedTimeframe === '5min' ? '24 hours (~96 candles)' :
                       selectedTimeframe === '15min' ? '48 hours (~64 candles)' :
                       selectedTimeframe === '30min' ? '96 hours (~64 candles)' :
                       selectedTimeframe === '1hour' ? '216 hours (~72 candles)' :
                       selectedTimeframe === '4hour' ? '60 days (~120 candles)' :
                       '210 days (~70 candles)'} around trade
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Selected Trade Details */}
            <Card className="bg-[#0F0F11] border-[#1A1A1C]">
              <CardHeader>
                <CardTitle className="text-white text-sm">Trade Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <p className="text-xs text-white/60 mb-1">Type</p>
                    <p className={`text-sm font-medium ${
                      selectedTrade.type === 'buy' ? 'text-emerald-400' : 'text-red-400'
                    }`}>
                      {selectedTrade.type.toUpperCase()}
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-white/60 mb-1">Price</p>
                    <p className="text-white font-mono text-sm">${selectedTrade.price.toFixed(2)}</p>
                  </div>
                </div>
                
                <div>
                  <p className="text-xs text-white/60 mb-1">Signal</p>
                  <p className="text-white text-sm">{selectedTrade.signal}</p>
                </div>
                
                <div>
                  <p className="text-xs text-white/60 mb-1">Confidence</p>
                  <div className="flex items-center gap-2">
                    <div className="flex-1 bg-[#1A1A1C] rounded-full h-2">
                      <div 
                        className="bg-blue-400 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${selectedTrade.confidence}%` }}
                      />
                    </div>
                    <span className="text-white text-sm font-mono">{selectedTrade.confidence}%</span>
                  </div>
                </div>

                <div>
                  <p className="text-xs text-white/60 mb-1">Time</p>
                  <p className="text-white text-sm font-mono">
                    {format(new Date(selectedTrade.date), 'MMM dd, HH:mm')}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Trade Outcome (if completed) */}
            {selectedPair?.exit && (
              <Card className="bg-[#0F0F11] border-[#1A1A1C]">
                <CardHeader>
                  <CardTitle className="text-white text-sm flex items-center gap-2">
                    <Target className="h-4 w-4" />
                    Trade Outcome
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <p className="text-xs text-white/60 mb-1">Exit Price</p>
                      <p className="text-white font-mono text-sm">${selectedPair.exit.price.toFixed(2)}</p>
                    </div>
                    <div>
                      <p className="text-xs text-white/60 mb-1">Duration</p>
                      <p className="text-white text-sm">{selectedPair.duration || 'N/A'}</p>
                    </div>
                  </div>
                  
                  <div>
                    <p className="text-xs text-white/60 mb-1">Profit/Loss</p>
                    <div className="flex items-center gap-2">
                      <span className={`font-mono text-sm ${
                        (selectedPair.profitLoss || 0) >= 0 ? 'text-emerald-400' : 'text-red-400'
                      }`}>
                        ${(selectedPair.profitLoss || 0).toFixed(2)}
                      </span>
                      <span className={`text-xs ${
                        (selectedPair.profitLossPercent || 0) >= 0 ? 'text-emerald-400' : 'text-red-400'
                      }`}>
                        ({(selectedPair.profitLossPercent || 0) >= 0 ? '+' : ''}{(selectedPair.profitLossPercent || 0).toFixed(2)}%)
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Summary Statistics */}
            <Card className="bg-[#0F0F11] border-[#1A1A1C]">
              <CardHeader>
                <CardTitle className="text-white text-sm">Backtest Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <p className="text-xs text-white/60 mb-1">Total Trades</p>
                    <p className="text-white font-mono text-sm">{summaryStats.totalTrades}</p>
                  </div>
                  <div>
                    <p className="text-xs text-white/60 mb-1">Win Rate</p>
                    <p className="text-white font-mono text-sm">{summaryStats.winRate.toFixed(1)}%</p>
                  </div>
                </div>
                
                <div>
                  <p className="text-xs text-white/60 mb-1">Total P&L</p>
                  <p className={`font-mono text-sm ${
                    summaryStats.totalPnL >= 0 ? 'text-emerald-400' : 'text-red-400'
                  }`}>
                    ${summaryStats.totalPnL.toFixed(2)}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TradeAnalysisModal;
