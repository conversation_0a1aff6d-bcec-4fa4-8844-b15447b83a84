import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Check, Loader2, <PERSON><PERSON><PERSON>, Zap } from 'lucide-react';

interface AnimatedButtonProps {
  children: React.ReactNode;
  onClick?: () => void | Promise<void>;
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'premium';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  disabled?: boolean;
  loading?: boolean;
  showSuccess?: boolean;
  successDuration?: number;
  className?: string;
  icon?: React.ReactNode;
  glowEffect?: boolean;
  pulseEffect?: boolean;
  bounceOnClick?: boolean;
}

const AnimatedButton: React.FC<AnimatedButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  showSuccess = false,
  successDuration = 2000,
  className = '',
  icon,
  glowEffect = false,
  pulseEffect = false,
  bounceOnClick = true
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccessState, setShowSuccessState] = useState(false);
  const [isPressed, setIsPressed] = useState(false);

  const handleClick = async () => {
    if (disabled || isLoading || showSuccessState) return;

    setIsPressed(true);
    setTimeout(() => setIsPressed(false), 150);

    if (onClick) {
      setIsLoading(true);
      try {
        await onClick();
        if (showSuccess) {
          setShowSuccessState(true);
          setTimeout(() => setShowSuccessState(false), successDuration);
        }
      } catch (error) {
        console.error('Button action failed:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'primary':
        return {
          bg: 'bg-gradient-to-r from-blue-500 to-purple-600',
          hover: 'hover:from-blue-600 hover:to-purple-700',
          text: 'text-white',
          border: 'border-blue-500/50',
          glow: 'shadow-blue-500/25'
        };
      case 'secondary':
        return {
          bg: 'bg-white/[0.05]',
          hover: 'hover:bg-white/[0.1]',
          text: 'text-white/90',
          border: 'border-white/[0.15]',
          glow: 'shadow-white/10'
        };
      case 'success':
        return {
          bg: 'bg-gradient-to-r from-green-500 to-emerald-600',
          hover: 'hover:from-green-600 hover:to-emerald-700',
          text: 'text-white',
          border: 'border-green-500/50',
          glow: 'shadow-green-500/25'
        };
      case 'danger':
        return {
          bg: 'bg-gradient-to-r from-red-500 to-pink-600',
          hover: 'hover:from-red-600 hover:to-pink-700',
          text: 'text-white',
          border: 'border-red-500/50',
          glow: 'shadow-red-500/25'
        };
      case 'premium':
        return {
          bg: 'bg-white/[0.08]',
          hover: 'hover:bg-white/[0.12]',
          text: 'text-white',
          border: 'border-white/[0.12]',
          glow: 'shadow-[0_0_20px_rgba(255,255,255,0.15)]'
        };
      default:
        return {
          bg: 'bg-gradient-to-r from-blue-500 to-purple-600',
          hover: 'hover:from-blue-600 hover:to-purple-700',
          text: 'text-white',
          border: 'border-blue-500/50',
          glow: 'shadow-blue-500/25'
        };
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-1.5 text-sm';
      case 'md':
        return 'px-4 py-2 text-sm';
      case 'lg':
        return 'px-6 py-3 text-base';
      case 'xl':
        return 'px-8 py-4 text-lg';
      default:
        return 'px-4 py-2 text-sm';
    }
  };

  const styles = getVariantStyles();
  const isDisabled = disabled || isLoading || showSuccessState;

  return (
    <motion.button
      onClick={handleClick}
      disabled={isDisabled}
      className={`
        relative overflow-hidden rounded-lg border font-medium transition-all duration-200
        ${styles.bg} ${styles.hover} ${styles.text} ${styles.border}
        ${getSizeStyles()}
        ${glowEffect ? `shadow-lg ${styles.glow}` : ''}
        ${isDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        ${isPressed && variant === 'premium' ? 'border-white/[0.3]' : ''}
        ${className}
      `}
      whileHover={!isDisabled ? { scale: 1.02 } : {}}
      whileTap={bounceOnClick && !isDisabled ? { scale: 0.98 } : {}}
      animate={pulseEffect ? {
        scale: [1, 1.02, 1],
      } : {}}
      transition={pulseEffect ? {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      } : { type: "spring", stiffness: 400, damping: 17 }}
    >
      {/* Glow effect overlay */}
      {glowEffect && (
        <motion.div
          animate={{
            opacity: [0.5, 1, 0.5],
            scale: [1, 1.05, 1]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className={`absolute inset-0 ${styles.bg} blur-sm`}
        />
      )}

      {/* Shimmer effect */}
      <motion.div
        animate={{
          x: ['-100%', '100%']
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
      />

      {/* Button content */}
      <div className="relative z-10 flex items-center justify-center gap-2">
        <AnimatePresence mode="wait">
          {showSuccessState ? (
            <motion.div
              key="success"
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              exit={{ scale: 0, rotate: 180 }}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
              className="flex items-center gap-2"
            >
              <Check className="w-4 h-4" />
              <span>Success!</span>
            </motion.div>
          ) : isLoading || loading ? (
            <motion.div
              key="loading"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="flex items-center gap-2"
            >
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>Loading...</span>
            </motion.div>
          ) : (
            <motion.div
              key="default"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="flex items-center gap-2"
            >
              {icon && (
                <motion.div
                  animate={isPressed ? { rotate: 360 } : {}}
                  transition={{ duration: 0.3 }}
                >
                  {icon}
                </motion.div>
              )}
              <span>{children}</span>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Click ripple effect */}
      <AnimatePresence>
        {isPressed && (
          <motion.div
            initial={{ scale: 0, opacity: 0.5 }}
            animate={{ scale: 4, opacity: 0 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.4 }}
            className="absolute inset-0 bg-white/20 rounded-full"
          />
        )}
      </AnimatePresence>
    </motion.button>
  );
};

export default AnimatedButton;
