import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Check } from 'lucide-react';

interface SuccessAnimationProps {
  isVisible: boolean;
  onComplete?: () => void;
  type?: 'portfolio' | 'scan' | 'block' | 'trade' | 'achievement' | 'streak' | 'level' | 'backtest';
  title?: string;
  subtitle?: string;
  value?: string | number;
  duration?: number;
  isFirstTime?: boolean;
}

const SuccessAnimation: React.FC<SuccessAnimationProps> = ({
  isVisible,
  onComplete,
  type = 'achievement',
  title,
  subtitle,
  value,
  duration = 2000,
  isFirstTime = false
}) => {
  const [showConfetti, setShowConfetti] = useState(false);

  useEffect(() => {
    if (isVisible) {
      setShowConfetti(true);
      const timer = setTimeout(() => {
        setShowConfetti(false);
        onComplete?.();
      }, duration);
      return () => clearTimeout(timer);
    }
  }, [isVisible, duration, onComplete]);

  // Clean centered checkmark only
  const getIcon = () => {
    return (
      <div className="w-20 h-20 rounded-full bg-green-500 flex items-center justify-center mx-auto">
        <Check className="w-10 h-10 text-white stroke-[3]" />
      </div>
    );
  };

  const getTitle = () => {
    if (title) return title;

    // Show congratulatory first-time messages
    if (isFirstTime) {
      switch (type) {
        case 'scan': return 'Congrats, Your First Scan!';
        case 'backtest':
        case 'trade': return 'Congrats, Your First Backtest!';
        case 'portfolio': return 'Congrats, Your First Portfolio!';
        default: return 'Congrats, First Success!';
      }
    }

    // Regular messages
    switch (type) {
      case 'portfolio': return 'Portfolio Created!';
      case 'scan': return 'Analysis Complete!';
      case 'block': return 'Block Added!';
      case 'backtest':
      case 'trade': return 'Backtest Complete!';
      default: return 'Success!';
    }
  };

  const getSubtitle = () => {
    if (subtitle) return subtitle;
    switch (type) {
      case 'portfolio': return 'Your AI-optimized portfolio is ready';
      case 'scan': return 'Market analysis completed successfully';
      case 'block': return 'Your agent is getting smarter';
      case 'trade': return 'Strategy analysis complete';
      default: return 'Action completed successfully';
    }
  };

  // Green confetti particles only
  const confettiParticles = Array.from({ length: 30 }, (_, i) => ({
    id: i,
    x: Math.random() * 100,
    y: Math.random() * 100,
    rotation: Math.random() * 360,
    scale: Math.random() * 0.5 + 0.5,
    color: '#10B981' // Only green
  }));

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm"
        >
          {/* Clean Success Display with Dopamine Boost */}
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            transition={{
              type: "spring",
              stiffness: 300,
              damping: 20
            }}
            className="relative flex flex-col items-center text-center"
          >
            {/* Green Glow Background */}
            <motion.div
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.3, 0.6, 0.3]
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="absolute inset-0 bg-green-500/30 rounded-full blur-3xl"
              style={{ width: '200px', height: '200px', left: '50%', top: '50%', transform: 'translate(-50%, -50%)' }}
            />

            <div className="relative z-10">
              {/* Clean Check Mark Icon */}
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{
                  type: "spring",
                  stiffness: 400,
                  damping: 15,
                  delay: 0.1
                }}
                className="mb-6"
              >
                <motion.div
                  animate={{
                    scale: [1, 1.05, 1]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                >
                  {getIcon()}
                </motion.div>
              </motion.div>

              {/* Title */}
              <motion.h2
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.3 }}
                className="text-3xl font-bold text-white mb-2"
                style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}
              >
                {getTitle()}
              </motion.h2>

              {/* Subtitle */}
              <motion.p
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.5 }}
                className="text-white/70 text-lg mb-8"
                style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
              >
                {getSubtitle()}
              </motion.p>

              {/* Clean Dopamine Progress Bar */}
              <div className="w-80 mx-auto">
                <div className="h-3 bg-white/[0.08] rounded-full overflow-hidden">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: '100%' }}
                    transition={{
                      duration: (duration / 1000) * 1.5, // Even slower for more buildup
                      ease: [0.12, 0, 0.39, 0] // More dramatic buildup curve
                    }}
                    className="h-full bg-gradient-to-r from-green-400 to-green-500 rounded-full shadow-[0_0_25px_rgba(16,185,129,0.6)]"
                  />
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default SuccessAnimation;
