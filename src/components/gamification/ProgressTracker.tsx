import React from 'react';
import { motion } from 'framer-motion';
import { Star, Zap, Trophy, Target, TrendingUp, Award, Sparkles, Crown } from 'lucide-react';

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  progress: number;
  maxProgress: number;
  unlocked: boolean;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

interface ProgressTrackerProps {
  level: number;
  xp: number;
  xpToNext: number;
  achievements: Achievement[];
  streak: number;
  totalActions: number;
}

const ProgressTracker: React.FC<ProgressTrackerProps> = ({
  level,
  xp,
  xpToNext,
  achievements,
  streak,
  totalActions
}) => {
  const progressPercentage = (xp / xpToNext) * 100;

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'border-gray-400 bg-gray-400/10 text-gray-400';
      case 'rare': return 'border-blue-400 bg-blue-400/10 text-blue-400';
      case 'epic': return 'border-purple-400 bg-purple-400/10 text-purple-400';
      case 'legendary': return 'border-yellow-400 bg-yellow-400/10 text-yellow-400';
      default: return 'border-gray-400 bg-gray-400/10 text-gray-400';
    }
  };

  const getRarityGlow = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'shadow-gray-400/20';
      case 'rare': return 'shadow-blue-400/30';
      case 'epic': return 'shadow-purple-400/40';
      case 'legendary': return 'shadow-yellow-400/50';
      default: return 'shadow-gray-400/20';
    }
  };

  return (
    <div className="space-y-6">
      {/* Level and XP Progress */}
      <div className="bg-white/[0.02] border border-white/[0.08] rounded-xl p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <motion.div
              animate={{
                rotate: [0, 10, -10, 0],
                scale: [1, 1.1, 1]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center"
            >
              <Crown className="w-6 h-6 text-white" />
            </motion.div>
            <div>
              <h3 className="text-xl font-bold text-white">Level {level}</h3>
              <p className="text-white/60 text-sm">Trading Master</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-white/60 text-sm">XP</p>
            <p className="text-white font-bold">{xp.toLocaleString()} / {xpToNext.toLocaleString()}</p>
          </div>
        </div>

        {/* XP Progress Bar */}
        <div className="relative">
          <div className="w-full h-3 bg-white/[0.05] rounded-full overflow-hidden">
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: `${progressPercentage}%` }}
              transition={{ duration: 1, ease: "easeOut" }}
              className="h-full bg-gradient-to-r from-green-400 to-blue-500 rounded-full relative"
            >
              <motion.div
                animate={{
                  x: ['-100%', '100%']
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
              />
            </motion.div>
          </div>
          <p className="text-white/60 text-xs mt-1">
            {Math.round(progressPercentage)}% to next level
          </p>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-3 gap-4">
        <motion.div
          whileHover={{ scale: 1.05 }}
          className="bg-white/[0.02] border border-white/[0.08] rounded-xl p-4 text-center"
        >
          <motion.div
            animate={{
              scale: [1, 1.2, 1]
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            <Zap className="w-6 h-6 text-yellow-400 mx-auto mb-2" />
          </motion.div>
          <p className="text-2xl font-bold text-white">{streak}</p>
          <p className="text-white/60 text-xs">Day Streak</p>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.05 }}
          className="bg-white/[0.02] border border-white/[0.08] rounded-xl p-4 text-center"
        >
          <Target className="w-6 h-6 text-blue-400 mx-auto mb-2" />
          <p className="text-2xl font-bold text-white">{totalActions}</p>
          <p className="text-white/60 text-xs">Total Actions</p>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.05 }}
          className="bg-white/[0.02] border border-white/[0.08] rounded-xl p-4 text-center"
        >
          <Trophy className="w-6 h-6 text-green-400 mx-auto mb-2" />
          <p className="text-2xl font-bold text-white">{achievements.filter(a => a.unlocked).length}</p>
          <p className="text-white/60 text-xs">Achievements</p>
        </motion.div>
      </div>

      {/* Recent Achievements */}
      <div className="bg-white/[0.02] border border-white/[0.08] rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
          <Award className="w-5 h-5 text-purple-400" />
          Achievements
        </h3>
        
        <div className="grid grid-cols-2 gap-3">
          {achievements.slice(0, 6).map((achievement, index) => (
            <motion.div
              key={achievement.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ scale: 1.02 }}
              className={`relative p-3 rounded-lg border transition-all duration-200 ${
                achievement.unlocked 
                  ? `${getRarityColor(achievement.rarity)} ${getRarityGlow(achievement.rarity)} shadow-lg` 
                  : 'border-white/[0.05] bg-white/[0.01] text-white/40'
              }`}
            >
              {achievement.unlocked && (
                <motion.div
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.5, 1, 0.5]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className={`absolute inset-0 rounded-lg ${getRarityColor(achievement.rarity).split(' ')[1]} blur-sm`}
                />
              )}
              
              <div className="relative z-10">
                <div className="flex items-center gap-2 mb-1">
                  <div className={`w-6 h-6 ${achievement.unlocked ? '' : 'opacity-40'}`}>
                    {achievement.icon}
                  </div>
                  <h4 className="font-medium text-sm truncate">{achievement.title}</h4>
                </div>
                
                <p className="text-xs opacity-80 mb-2 line-clamp-2">{achievement.description}</p>
                
                {/* Progress Bar */}
                <div className="w-full h-1.5 bg-white/[0.1] rounded-full overflow-hidden">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: `${(achievement.progress / achievement.maxProgress) * 100}%` }}
                    transition={{ duration: 1, delay: index * 0.1 }}
                    className={`h-full rounded-full ${
                      achievement.unlocked 
                        ? 'bg-current' 
                        : 'bg-white/20'
                    }`}
                  />
                </div>
                
                <p className="text-xs mt-1 opacity-60">
                  {achievement.progress} / {achievement.maxProgress}
                </p>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ProgressTracker;
