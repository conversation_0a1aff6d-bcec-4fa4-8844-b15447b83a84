import React from 'react';
import { useGamification } from '@/contexts/GamificationContext';
import SuccessAnimation from './SuccessAnimation';

interface GamificationWrapperProps {
  children: React.ReactNode;
}

const GamificationWrapper: React.FC<GamificationWrapperProps> = ({ children }) => {
  const {
    showSuccessAnimation,
    successAnimationType,
    successAnimationData,
    hideSuccessAnimation
  } = useGamification();

  return (
    <>
      {children}

      {/* Clean Success Animation Only */}
      <SuccessAnimation
        isVisible={showSuccessAnimation}
        type={successAnimationType as any}
        title={successAnimationData?.title}
        subtitle={successAnimationData?.subtitle}
        value={successAnimationData?.value}
        isFirstTime={successAnimationData?.isFirstTime}
        onComplete={hideSuccessAnimation}
      />
    </>
  );
};

export default GamificationWrapper;
