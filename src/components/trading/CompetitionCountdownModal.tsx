import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Timer,
  Trophy,
  Clock,
  Calendar,
  Users,
  Target
} from 'lucide-react';

interface TimeRemaining {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
}

interface CompetitionCountdownModalProps {
  isOpen: boolean;
  competitionStartDate: Date;
  onCompetitionStart?: () => void;
}

const CompetitionCountdownModal: React.FC<CompetitionCountdownModalProps> = ({
  isOpen,
  competitionStartDate,
  onCompetitionStart
}) => {
  const [timeRemaining, setTimeRemaining] = useState<TimeRemaining>({ days: 0, hours: 0, minutes: 0, seconds: 0 });
  const [hasStarted, setHasStarted] = useState(false);

  const calculateTimeRemaining = (targetDate: Date): TimeRemaining => {
    const now = new Date().getTime();
    const target = targetDate.getTime();
    const difference = target - now;

    if (difference <= 0) {
      return { days: 0, hours: 0, minutes: 0, seconds: 0 };
    }

    const days = Math.floor(difference / (1000 * 60 * 60 * 24));
    const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((difference % (1000 * 60)) / 1000);

    return { days, hours, minutes, seconds };
  };

  useEffect(() => {
    const interval = setInterval(() => {
      const remaining = calculateTimeRemaining(competitionStartDate);
      setTimeRemaining(remaining);

      // Check if competition has started
      const now = new Date();
      if (now >= competitionStartDate && !hasStarted) {
        setHasStarted(true);
        console.log('🏁 Competition has started!');
        if (onCompetitionStart) {
          onCompetitionStart();
        }
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [competitionStartDate, hasStarted, onCompetitionStart]);

  // Don't show modal if competition has started
  if (hasStarted) {
    return null;
  }

  const hasTimeLeft = timeRemaining.days > 0 || timeRemaining.hours > 0 || timeRemaining.minutes > 0 || timeRemaining.seconds > 0;
  const isUrgent = timeRemaining.days === 0 && timeRemaining.hours < 24;

  const competitionInfo = [
    {
      icon: <Trophy className="h-4 w-4" />,
      label: "Competition Type",
      value: "Paper Trading"
    },
    {
      icon: <Users className="h-4 w-4" />,
      label: "Starting Portfolio",
      value: "$100,000 Virtual"
    },
    {
      icon: <Target className="h-4 w-4" />,
      label: "Duration",
      value: "2 Weeks"
    },
    {
      icon: <Calendar className="h-4 w-4" />,
      label: "Market Hours",
      value: "9:30 AM - 4:00 PM EST"
    }
  ];

  return (
    <Dialog open={isOpen} onOpenChange={() => {}} modal>
      <DialogContent
        className="max-w-2xl bg-black border border-white/10 backdrop-blur-xl shadow-[0_8px_32px_rgba(0,0,0,0.4)] text-white"
        hideCloseButton
      >
        <div className="p-8">
          {/* Header with logo and text */}
          <div className="flex items-center gap-4 mb-12">
            <img
              src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/sign/logos/ChatGPT%20Image%20Jul%2010,%202025,%2006_41_45%20PM.png?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV8yNWIzMmY0OC0yOWY3LTRlNTgtOWMzNi1jYmZjNGJjM2NhMmYiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJsb2dvcy9DaGF0R1BUIEltYWdlIEp1bCAxMCwgMjAyNSwgMDZfNDFfNDUgUE0ucG5nIiwiaWF0IjoxNzUyMTg3MzYwLCJleHAiOjE3ODM3MjMzNjB9.LO610sLTUtWz6tK1WkUSzsKwWEAHFqduoQV70HkQ2-k"
              alt="Logo"
              className="w-16 h-16"
            />
            <div>
              <h1 className="text-2xl font-normal text-white leading-tight">
                Congrats, and welcome to the
              </h1>
              <h2 className="text-2xl font-normal text-white leading-tight">
                biggest competition yet.
              </h2>
            </div>
          </div>

          {/* Countdown Section */}
          <div className="mb-12">
            <h3 className="text-white text-lg font-normal mb-8">Competition starts in:</h3>

            <div className="flex items-center justify-center gap-6">

              {hasTimeLeft ? (
                <>
                  {/* Days */}
                  <div className="flex flex-col items-center">
                    <div className="w-20 h-20 border border-white/20 rounded-lg flex items-center justify-center mb-2">
                      <motion.span
                        key={timeRemaining.days}
                        initial={{ scale: 1.02, opacity: 0.7, y: -1 }}
                        animate={{ scale: 1, opacity: 1, y: 0 }}
                        exit={{ scale: 0.98, opacity: 0.7, y: 1 }}
                        transition={{
                          duration: 0.8,
                          ease: [0.16, 1, 0.3, 1],
                          type: "spring",
                          stiffness: 120,
                          damping: 20
                        }}
                        className="text-3xl font-normal text-white tracking-[-0.09em]"
                      >
                        {timeRemaining.days.toString().padStart(2, '0')}
                      </motion.span>
                    </div>
                    <span className="text-sm text-white/60 uppercase tracking-wider">DAYS</span>
                  </div>

                  {/* Hours */}
                  <div className="flex flex-col items-center">
                    <div className="w-20 h-20 border border-white/20 rounded-lg flex items-center justify-center mb-2">
                      <motion.span
                        key={timeRemaining.hours}
                        initial={{ scale: 1.02, opacity: 0.7, y: -1 }}
                        animate={{ scale: 1, opacity: 1, y: 0 }}
                        exit={{ scale: 0.98, opacity: 0.7, y: 1 }}
                        transition={{
                          duration: 0.8,
                          ease: [0.16, 1, 0.3, 1],
                          type: "spring",
                          stiffness: 120,
                          damping: 20
                        }}
                        className="text-3xl font-normal text-white tracking-[-0.09em]"
                      >
                        {timeRemaining.hours.toString().padStart(2, '0')}
                      </motion.span>
                    </div>
                    <span className="text-sm text-white/60 uppercase tracking-wider">HOURS</span>
                  </div>

                  {/* Minutes */}
                  <div className="flex flex-col items-center">
                    <div className="w-20 h-20 border border-white/20 rounded-lg flex items-center justify-center mb-2">
                      <motion.span
                        key={timeRemaining.minutes}
                        initial={{ scale: 1.02, opacity: 0.7, y: -1 }}
                        animate={{ scale: 1, opacity: 1, y: 0 }}
                        exit={{ scale: 0.98, opacity: 0.7, y: 1 }}
                        transition={{
                          duration: 0.8,
                          ease: [0.16, 1, 0.3, 1],
                          type: "spring",
                          stiffness: 120,
                          damping: 20
                        }}
                        className="text-3xl font-normal text-white tracking-[-0.09em]"
                      >
                        {timeRemaining.minutes.toString().padStart(2, '0')}
                      </motion.span>
                    </div>
                    <span className="text-sm text-white/60 uppercase tracking-wider">MINUTES</span>
                  </div>

                  {/* Seconds */}
                  <div className="flex flex-col items-center">
                    <div className="w-20 h-20 border border-white/20 rounded-lg flex items-center justify-center mb-2">
                      <motion.span
                        key={timeRemaining.seconds}
                        initial={{ scale: 1.02, opacity: 0.7, y: -1 }}
                        animate={{ scale: 1, opacity: 1, y: 0 }}
                        exit={{ scale: 0.98, opacity: 0.7, y: 1 }}
                        transition={{
                          duration: 0.8,
                          ease: [0.16, 1, 0.3, 1],
                          type: "spring",
                          stiffness: 120,
                          damping: 20
                        }}
                        className="text-3xl font-normal text-white tracking-[-0.09em]"
                      >
                        {timeRemaining.seconds.toString().padStart(2, '0')}
                      </motion.span>
                    </div>
                    <span className="text-sm text-white/60 uppercase tracking-wider">SECONDS</span>
                  </div>
                </>
              ) : (
                <div className="text-center col-span-4">
                  <motion.span
                    initial={{ scale: 0.95, opacity: 0, y: 4 }}
                    animate={{ scale: 1, opacity: 1, y: 0 }}
                    transition={{
                      duration: 0.8,
                      ease: [0.25, 0.46, 0.45, 0.94],
                      type: "spring"
                    }}
                    className="text-2xl font-bold text-amber-400 tracking-wide"
                  >
                    Competition Starting!
                  </motion.span>
                </div>
              )}
            </div>
          </div>

          {/* Competition Details */}
          <div className="bg-white/5 border border-white/10 rounded-lg p-6">
            <h3 className="text-lg font-normal text-white mb-4">Competition Details</h3>

            <div className="space-y-4">
              <p className="text-white/80 text-base leading-relaxed">
                You'll start with a 100k account - highest balance at the end wins.
              </p>
              <p className="text-white/80 text-base leading-relaxed">
                Till then, prepare. Emails will go out on start date. Good luck, trader.
              </p>
            </div>
          </div>
        </div>

          {/* Preparation Message */}
          <div className="text-center space-y-2">
            <p className="text-white/70">
              Get ready to trade! The competition will begin automatically when the countdown reaches zero.
            </p>
            <p className="text-white/50 text-sm">
              Use this time to review your trading strategy and familiarize yourself with the platform.
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CompetitionCountdownModal;
