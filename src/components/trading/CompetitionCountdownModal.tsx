import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Timer,
  Trophy,
  Clock,
  Calendar,
  Users,
  Target
} from 'lucide-react';

interface TimeRemaining {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
}

interface CompetitionCountdownModalProps {
  isOpen: boolean;
  competitionStartDate: Date;
  onCompetitionStart?: () => void;
}

const CompetitionCountdownModal: React.FC<CompetitionCountdownModalProps> = ({
  isOpen,
  competitionStartDate,
  onCompetitionStart
}) => {
  const [timeRemaining, setTimeRemaining] = useState<TimeRemaining>({ days: 0, hours: 0, minutes: 0, seconds: 0 });
  const [hasStarted, setHasStarted] = useState(false);

  const calculateTimeRemaining = (targetDate: Date): TimeRemaining => {
    const now = new Date().getTime();
    const target = targetDate.getTime();
    const difference = target - now;

    if (difference <= 0) {
      return { days: 0, hours: 0, minutes: 0, seconds: 0 };
    }

    const days = Math.floor(difference / (1000 * 60 * 60 * 24));
    const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((difference % (1000 * 60)) / 1000);

    return { days, hours, minutes, seconds };
  };

  useEffect(() => {
    const interval = setInterval(() => {
      const remaining = calculateTimeRemaining(competitionStartDate);
      setTimeRemaining(remaining);

      // Check if competition has started
      const now = new Date();
      if (now >= competitionStartDate && !hasStarted) {
        setHasStarted(true);
        console.log('🏁 Competition has started!');
        if (onCompetitionStart) {
          onCompetitionStart();
        }
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [competitionStartDate, hasStarted, onCompetitionStart]);

  // Don't show modal if competition has started
  if (hasStarted) {
    return null;
  }

  const hasTimeLeft = timeRemaining.days > 0 || timeRemaining.hours > 0 || timeRemaining.minutes > 0 || timeRemaining.seconds > 0;
  const isUrgent = timeRemaining.days === 0 && timeRemaining.hours < 24;

  const competitionInfo = [
    {
      icon: <Trophy className="h-4 w-4" />,
      label: "Competition Type",
      value: "Paper Trading"
    },
    {
      icon: <Users className="h-4 w-4" />,
      label: "Starting Portfolio",
      value: "$100,000 Virtual"
    },
    {
      icon: <Target className="h-4 w-4" />,
      label: "Duration",
      value: "2 Weeks"
    },
    {
      icon: <Calendar className="h-4 w-4" />,
      label: "Market Hours",
      value: "9:30 AM - 4:00 PM EST"
    }
  ];

  return (
    <Dialog open={isOpen} onOpenChange={() => {}} modal>
      <DialogContent 
        className="max-w-2xl bg-[#0A0A0C] border-[#1A1A1C] text-white"
        hideCloseButton
      >
        <DialogHeader>
          <DialogTitle className="text-center">
            <div className="flex items-center justify-center gap-2 mb-2">
              <Timer className="h-6 w-6 text-blue-400" />
              <span className="text-xl">Trading Competition</span>
            </div>
            <Badge className={`${isUrgent ? 'bg-red-500/20 text-red-400 border-red-500/30' : 'bg-blue-500/20 text-blue-400 border-blue-500/30'}`}>
              {isUrgent ? 'Starting Soon!' : 'Preparing to Start'}
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="py-6 space-y-6">
          {/* Countdown Display */}
          <motion.div
            animate={isUrgent ? { scale: [1, 1.02, 1] } : {}}
            transition={isUrgent ? { duration: 2, repeat: Infinity } : {}}
          >
            <Card className={`bg-gradient-to-br from-blue-500/10 to-purple-500/10 border-blue-500/20 ${isUrgent ? 'border-red-500/30' : ''}`}>
              <CardContent className="p-6">
                <div className="text-center space-y-4">
                  <div className="flex items-center justify-center gap-2">
                    <Clock className={`h-5 w-5 ${isUrgent ? 'text-red-400' : 'text-blue-400'}`} />
                    <span className={`text-lg font-medium ${isUrgent ? 'text-red-400' : 'text-white/80'}`}>
                      Competition starts in
                    </span>
                  </div>

                  {hasTimeLeft ? (
                    <div className="grid grid-cols-4 gap-4">
                      {/* Days */}
                      <div className="text-center">
                        <motion.div
                          key={timeRemaining.days}
                          initial={{ scale: 1.2, opacity: 0.8 }}
                          animate={{ scale: 1, opacity: 1 }}
                          className={`text-3xl font-bold ${isUrgent ? 'text-red-400' : 'text-white'}`}
                        >
                          {timeRemaining.days.toString().padStart(2, '0')}
                        </motion.div>
                        <div className="text-xs text-white/60 mt-1">Days</div>
                      </div>

                      {/* Hours */}
                      <div className="text-center">
                        <motion.div
                          key={timeRemaining.hours}
                          initial={{ scale: 1.2, opacity: 0.8 }}
                          animate={{ scale: 1, opacity: 1 }}
                          className={`text-3xl font-bold ${isUrgent ? 'text-red-400' : 'text-white'}`}
                        >
                          {timeRemaining.hours.toString().padStart(2, '0')}
                        </motion.div>
                        <div className="text-xs text-white/60 mt-1">Hours</div>
                      </div>

                      {/* Minutes */}
                      <div className="text-center">
                        <motion.div
                          key={timeRemaining.minutes}
                          initial={{ scale: 1.2, opacity: 0.8 }}
                          animate={{ scale: 1, opacity: 1 }}
                          className={`text-3xl font-bold ${isUrgent ? 'text-red-400' : 'text-white'}`}
                        >
                          {timeRemaining.minutes.toString().padStart(2, '0')}
                        </motion.div>
                        <div className="text-xs text-white/60 mt-1">Minutes</div>
                      </div>

                      {/* Seconds */}
                      <div className="text-center">
                        <motion.div
                          key={timeRemaining.seconds}
                          initial={{ scale: 1.2, opacity: 0.8 }}
                          animate={{ scale: 1, opacity: 1 }}
                          className={`text-3xl font-bold ${isUrgent ? 'text-red-400' : 'text-white'}`}
                        >
                          {timeRemaining.seconds.toString().padStart(2, '0')}
                        </motion.div>
                        <div className="text-xs text-white/60 mt-1">Seconds</div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center">
                      <span className="text-2xl font-bold text-green-400">
                        Competition Starting!
                      </span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Competition Information */}
          <Card className="bg-white/[0.02] border-white/[0.08]">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                <Trophy className="h-5 w-5 text-yellow-400" />
                Competition Details
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {competitionInfo.map((info, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="text-blue-400">
                      {info.icon}
                    </div>
                    <div>
                      <div className="text-white/60 text-xs">{info.label}</div>
                      <div className="text-white font-medium text-sm">{info.value}</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Preparation Message */}
          <div className="text-center space-y-2">
            <p className="text-white/70">
              Get ready to trade! The competition will begin automatically when the countdown reaches zero.
            </p>
            <p className="text-white/50 text-sm">
              Use this time to review your trading strategy and familiarize yourself with the platform.
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CompetitionCountdownModal;
