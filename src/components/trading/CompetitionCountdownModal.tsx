import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Timer,
  Trophy,
  Clock,
  Calendar,
  Users,
  Target
} from 'lucide-react';

interface TimeRemaining {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
}

interface CompetitionCountdownModalProps {
  isOpen: boolean;
  competitionStartDate: Date;
  onCompetitionStart?: () => void;
}

const CompetitionCountdownModal: React.FC<CompetitionCountdownModalProps> = ({
  isOpen,
  competitionStartDate,
  onCompetitionStart
}) => {
  const [timeRemaining, setTimeRemaining] = useState<TimeRemaining>({ days: 0, hours: 0, minutes: 0, seconds: 0 });
  const [hasStarted, setHasStarted] = useState(false);

  const calculateTimeRemaining = (targetDate: Date): TimeRemaining => {
    const now = new Date().getTime();
    const target = targetDate.getTime();
    const difference = target - now;

    if (difference <= 0) {
      return { days: 0, hours: 0, minutes: 0, seconds: 0 };
    }

    const days = Math.floor(difference / (1000 * 60 * 60 * 24));
    const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((difference % (1000 * 60)) / 1000);

    return { days, hours, minutes, seconds };
  };

  useEffect(() => {
    const interval = setInterval(() => {
      const remaining = calculateTimeRemaining(competitionStartDate);
      setTimeRemaining(remaining);

      // Check if competition has started
      const now = new Date();
      if (now >= competitionStartDate && !hasStarted) {
        setHasStarted(true);
        console.log('🏁 Competition has started!');
        if (onCompetitionStart) {
          onCompetitionStart();
        }
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [competitionStartDate, hasStarted, onCompetitionStart]);

  // Don't show modal if competition has started
  if (hasStarted) {
    return null;
  }

  const hasTimeLeft = timeRemaining.days > 0 || timeRemaining.hours > 0 || timeRemaining.minutes > 0 || timeRemaining.seconds > 0;
  const isUrgent = timeRemaining.days === 0 && timeRemaining.hours < 24;

  const competitionInfo = [
    {
      icon: <Trophy className="h-4 w-4" />,
      label: "Competition Type",
      value: "Paper Trading"
    },
    {
      icon: <Users className="h-4 w-4" />,
      label: "Starting Portfolio",
      value: "$100,000 Virtual"
    },
    {
      icon: <Target className="h-4 w-4" />,
      label: "Duration",
      value: "2 Weeks"
    },
    {
      icon: <Calendar className="h-4 w-4" />,
      label: "Market Hours",
      value: "9:30 AM - 4:00 PM EST"
    }
  ];

  return (
    <Dialog open={isOpen} onOpenChange={() => {}} modal>
      <DialogContent
        className="max-w-2xl bg-[#141414]/80 border-white/[0.08] backdrop-blur-xl shadow-[0_8px_32px_rgba(0,0,0,0.4)] text-white"
        hideCloseButton
      >
        <DialogHeader>
          <DialogTitle className="text-center relative">
            {/* Logo in top left */}
            <div className="absolute -top-2 -left-2">
              <img
                src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/sign/logos/ChatGPT%20Image%20Jul%2010,%202025,%2006_30_31%20PM.png?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV8yNWIzMmY0OC0yOWY3LTRlNTgtOWMzNi1jYmZjNGJjM2NhMmYiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJsb2dvcy9DaGF0R1BUIEltYWdlIEp1bCAxMCwgMjAyNSwgMDZfMzBfMzEgUE0ucG5nIiwiaWF0IjoxNzUyMTg2NzM3LCJleHAiOjE3ODM3MjI3Mzd9.O36Am724O9n5VwTztpMUrjDHH3EklhbhposWX9DhnQ0"
                alt="Logo"
                className="w-8 h-8 opacity-60"
              />
            </div>

            <div className="mb-6">
              <span className="text-2xl font-semibold text-white -tracking-[0.02em]">Trading Competition</span>
            </div>
            <Badge className={`
              ${isUrgent
                ? 'bg-red-500/10 text-red-400 border-red-500/20'
                : 'bg-white/[0.05] text-white/80 border-white/[0.08]'
              }
              px-6 py-2 text-sm font-medium rounded-full border backdrop-blur-sm
              shadow-[inset_0_1px_0_rgba(255,255,255,0.1)] -tracking-[0.01em]
            `}>
              {isUrgent ? 'Starting Soon!' : 'Preparing to Start'}
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="py-8 space-y-8">
          {/* Countdown Display */}
          <motion.div
            animate={isUrgent ? { scale: [1, 1.005, 1] } : {}}
            transition={isUrgent ? { duration: 4, repeat: Infinity, ease: "easeInOut" } : {}}
          >
            <Card className={`
              bg-[#141414]/80 border-white/[0.08] backdrop-blur-xl
              shadow-[inset_0_1px_0_rgba(255,255,255,0.05),0_4px_24px_rgba(0,0,0,0.3)]
              ${isUrgent ? 'border-red-500/20' : ''}
              transition-all duration-500 ease-out
            `}>
              <CardContent className="p-8">
                <div className="text-center space-y-8">
                  <div className="flex items-center justify-center">
                    <span className={`
                      text-lg font-medium -tracking-[0.01em]
                      ${isUrgent ? 'text-red-400' : 'text-white/80'}
                    `}>
                      Competition starts in
                    </span>
                  </div>

                  {hasTimeLeft ? (
                    <div className="flex items-center justify-center gap-8">
                      {/* Days */}
                      <motion.div
                        key={timeRemaining.days}
                        initial={{ scale: 1.02, opacity: 0.7, y: -1 }}
                        animate={{ scale: 1, opacity: 1, y: 0 }}
                        exit={{ scale: 0.98, opacity: 0.7, y: 1 }}
                        transition={{
                          duration: 0.8,
                          ease: [0.16, 1, 0.3, 1],
                          type: "spring",
                          stiffness: 120,
                          damping: 20
                        }}
                        className={`
                          text-6xl font-bold -tracking-[0.03em]
                          ${isUrgent ? 'text-red-400' : 'text-white'}
                          font-mono
                        `}
                      >
                        {timeRemaining.days.toString().padStart(2, '0')}
                      </motion.div>

                      {/* Separator */}
                      <div className="text-4xl font-bold text-white/40 font-mono -tracking-[0.02em]">:</div>

                      {/* Hours */}
                      <motion.div
                        key={timeRemaining.hours}
                        initial={{ scale: 1.02, opacity: 0.7, y: -1 }}
                        animate={{ scale: 1, opacity: 1, y: 0 }}
                        exit={{ scale: 0.98, opacity: 0.7, y: 1 }}
                        transition={{
                          duration: 0.8,
                          ease: [0.16, 1, 0.3, 1],
                          type: "spring",
                          stiffness: 120,
                          damping: 20
                        }}
                        className={`
                          text-6xl font-bold -tracking-[0.03em]
                          ${isUrgent ? 'text-red-400' : 'text-white'}
                          font-mono
                        `}
                      >
                        {timeRemaining.hours.toString().padStart(2, '0')}
                      </motion.div>

                      {/* Separator */}
                      <div className="text-4xl font-bold text-white/40 font-mono -tracking-[0.02em]">:</div>

                      {/* Minutes */}
                      <motion.div
                        key={timeRemaining.minutes}
                        initial={{ scale: 1.02, opacity: 0.7, y: -1 }}
                        animate={{ scale: 1, opacity: 1, y: 0 }}
                        exit={{ scale: 0.98, opacity: 0.7, y: 1 }}
                        transition={{
                          duration: 0.8,
                          ease: [0.16, 1, 0.3, 1],
                          type: "spring",
                          stiffness: 120,
                          damping: 20
                        }}
                        className={`
                          text-6xl font-bold -tracking-[0.03em]
                          ${isUrgent ? 'text-red-400' : 'text-white'}
                          font-mono
                        `}
                      >
                        {timeRemaining.minutes.toString().padStart(2, '0')}
                      </motion.div>

                      {/* Separator */}
                      <div className="text-4xl font-bold text-white/40 font-mono -tracking-[0.02em]">:</div>

                      {/* Seconds */}
                      <motion.div
                        key={timeRemaining.seconds}
                        initial={{ scale: 1.02, opacity: 0.7, y: -1 }}
                        animate={{ scale: 1, opacity: 1, y: 0 }}
                        exit={{ scale: 0.98, opacity: 0.7, y: 1 }}
                        transition={{
                          duration: 0.8,
                          ease: [0.16, 1, 0.3, 1],
                          type: "spring",
                          stiffness: 120,
                          damping: 20
                        }}
                        className={`
                          text-6xl font-bold -tracking-[0.03em]
                          ${isUrgent ? 'text-red-400' : 'text-white'}
                          font-mono
                        `}
                      >
                        {timeRemaining.seconds.toString().padStart(2, '0')}
                      </motion.div>
                    </div>
                  ) : (
                    <div className="text-center">
                      <div className="p-6 rounded-xl bg-white/[0.05] border border-white/[0.08] backdrop-blur-sm shadow-[inset_0_1px_0_rgba(255,255,255,0.1)]">
                        <motion.span
                          initial={{ scale: 0.95, opacity: 0, y: 4 }}
                          animate={{ scale: 1, opacity: 1, y: 0 }}
                          transition={{
                            duration: 0.8,
                            ease: [0.25, 0.46, 0.45, 0.94],
                            type: "spring"
                          }}
                          className="text-2xl font-bold text-emerald-400 tracking-wide"
                        >
                          Competition Starting!
                        </motion.span>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Competition Information */}
          <Card className="bg-[#141414]/80 border-white/[0.08] backdrop-blur-xl shadow-[inset_0_1px_0_rgba(255,255,255,0.05),0_4px_24px_rgba(0,0,0,0.3)]">
            <CardContent className="p-8">
              <h3 className="text-lg font-semibold text-white mb-8 -tracking-[0.01em]">
                Competition Details
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {competitionInfo.map((info, index) => (
                  <div key={index} className="flex items-start gap-4">
                    <div className="w-1.5 h-1.5 bg-white/60 rounded-full flex-shrink-0 mt-2" />
                    <div>
                      <div className="text-white/60 text-xs font-medium uppercase -tracking-[0.01em] mb-1.5">
                        {info.label}
                      </div>
                      <div className="text-white font-semibold text-sm -tracking-[0.01em]">
                        {info.value}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Preparation Message */}
          <div className="text-center space-y-2">
            <p className="text-white/70">
              Get ready to trade! The competition will begin automatically when the countdown reaches zero.
            </p>
            <p className="text-white/50 text-sm">
              Use this time to review your trading strategy and familiarize yourself with the platform.
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CompetitionCountdownModal;
