import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogDescription 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  TrendingUp, 
  BarChart3, 
  Target, 
  Shield, 
  Zap, 
  CreditCard,
  CheckCircle,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { whopIntermediaryClient } from '@/lib/whopIntermediaryClient';
import { useTradingOnboarding } from '@/hooks/useTradingOnboarding';

interface TradingOnboardingModalProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: () => void;
}

const TradingOnboardingModal: React.FC<TradingOnboardingModalProps> = ({
  isOpen,
  onClose,
  onComplete
}) => {
  const { toast } = useToast();
  const { isWhopUser, whopUser } = useTradingOnboarding();
  const [currentStep, setCurrentStep] = useState(0);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [paymentError, setPaymentError] = useState<string | null>(null);

  const features = [
    {
      icon: <TrendingUp className="w-8 h-8 text-green-400" />,
      title: "Real-Time Trading",
      description: "Execute paper trades with live market data and real-time price updates"
    },
    {
      icon: <BarChart3 className="w-8 h-8 text-blue-400" />,
      title: "Advanced Charts",
      description: "Professional-grade candlestick charts with technical indicators and drawing tools"
    },
    {
      icon: <Target className="w-8 h-8 text-purple-400" />,
      title: "Portfolio Tracking",
      description: "Monitor your positions, P&L, and trading performance in real-time"
    },
    {
      icon: <Shield className="w-8 h-8 text-yellow-400" />,
      title: "Risk Management",
      description: "Built-in stop losses, position sizing, and risk controls to protect your capital"
    }
  ];

  const steps = [
    {
      title: "Welcome to Trade Sensei",
      content: (
        <div className="space-y-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <Zap className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-white mb-2">
              Welcome to the Future of Trading
            </h3>
            <p className="text-gray-300 text-lg">
              Experience professional-grade paper trading with real market data
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {features.map((feature, index) => (
              <Card key={index} className="bg-gray-800/50 border-gray-700">
                <CardContent className="p-4">
                  <div className="flex items-start space-x-3">
                    {feature.icon}
                    <div>
                      <h4 className="font-semibold text-white mb-1">{feature.title}</h4>
                      <p className="text-sm text-gray-400">{feature.description}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )
    },
    {
      title: "Get Started with Premium Access",
      content: (
        <div className="space-y-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <CreditCard className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-white mb-2">
              Unlock Full Trading Experience
            </h3>
            <p className="text-gray-300 text-lg mb-6">
              Get instant access to all premium trading features for just $10
            </p>
          </div>

          <Card className="bg-gradient-to-r from-green-900/20 to-emerald-900/20 border-green-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-xl font-bold text-white">Premium Trading Access</h4>
                <div className="text-right">
                  <div className="text-3xl font-bold text-green-400">$10</div>
                  <div className="text-sm text-gray-400">One-time payment</div>
                </div>
              </div>
              
              <div className="space-y-3">
                {[
                  "Unlimited paper trading",
                  "Real-time market data",
                  "Advanced charting tools",
                  "Portfolio analytics",
                  "Risk management tools",
                  "Competition access"
                ].map((feature, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-400" />
                    <span className="text-gray-300">{feature}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {paymentError && (
            <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className="w-5 h-5 text-red-400" />
                <span className="text-red-300">{paymentError}</span>
              </div>
            </div>
          )}
        </div>
      )
    }
  ];

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handlePayment = async () => {
    if (!isWhopUser || !whopUser) {
      setPaymentError('Whop user authentication required');
      return;
    }

    setIsProcessingPayment(true);
    setPaymentError(null);

    try {
      // TODO: Implement real Whop payment flow
      // This would involve:
      // 1. Creating a charge via intermediary server using whopSdk.payments.chargeUser()
      // 2. Using the Whop iframe SDK to open the payment modal
      // 3. Handling the payment confirmation

      // For now, we'll simulate the payment flow
      toast({
        title: "Payment Processing",
        description: "Processing your $10 payment...",
        duration: 2000
      });

      // Simulate payment processing delay
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Simulate successful payment
      toast({
        title: "Payment Successful!",
        description: "Welcome to Trade Sensei Premium! You now have full access to all trading features.",
        duration: 4000
      });

      // Complete onboarding
      onComplete();

    } catch (error) {
      console.error('Payment error:', error);
      setPaymentError(error instanceof Error ? error.message : 'Payment failed');

      toast({
        variant: "destructive",
        title: "Payment Failed",
        description: "There was an error processing your payment. Please try again."
      });
    } finally {
      setIsProcessingPayment(false);
    }
  };

  const handleSkip = () => {
    onClose();
  };

  if (!isWhopUser) {
    return null; // Don't show onboarding for non-Whop users
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-[#1A1A1A] border-gray-800 text-white max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-center">
            {steps[currentStep].title}
          </DialogTitle>
          <DialogDescription className="text-center text-gray-400">
            Step {currentStep + 1} of {steps.length}
          </DialogDescription>
        </DialogHeader>

        <div className="py-6">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              {steps[currentStep].content}
            </motion.div>
          </AnimatePresence>
        </div>

        <div className="flex justify-between items-center pt-6 border-t border-gray-800">
          <div className="flex space-x-2">
            {steps.map((_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full ${
                  index === currentStep ? 'bg-green-400' : 'bg-gray-600'
                }`}
              />
            ))}
          </div>

          <div className="flex space-x-3">
            {currentStep > 0 && (
              <Button
                variant="outline"
                onClick={handleBack}
                className="border-gray-600 text-gray-300 hover:bg-gray-800"
                disabled={isProcessingPayment}
              >
                Back
              </Button>
            )}
            
            {currentStep < steps.length - 1 ? (
              <Button
                onClick={handleNext}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                Next
              </Button>
            ) : (
              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  onClick={handleSkip}
                  className="border-gray-600 text-gray-300 hover:bg-gray-800"
                  disabled={isProcessingPayment}
                >
                  Skip for Now
                </Button>
                <Button
                  onClick={handlePayment}
                  className="bg-green-600 hover:bg-green-700 text-white"
                  disabled={isProcessingPayment}
                >
                  {isProcessingPayment ? (
                    <div className="flex items-center space-x-2">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span>Processing...</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <CreditCard className="w-4 h-4" />
                      <span>Pay $10</span>
                    </div>
                  )}
                </Button>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TradingOnboardingModal;
