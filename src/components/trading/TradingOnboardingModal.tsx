import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  TrendingUp,
  BarChart3,
  Target,
  Shield,
  Trophy,
  Users,
  Clock,
  CheckCircle,
  ArrowRight,
  Sparkles
} from 'lucide-react';
import { useIframeSdk } from '@/hooks/useIframeSdk';
import { whopIntermediaryClient } from '@/lib/whopIntermediaryClient';
import { useToast } from '@/components/ui/use-toast';

interface TradingOnboardingModalProps {
  isOpen: boolean;
  onComplete: () => void;
}

const TradingOnboardingModal: React.FC<TradingOnboardingModalProps> = ({
  isOpen,
  onComplete
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [paymentError, setPaymentError] = useState<string | null>(null);
  const iframeSdk = useIframeSdk();
  const { toast } = useToast();

  const totalSteps = 2;

  const features = [
    {
      icon: <TrendingUp className="h-5 w-5" />,
      title: "Real-Time Paper Trading",
      description: "Practice with live market data without risking real money"
    },
    {
      icon: <BarChart3 className="h-5 w-5" />,
      title: "Advanced Charts",
      description: "Professional-grade charts with technical indicators"
    },
    {
      icon: <Target className="h-5 w-5" />,
      title: "Risk Management",
      description: "Set stop losses and manage position sizing"
    },
    {
      icon: <Trophy className="h-5 w-5" />,
      title: "Trading Competition",
      description: "Compete with other traders in live competitions"
    }
  ];

  const competitionFeatures = [
    "🏆 Live trading competition with real prizes",
    "📊 Real-time leaderboard and rankings", 
    "💰 Paper trading with $100,000 virtual portfolio",
    "⏰ 2-week competition duration",
    "🎯 Professional trading environment",
    "📈 Advanced analytics and performance tracking"
  ];

  const handlePayment = async () => {
    if (!iframeSdk) {
      setPaymentError('Payment system not available');
      return;
    }

    setIsProcessingPayment(true);
    setPaymentError(null);

    try {
      console.log('💳 Starting Whop payment flow for trading competition...');

      // Create charge via intermediary server (set to $0 as requested)
      const chargeResponse = await whopIntermediaryClient.createCharge(
        0, // $0.00 - free entry as requested
        'usd',
        'Trading Competition Entry - Paper Trading Access'
      );

      console.log('📡 Charge creation response:', chargeResponse);

      if (!chargeResponse.success || !chargeResponse.data?.inAppPurchase) {
        throw new Error(chargeResponse.error || 'Failed to create charge');
      }

      // Open Whop payment modal
      console.log('🖼️ Opening Whop payment modal...');
      const paymentResult = await iframeSdk.inAppPurchase(chargeResponse.data.inAppPurchase);

      console.log('💳 Payment result:', paymentResult);

      if (paymentResult?.status === "ok") {
        console.log('✅ Payment successful, completing onboarding');
        toast({
          title: "Welcome to the Trading Competition!",
          description: "Your entry has been confirmed. Get ready to trade!",
        });
        onComplete();
      } else {
        throw new Error(paymentResult?.error || 'Payment was not completed');
      }
    } catch (error) {
      console.error('❌ Payment error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Payment failed';
      setPaymentError(errorMessage);
      toast({
        title: "Payment Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsProcessingPayment(false);
    }
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const renderStep1 = () => (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className="space-y-6"
    >
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center mb-4">
          <div className="bg-gradient-to-r from-green-500 to-blue-500 p-3 rounded-full">
            <Trophy className="h-8 w-8 text-white" />
          </div>
        </div>
        <h2 className="text-2xl font-bold text-white">
          Welcome to the Trading Competition!
        </h2>
        <p className="text-white/70 text-lg">
          Join our exclusive paper trading competition and test your skills against other traders
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {features.map((feature, index) => (
          <Card key={index} className="bg-white/[0.02] border-white/[0.08] hover:bg-white/[0.04] transition-colors">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <div className="text-green-400 mt-1">
                  {feature.icon}
                </div>
                <div>
                  <h3 className="text-white font-medium text-sm">
                    {feature.title}
                  </h3>
                  <p className="text-white/60 text-xs mt-1">
                    {feature.description}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="flex justify-center">
        <Button
          onClick={nextStep}
          className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white px-8 py-2"
        >
          Get Started
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  );

  const renderStep2 = () => (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className="space-y-6"
    >
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center mb-4">
          <div className="bg-gradient-to-r from-green-500 to-blue-500 p-3 rounded-full">
            <Sparkles className="h-8 w-8 text-white" />
          </div>
        </div>
        <h2 className="text-2xl font-bold text-white">
          Join the Competition
        </h2>
        <p className="text-white/70">
          Secure your spot in our exclusive trading competition
        </p>
      </div>

      <Card className="bg-gradient-to-br from-green-500/10 to-blue-500/10 border-green-500/20">
        <CardContent className="p-6 space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-bold text-white">Competition Entry</h3>
            <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
              FREE
            </Badge>
          </div>
          
          <div className="space-y-3">
            {competitionFeatures.map((feature, index) => (
              <div key={index} className="flex items-center gap-3">
                <CheckCircle className="h-4 w-4 text-green-400 flex-shrink-0" />
                <span className="text-white/80 text-sm">{feature}</span>
              </div>
            ))}
          </div>

          <div className="pt-4 border-t border-white/10">
            <div className="flex items-center justify-between text-sm">
              <span className="text-white/60">Entry Fee:</span>
              <span className="text-2xl font-bold text-green-400">FREE</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {paymentError && (
        <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
          <p className="text-red-400 text-sm">{paymentError}</p>
        </div>
      )}

      <div className="flex justify-center">
        <Button
          onClick={handlePayment}
          disabled={isProcessingPayment}
          className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white px-8 py-3 text-lg"
        >
          {isProcessingPayment ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Processing...
            </>
          ) : (
            <>
              Join Competition
              <Trophy className="ml-2 h-4 w-4" />
            </>
          )}
        </Button>
      </div>
    </motion.div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={() => {}} modal>
      <DialogContent 
        className="max-w-4xl bg-[#0A0A0C] border-[#1A1A1C] text-white"
        hideCloseButton
      >
        <DialogHeader>
          <DialogTitle className="text-center">
            <div className="flex items-center justify-center gap-2">
              <Users className="h-5 w-5 text-blue-400" />
              <span>Trading Competition Onboarding</span>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="py-6">
          <AnimatePresence mode="wait">
            {currentStep === 1 && renderStep1()}
            {currentStep === 2 && renderStep2()}
          </AnimatePresence>
        </div>

        {/* Progress indicators */}
        <div className="flex justify-center gap-2 mt-6">
          {Array.from({ length: totalSteps }, (_, i) => (
            <div
              key={i}
              className={`h-2 w-8 rounded-full transition-colors ${
                i + 1 <= currentStep ? 'bg-green-500' : 'bg-white/20'
              }`}
            />
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TradingOnboardingModal;
