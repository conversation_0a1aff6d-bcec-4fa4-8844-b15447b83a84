import React from 'react';

interface WaveTextProps {
  text: string;
  delay?: number;
  className?: string;
}

export const WaveText: React.FC<WaveTextProps> = ({ text, delay = 0.1, className = '' }) => {
  return (
    <span className={`${className} text-white`}>
      {text.split('').map((char, index) => (
        <span
          key={index}
          style={{
            display: 'inline-block',
            animation: 'rippleChar 0.8s cubic-bezier(0.4, 0, 0.2, 1) infinite',
            animationDelay: `${index * delay}s`,
            marginRight: char === ' ' ? '0.3em' : '0.02em'
          }}
        >
          {char}
        </span>
      ))}
      <style>
        {`
          @keyframes rippleChar {
            0% { transform: translateY(0px); }
            25% { transform: translateY(-2px); }
            50% { transform: translateY(0px); }
            100% { transform: translateY(0px); }
          }
        `}
      </style>
    </span>
  );
};

export default WaveText; 