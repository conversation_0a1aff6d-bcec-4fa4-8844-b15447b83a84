import { useNavigate, useLocation } from "react-router-dom";
import { Sidebar, SidebarContent, SidebarGroup, SidebarGroupLabel, SidebarMenu, SidebarMenuItem, SidebarMenuButton, SidebarProvider } from "@/components/ui/sidebar";
import { Plus, Youtube, Bot, Code, Search, TrendingUp, BookOpen, BarChart3 } from "lucide-react";
import { Button } from "@/components/ui/button";
import Header from "./Header";

export const MainLayout = ({ children, loading }: { children: React.ReactNode, loading?: boolean }) => {
  const navigate = useNavigate();
  const location = useLocation();

  // Check if we're on a subscription page
  const isSubscriptionPage = location.pathname.includes('/subscription');

  const handleNewChat = (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    // ALL REDIRECT CODE REMOVED
    console.log('Redirect functionality removed');
  };

  return (
    <SidebarProvider defaultOpen={false}>
      <div className="min-h-screen flex w-full">
        {!isSubscriptionPage && (
          <Sidebar className="bg-[#0A0A0A] border-r border-white/5">
            <SidebarContent className="pt-14">
              <div className="px-3 py-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleNewChat}
                  className="w-full mb-4 justify-start gap-2.5 bg-white/5 border-white/10 hover:bg-white/10 hover:border-white/20 transition-all duration-200"
                >
                  <Plus className="w-3.5 h-3.5 text-white/70" />
                  <span className="text-sm font-medium text-white/70">New Chat</span>
                </Button>

                <SidebarGroup>
                  <SidebarGroupLabel>Tools</SidebarGroupLabel>
                  <SidebarMenu>
                    <SidebarMenuItem>
                      <SidebarMenuButton
                        onClick={() => navigate('/youtube-search')}
                        className="w-full flex items-center gap-2 px-4 py-2 text-white/70 hover:bg-white/5 rounded-md transition-colors"
                      >
                        <Youtube className="w-4 h-4" />
                        <span>YouTube Research</span>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                    <SidebarMenuItem>
                      <SidebarMenuButton
                        onClick={() => navigate('/agents')}
                        className="w-full flex items-center gap-2 px-4 py-2 text-white/70 hover:bg-white/5 rounded-md transition-colors"
                      >
                        <Bot className="w-4 h-4" />
                        <span>Trading Agents</span>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                    <SidebarMenuItem>
                      <SidebarMenuButton
                        onClick={() => navigate('/agent-builder')}
                        className="w-full flex items-center gap-2 px-4 py-2 text-white/70 hover:bg-white/5 rounded-md transition-colors"
                      >
                        <Code className="w-4 h-4" />
                        <span>Agent Builder</span>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                    <SidebarMenuItem>
                      <SidebarMenuButton
                        onClick={() => navigate('/agent-builder-docs')}
                        className="w-full flex items-center gap-2 px-4 py-2 text-white/70 hover:bg-white/5 rounded-md transition-colors"
                      >
                        <BookOpen className="w-4 h-4" />
                        <span>Builder Docs</span>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                    <SidebarMenuItem>
                      <SidebarMenuButton
                        onClick={() => navigate('/agent-scanner')}
                        className="w-full flex items-center gap-2 px-4 py-2 text-white/70 hover:bg-white/5 rounded-md transition-colors"
                      >
                        <Search className="w-4 h-4" />
                        <span>Agent Scanner</span>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                    <SidebarMenuItem>
                      <SidebarMenuButton
                        onClick={() => navigate('/agent-backtesting')}
                        className="w-full flex items-center gap-2 px-4 py-2 text-white/70 hover:bg-white/5 rounded-md transition-colors"
                      >
                        <TrendingUp className="w-4 h-4" />
                        <span>Agent Backtesting</span>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                    <SidebarMenuItem>
                      <SidebarMenuButton
                        onClick={() => navigate('/stock-screener')}
                        className="w-full flex items-center gap-2 px-4 py-2 text-white/70 hover:bg-white/5 rounded-md transition-colors"
                      >
                        <BarChart3 className="w-4 h-4" />
                        <span>Stock Screener</span>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  </SidebarMenu>
                </SidebarGroup>
              </div>
            </SidebarContent>
          </Sidebar>
        )}

        <div className="flex-1">
          <Header onNewChat={handleNewChat} />
          <main className="pt-14 h-[calc(100vh-3.5rem)] overflow-auto">
            {children}
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};
