import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import {
  <PERSON><PERSON><PERSON>,
  Hammer,
  Settings,
  ChevronRight,
  Plus,
  Sparkles,
  X,
  Lock
} from 'lucide-react';
import AuthButton from '@/components/auth/AuthButton';
import { supabase } from '@/integrations/supabase/client';
import { usePageLock } from '@/hooks/usePageLock';

import { useMobileDetection } from '@/hooks/useMobileDetection';
import MobileRestrictionModal from '@/components/mobile/MobileRestrictionModal';
import { getCurrentAuthenticatedUser } from '@/utils/authUtils';


interface SidebarItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  path: string;
  description?: string;
}

interface AppSidebarProps {
  onMinimizedChange?: (isMinimized: boolean) => void;
}

const AppSidebar: React.FC<AppSidebarProps> = ({ onMinimizedChange }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isPageLocked, planType } = usePageLock();
  const { isMobile } = useMobileDetection();

  // Sidebar minimizer state with persistence
  const [isMinimized, setIsMinimized] = useState(() => {
    const saved = localStorage.getItem('sidebar-minimized');
    return saved ? JSON.parse(saved) : false;
  });





  // Mobile restriction modal state
  const [showMobileRestriction, setShowMobileRestriction] = useState(false);



  // Real user data from Supabase
  const [userData, setUserData] = useState({
    name: "",
    email: "",
    avatar: "",
    isOnline: true
  });

  // Fetch user data
  useEffect(() => {
    let isMounted = true;

    const fetchUserData = async () => {
      try {
        // Use unified authentication that works for both regular and Whop users
        const authenticatedUser = await getCurrentAuthenticatedUser();

        if (authenticatedUser && isMounted) {
          console.log('🔍 AppSidebar - Authenticated user:', {
            id: authenticatedUser.id,
            email: authenticatedUser.email,
            isWhopUser: authenticatedUser.isWhopUser,
            metadata: authenticatedUser.user_metadata
          });

          // Get profile data from database
          const { data: profile } = await supabase
            .from('profiles')
            .select('full_name, avatar_url')
            .eq('id', authenticatedUser.id)
            .single();

          console.log('🔍 AppSidebar - Profile data:', profile);

          if (isMounted) {
            // Determine display name with fallback priority
            let displayName = profile?.full_name ||
                            authenticatedUser.user_metadata?.full_name ||
                            authenticatedUser.user_metadata?.username ||
                            authenticatedUser.email?.split('@')[0] ||
                            'User';

            // Determine avatar with fallback priority
            let avatarUrl = profile?.avatar_url ||
                          authenticatedUser.user_metadata?.avatar_url ||
                          '';

            setUserData({
              name: displayName,
              email: authenticatedUser.email || '',
              avatar: avatarUrl,
              isOnline: true
            });

            console.log('✅ AppSidebar - User data set:', {
              name: displayName,
              email: authenticatedUser.email,
              hasAvatar: !!avatarUrl,
              isWhopUser: authenticatedUser.isWhopUser
            });
          }
        } else if (isMounted) {
          console.log('🚫 AppSidebar - No authenticated user found');
          setUserData({ name: '', email: '', avatar: '', isOnline: false });
        }
      } catch (error) {
        console.error('❌ AppSidebar - Error fetching user data:', error);
        if (isMounted) {
          setUserData({ name: '', email: '', avatar: '', isOnline: false });
        }
      }
    };

    fetchUserData();

    // Listen for auth changes - but only on sign out
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      if (event === 'SIGNED_OUT' && isMounted) {
        setUserData({ name: '', email: '', avatar: '', isOnline: false });
      }
      // Don't refetch on other auth events to prevent remounting
    });

    return () => {
      isMounted = false;
      subscription.unsubscribe();
    };
  }, []);

  // Persist minimizer state and notify parent
  useEffect(() => {
    localStorage.setItem('sidebar-minimized', JSON.stringify(isMinimized));
    onMinimizedChange?.(isMinimized);
  }, [isMinimized, onMinimizedChange]);



  const toggleMinimized = () => {
    setIsMinimized(!isMinimized);
  };

  // Main navigation items - reordered as requested
  const mainNavItems: SidebarItem[] = [
    {
      id: 'home',
      label: 'Headquarters',
      icon: <img
        src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/icons/dashboard.svg"
        alt="Dashboard"
        className="w-4 h-4"
      />,
      path: '/home'
    },
    {
      id: 'portfolio-builder',
      label: 'Portfolio Builder',
      icon: <PieChart className="w-4 h-4" />,
      path: '/portfolio-builder'
    }
  ];

  // Tools navigation items
  const toolsNavItems: SidebarItem[] = [
    {
      id: 'agent-builder',
      label: 'Builder',
      icon: <Hammer className="w-4 h-4" />,
      path: '/agent-builder'
    },
    {
      id: 'discover',
      label: 'Discover',
      icon: <img
        src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/icons//glboe.svg"
        alt="Discover"
        className="w-4 h-4"
      />,
      path: '/discover'
    },
    // {
    //   id: 'stock-search',
    //   label: 'Stock Search',
    //   icon: <img
    //     src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/icons/Search.svg"
    //     alt="Stock Search"
    //     className="w-4 h-4"
    //   />,
    //   path: '/stock-search'
    // },
    {
      id: 'stock-screener',
      label: 'Stock Screener',
      icon: <img
        src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/icons/Search.svg"
        alt="Stock Screener"
        className="w-4 h-4"
      />,
      path: '/stock-screener'
    },
    {
      id: 'stock-scanner',
      label: 'Stock Scanner',
      icon: <img
        src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/icons/Scan.svg"
        alt="Scan"
        className="w-4 h-4"
      />,
      path: '/agent-scanner'
    },
    {
      id: 'agent-backtesting',
      label: 'Backtesting',
      icon: <img
        src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/icons/Bar%20chart.svg"
        alt="Bar Chart"
        className="w-4 h-4"
      />,
      path: '/agent-backtesting'
    }
  ];





  const isActiveRoute = (path: string) => {
    if (path === '/home') {
      return location.pathname === '/' || location.pathname === '/home';
    }
    if (path === '/chat') {
      return location.pathname === '/chat' || location.pathname.startsWith('/chat/');
    }
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const handleNavigation = (path: string) => {
    console.log('AppSidebar handleNavigation:', { path, planType });

    // Check if it's mobile and trying to access builder
    if (isMobile && path.includes('/agent-builder')) {
      setShowMobileRestriction(true);
      return;
    }

    // No pages are locked - all users have access to all features

    console.log('Navigating to:', path);
    navigate(path);
  };



  return (
    <div
      className={cn(
        "h-screen flex flex-col relative fixed left-0 top-0 z-50",
        isMinimized ? "w-16" : "w-52"
      )}
    >
      {/* Solid Background */}
      <div
        className="absolute inset-0 z-0"
        style={{
          backgroundColor: '#141414'
        }}
      />


      {/* Logo/Brand Section with Collapse Button */}
      <div className={cn("relative z-10", isMinimized ? "px-3 py-4" : "px-4 py-5")}>
        <div className={cn("flex items-center", isMinimized ? "justify-center" : "justify-between")}>
          <div className={cn("flex items-center", isMinimized ? "justify-center" : "gap-2.5")}>
            <img
              src="http://thecodingkid.oyosite.com/logo_only.png"
              alt="Osis Logo"
              className="h-6 w-6 drop-shadow-sm flex-shrink-0"
            />
            {!isMinimized && (
              <span className="text-lg font-medium text-white tracking-tight font-sf-pro-display">Osis</span>
            )}
          </div>
          {!isMinimized && (
            <button
              onClick={toggleMinimized}
              className="p-1.5 rounded-lg transition-all duration-300 hover:bg-white/[0.08] text-white/70 hover:text-white"
              title="Collapse sidebar"
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      {/* Profile Section */}
      <div className={cn("relative z-10", isMinimized ? "px-3 py-3" : "px-4 py-4")}>
        <button
          onClick={() => handleNavigation('/settings')}
          className={cn(
            "w-full flex items-center rounded-lg text-left transition-all duration-300 group font-sans bg-white/[0.02] border border-white/[0.04] hover:bg-white/[0.04] hover:border-white/[0.06]",
            isMinimized ? "justify-center p-2" : "gap-3 p-2"
          )}
          title={isMinimized ? "Settings" : undefined}
        >
          {isMinimized ? (
            <div className="flex justify-center">
              <div className="relative">
                <img
                  src={userData.avatar}
                  alt={userData.name}
                  className="w-7 h-7 rounded-full object-cover"
                />
                {userData.isOnline && (
                  <div className="absolute bottom-0.5 right-0.5 w-2 h-2 bg-green-400 rounded-full shadow-[0_0_8px_rgba(0,255,136,0.6)]" />
                )}
              </div>
            </div>
          ) : (
            <>
              <div className="relative">
                <img
                  src={userData.avatar}
                  alt={userData.name}
                  className="w-9 h-9 rounded-full object-cover"
                />
                {userData.isOnline && (
                  <div className="absolute bottom-0.5 right-0.5 w-2.5 h-2.5 bg-green-400 rounded-full shadow-[0_0_8px_rgba(0,255,136,0.6)]" />
                )}
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-white font-medium text-sm truncate">{userData.name}</div>
                <div className="text-white/60 text-xs truncate">{userData.email}</div>
              </div>
              <div className="flex-shrink-0">
                <Settings className="w-4 h-4 text-white/40 group-hover:text-white/60 transition-colors duration-300" />
              </div>
            </>
          )}
        </button>
      </div>

      {/* Main Navigation - Scrollable Area */}
      <div className="flex-1 overflow-y-auto relative z-10 scrollbar-hide">

        {/* Build Agent Button - Always at Top of scrollable area */}
        {!isMinimized && (
          <div className="px-4 pt-6 pb-4">
            <button
              onClick={() => handleNavigation('/agent-builder/new')}
              className="w-full flex items-center gap-2.5 px-3 py-2.5 rounded-lg text-left transition-all duration-300 bg-white text-black hover:bg-gray-100 shadow-md"
            >
              <Plus className="w-4 h-4 text-black/80 flex-shrink-0" />
              <span className="font-semibold text-sm truncate">Build Agent</span>
            </button>
          </div>
        )}

        {/* Separator below Build Agent button */}
        {!isMinimized && (
          <div className="px-4 mb-2">
            <div className="border-t border-white/[0.1]"></div>
          </div>
        )}

        <div className="p-4 pt-2">
          <div className="space-y-2">
            {mainNavItems.map((item) => (
              <button
                key={item.id}
                onClick={() => handleNavigation(item.path)}
                className={cn(
                  "w-full flex items-center rounded-lg text-left transition-all duration-300 group font-sans border",
                  isMinimized ? "justify-center px-2 py-2.5" : "gap-2.5 px-3 py-2",
                  isActiveRoute(item.path)
                    ? "bg-white/[0.08] text-white shadow-[inset_0_2px_6px_rgba(0,0,0,0.15),inset_0_1px_0_rgba(255,255,255,0.1)] border-white/[0.05]"
                    : "text-white/70 hover:text-white hover:bg-white/[0.04] hover:shadow-[inset_0_1px_3px_rgba(0,0,0,0.1)] border-transparent"
                )}
                title={isMinimized ? item.label : undefined}
              >
                <div className={cn(
                  "flex-shrink-0 transition-colors duration-300",
                  isActiveRoute(item.path) ? "text-white" : "text-white/80 group-hover:text-white"
                )}>
                  {item.icon}
                </div>
                {!isMinimized && (
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-sm truncate">
                      {item.label}
                    </div>
                  </div>
                )}
              </button>
            ))}
          </div>

          {/* Tools Navigation */}
          <div className="mt-6">
            {!isMinimized && (
              <div className="px-2.5 mb-3">
                <span className="text-xs font-medium text-white/40 font-sans">Tools</span>
              </div>
            )}
            <div className="space-y-2">
              {toolsNavItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => handleNavigation(item.path)}
                  className={cn(
                    "w-full flex items-center rounded-lg text-left transition-all duration-300 group font-sans border",
                    isMinimized ? "justify-center px-2 py-2.5" : "gap-2.5 px-3 py-2",
                    isActiveRoute(item.path)
                      ? "bg-white/[0.08] text-white shadow-[inset_0_2px_6px_rgba(0,0,0,0.15),inset_0_1px_0_rgba(255,255,255,0.1)] border-white/[0.05]"
                      : "text-white/70 hover:text-white hover:bg-white/[0.04] hover:shadow-[inset_0_1px_3px_rgba(0,0,0,0.1)] border-transparent"
                  )}
                  title={isMinimized ? item.label : undefined}
                >
                  <div className={cn(
                    "flex-shrink-0 transition-colors duration-300",
                    isActiveRoute(item.path) ? "text-white" : "text-white/80 group-hover:text-white"
                  )}>
                    {item.icon}
                  </div>
                  {!isMinimized && (
                    <div className="flex-1 min-w-0 flex items-center justify-between">
                      <div className={cn(
                        "font-medium text-sm truncate"
                      )}>
                        {item.label}
                      </div>
                      {/* Add "New" badge to Agent Builder */}
                      {item.id === 'agent-builder' && (
                        <div className="flex items-center gap-1.5 ml-2 px-2.5 py-1 bg-green-500/20 border border-green-500/30 rounded-full">
                          <Sparkles className="w-3 h-3 text-green-400" />
                          <span className="text-xs font-medium text-green-400">New</span>
                        </div>
                      )}
                    </div>
                  )}
                </button>
              ))}
            </div>
          </div>







          {/* Spacer to push upgrade card to bottom */}
          <div className="flex-1"></div>


        </div>
      </div>

      {/* Minimized State Expand Button */}
      {isMinimized && (
        <div className="relative z-10 p-3">
          <button
            onClick={toggleMinimized}
            className="w-full flex items-center justify-center rounded-lg transition-all duration-300 group bg-white/[0.06] hover:bg-white/[0.08] border border-white/[0.05] py-2"
            title="Expand sidebar"
          >
            <ChevronRight className="w-4 h-4 text-white/70 group-hover:text-white transition-colors duration-300" />
          </button>
        </div>
      )}



      {/* Mobile Restriction Modal */}
      <MobileRestrictionModal
        isOpen={showMobileRestriction}
        onClose={() => setShowMobileRestriction(false)}
      />
    </div>
  );
};

export default AppSidebar;
