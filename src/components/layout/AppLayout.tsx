import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import AppSidebar from './AppSidebar';
import Header from './Header';
import { useMobileDetection } from '@/hooks/useMobileDetection';
import BottomNavigation from './BottomNavigation';
import WhopPageLayout from '@/components/whop/WhopPageLayout';
import { useWhopUser } from '@/contexts/WhopContext';

interface AppLayoutProps {
  children: React.ReactNode;
  loading?: boolean;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children, loading }) => {
  const location = useLocation();
  const [sidebarMinimized, setSidebarMinimized] = useState(false);
  const { isMobile } = useMobileDetection();
  const { isWhopUser } = useWhopUser();

  // Check if we're on pages that shouldn't have the sidebar
  const isAuthPage = location.pathname.includes('/auth') || location.pathname.includes('/login');
  const isTermsOrPrivacy = ['/terms', '/privacy'].includes(location.pathname);

  // Pages that should not use the new layout (removed subscription pages)
  const shouldUseOldLayout = isAuthPage || isTermsOrPrivacy;

  // If user is a Whop user, use the Whop layout instead
  if (isWhopUser) {
    return (
      <WhopPageLayout>
        {loading ? (
          <div className="flex items-center justify-center h-screen">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white/30"></div>
          </div>
        ) : (
          children
        )}
      </WhopPageLayout>
    );
  }

  if (shouldUseOldLayout) {
    // Use the old layout for these pages
    return (
      <div className="min-h-screen bg-[#0A0A0A]">
        <Header onNewChat={() => window.location.href = "/"} />
        <main className="pt-14">
          {children}
        </main>
      </div>
    );
  }

  return (
    <div className="h-screen bg-[#141414] overflow-hidden">
      {/* Desktop Sidebar - Fixed position */}
      {!isMobile && (
        <AppSidebar onMinimizedChange={setSidebarMinimized} />
      )}

      {/* Main Content Area - Full width on mobile, with margin on desktop */}
      <div
        className={`h-full relative transition-all duration-300 ease-in-out ${
          isMobile
            ? 'p-0' // No padding on mobile for full screen
            : 'p-4' // Padding on desktop for floating effect
        }`}
        style={{
          marginLeft: isMobile ? '0' : (sidebarMinimized ? '64px' : '208px')
        }}
      >
        {/* Main Content Container - Different styling for mobile vs desktop */}
        <div className={`h-full overflow-hidden ${
          isMobile
            ? 'bg-[#0A0A0A]' // No rounded corners on mobile
            : 'bg-[#0A0A0A] rounded-2xl shadow-2xl' // Floating effect on desktop
        }`}>
          {/* Main Content */}
          <main className={`h-full overflow-auto ${isMobile ? 'pb-20' : ''}`}> {/* Added bottom padding only for mobile */}
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white/30"></div>
              </div>
            ) : (
              <div className={`h-full ${
                isMobile
                  ? 'p-4' // Less padding on mobile
                  : 'p-6 rounded-2xl' // More padding and rounded corners on desktop
              }`}>
                {children}
              </div>
            )}
          </main>
        </div>
      </div>

      {/* Bottom Navigation - Mobile Only */}
      {isMobile && (
        <BottomNavigation />
      )}

      {/* Custom Styles for Enhanced Visual Effects */}
      <style>{`
        /* Enhanced shadow for floating effect */
        .shadow-2xl {
          box-shadow:
            0 25px 50px -12px rgba(0, 0, 0, 0.8),
            0 0 0 1px rgba(255, 255, 255, 0.05),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        /* Smooth transitions for all interactive elements */
        * {
          transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
          transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
          transition-duration: 200ms;
        }

        /* Custom scrollbar for main content */
        main::-webkit-scrollbar {
          width: 6px;
        }

        main::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.02);
        }

        main::-webkit-scrollbar-thumb {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 3px;
        }

        main::-webkit-scrollbar-thumb:hover {
          background: rgba(255, 255, 255, 0.15);
        }

        /* Hide scrollbar for sidebar */
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }

        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }

        /* Ensure proper layering */
        .z-10 {
          z-index: 10;
        }

        .z-20 {
          z-index: 20;
        }

        .z-50 {
          z-index: 50;
        }

        /* Subtle gradient overlay for depth */
        .bg-\\[\\#141414\\]::before {
          content: '';
          position: absolute;
          inset: 0;
          background: radial-gradient(circle at 50% 0%, rgba(255, 255, 255, 0.02) 0%, transparent 50%);
          pointer-events: none;
        }
      `}</style>
    </div>
  );
};

export default AppLayout;
