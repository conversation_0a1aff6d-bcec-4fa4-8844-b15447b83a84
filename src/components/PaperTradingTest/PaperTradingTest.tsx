import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { usePaperTrading } from '@/hooks/usePaperTrading';
import { Activity, DollarSign, TrendingUp, TrendingDown } from 'lucide-react';

const PaperTradingTest: React.FC = () => {
  const { 
    account, 
    positions, 
    orders, 
    loading, 
    placeOrder, 
    refreshPortfolio 
  } = usePaperTrading();

  const [testSymbol, setTestSymbol] = useState('AAPL');
  const [testQuantity, setTestQuantity] = useState('10');
  const [testPrice, setTestPrice] = useState('150.00');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleTestBuy = async () => {
    setIsSubmitting(true);
    try {
      await placeOrder({
        symbol: testSymbol,
        order_type: 'market',
        side: 'buy',
        quantity: parseFloat(testQuantity),
        time_in_force: 'DAY'
      });
    } catch (error) {
      console.error('Test buy failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleTestSell = async () => {
    setIsSubmitting(true);
    try {
      await placeOrder({
        symbol: testSymbol,
        order_type: 'market',
        side: 'sell',
        quantity: parseFloat(testQuantity),
        time_in_force: 'DAY'
      });
    } catch (error) {
      console.error('Test sell failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleTestLimitOrder = async () => {
    setIsSubmitting(true);
    try {
      await placeOrder({
        symbol: testSymbol,
        order_type: 'limit',
        side: 'buy',
        quantity: parseFloat(testQuantity),
        price: parseFloat(testPrice),
        time_in_force: 'GTC'
      });
    } catch (error) {
      console.error('Test limit order failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading paper trading system...</span>
      </div>
    );
  }

  if (!account) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-2">Paper Trading System</h1>
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
            <p className="text-yellow-800">
              Setting up your paper trading account... This may take a moment.
            </p>
          </div>
          <Button onClick={refreshPortfolio} disabled={loading}>
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Paper Trading System Test</h1>
        <p className="text-gray-600">Test the paper trading functionality</p>
      </div>

      {/* Account Summary */}
      {account && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="w-5 h-5" />
              Account Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  ${account.total_value.toLocaleString()}
                </div>
                <div className="text-sm text-gray-500">Total Value</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">
                  ${account.available_balance.toLocaleString()}
                </div>
                <div className="text-sm text-gray-500">Available Cash</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">
                  ${account.portfolio_value.toLocaleString()}
                </div>
                <div className="text-sm text-gray-500">Portfolio Value</div>
              </div>
              <div className="text-center">
                <div className={`text-2xl font-bold ${
                  account.total_pnl >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  ${account.total_pnl.toLocaleString()}
                </div>
                <div className="text-sm text-gray-500">Total P&L</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Test Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="w-5 h-5" />
            Test Trading Orders
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="symbol">Symbol</Label>
              <Input
                id="symbol"
                value={testSymbol}
                onChange={(e) => setTestSymbol(e.target.value.toUpperCase())}
                placeholder="AAPL"
              />
            </div>
            <div>
              <Label htmlFor="quantity">Quantity</Label>
              <Input
                id="quantity"
                type="number"
                value={testQuantity}
                onChange={(e) => setTestQuantity(e.target.value)}
                placeholder="10"
              />
            </div>
            <div>
              <Label htmlFor="price">Limit Price</Label>
              <Input
                id="price"
                type="number"
                value={testPrice}
                onChange={(e) => setTestPrice(e.target.value)}
                placeholder="150.00"
                step="0.01"
              />
            </div>
          </div>
          
          <div className="flex gap-4">
            <Button
              onClick={handleTestBuy}
              disabled={isSubmitting}
              className="bg-green-600 hover:bg-green-700"
            >
              <TrendingUp className="w-4 h-4 mr-2" />
              Test Market Buy
            </Button>
            <Button
              onClick={handleTestSell}
              disabled={isSubmitting}
              className="bg-red-600 hover:bg-red-700"
            >
              <TrendingDown className="w-4 h-4 mr-2" />
              Test Market Sell
            </Button>
            <Button
              onClick={handleTestLimitOrder}
              disabled={isSubmitting}
              variant="outline"
            >
              Test Limit Order
            </Button>
            <Button
              onClick={refreshPortfolio}
              disabled={loading}
              variant="outline"
            >
              Refresh Data
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Positions */}
      {positions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Current Positions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {positions.map((position) => (
                <div key={position.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-semibold text-lg">{position.symbol}</h3>
                    <div className="text-right">
                      <div className="font-semibold">
                        ${(position.market_value || 0).toLocaleString()}
                      </div>
                      <div className="text-sm text-gray-500">
                        {position.quantity.toLocaleString()} shares
                      </div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <div className="text-gray-500">Avg Cost</div>
                      <div className="font-medium">${position.avg_cost.toFixed(2)}</div>
                    </div>
                    <div>
                      <div className="text-gray-500">Market Price</div>
                      <div className="font-medium">
                        ${(position.market_price || 0).toFixed(2)}
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-500">Unrealized P&L</div>
                      <div className={`font-medium ${
                        (position.unrealized_pnl || 0) >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        ${(position.unrealized_pnl || 0).toLocaleString()}
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-500">Return %</div>
                      <div className={`font-medium ${
                        (position.unrealized_pnl_percent || 0) >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {(position.unrealized_pnl_percent || 0).toFixed(2)}%
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recent Orders */}
      {orders.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Recent Orders</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {orders.slice(0, 10).map((order) => (
                <div key={order.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold">{order.symbol}</h3>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        order.side === 'buy' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {order.side.toUpperCase()}
                      </span>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        order.status === 'filled' 
                          ? 'bg-blue-100 text-blue-800' 
                          : order.status === 'pending'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {order.status.toUpperCase()}
                      </span>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">
                        ${(order.total_value || 0).toLocaleString()}
                      </div>
                      <div className="text-sm text-gray-500">
                        {order.quantity.toLocaleString()} shares
                      </div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <div className="text-gray-500">Order Type</div>
                      <div className="font-medium capitalize">{order.order_type}</div>
                    </div>
                    <div>
                      <div className="text-gray-500">Price</div>
                      <div className="font-medium">
                        ${(order.avg_fill_price || order.price || 0).toFixed(2)}
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-500">Time in Force</div>
                      <div className="font-medium">{order.time_in_force}</div>
                    </div>
                    <div>
                      <div className="text-gray-500">Created</div>
                      <div className="font-medium">
                        {new Date(order.created_at).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default PaperTradingTest;
