import React, { useState } from 'react';
import { X, TrendingUp, TrendingDown, ChevronDown, ChevronUp, Clock, CheckCircle, XCircle, DollarSign } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { usePaperTrading, PlaceOrderRequest } from '@/hooks/usePaperTrading';
import { useToast } from '@/components/ui/use-toast';
import './TradingInterface.css';

interface TradingInterfaceProps {
  isOpen: boolean;
  onClose: () => void;
  symbol: string;
  currentPrice?: number;
  // Orders panel props - optional for backward compatibility
  showOrdersPanel?: boolean;
  setShowOrdersPanel?: (show: boolean) => void;
  orders?: Order[];
  setOrders?: (orders: Order[] | ((prev: Order[]) => Order[])) => void;
  positions?: Position[];
  setPositions?: (positions: Position[] | ((prev: Position[]) => Position[])) => void;
  // Props for pre-populating sell orders
  initialTab?: 'buy' | 'sell';
  initialQuantity?: string;
}

interface Order {
  id: string;
  symbol: string;
  type: 'buy' | 'sell';
  orderType: 'market' | 'limit' | 'stop';
  quantity: number;
  price: number;
  status: 'pending' | 'filled' | 'cancelled';
  timestamp: Date;
  purchaseType: 'shares' | 'dollars';
  timeInForce: 'DAY' | 'GTC' | 'IOC' | 'FOK';
}

interface Position {
  symbol: string;
  quantity: number;
  averagePrice: number;
  currentPrice: number;
  unrealizedPnL: number;
  unrealizedPnLPercent: number;
}

const TradingInterface: React.FC<TradingInterfaceProps> = ({
  isOpen,
  onClose,
  symbol,
  currentPrice = 0,
  setShowOrdersPanel: externalSetShowOrdersPanel,
  setOrders: externalSetOrders,
  setPositions: externalSetPositions,
  initialTab = 'buy',
  initialQuantity = '1'
}) => {
  const { toast } = useToast();
  const { account, positions: paperPositions, placeOrder } = usePaperTrading();
  const [activeTab, setActiveTab] = useState<'buy' | 'sell'>(initialTab);
  const [orderType, setOrderType] = useState<'market' | 'limit' | 'stop'>('market');
  const [quantity, setQuantity] = useState<string>(initialQuantity);
  const [price, setPrice] = useState<string>(currentPrice.toString());
  const [stopPrice, setStopPrice] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [editableSymbol, setEditableSymbol] = useState<string>(symbol);
  const [purchaseType, setPurchaseType] = useState<'shares' | 'dollars'>('shares');
  const [timeInForce, setTimeInForce] = useState<'DAY' | 'GTC' | 'IOC' | 'FOK'>('DAY');

  // Orders panel state setters - use external if provided, otherwise no-op
  const setShowOrdersPanel = externalSetShowOrdersPanel || (() => {});
  const setOrders = externalSetOrders || (() => {});
  const setPositions = externalSetPositions || (() => {});

  // Get current position for this symbol
  const currentPosition = paperPositions.find(p => p.symbol === editableSymbol);

  // Calculate buying power and estimated total
  const buyingPower = account?.available_balance || 201510.18;
  const estimatedTotal = parseFloat(quantity) * (orderType === 'market' ? currentPrice : parseFloat(price) || 0);

  const handleSubmit = async () => {
    setIsSubmitting(true);

    try {
      // Use paper trading system
      const orderData: PlaceOrderRequest = {
        symbol: editableSymbol,
        order_type: orderType,
        side: activeTab,
        quantity: parseFloat(quantity),
        time_in_force: timeInForce
      };

      // Add price for limit orders
      if (orderType === 'limit') {
        orderData.price = parseFloat(price);
      }

      // Add stop price for stop orders
      if (orderType === 'stop') {
        orderData.stop_price = parseFloat(stopPrice);
      }

      await placeOrder(orderData);

      // Show orders panel after successful trade
      if (externalSetShowOrdersPanel) {
        externalSetShowOrdersPanel(true);
      }

      // Reset form
      setQuantity('1');
      setPrice(currentPrice.toString());
      setStopPrice('');

      // Close interface after successful order
      onClose();
    } catch (error) {
      console.error('Order submission error:', error);
      toast({
        variant: "destructive",
        title: "Order Failed",
        description: error.message || 'Failed to place order',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const isFormValid = () => {
    const qty = parseFloat(quantity);
    const orderPrice = parseFloat(price);

    if (!qty || qty <= 0) return false;

    if (orderType === 'limit' && (!orderPrice || orderPrice <= 0)) return false;
    if (orderType === 'stop' && (!orderPrice || orderPrice <= 0)) return false;

    // Check buying power for buy orders
    if (activeTab === 'buy') {
      const orderValue = qty * (orderType === 'market' ? currentPrice : orderPrice);
      if (orderValue > buyingPower) return false;
    }

    // Check available shares for sell orders
    if (activeTab === 'sell' && currentPosition) {
      if (qty > currentPosition.quantity) return false;
    }

    return true;
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div 
        className="trading-interface-backdrop"
        onClick={onClose}
      />
      
      {/* Trading Sidebar */}
      <div className={`trading-interface-sidebar ${isOpen ? 'open' : ''}`}>
        {/* Header */}
        <div className="trading-interface-header">
          <div className="flex items-center gap-3">
            <div className="trading-symbol-badge">
              {editableSymbol}
            </div>
            <div className="trading-price-info">
              <span className="trading-current-price">${currentPrice.toFixed(2)}</span>
            </div>
          </div>
          <button
            onClick={onClose}
            className="trading-close-button"
            title="Close trading panel"
          >
            <X className="w-4 h-4" />
          </button>
        </div>



        {/* Trading Form */}
        <div className="trading-interface-content">
          {/* Buy/Sell Tabs */}
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'buy' | 'sell')}>
            <TabsList className="trading-tabs-list">
              <TabsTrigger value="buy" className="trading-tab-buy">
                <TrendingUp className="w-4 h-4 mr-2" />
                Buy
              </TabsTrigger>
              <TabsTrigger value="sell" className="trading-tab-sell">
                <TrendingDown className="w-4 h-4 mr-2" />
                Sell
              </TabsTrigger>
            </TabsList>

            <TabsContent value="buy" className="trading-tab-content">
              <TradingForm
                type="buy"
                symbol={editableSymbol}
                setSymbol={setEditableSymbol}
                orderType={orderType}
                setOrderType={setOrderType}
                quantity={quantity}
                setQuantity={setQuantity}
                price={price}
                setPrice={setPrice}
                stopPrice={stopPrice}
                setStopPrice={setStopPrice}
                currentPrice={currentPrice}
                buyingPower={buyingPower}
                estimatedTotal={estimatedTotal}
                purchaseType={purchaseType}
                setPurchaseType={setPurchaseType}
                timeInForce={timeInForce}
                setTimeInForce={setTimeInForce}
                isFormValid={isFormValid()}
                isSubmitting={isSubmitting}
                onSubmit={handleSubmit}
              />
            </TabsContent>

            <TabsContent value="sell" className="trading-tab-content">
              <TradingForm
                type="sell"
                symbol={editableSymbol}
                setSymbol={setEditableSymbol}
                orderType={orderType}
                setOrderType={setOrderType}
                quantity={quantity}
                setQuantity={setQuantity}
                price={price}
                setPrice={setPrice}
                stopPrice={stopPrice}
                setStopPrice={setStopPrice}
                currentPrice={currentPrice}
                buyingPower={buyingPower}
                estimatedTotal={estimatedTotal}
                purchaseType={purchaseType}
                setPurchaseType={setPurchaseType}
                timeInForce={timeInForce}
                setTimeInForce={setTimeInForce}
                isFormValid={isFormValid()}
                isSubmitting={isSubmitting}
                onSubmit={handleSubmit}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </>
  );
};

interface TradingFormProps {
  type: 'buy' | 'sell';
  symbol: string;
  setSymbol: (symbol: string) => void;
  orderType: 'market' | 'limit' | 'stop';
  setOrderType: (type: 'market' | 'limit' | 'stop') => void;
  quantity: string;
  setQuantity: (quantity: string) => void;
  price: string;
  setPrice: (price: string) => void;
  stopPrice: string;
  setStopPrice: (price: string) => void;
  currentPrice: number;
  buyingPower: number;
  estimatedTotal: number;
  purchaseType: 'shares' | 'dollars';
  setPurchaseType: (type: 'shares' | 'dollars') => void;
  timeInForce: 'DAY' | 'GTC' | 'IOC' | 'FOK';
  setTimeInForce: (time: 'DAY' | 'GTC' | 'IOC' | 'FOK') => void;
  isFormValid: boolean;
  isSubmitting: boolean;
  onSubmit: () => void;
}

const TradingForm: React.FC<TradingFormProps> = ({
  type,
  symbol,
  setSymbol,
  orderType,
  setOrderType,
  quantity,
  setQuantity,
  price,
  setPrice,
  stopPrice,
  setStopPrice,
  currentPrice,
  buyingPower,
  estimatedTotal,
  purchaseType,
  setPurchaseType,
  timeInForce,
  setTimeInForce,
  isFormValid,
  isSubmitting,
  onSubmit
}) => {
  return (
    <div className="trading-form">
      {/* Symbol Input */}
      <div className="trading-form-group">
        <Label htmlFor="symbol" className="trading-form-label">Symbol</Label>
        <Input
          id="symbol"
          type="text"
          placeholder="Enter symbol"
          value={symbol}
          onChange={(e) => setSymbol(e.target.value.toUpperCase())}
          className="trading-input"
        />
      </div>

      {/* Market Price Display */}
      <div className="trading-form-group">
        <div className="flex justify-between items-center">
          <Label className="trading-form-label">Market Price</Label>
          <span className="text-white font-semibold">${currentPrice.toFixed(2)}</span>
        </div>
      </div>

      {/* Quantity */}
      <div className="trading-form-group">
        <Label htmlFor="quantity" className="trading-form-label">
          {purchaseType === 'shares' ? 'Quantity' : 'Amount'}
        </Label>
        <Input
          id="quantity"
          type="number"
          placeholder="0"
          value={quantity}
          onChange={(e) => setQuantity(e.target.value)}
          className="trading-input"
          min="0"
          step={purchaseType === 'shares' ? '1' : '0.01'}
        />
      </div>

      {/* Order Type */}
      <div className="trading-form-group">
        <Label htmlFor="orderType" className="trading-form-label">Order Type</Label>
        <Select value={orderType} onValueChange={setOrderType}>
          <SelectTrigger className="trading-select">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="market">Market</SelectItem>
            <SelectItem value="limit">Limit</SelectItem>
            <SelectItem value="stop">Stop</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Choose how to buy */}
      <div className="trading-form-group">
        <Label className="trading-form-label">Choose how to buy</Label>
        <RadioGroup
          value={purchaseType}
          onValueChange={setPurchaseType}
          className="flex flex-row gap-6 mt-2"
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="shares" id="shares" className="border-white/30 text-blue-500" />
            <Label htmlFor="shares" className="text-white/80 cursor-pointer">Shares</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="dollars" id="dollars" className="border-white/30 text-blue-500" />
            <Label htmlFor="dollars" className="text-white/80 cursor-pointer">Dollars</Label>
          </div>
        </RadioGroup>
      </div>

      {/* Time in Force */}
      <div className="trading-form-group">
        <Label htmlFor="timeInForce" className="trading-form-label">Time in Force</Label>
        <Select value={timeInForce} onValueChange={setTimeInForce}>
          <SelectTrigger className="trading-select">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="DAY">DAY</SelectItem>
            <SelectItem value="GTC">GTC</SelectItem>
            <SelectItem value="IOC">IOC</SelectItem>
            <SelectItem value="FOK">FOK</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Price (for limit/stop orders) */}
      {orderType !== 'market' && (
        <div className="trading-form-group">
          <Label htmlFor="price" className="trading-form-label">
            {orderType === 'limit' ? 'Limit Price' : 'Stop Price'}
          </Label>
          <Input
            id="price"
            type="number"
            placeholder={currentPrice.toFixed(2)}
            value={price}
            onChange={(e) => setPrice(e.target.value)}
            className="trading-input"
            min="0"
            step="0.01"
          />
        </div>
      )}

      {/* Order Summary */}
      <div className="trading-form-group">
        <div className="flex justify-between items-center">
          <Label className="trading-form-label">Estimated Cost</Label>
          <span className="text-white font-semibold">
            ${isNaN(estimatedTotal) ? '0.00' : estimatedTotal.toFixed(2)}
          </span>
        </div>
      </div>

      {/* Buying Power */}
      <div className="trading-form-group">
        <div className="flex justify-between items-center">
          <Label className="trading-form-label">Buying Power</Label>
          <span className="text-white font-semibold">
            ${buyingPower.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
          </span>
        </div>
      </div>

      {/* Submit Button */}
      <Button
        onClick={onSubmit}
        disabled={!isFormValid || isSubmitting}
        className={`trading-submit-button ${type === 'buy' ? 'buy' : 'sell'}`}
        size="lg"
      >
        {isSubmitting ? (
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
            Submitting...
          </div>
        ) : (
          'Review Order'
        )}
      </Button>
    </div>
  );
};

// Orders Panel Component
interface OrdersPanelProps {
  isOpen: boolean;
  onClose: () => void;
  orders: Order[];
  positions: Position[];
  onSellPosition?: (symbol: string, quantity: number, currentPrice: number) => void;
}

const OrdersPanel: React.FC<OrdersPanelProps> = ({
  isOpen,
  onClose,
  orders,
  positions,
  onSellPosition
}) => {
  const {
    account,
    positions: paperPositions,
    orders: paperOrders,
    refreshPortfolio,
    refreshAccount
  } = usePaperTrading();
  const [activeTab, setActiveTab] = useState<'orders' | 'positions' | 'account'>('orders');
  const [isMinimized, setIsMinimized] = useState<boolean>(false);
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);

  if (!isOpen) return null;

  // Handle refresh button click
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await Promise.all([refreshPortfolio(), refreshAccount()]);
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'filled':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'partially_filled':
        return <Clock className="w-4 h-4 text-blue-500" />;
      case 'cancelled':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'rejected':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'expired':
        return <XCircle className="w-4 h-4 text-gray-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const formatTime = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).format(date);
  };

  return (
    <div className={`orders-panel ${isOpen ? 'open' : ''} ${isMinimized ? 'minimized' : ''}`}>
      {/* Header */}
      <div className="orders-panel-header">
        <div className="orders-panel-header-content">
          <h3 className="orders-panel-title">Orders & Positions</h3>
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'orders' | 'positions' | 'account')}>
            <TabsList className="orders-panel-tabs">
              <TabsTrigger value="orders" className="orders-panel-tab">
                Orders ({paperOrders?.length || 0})
              </TabsTrigger>
              <TabsTrigger value="positions" className="orders-panel-tab">
                Positions ({paperPositions?.length || 0})
              </TabsTrigger>
              <TabsTrigger value="account" className="orders-panel-tab">
                <DollarSign className="w-4 h-4 mr-1" />
                Account
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={handleRefresh}
            className="orders-panel-control-button"
            title="Refresh data"
            disabled={isRefreshing}
          >
            {isRefreshing ? (
              <div className="animate-spin">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M21 12a9 9 0 11-6.219-8.56"/>
                </svg>
              </div>
            ) : (
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                <path d="M21 3v5h-5"/>
                <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                <path d="M3 21v-5h5"/>
              </svg>
            )}
          </button>
          <button
            onClick={() => setIsMinimized(!isMinimized)}
            className="orders-panel-control-button"
            title={isMinimized ? "Expand panel" : "Minimize panel"}
          >
            {isMinimized ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
          </button>
        </div>
      </div>

      {/* Content */}
      {!isMinimized && (
        <div className="orders-panel-content">
          <Tabs value={activeTab}>
            <TabsContent value="orders" className="orders-panel-tab-content">
              {!paperOrders || paperOrders.length === 0 ? (
                <div className="orders-panel-empty">
                  <p className="text-white/60">No orders yet</p>
                </div>
              ) : (
                <div className="orders-list">
                  {paperOrders.map((order) => (
                    <Card key={order.id} className="order-card">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            {getStatusIcon(order.status)}
                            <div>
                              <div className="flex items-center gap-2">
                                <span className="font-semibold text-white">{order.symbol}</span>
                                <span className={`order-type-badge ${order.side}`}>
                                  {order.side.toUpperCase()}
                                </span>
                                <span className="text-white/60 text-sm">
                                  {order.order_type.toUpperCase()}
                                </span>
                              </div>
                              <div className="text-sm text-white/60">
                                {order.quantity} shares @ {formatCurrency(order.avg_fill_price || order.price || 0)}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-white font-semibold">
                              {formatCurrency(order.total_value || (order.quantity * (order.avg_fill_price || order.price || 0)))}
                            </div>
                            <div className="text-xs text-white/60">
                              {formatTime(new Date(order.created_at))}
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="positions" className="orders-panel-tab-content">
              {!paperPositions || paperPositions.length === 0 ? (
                <div className="orders-panel-empty">
                  <p className="text-white/60">No positions yet</p>
                </div>
              ) : (
                <div className="positions-list">
                  {paperPositions.map((position) => (
                    <Card key={position.symbol} className="position-card">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <span className="font-semibold text-white">{position.symbol}</span>
                              <span className="text-white/60 text-sm">
                                {position.quantity} shares
                              </span>
                            </div>
                            <div className="text-sm text-white/60">
                              Avg: {formatCurrency(position.avg_cost)} |
                              Current: {formatCurrency(position.market_price || 0)}
                            </div>
                          </div>
                          <div className="flex items-center gap-3">
                            <div className="text-right">
                              <div className={`font-semibold ${(position.unrealized_pnl || 0) >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                                {(position.unrealized_pnl || 0) >= 0 ? '+' : ''}{formatCurrency(position.unrealized_pnl || 0)}
                              </div>
                              <div className={`text-sm ${(position.unrealized_pnl_percent || 0) >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                                {(position.unrealized_pnl_percent || 0) >= 0 ? '+' : ''}{(position.unrealized_pnl_percent || 0).toFixed(2)}%
                              </div>
                            </div>
                            {onSellPosition && (
                              <Button
                                onClick={() => onSellPosition(position.symbol, position.quantity, position.market_price || 0)}
                                size="sm"
                                variant="outline"
                                className="bg-red-600 hover:bg-red-700 border-red-600 hover:border-red-700 text-white text-xs px-3 py-1 h-7"
                              >
                                Sell
                              </Button>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="account" className="orders-panel-tab-content">
              {account ? (
                <div className="space-y-4">
                  {/* Account Summary */}
                  <Card className="trading-account-summary">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm flex items-center gap-2 text-white">
                        <DollarSign className="w-4 h-4" />
                        Account Summary
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <div className="text-white/60">Total Value</div>
                          <div className="font-semibold text-white text-lg">${account.total_value.toLocaleString()}</div>
                        </div>
                        <div>
                          <div className="text-white/60">Available Cash</div>
                          <div className="font-semibold text-white text-lg">${account.available_balance.toLocaleString()}</div>
                        </div>
                        <div>
                          <div className="text-white/60">Portfolio Value</div>
                          <div className="font-semibold text-white text-lg">${account.portfolio_value.toLocaleString()}</div>
                        </div>
                        <div>
                          <div className="text-white/60">Total P&L</div>
                          <div className={`font-semibold text-lg ${account.total_pnl >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                            {account.total_pnl >= 0 ? '+' : ''}${account.total_pnl.toLocaleString()}
                          </div>
                        </div>
                      </div>

                      <div className="mt-4 pt-4 border-t border-white/10">
                        <div className="flex justify-between items-center">
                          <span className="text-white/60">Return on Investment</span>
                          <span className={`font-semibold ${account.total_pnl >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                            {account.total_pnl >= 0 ? '+' : ''}{((account.total_pnl / account.starting_balance) * 100).toFixed(2)}%
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <div className="orders-panel-empty">
                  <p className="text-white/60">Loading account information...</p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      )}
    </div>
  );
};

export default TradingInterface;
export { OrdersPanel };
export type { Order, Position };
