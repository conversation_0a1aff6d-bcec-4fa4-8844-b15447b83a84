import { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { getSelectedPlanType, PLAN_TYPES } from '@/utils/planUtils';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { motion, AnimatePresence } from "framer-motion";
import { ArrowRight } from "lucide-react";

// Typing Animation Component
const TypingAnimation = ({ onComplete }: { onComplete?: () => void }) => {
  const [displayedText, setDisplayedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [phase, setPhase] = useState(1);
  const firstPhrase = "Investing is hard...";
  const secondPhrase = "without Osis";

  useEffect(() => {
    // Phase 1: Type "Investing is hard..."
    if (phase === 1 && currentIndex < firstPhrase.length) {
      const timer = setTimeout(() => {
        setDisplayedText(prev => prev + firstPhrase[currentIndex]);
        setCurrentIndex(prev => prev + 1);
      }, 60);

      return () => clearTimeout(timer);
    }
    // Transition to phase 2 after finishing first phrase
    else if (phase === 1 && currentIndex === firstPhrase.length) {
      const pause = setTimeout(() => {
        setPhase(2);
        setCurrentIndex(0);
      }, 1000); // Pause for one second after first phrase

      return () => clearTimeout(pause);
    }
    // Phase 2: Type "without Osis :)"
    else if (phase === 2 && currentIndex < secondPhrase.length) {
      const timer = setTimeout(() => {
        setDisplayedText(prev => prev + secondPhrase[currentIndex]);
        setCurrentIndex(prev => prev + 1);
      }, 60);

      return () => clearTimeout(timer);
    }
    // Complete animation after both phrases
    else if (phase === 2 && currentIndex === secondPhrase.length && onComplete) {
      const completeTimer = setTimeout(onComplete, 600);
      return () => clearTimeout(completeTimer);
    }
  }, [currentIndex, phase, onComplete]);

  return (
    <motion.h2
      className="text-3xl text-white font-medium text-center"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.4 }}
    >
      {displayedText}
      <motion.span
        animate={{
          opacity: [0, 1, 0],
        }}
        transition={{
          duration: 0.8,
          repeat: Infinity,
          repeatType: "loop"
        }}
        className="inline-block ml-1 w-[2px] h-6 bg-white/70"
      />
    </motion.h2>
  );
};

const AuthModal = ({ skipIntro = false }: { skipIntro?: boolean }) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isSignUp, setIsSignUp] = useState(false);
  const [acceptedTerms, setAcceptedTerms] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    name: ''
  });
  const [showLogo, setShowLogo] = useState(false);
  const [introAnimationComplete, setIntroAnimationComplete] = useState(skipIntro);

  // Listen for auth state changes
  useEffect(() => {
    return () => {}; // Clean up
  }, [navigate, toast]);

  const handleTypingComplete = () => {
    // Complete the intro animation after a professional pause
    setTimeout(() => {
      setIntroAnimationComplete(true);
    }, 1000);
  };

  const handleEmailAuth = async (e: React.FormEvent) => {
    e.preventDefault();

    // If trying to sign up, direct users to use Google authentication instead
    if (isSignUp) {
      toast({
        title: "Google Sign Up Required",
        description: "Please use Google authentication to create a new account."
      });
      return;
    }

    if (!acceptedTerms) {
      toast({
        variant: "destructive",
        title: "Terms Required",
        description: "You must accept the Terms of Service to continue."
      });
      return;
    }

    setIsLoading(true);

    try {
      // Only allow email/password for sign in (not sign up)
      const { data, error } = await supabase.auth.signInWithPassword({
        email: formData.email,
        password: formData.password
      });
      if (error) throw error;

      if (data?.session) {
        navigate('/');
      }
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleAuth = async () => {
    if (isSignUp && !acceptedTerms) {
      toast({
        variant: "destructive",
        title: "Terms Required",
        description: "You must accept the Terms of Service to continue."
      });
      return;
    }

    setIsLoading(true);
    try {
      // Get the current URL to redirect back to exactly where the user was
      const currentPath = window.location.pathname + window.location.search;

      // Check if there's a selected plan type to preserve through auth flow
      const selectedPlanType = getSelectedPlanType();
      let redirectUrl = `${window.location.origin}${currentPath}`;

      // Add the plan type to the redirect URL if it exists
      if (selectedPlanType === PLAN_TYPES.BASIC) {
        // Add basic parameter to the URL
        redirectUrl = redirectUrl + (redirectUrl.includes('?') ? '&basic' : '?basic');
      } else if (selectedPlanType === PLAN_TYPES.PRO) {
        // Add pro parameter to the URL
        redirectUrl = redirectUrl + (redirectUrl.includes('?') ? '&pro' : '?pro');
      }

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: redirectUrl,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent'
          }
        }
      });

      if (error) throw error;

      // The browser will be redirected to Google automatically
      // When it comes back, the auth state change listener will handle profile and tokens creation
    } catch (error: any) {
      console.error('Google auth error:', error);
      toast({
        variant: "destructive",
        title: "Authentication Error",
        description: error.message
      });
      setIsLoading(false);
    }
  };

  // We're now accepting new users, so we don't need to show the NotAcceptingNewUsers component

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm p-4">
      <motion.div
        className="relative w-full max-w-md bg-[#121212]/60 rounded-2xl shadow-2xl border border-white/[0.08] overflow-hidden backdrop-blur-sm"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{
          opacity: 1,
          scale: 1,
          boxShadow: "0 0 0 1px rgba(255, 255, 255, 0.08), 0 10px 40px rgba(0, 0, 0, 0.6), 0 0 25px rgba(0, 0, 0, 0.5)"
        }}
        exit={{ opacity: 0, scale: 0.95 }}
        transition={{ duration: 0.4 }}
      >
        <AnimatePresence mode="wait">
          {!introAnimationComplete ? (
            <motion.div
              key="introAnimation"
              className="absolute inset-0 flex items-center justify-center bg-[#121212]/60 backdrop-blur-sm z-20"
              exit={{
                opacity: 0,
                transition: {
                  duration: 0.8,
                  ease: [0.19, 1, 0.22, 1]
                }
              }}
            >
              <div className="flex flex-col items-center justify-center px-8">
                <TypingAnimation onComplete={handleTypingComplete} />
              </div>
            </motion.div>
          ) : null}
        </AnimatePresence>

        <motion.div
          className="p-6"
          initial={{ opacity: 0 }}
          animate={{
            opacity: introAnimationComplete ? 1 : 0,
            y: introAnimationComplete ? 0 : 5
          }}
          transition={{
            duration: 0.7,
            ease: [0.19, 1, 0.22, 1],
            delay: 0.1
          }}
        >
          <div className="text-center mb-6">
            <div className="mx-auto w-12 h-12 mb-4 opacity-90">
              <img
                src="http://thecodingkid.oyosite.com/logo_only.png"
                alt="Osis Logo"
                className="w-full h-full object-contain"
                style={{ filter: "drop-shadow(0 0 10px rgba(255,255,255,0.12))" }}
              />
            </div>
            <h2 className="text-white text-[24px] font-medium mb-2 tracking-tight">
              {isSignUp ? 'Create account' : 'Welcome back'}
            </h2>
            <p className="text-white/60 text-[14px] leading-relaxed">
              {isSignUp
                ? 'Create your account with Google to start trading with confidence'
                : 'Sign in to continue your trading journey'}
            </p>
          </div>

          <form onSubmit={handleEmailAuth} className="space-y-4">
            {/* Only show email/password form when not in sign-up mode */}
            {!isSignUp && (
              <>
                <div className="space-y-1">
                  <Label htmlFor="email" className="text-sm text-white/50 font-normal">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                    required
                    className="bg-[#1A1A1A]/80 border-white/[0.05] focus:border-white/[0.2] focus:ring-0 placeholder:text-white/15 h-10 rounded-lg text-white/90"
                    disabled={isLoading}
                  />
                </div>
                <div className="space-y-1">
                  <Label htmlFor="password" className="text-sm text-white/50 font-normal">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="••••••••"
                    value={formData.password}
                    onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                    required
                    className="bg-[#1A1A1A]/80 border-white/[0.05] focus:border-white/[0.2] focus:ring-0 placeholder:text-white/15 h-10 rounded-lg text-white/90"
                    disabled={isLoading}
                  />
                </div>
              </>
            )}
            {isSignUp && (
              <div className="text-center py-2">
                <p className="text-white/60 text-sm">Please use Google authentication to create a new account</p>
              </div>
            )}

            {/* Terms Checkbox */}
            <div className="space-y-2">
              {(isSignUp || true) && (
                <div className="flex items-start space-x-2">
                  <Checkbox
                    id="terms"
                    checked={acceptedTerms}
                    onCheckedChange={(checked) => setAcceptedTerms(checked as boolean)}
                    className="mt-0.5"
                    disabled={isLoading}
                  />
                  <label
                    htmlFor="terms"
                    className="text-sm text-white/50 leading-tight cursor-pointer"
                  >
                    I agree to the{" "}
                    <Link
                      to="/terms"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-white/70 hover:text-white/90 underline underline-offset-2 decoration-white/20 hover:decoration-white/40"
                      onClick={(e) => e.stopPropagation()}
                    >
                      Terms
                    </Link>
                    {" "}and{" "}
                    <Link
                      to="/privacy"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-white/70 hover:text-white/90 underline underline-offset-2 decoration-white/20 hover:decoration-white/40"
                      onClick={(e) => e.stopPropagation()}
                    >
                      Privacy Policy
                    </Link>
                  </label>
                </div>
              )}
            </div>

            {/* Only show submit button for sign-in, not sign-up */}
            {!isSignUp && (
              <Button
                type="submit"
                className="w-full h-10 bg-[#1E1E1E] hover:bg-[#252525] text-white/90 text-sm rounded-lg border border-white/[0.08] transition-all duration-200 mt-4 font-normal disabled:opacity-50"
                disabled={isLoading || !acceptedTerms}
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <div className="h-4 w-4 rounded-full border-2 border-white/10 border-t-white/60 animate-spin mr-2" />
                    <span>Processing...</span>
                  </div>
                ) : (
                  <div className="flex items-center justify-center">
                    <span>Sign in</span>
                    <ArrowRight className="ml-2 h-4 w-4 opacity-70" />
                  </div>
                )}
              </Button>
            )}
          </form>

          <div className="relative my-4">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-white/[0.06]"></div>
            </div>
            <div className="relative flex justify-center text-xs">
              <span className="bg-[#121212]/60 px-3 text-white/30 uppercase tracking-wider text-[10px]">or</span>
            </div>
          </div>

          <Button type="button" variant="outline"
            className="w-full bg-[#1A1A1A]/80 border-white/[0.06] hover:bg-[#1D1D1D] text-white/80 flex items-center justify-center gap-2 h-10 relative overflow-hidden group rounded-lg transition-all duration-200"
            onClick={handleGoogleAuth}
            disabled={isLoading || (isSignUp && !acceptedTerms)}>
            <svg viewBox="0 0 24 24" width="18" height="18" xmlns="http://www.w3.org/2000/svg">
              <g transform="matrix(1, 0, 0, 1, 27.009001, -39.238998)">
                <path fill="#4285F4" d="M -3.264 51.509 C -3.264 50.719 -3.334 49.969 -3.454 49.239 L -14.754 49.239 L -14.754 53.749 L -8.284 53.749 C -8.574 55.229 -9.424 56.479 -10.684 57.329 L -10.684 60.329 L -6.824 60.329 C -4.564 58.239 -3.264 55.159 -3.264 51.509 Z"/>
                <path fill="#34A853" d="M -14.754 63.239 C -11.514 63.239 -8.804 62.159 -6.824 60.329 L -10.684 57.329 C -11.764 58.049 -13.134 58.489 -14.754 58.489 C -17.884 58.489 -20.534 56.379 -21.484 53.529 L -25.464 53.529 L -25.464 56.619 C -23.494 60.539 -19.444 63.239 -14.754 63.239 Z"/>
                <path fill="#FBBC05" d="M -21.484 53.529 C -21.734 52.809 -21.864 52.039 -21.864 51.239 C -21.864 50.439 -21.724 49.669 -21.484 48.949 L -21.484 45.859 L -25.464 45.859 C -26.284 47.479 -26.754 49.299 -26.754 51.239 C -26.754 53.179 -26.284 54.999 -25.464 56.619 L -21.484 53.529 Z"/>
                <path fill="#EA4335" d="M -14.754 43.989 C -12.984 43.989 -11.404 44.599 -10.154 45.789 L -6.734 42.369 C -8.804 40.429 -11.514 39.239 -14.754 39.239 C -19.444 39.239 -23.494 41.939 -25.464 45.859 L -21.484 48.949 C -20.534 46.099 -17.884 43.989 -14.754 43.989 Z"/>
              </g>
            </svg>
            <span className="text-sm font-normal">{isSignUp ? 'Sign up with Google' : 'Continue with Google'}</span>
          </Button>

          <div className="mt-4 text-center">
            <button
              type="button"
              onClick={() => setIsSignUp(!isSignUp)}
              className="text-white/50 hover:text-white/80 text-sm transition-colors duration-200"
              disabled={isLoading}
            >
              {isSignUp ? 'Already have an account? Sign in' : "Don't have an account? Sign up"}
            </button>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default AuthModal;