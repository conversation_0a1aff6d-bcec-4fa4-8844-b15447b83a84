import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { User, LogOut, CreditCard, Settings, Info, BarChart2, FileText, Shield } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { supabase } from "@/integrations/supabase/client";
import MessageCounter from '@/components/chat/MessageCounter';
import { useUserLimits } from '@/hooks/useUserLimits';
import { useAuth } from '@/contexts/AuthContext';

const AuthButton = () => {
  const navigate = useNavigate();
  const [user, setUser] = useState<any>(null);
  const { messagesRemaining, messagesLimit, planType } = useUserLimits();
  const { signOut } = useAuth();

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null);
    });

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user ?? null);
    });

    return () => subscription.unsubscribe();
  }, []);

  const handleSignOut = async (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent default button behavior
    e.stopPropagation(); // Stop event propagation

    try {
      // Use the signOut function from AuthContext
      await signOut();
      // Navigate to auth page after successful sign out
      navigate('/auth');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  if (!user) {
    return (
      <Button
        variant="ghost"
        size="sm"
        onClick={() => navigate('/auth')}
        className="text-white/70 h-6 w-6 p-0"
      >
        <User className="h-3.5 w-3.5" />
      </Button>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="relative h-6 w-6 p-0">
          <Settings className="h-4 w-4 text-white/70 hover:text-white transition-colors" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" side="top" className="w-[280px] bg-[#141414] border-white/10 mb-2">
        <div className="px-3 pt-3 pb-2">
          <MessageCounter
            tokensRemaining={messagesRemaining}
            maxTokens={messagesLimit}
            subscriptionPlan={planType}
          />
        </div>
        <DropdownMenuSeparator className="bg-white/5" />
        <div className="px-3 py-2">
          <p className="text-xs text-white/50 font-sans">
            {user.email}
          </p>
        </div>
        <DropdownMenuSeparator className="bg-white/5" />
        <div className="p-1">
          <DropdownMenuItem
            onClick={() => navigate('/subscription/manage')}
            className="text-white/70 focus:bg-white/5 focus:text-white cursor-pointer font-sans text-sm tracking-wide px-2 py-1.5 rounded-sm"
          >
            <CreditCard className="mr-2 h-4 w-4" />
            Manage Subscription
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => navigate('/settings')}
            className="text-white/70 focus:bg-white/5 focus:text-white cursor-pointer font-sans text-sm tracking-wide px-2 py-1.5 rounded-sm"
          >
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => navigate('/about')}
            className="text-white/70 focus:bg-white/5 focus:text-white cursor-pointer font-sans text-sm tracking-wide px-2 py-1.5 rounded-sm"
          >
            <Info className="mr-2 h-4 w-4" />
            About
          </DropdownMenuItem>
          <DropdownMenuLabel className="text-xs text-white/40 font-sans px-2 py-1 mt-1">Investing</DropdownMenuLabel>
          <DropdownMenuItem
            onClick={() => navigate('/portfolio-builder')}
            className="text-white/70 focus:bg-white/5 focus:text-white cursor-pointer font-sans text-sm tracking-wide px-2 py-1.5 rounded-sm"
          >
            <BarChart2 className="mr-2 h-4 w-4" />
            Portfolio Builder
          </DropdownMenuItem>
          <DropdownMenuSeparator className="bg-white/5 my-1" />
          <DropdownMenuLabel className="text-xs text-white/40 font-sans px-2 py-1 mt-1">Legal</DropdownMenuLabel>
          <DropdownMenuItem
            onClick={() => navigate('/terms')}
            className="text-white/70 focus:bg-white/5 focus:text-white cursor-pointer font-sans text-sm tracking-wide px-2 py-1.5 rounded-sm"
          >
            <FileText className="mr-2 h-4 w-4" />
            Terms of Service
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => navigate('/privacy')}
            className="text-white/70 focus:bg-white/5 focus:text-white cursor-pointer font-sans text-sm tracking-wide px-2 py-1.5 rounded-sm"
          >
            <Shield className="mr-2 h-4 w-4" />
            Privacy Policy
          </DropdownMenuItem>
          <DropdownMenuSeparator className="bg-white/5 my-1" />
          <DropdownMenuItem
            onSelect={(e) => {
              e.preventDefault();
              handleSignOut(e as unknown as React.MouseEvent);
            }}
            className="text-white/70 focus:bg-white/5 focus:text-white cursor-pointer font-sans text-sm tracking-wide px-2 py-1.5 rounded-sm"
          >
            <LogOut className="mr-2 h-4 w-4" />
            Sign Out
          </DropdownMenuItem>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default AuthButton;
