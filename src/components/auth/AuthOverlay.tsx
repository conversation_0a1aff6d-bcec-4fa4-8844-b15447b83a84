import { useState } from 'react';
import { Card } from "@/components/ui/card";
import { getSelectedPlanType, PLAN_TYPES } from '@/utils/planUtils';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Checkbox } from "@/components/ui/checkbox";

interface AuthOverlayProps {
  onClose: () => void;
}

const AuthOverlay = ({ onClose }: AuthOverlayProps) => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isSignUp, setIsSignUp] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    name: ''
  });
  const [acceptedTerms, setAcceptedTerms] = useState(false);

  const handleEmailAuth = async (e: React.FormEvent) => {
    e.preventDefault();

    if (isSignUp && !acceptedTerms) {
      toast({
        variant: "destructive",
        title: "Terms Required",
        description: "You must accept the Terms of Service to continue."
      });
      return;
    }

    setIsLoading(true);

    try {
      if (isSignUp) {
        const { error } = await supabase.auth.signUp({
          email: formData.email,
          password: formData.password,
          options: {
            data: {
              full_name: formData.name
            }
          }
        });
        if (error) throw error;

        // Create user_tokens record
        const { data: userData } = await supabase.auth.getUser();
        if (userData?.user) {
          const { error: tokensError } = await supabase
            .from('user_tokens')
            .upsert({
              user_id: userData.user.id,
              tokens_remaining: 0, // Default token limit for free tier
              last_reset: new Date().toISOString(),
              last_reset_date: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }, {
              onConflict: 'user_id'
            });

          if (tokensError) throw tokensError;
        }

        toast({
          title: "Success!",
          description: "Account created successfully!"
        });
        onClose();
      } else {
        const { error: signInError, data } = await supabase.auth.signInWithPassword({
          email: formData.email,
          password: formData.password
        });

        if (signInError) throw signInError;

        // Create user_tokens record
        if (data.user) {
          const { error: tokensError } = await supabase
            .from('user_tokens')
            .upsert({
              user_id: data.user.id,
              tokens_remaining: 0, // Default token limit for free tier
              last_reset: new Date().toISOString(),
              last_reset_date: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }, {
              onConflict: 'user_id'
            });

          if (tokensError) throw tokensError;
        }

        toast({
          title: "Success!",
          description: "You have been signed in successfully."
        });
        onClose();
      }
    } catch (error: any) {
      console.error("Auth error:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleAuth = async () => {
    setIsLoading(true);
    try {
      // Get the current URL to redirect back to exactly where the user was
      const currentPath = window.location.pathname + window.location.search;

      // Check if there's a selected plan type to preserve through auth flow
      const selectedPlanType = getSelectedPlanType();
      let redirectUrl = `${window.location.origin}${currentPath}`;

      // Add the plan type to the redirect URL if it exists
      if (selectedPlanType === PLAN_TYPES.BASIC) {
        // Add basic parameter to the URL
        redirectUrl = redirectUrl + (redirectUrl.includes('?') ? '&basic' : '?basic');
      } else if (selectedPlanType === PLAN_TYPES.PRO) {
        // Add pro parameter to the URL
        redirectUrl = redirectUrl + (redirectUrl.includes('?') ? '&pro' : '?pro');
      }

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: redirectUrl,
          queryParams: {
            prompt: 'select_account',
            access_type: 'offline',  // Request a refresh token for long term auth
          }
        }
      });

      if (error) throw error;

      // If we have data, the redirect will happen automatically
      if (data) {
        // Close the overlay before the redirect
        onClose();
      }
    } catch (error: any) {
      console.error("Google auth error:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message
      });
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-[999] flex items-center justify-center p-4">
      <Card className="w-full max-w-md p-6 bg-[#141414] border-white/10 shadow-2xl animate-in fade-in-0 zoom-in-95">
        <h2 className="text-2xl font-medium text-white text-center mb-2">
          Welcome back
        </h2>
        <p className="text-white/70 text-base text-center mb-5">
          Sign in to continue your trading journey
        </p>

        {/* Google Auth Button */}
        <Button
          type="button"
          variant="outline"
          className="w-full mb-4 bg-[#1A1A1A] border-white/10 hover:bg-[#252525] text-white flex items-center justify-center gap-2"
          onClick={handleGoogleAuth}
          disabled={isLoading}
        >
          {/* Google icon */}
          <svg viewBox="0 0 24 24" width="20" height="20" className="mr-1">
            <g transform="matrix(1, 0, 0, 1, 27.009001, -39.238998)">
              <path fill="#4285F4" d="M -3.264 51.509 C -3.264 50.719 -3.334 49.969 -3.454 49.239 L -14.754 49.239 L -14.754 53.749 L -8.284 53.749 C -8.574 55.229 -9.424 56.479 -10.684 57.329 L -10.684 60.329 L -6.824 60.329 C -4.564 58.239 -3.264 55.159 -3.264 51.509 Z"/>
              <path fill="#34A853" d="M -14.754 63.239 C -11.514 63.239 -8.804 62.159 -6.824 60.329 L -10.684 57.329 C -11.764 58.049 -13.134 58.489 -14.754 58.489 C -17.884 58.489 -20.534 56.379 -21.484 53.529 L -25.464 53.529 L -25.464 56.619 C -23.494 60.539 -19.444 63.239 -14.754 63.239 Z"/>
              <path fill="#FBBC05" d="M -21.484 53.529 C -21.734 52.809 -21.864 52.039 -21.864 51.239 C -21.864 50.439 -21.724 49.669 -21.484 48.949 L -21.484 45.859 L -25.464 45.859 C -26.284 47.479 -26.754 49.299 -26.754 51.239 C -26.754 53.179 -26.284 54.999 -25.464 56.619 L -21.484 53.529 Z"/>
              <path fill="#EA4335" d="M -14.754 43.989 C -12.984 43.989 -11.404 44.599 -10.154 45.789 L -6.734 42.369 C -8.804 40.429 -11.514 39.239 -14.754 39.239 C -19.444 39.239 -23.494 41.939 -25.464 45.859 L -21.484 48.949 C -20.534 46.099 -17.884 43.989 -14.754 43.989 Z"/>
            </g>
          </svg>
          {isLoading ? 'Loading...' : 'Continue with Google'}
        </Button>

        <div className="relative my-4">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-white/10"></div>
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-[#141414] px-2 text-white/50">OR CONTINUE WITH EMAIL</span>
          </div>
        </div>

        <form onSubmit={handleEmailAuth} className="space-y-4">
          {isSignUp && (
            <div className="space-y-2">
              <Label htmlFor="name" className="text-sm font-medium text-white/70">Full Name</Label>
              <Input
                id="name"
                type="text"
                placeholder="John Doe"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                required={isSignUp}
                className="bg-[#1A1A1A] border-white/10 focus:border-[#0EA5E9] focus:ring-1 focus:ring-[#0EA5E9] placeholder:text-white/30"
              />
            </div>
          )}
          <div className="space-y-2">
            <Label htmlFor="email" className="text-sm font-medium text-white/70">Email</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={formData.email}
              onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
              required
              className="bg-[#1A1A1A] border-white/10 focus:border-[#0EA5E9] focus:ring-1 focus:ring-[#0EA5E9] placeholder:text-white/30"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="password" className="text-sm font-medium text-white/70">Password</Label>
            <Input
              id="password"
              type="password"
              placeholder="••••••••"
              value={formData.password}
              onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
              required
              className="bg-[#1A1A1A] border-white/10 focus:border-[#0EA5E9] focus:ring-1 focus:ring-[#0EA5E9] placeholder:text-white/30"
            />
          </div>

          {isSignUp && (
            <div className="space-y-4 mt-4">
              <div className="flex items-start space-x-2">
                <Checkbox
                  id="terms"
                  checked={acceptedTerms}
                  onCheckedChange={(checked) => setAcceptedTerms(checked as boolean)}
                  className="mt-0.5"
                  disabled={isLoading}
                />
                <label
                  htmlFor="terms"
                  className="text-sm text-white/50 leading-tight cursor-pointer"
                >
                  I agree to the{" "}
                  <button
                    type="button"
                    className="text-[#0EA5E9] hover:underline"
                    onClick={() => window.open('/terms', '_blank')}
                  >
                    Terms
                  </button>
                  {" "}and{" "}
                  <button
                    type="button"
                    className="text-[#0EA5E9] hover:underline"
                    onClick={() => window.open('/privacy', '_blank')}
                  >
                    Privacy Policy
                  </button>
                </label>
              </div>
            </div>
          )}

          <Button
            type="submit"
            className="w-full bg-[#0EA5E9] hover:bg-[#0EA5E9]/90 text-white mt-4"
            disabled={isLoading}
          >
            {isLoading ? 'Loading...' : (isSignUp ? 'Create account' : 'Sign In')}
          </Button>
        </form>
        <div className="mt-4 text-center text-sm">
          <button
            type="button"
            onClick={() => setIsSignUp(!isSignUp)}
            className="text-[#0EA5E9] hover:text-[#0EA5E9]/90 hover:underline"
          >
            {isSignUp ? 'Already have an account? Sign in' : "Don't have an account? Sign up"}
          </button>
        </div>
      </Card>
    </div>
  );
};

export default AuthOverlay;