import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { supabase } from "@/integrations/supabase/client";
import AuthOverlay from '@/components/auth/AuthOverlay';
import { useAuth } from '@/contexts/AuthContext';

interface AuthGuardProps {
  children: React.ReactNode;
}

const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  const { isAuthenticated } = useAuth();

  // Show loading state while checking authentication
  if (isAuthenticated === null) {
    return (
      <div className="fixed inset-0 bg-black flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#0EA5E9]"></div>
      </div>
    );
  }

  // If not authenticated, show the auth overlay
  if (!isAuthenticated) {
    return (
      <>
        {children}
        <AuthOverlay />
      </>
    );
  }

  // If authenticated, render children
  return <>{children}</>;
};

export default AuthGuard; 