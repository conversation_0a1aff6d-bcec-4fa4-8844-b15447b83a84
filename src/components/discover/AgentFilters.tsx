import React, { useState } from 'react';
import { X, Star, Download, Calendar, TrendingUp, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { type AgentCategory, type DiscoverFilters } from '@/services/discoverService';

interface AgentFiltersProps {
  categories: AgentCategory[];
  filters: DiscoverFilters & { verified_only?: boolean };
  onFilterChange: (filters: Partial<DiscoverFilters & { verified_only?: boolean }>) => void;
  showVerifiedFilter?: boolean;
}

const AgentFilters: React.FC<AgentFiltersProps> = ({ categories, filters, onFilterChange, showVerifiedFilter = false }) => {
  const [selectedTags, setSelectedTags] = useState<string[]>(filters.tags || []);

  // Common tags for filtering
  const commonTags = [
    'RSI', 'MACD', 'Moving Average', 'Bollinger Bands', 'Support/Resistance',
    'Breakout', 'Momentum', 'Volume', 'Candlestick', 'Trend Following',
    'Mean Reversion', 'Scalping', 'Position Sizing', 'Stop Loss', 'Take Profit'
  ];

  const handleCategoryChange = (category: string, checked: boolean) => {
    if (checked) {
      onFilterChange({ category });
    } else {
      onFilterChange({ category: '' });
    }
  };

  const handleTagChange = (tag: string, checked: boolean) => {
    let newTags: string[];
    if (checked) {
      newTags = [...selectedTags, tag];
    } else {
      newTags = selectedTags.filter(t => t !== tag);
    }
    setSelectedTags(newTags);
    onFilterChange({ tags: newTags });
  };

  const handleFeaturedChange = (checked: boolean) => {
    onFilterChange({ featured: checked || undefined });
  };

  const clearAllFilters = () => {
    setSelectedTags([]);
    onFilterChange({
      category: '',
      tags: [],
      featured: undefined,
      verified_only: false
    });
  };

  const hasActiveFilters = filters.category || (filters.tags && filters.tags.length > 0) || filters.featured || filters.verified_only;

  return (
    <div className="space-y-6">
      {/* Enhanced Filters Card */}
      <Card className="bg-gradient-to-br from-white/[0.04] to-white/[0.02] border border-white/[0.08] rounded-2xl shadow-[inset_0_1px_0_rgba(255,255,255,0.1)] backdrop-blur-sm">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg text-white flex items-center gap-3" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
              <div className="p-2 rounded-lg bg-gradient-to-br from-white/[0.08] to-white/[0.04] shadow-[inset_0_1px_0_rgba(255,255,255,0.2)]">
                <img
                  src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/icons/Sliders.svg"
                  alt="Filters"
                  className="w-4 h-4"
                />
              </div>
              Filters
            </CardTitle>
            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllFilters}
                className="text-white/50 hover:text-white hover:bg-white/[0.06] rounded-lg transition-all duration-200"
              >
                <X className="w-4 h-4 mr-1" />
                Clear
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent className="pt-0 space-y-6">
          {/* Quick Filters Section */}
          <div>
            <h4 className="text-xs font-medium text-white/50 mb-3 uppercase tracking-wider">Quick Filters</h4>
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="featured"
                  checked={filters.featured || false}
                  onCheckedChange={handleFeaturedChange}
                  className="border-white/[0.12] data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
                />
                <label htmlFor="featured" className="text-sm text-white/70 cursor-pointer" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>
                  Featured Agents
                </label>
              </div>

              {showVerifiedFilter && (
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="verified"
                    checked={filters.verified_only || false}
                    onCheckedChange={(checked) => onFilterChange({ verified_only: checked as boolean })}
                    className="border-white/[0.12] data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"
                  />
                  <label htmlFor="verified" className="text-sm text-white/70 cursor-pointer flex items-center gap-1" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>
                    <Shield className="w-3 h-3" />
                    Official OSIS Verified
                  </label>
                </div>
              )}
            </div>
          </div>

          {/* Categories Section */}
          <div>
            <h4 className="text-sm font-medium text-white/70 mb-4" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>Categories</h4>
            <div className="space-y-3">
              {categories.map((category) => (
                <div key={category.id} className="flex items-center space-x-3 group">
                  <Checkbox
                    id={`category-${category.id}`}
                    checked={filters.category === category.name}
                    onCheckedChange={(checked) => handleCategoryChange(category.name, checked as boolean)}
                    className="border-white/[0.15] data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500 rounded-md"
                  />
                  <label
                    htmlFor={`category-${category.id}`}
                    className="text-sm text-white/70 group-hover:text-white cursor-pointer flex-1 transition-colors duration-200"
                    style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
                  >
                    {category.name}
                  </label>
                </div>
              ))}
            </div>
          </div>

          <Separator className="bg-white/[0.08]" />

          {/* Enhanced Tags Section */}
          <div>
            <h4 className="text-sm font-medium text-white/70 mb-4" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>Popular Tags</h4>
            <div className="flex flex-wrap gap-2">
              {commonTags.map((tag) => (
                <Badge
                  key={tag}
                  variant={selectedTags.includes(tag) ? 'default' : 'outline'}
                  className={`cursor-pointer text-xs h-7 px-3 transition-all duration-200 rounded-lg font-medium ${
                    selectedTags.includes(tag)
                      ? 'bg-gradient-to-br from-green-500/20 to-green-500/10 text-green-400 border-green-500/30 hover:from-green-500/30 hover:to-green-500/15 shadow-[inset_0_1px_0_rgba(34,197,94,0.2)]'
                      : 'border-white/[0.12] text-white/60 hover:text-white hover:border-white/[0.2] hover:bg-white/[0.04] bg-gradient-to-br from-white/[0.02] to-white/[0.01] shadow-[inset_0_1px_0_rgba(255,255,255,0.05)]'
                  }`}
                  onClick={() => handleTagToggle(tag)}
                  style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
                >
                  {tag}
                </Badge>
              ))}
            </div>
          </div>

          {/* Active Filters Summary */}
          {hasActiveFilters && (
            <div>
              <h4 className="text-xs font-medium text-white/50 mb-3 uppercase tracking-wider">Active Filters</h4>
              <div className="flex flex-wrap gap-2">
                {filters.featured && (
                  <Badge variant="outline" className="border-green-500/30 text-green-400 bg-green-500/10">
                    Featured
                  </Badge>
                )}
                {filters.verified_only && (
                  <Badge variant="outline" className="border-blue-500/30 text-blue-400 bg-blue-500/10">
                    <Shield className="w-3 h-3 mr-1" />
                    Verified
                  </Badge>
                )}
                {filters.category && (
                  <Badge variant="outline" className="border-white/[0.12] text-white/70 bg-white/[0.04]">
                    {filters.category}
                  </Badge>
                )}
                {filters.tags && filters.tags.map((tag) => (
                  <Badge key={tag} variant="outline" className="border-white/[0.12] text-white/70 bg-white/[0.04]">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AgentFilters;
