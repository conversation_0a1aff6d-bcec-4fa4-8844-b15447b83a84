import { Database } from "@/integrations/supabase/types";

export interface Source {
  url: string;
  title: string;
  description: string;
  domain: string;
  favicon?: string;
}

export interface Section {
  title: string;
  content: string;
}

export interface CandleData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume?: number;
}

export interface ChartConfig {
  timeframe: '1D' | '1W' | '1M';
  showVolume?: boolean;
}

export interface ChartData {
  symbol: string;
  data: CandleData[];
  config: ChartConfig;
  previousClose?: number;
  currentPrice?: number;
}

export interface AnalysisProgress {
  stage: 'initializing' | 'research' | 'market-data' | 'analysis' | 'complete';
  progress: number;
  currentStep: string;
  steps: {
    id: string;
    label: string;
    status: 'pending' | '' | 'complete' | 'error';
  }[];
}

export interface MessageContent {
  text: string | null;
  marketData?: any;
  webResults?: any[];
  symbol?: string | null;
  searchQueries?: string[];
  evaAnalysis?: any;
  symbols?: string[];
  isLoading?: boolean;
  analysisProgress?: AnalysisProgress;
  aiAnalysis?: string;
  symbolTypes?: any;
  loadingPlaceholder?: boolean;
  isGeneralMessage?: boolean;
  agentResults?: any[];
  selectedAgent?: string | null;
  isSingleAgentAnalysis?: boolean;
  sources?: Source[];
  sections?: Section[];
  chartData?: ChartData;
  youtubeResults?: YouTubeResult[];
  EXAAnalysis?: EXAAnalysis;
}

export interface Message {
  id: string;
  content: MessageContent;
  role: 'user' | 'assistant';
}

export interface AnalysisState {
  isLoadingMarketData: boolean;
  isLoadingNews: boolean;
  isLoadingAnalysis: boolean;
  isLoadingAI: boolean;
}

export interface MarketData {
  c: number; // close
  o: number; // open
  h: number; // high
  l: number; // low
  v: number; // volume
  vw: number; // volume weighted
  t: number; // timestamp
}

export interface WebResult {
  title: string;
  link: string;
  snippet: string;
  source: string;
  favicon?: string;
}

export interface YouTubeResult {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  publishedAt: string;
  channelTitle: string;
}

export interface EXAAnalysis {
  [key: string]: any;
}

export interface Chat {
  id: string;
  title: string;
  user_id: string;
  created_at: string;
  updated_at: string;
  chat_type: Database['public']['Enums']['chat_type'] | null;
  messages?: {
    content: string;
    created_at: string;
    role: 'user' | 'assistant';
    id?: string;
  }[];
}
