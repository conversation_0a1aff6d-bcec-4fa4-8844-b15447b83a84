import React from 'react';
import { Globe } from 'lucide-react';

// Define WebResult interface
export interface WebResult {
  url: string;
  favicon?: string;
  domain?: string;
  title: string;
  content: string;
  symbol?: string; // Add symbol to track which ticker this result is for
}

interface SearchResultsProps {
  webResults?: WebResult[];
  symbols?: string[];
}

// Helper function to check if URL is valid
const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

// Add URL normalization helper
const normalizeUrl = (url: string): string => {
  if (!url) return '';
  return url.startsWith('http') ? url : `https://${url}`;
};

export const SearchResults: React.FC<SearchResultsProps> = ({ webResults = [], symbols = [] }) => {
  if (!webResults || webResults.length === 0) {
    return null;
  }

  // Group results by symbol if we have multiple symbols
  const resultsBySymbol = symbols.reduce((acc, symbol) => {
    const symbolResults = webResults.filter(result => 
      result?.url && 
      (result.symbol === symbol || result.content?.includes(symbol))
    ) || [];
    
    if (symbolResults.length > 0) {
      acc[symbol] = symbolResults.map(result => ({
        ...result,
        url: normalizeUrl(result.url),
        title: result.title || 'No Title',
        content: result.content || 'No content available',
        favicon: result.favicon && isValidUrl(result.favicon) ? result.favicon : null
      }));
    }
    return acc;
  }, {} as { [key: string]: WebResult[] }) || {};

  // Get general results (not associated with any specific symbol)
  const generalResults = webResults.filter(result => 
    result?.url && !symbols.some(symbol => 
      result.symbol === symbol || result.content?.includes(symbol)
    )
  ).map(result => ({
    ...result,
    url: normalizeUrl(result.url),
    title: result.title || 'No Title',
    content: result.content || 'No content available',
    favicon: result.favicon && isValidUrl(result.favicon) ? result.favicon : null
  }));

  return (
    <div className="response-section">
      {/* Render symbol-specific results first */}
      {Object.entries(resultsBySymbol).map(([symbol, results]) => (
        <div key={symbol} className="mb-6">
          <div className="flex items-center gap-2 mb-3">
            <Globe className="h-5 w-5 text-white/60" />
            <span className="text-md font-medium text-white/80">
              {symbol} Results
            </span>
            <span className="text-sm text-white/40">
              {results.length} sources
            </span>
          </div>
          <div className="space-y-3">
            {results.map((result, i) => (
              <ResultCard key={`${symbol}-${i}`} result={result} />
            ))}
          </div>
        </div>
      ))}

      {/* Render general results if any */}
      {generalResults.length > 0 && (
        <div className="mt-6">
          <div className="flex items-center gap-2 mb-3">
            <Globe className="h-5 w-5 text-white/60" />
            <span className="text-md font-medium text-white/80">
              General Results
            </span>
            <span className="text-sm text-white/40">
              {generalResults.length} sources
            </span>
          </div>
          <div className="space-y-3">
            {generalResults.map((result, i) => (
              <ResultCard key={`general-${i}`} result={result} />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// Separate ResultCard component for cleaner code
const ResultCard: React.FC<{ result: WebResult }> = ({ result }) => {
  let domain = 'unknown';
  
  if (result.url) {
    try {
      // First normalize the URL
      const normalizedUrl = normalizeUrl(result.url);
      const urlObj = new URL(normalizedUrl);
      domain = urlObj.hostname.replace('www.', '');
    } catch (error) {
      // Keep default domain if URL parsing fails
    }
  }

  return (
    <div className="web-search-result">
      <div className="flex items-center gap-2 mb-2">
        {result.favicon && isValidUrl(result.favicon) && (
          <img 
            src={result.favicon} 
            alt="" 
            className="w-4 h-4"
            onError={(e) => {
              e.currentTarget.style.display = 'none';
            }}
          />
        )}
        <span className="text-xs text-white/40">{domain}</span>
      </div>
      <h3 className="text-sm font-medium text-white/90 mb-1">
        <a 
          href={normalizeUrl(result.url)} 
          target="_blank" 
          rel="noopener noreferrer"
          className="hover:underline"
        >
          {result.title}
        </a>
      </h3>
      <p className="text-sm text-white/60">{result.content}</p>
    </div>
  );
};

export default SearchResults;
