import React, { useState, useEffect } from 'react';
import { Co<PERSON>, RotateCcw } from 'lucide-react';
import AgentResultsCard from './AgentResultsCard';
import { AgentExecutionResult } from './types';

interface ChatResponseProps {
  userQuery: string;
  response: string;
  symbols?: string[];
  agentResults?: AgentExecutionResult[];
  hasAgentAnalysis?: boolean;
  isLoading?: boolean;
  onRegenerate?: () => Promise<void>;
}

export const ChatResponse: React.FC<ChatResponseProps> = ({
  userQuery,
  response,
  symbols = [],
  agentResults = [],
  hasAgentAnalysis = false,
  isLoading = false,
  onRegenerate
}) => {
  const [streamedText, setStreamedText] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);



  // Stream text effect
  useEffect(() => {
    if (response && !isLoading && !isStreaming) {
      setIsStreaming(true);
      setStreamedText('');

      const words = response.split(' ');
      let currentIndex = 0;

      const streamInterval = setInterval(() => {
        if (currentIndex < words.length) {
          const chunk = words.slice(currentIndex, currentIndex + 3).join(' ') + ' ';
          setStreamedText(prev => prev + chunk);
          currentIndex += 3;
        } else {
          clearInterval(streamInterval);
          setIsStreaming(false);
        }
      }, 50);

      return () => clearInterval(streamInterval);
    }
  }, [response, isLoading]);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(streamedText);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 1500);
    } catch (error) {
      console.error('Copy failed:', error);
    }
  };

  const handleRegenerate = async () => {
    if (onRegenerate) {
      setStreamedText('');
      setIsStreaming(false);
      await onRegenerate();
    }
  };

  if (isLoading) {
    return (
      <div className="mt-6 space-y-4">
        <div className="flex flex-col space-y-4 pb-4">
          <div className="text-white/70 flex items-center gap-2">
            <div className="h-4 w-4 rounded-full border-2 border-white/10 border-t-white/30 animate-spin" />
            Analyzing your request...
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full space-y-6">
      {/* Agent Results Card */}
      {hasAgentAnalysis && agentResults.length > 0 && (
        <div className="w-full">
          <AgentResultsCard
            agentResults={agentResults}
            symbols={symbols}
            className="mb-6"
          />
        </div>
      )}

      {/* Main Response */}
      <div className="bg-[#1A1A1A] border border-gray-800 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">Analysis</h3>
          <div className="flex items-center gap-2">
            <button
              onClick={handleCopy}
              className="p-2 text-gray-400 hover:text-white transition-colors rounded-lg hover:bg-gray-800"
              title="Copy response"
            >
              <Copy className="h-4 w-4" />
            </button>
            {onRegenerate && (
              <button
                onClick={handleRegenerate}
                className="p-2 text-gray-400 hover:text-white transition-colors rounded-lg hover:bg-gray-800"
                title="Regenerate response"
              >
                <RotateCcw className="h-4 w-4" />
              </button>
            )}
          </div>
        </div>

        <div className="prose prose-invert max-w-none">
          <div className="text-gray-300 leading-relaxed whitespace-pre-wrap">
            {streamedText || response}
          </div>
        </div>

        {copySuccess && (
          <div className="mt-2 text-sm text-green-400">
            Copied to clipboard!
          </div>
        )}
      </div>

      {/* Symbols Display */}
      {symbols.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {symbols.map((symbol) => (
            <span
              key={symbol}
              className="px-3 py-1 bg-blue-500/20 text-blue-300 rounded-full text-sm"
            >
              {symbol}
            </span>
          ))}
        </div>
      )}
    </div>
  );
};

export default ChatResponse;
