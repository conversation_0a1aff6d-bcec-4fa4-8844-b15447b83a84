import React from 'react';
import { AdvancedStockChart } from '@/components/charts/AdvancedStockChart';

interface MessageContentProps {
  message: {
    content: {
      text: string;
      stockChart?: {
        symbol: string;
      } | null;
    };
  };
}

const MessageContent: React.FC<MessageContentProps> = ({ message }) => {
  if (message.content.stockChart) {
    return (
      <div className="space-y-4">
        <div data-testid="message-content" className="prose dark:prose-invert">
          {message.content.text}
        </div>
        
        <AdvancedStockChart 
          symbol={message.content.stockChart.symbol} 
          height={300}
          showVolume={true}
          showControls={true}
        />
      </div>
    );
  }
  
  return (
    <div data-testid="message-content" className="prose dark:prose-invert">
      {message.content.text}
    </div>
  );
};

export default MessageContent; 