import React from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { cn } from "@/lib/utils";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectSeparator,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Export these types for other components to use
export type AIModelType = 'claude-3-sonnet';

export interface ModelConfig {
  value: AIModelType;
  label: string;
  description: string;
  icon: React.ReactNode;
  color: string;
}

// Single model configuration with enhanced styling
const modelConfig = {
  value: 'claude-3-sonnet',
  label: 'Osis',
  description: 'Trading AI Assistant',
  icon: null,
};

const ModelSelector = () => {
  const [imageLoaded, setImageLoaded] = React.useState(false);
  const [imageError, setImageError] = React.useState(false);

  return (
    <div className="relative flex items-center">
      <div className="relative group">
        <div className="relative w-8 h-8 overflow-hidden bg-transparent">
          {!imageLoaded && !imageError && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-4 h-4 border-2 border-white/10 border-t-white/30 rounded-full animate-spin" />
            </div>
          )}
          
          {!imageError ? (
            <img
              src="http://thecodingkid.oyosite.com/logo_only.png"
              alt="Osis Logo"
              className={cn(
                "w-full h-full object-cover transition-all duration-300 scale-[0.8]",
                !imageLoaded && "opacity-0"
              )}
              onLoad={() => setImageLoaded(true)}
              onError={() => setImageError(true)}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-white/40 text-xs font-medium">
              O
            </div>
          )}
          
          {imageLoaded && !imageError && (
            <div className="absolute inset-0 opacity-0 group-hover:opacity-100 bg-gradient-to-t from-black/5 to-transparent transition-opacity duration-300" />
          )}
        </div>
      </div>
    </div>
  );
};

export default ModelSelector;