import React, { useState, useRef, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { ChevronDown, ChevronUp, Edit2, Save, Globe, ChevronLeft, ChevronRight, TrendingUp, BarChart3, ChartBar, LineChart, Sparkles, PieChart, AlertTriangle, ArrowUpDown, Activity, Target, Shield, Gauge, Flame, BarChart2, Loader2 } from 'lucide-react';
import { SearchResults } from './SearchResults';
import { AdvancedStockChart } from '@/components/charts/AdvancedStockChart';
import { MessageContent, AnalysisState } from './types';
import { cn } from '@/lib/utils';
import TypingEffect from '@/components/ui/typing-effect';
import { useResponsive } from '@/hooks/useResponsive';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { supabase } from '@/integrations/supabase/client';
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { DataFetchingIndicator } from '@/components/ui/data-fetching-indicator';
import { MiniStockChart } from '@/components/charts/MiniStockChart';
import { OsisThinking, MarketDataLoading, ChartSkeleton, LoadingProgress, EnhancedChart } from '@/components/chat/LoadingStates';
// import { DirectStockChart } from './DirectStockChart';
// import { formatAIResponse } from '@/utils/formatMessage';

// Update the formatAIResponse function to only highlight confirmed symbols
const formatAIResponse = (text: string, symbols?: string[]) => {
  if (!text) return '';
  
  // Split content into sections
  const sections = text.split(/(?=<h[1-6])/);
  
  return sections.map(section => {
    // Format section headers
    section = section.replace(
      /(Market Context:|Company Overview:|Technical Levels:|Risks:|Strategies:|Summary:)/g,
      '<h3 class="text-lg font-semibold text-white/90 mt-5 mb-2">$1</h3>'
    );

    // Format stock symbols
    section = section.replace(/\b[A-Z]{1,5}\b/g, (match) => 
      symbols?.includes(match) 
        ? `<span class="text-[#33C3F0] font-semibold">${match}</span>`
        : match
    );

    // Format financial metrics inline
    section = section.replace(/([A-Za-z/() ]+):\s*([-\d.]+[%x]?)/g, (match, label, value) => {
      const num = parseFloat(value);
      const color = !isNaN(num) && value.includes('%') 
        ? (num >= 0 ? 'text-green-500' : 'text-red-500')
        : 'text-white/90';
      return `<span class="text-white/70">${label}:</span> <span class="${color}">${value}</span>`;
    });

    // Format percentages
    section = section.replace(/([+-]?\d+\.?\d*%)/g, (match) => {
      const num = parseFloat(match);
      if (isNaN(num)) return match;
      return num >= 0 
        ? `<span class="text-green-500">+${match}</span>`
        : `<span class="text-red-500">${match}</span>`;
    });

    // Format bullet points with cleaner styling
    section = section.replace(/^[-•]\s(.+)$/gm, '<li class="flex items-start mb-2"><span class="text-white/70 mr-2">•</span><span>$1</span></li>');
    section = section.replace(/(<li.*<\/li>)/gs, '<ul class="space-y-1 my-2">$1</ul>');
    // Replace asterisks with bullets
    section = section.replace(/\*/g, '•');
    
    // Format percentages without unnecessary decimals
    section = section.replace(/(\d+)\.0+%/g, '$1%');

    // Format paragraphs
    section = section.replace(/\n\n/g, '</p><p class="mb-2">');
    
    // Ensure proper paragraph wrapping
    if (!section.startsWith('<h3') && !section.startsWith('<p')) {
      section = `<p class="mb-2">${section}`;
    }
    if (!section.endsWith('</p>') && !section.endsWith('</ul>')) {
      section = `${section}</p>`;
    }

    // Replace price_vs_bb and RSI patterns
    section = section.replace(
      /(price_vs_bb = )(\d+\.\d+)/g, 
      '$1$2'
    ).replace(
      /(RSI_)(\d+\.\d+)/g,
      '$1$2'
    ).replace(
      /(RSI\s*)(\d+\.\d+)/g,
      '$1$2'
    );

    return section;
  }).join('');
};

// Define WebResult interface
interface WebResult {
  url: string;
  favicon?: string;
  domain?: string;
  title: string;
  content: string;
  symbol?: string; // Add symbol to track which ticker this result is for
}

// Add interface for market data
interface MarketData {
  price?: number;
  change?: number;
  changePercent?: number;
  volume?: number;
  // Add other market data fields as needed
}

// Update interface to handle multiple ticker results
interface ExtendedMessageContent extends MessageContent {
  webResults?: WebResult[];
  symbols?: string[];  // Add symbols array
  marketData?: { [key: string]: MarketData };  // Update type to be more specific
  EXAAnalysis?: { [key: string]: any };  // Add EXA analysis object keyed by symbol
  searchEnabled?: boolean;
  aiAnalysis?: string; // Add this field
  chartData?: { symbol: string }; // Add this field
}

interface MessageCardProps {
  role: 'user' | 'assistant';
  content: ExtendedMessageContent;
  marketData?: any;
  webResults?: WebResult[];
  technicalAnalysis?: string;
  riskAssessment?: string;
  loadingState?: AnalysisState;
  onEdit?: (newText: string) => void;
  onSymbolChange?: (symbol: string) => void;
  isTyping?: boolean;
  userEmail?: string;
  avatarUrl?: string;
  onSuggestedQuestionClick?: (question: string) => void;
  suggestedQuestions?: string[];
  isLoadingSuggestions?: boolean;
  isLastMessage?: boolean;
  loadingPhase?: string;
}

// Add URL normalization helper
const normalizeUrl = (url: string): string => {
  if (!url) return '';
  return url.startsWith('http') ? url : `https://${url}`;
};

// Update URL validation helper
const isValidUrl = (urlString: string): boolean => {
  try {
    if (!urlString) return false;
    const urlToCheck = normalizeUrl(urlString);
    new URL(urlToCheck);
    return true;
  } catch {
    return false;
  }
};

// Add this type for our loading steps
type LoadingStep = {
  id: number;
  message: string;
  subMessage: string;
  duration: number;
};

const loadingSteps: LoadingStep[] = [
  {
    id: 1,
    message: "Analyzing your request",
    subMessage: "Processing query and identifying market symbols...",
    duration: 1200
  },
  {
    id: 2,
    message: "Fetching market data",
    subMessage: "Retrieving real-time prices and historical data...",
    duration: 2000
  },
  {
    id: 3,
    message: "Generating insights",
    subMessage: "Analyzing patterns and market conditions...",
    duration: 1800
  }
];

const MessageCard: React.FC<MessageCardProps> = ({ 
  role, 
  content, 
  marketData, 
  webResults = [], 
  technicalAnalysis,
  riskAssessment,
  loadingState,
  onEdit,
  onSymbolChange,
  isTyping,
  userEmail,
  avatarUrl,
  onSuggestedQuestionClick,
  suggestedQuestions = [],
  isLoadingSuggestions = false,
  isLastMessage,
  loadingPhase = 'thinking'
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isExpanded, setIsExpanded] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [editText, setEditText] = useState(content.text || "");
  const hasMarketData = marketData?.results?.length > 0;
  const [user, setUser] = useState<any>(null);
  const [isSourcesExpanded, setIsSourcesExpanded] = useState(false);
  const [showAllSources, setShowAllSources] = useState(false);
  const [isChartLoading, setIsChartLoading] = useState(false);
  const [showThinking, setShowThinking] = useState(false);
  const [showChartSkeleton, setShowChartSkeleton] = useState(false);
  const [showContent, setShowContent] = useState(false);
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [typedText, setTypedText] = useState('');
  const [isTyping, setIsTyping] = useState(false);

  // Add responsive hook
  const { isMobile, classes } = useResponsive();

  // Extract symbol safely
  const extractSymbol = (text: string | null): string | null => {
    // Check if text is null or undefined
    if (!text) return null;
    
    // Simple regex to find stock symbols like $AAPL or TSLA
    const symbolMatch = text.match(/\$?([A-Z]{1,5})\b/);
    return symbolMatch ? symbolMatch[1] : null;
  };

  const symbol = content.symbol || extractSymbol(content.text);

  // Generate suggested follow-up questions
  useEffect(() => {
    if (!content.text) {
      setIsLoadingSuggestions(false);
      return;
    }
    
    setIsLoadingSuggestions(true);
    
    const timer = setTimeout(() => {
      const detectedSymbols = content.symbols || [];
      
      let questions: string[] = [];
      
      if (detectedSymbols.length > 0) {
        const stockQuestions = detectedSymbols.flatMap(symbol => [
          `${symbol} technical analysis`,
          `${symbol} price target`,
          `${symbol} long term outlook`
        ]);
        
        questions = stockQuestions.slice(0, 3);
      } else {
        const generalQuestions = [
          "What are the best performing stocks this quarter?",
          "How to analyze market trends?",
          "Latest financial news"
        ];
        
        questions = generalQuestions;
      }
      
      setSuggestedQuestions(questions);
      setIsLoadingSuggestions(false);
    }, 800);
    
    return () => clearTimeout(timer);
  }, [content.text, content.symbols]);

  // Fetch user data on component mount
  useEffect(() => {
    const fetchUser = async () => {
      const { data } = await supabase.auth.getUser();
      if (data?.user) {
        setUser(data.user);
      }
    };
    
    fetchUser();
  }, []);

  // Effect to handle loading phases
  useEffect(() => {
    if (isTyping) {
      setLoadingPhase('thinking');
    } else if (!content.text || loadingState?.isLoadingNews) {
      setLoadingPhase('loading');
      
      // Transition to complete after data starts coming in
      if (content.marketData) {
        const timer = setTimeout(() => {
          setLoadingPhase('complete');
        }, 800);
        return () => clearTimeout(timer);
      }
    } else {
      setLoadingPhase('complete');
    }
  }, [isTyping, content, loadingState]);

  useEffect(() => {
    if (loadingPhase === 'thinking') {
      setShowThinking(true);
      setCurrentStep(1);
      
      // Progress through loading steps
      loadingSteps.forEach((step, index) => {
        setTimeout(() => {
          setCurrentStep(step.id);
        }, loadingSteps.slice(0, index).reduce((acc, s) => acc + s.duration, 0));
      });

      // Show chart skeleton after first step
      setTimeout(() => {
        setShowChartSkeleton(true);
      }, 800);
    }
    
    if (loadingPhase === 'complete') {
      setTimeout(() => {
        setShowContent(true);
        setTimeout(() => {
          setShowThinking(false);
          setCurrentStep(0);
        }, 500);
      }, 600);
    }
  }, [loadingPhase]);

  useEffect(() => {
    if (content.text && loadingPhase === 'complete') {
      setIsTyping(true);
      let index = 0;
      const text = content.text;
      
      const typeText = () => {
        if (index < text.length) {
          setTypedText(text.substring(0, index + 1));
          index++;
          setTimeout(typeText, 20);
        } else {
          setIsTyping(false);
        }
      };
      
      typeText();
    }
  }, [content.text, loadingPhase]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    if (onEdit) {
      onEdit(editText);
    }
    setIsEditing(false);
  };

  const handleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const renderWebResults = () => {
    // Early return if no web results or symbols
    if (!content.webResults?.length && !content.symbols?.length) return null;

    // Group results by symbol if we have multiple symbols
    const resultsBySymbol = content.symbols?.reduce((acc, symbol) => {
      const symbolResults = content.webResults?.filter(result => 
        result?.url && 
        (result.symbol === symbol || result.content?.includes(symbol))
      ) || [];
      
      if (symbolResults.length > 0) {
        acc[symbol] = symbolResults.map(result => ({
          ...result,
          url: normalizeUrl(result.url),
          title: result.title || 'No Title',
          content: result.content || 'No content available',
          favicon: result.favicon && isValidUrl(result.favicon) ? result.favicon : null
        }));
      }
      return acc;
    }, {} as { [key: string]: WebResult[] }) || {};

    // Handle general results (not tied to a specific symbol)
    const generalResults = content.webResults?.filter(result => 
      result?.url && !content.symbols?.some(symbol => result.symbol === symbol)
    ).map(result => ({
      ...result,
      url: normalizeUrl(result.url),
      title: result.title || 'No Title',
      content: result.content || 'No content available',
      favicon: result.favicon && isValidUrl(result.favicon) ? result.favicon : null
    })) || [];

    return (
      <div className="response-section">
        {/* Render symbol-specific results first */}
        {Object.entries(resultsBySymbol).map(([symbol, results]) => (
          <div key={symbol} className="mb-6">
            <div className="flex items-center gap-2 mb-3">
              <Globe className="h-5 w-5 text-white/60" />
              <span className="text-md font-medium text-white/80">
                {symbol} Results
              </span>
              <span className="text-sm text-white/40">
                {results.length} sources
              </span>
            </div>
            <div className="space-y-3">
              {results.map((result, i) => (
                <ResultCard key={`${symbol}-${i}`} result={result} />
              ))}
            </div>
          </div>
        ))}

        {/* Render general results if any */}
        {generalResults.length > 0 && (
          <div className="mt-6">
            <div className="flex items-center gap-2 mb-3">
              <Globe className="h-5 w-5 text-white/60" />
              <span className="text-md font-medium text-white/80">
                General Results
              </span>
              <span className="text-sm text-white/40">
                {generalResults.length} sources
              </span>
            </div>
            <div className="space-y-3">
              {generalResults.map((result, i) => (
                <ResultCard key={`general-${i}`} result={result} />
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  // Separate ResultCard component for cleaner code
  const ResultCard: React.FC<{ result: WebResult }> = ({ result }) => {
    let domain = 'unknown';
    
    if (result.url) {
      try {
        // First normalize the URL
        const normalizedUrl = normalizeUrl(result.url);
        const urlObj = new URL(normalizedUrl);
        domain = urlObj.hostname.replace('www.', '');
      } catch (error) {
        // Keep default domain if URL parsing fails
      }
    }

    return (
      <div className="web-search-result">
        <div className="flex items-center gap-2 mb-2">
          {result.favicon && isValidUrl(result.favicon) && (
            <img 
              src={result.favicon} 
              alt="" 
              className="w-4 h-4"
              onError={(e) => {
                e.currentTarget.style.display = 'none';
              }}
            />
          )}
          <span className="text-xs text-white/40">{domain}</span>
        </div>
        <h3 className="text-sm font-medium text-white/90 mb-1">
          <a 
            href={normalizeUrl(result.url)} 
            target="_blank" 
            rel="noopener noreferrer"
            className="hover:underline"
          >
            {result.title}
          </a>
        </h3>
        <p className="text-sm text-white/60">{result.content}</p>
      </div>
    );
  };

  // Render market data section - premium design with improved layout
  const renderMarketData = () => {
    if (!content.marketData || Object.keys(content.marketData).length === 0) return null;

    return (
      <div className="mt-4 w-full">
        {/* Loading indicator with steps */}
        {renderLoadingIndicator()}

        <div className="space-y-6">
          {/* Always render chart container, but with skeleton/loading state when needed */}
          {(showChartSkeleton || showContent) && Object.entries(content.marketData || {}).map(([symbol, data]: [string, any]) => {
            return (
              <div key={symbol} className="w-full">
                {/* Chart Card with proper loading states */}
                <div 
                  className="bg-gradient-to-br from-[#0A0C12] to-[#12141A] rounded-xl border border-[#1E2233] 
                    overflow-hidden hover:border-[#2A2F42] transition-all duration-500 shadow-xl"
                  style={{
                    opacity: showContent ? 1 : 0.8,
                    transform: `translateY(${showContent ? '0' : '10px'})`,
                  }}
                >
                  {/* Chart Header - Always visible */}
                  <div className="p-4 border-b border-[#1E2233] bg-[#0D0E14]/80 backdrop-blur-xl">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <span className="text-sm font-medium bg-gradient-to-r from-white/90 to-white/70 
                          bg-clip-text text-transparent">Price Chart</span>
                        <div className="h-4 w-[1px] bg-[#1E2233]"></div>
                        <div className="flex gap-1.5">
                          {['1D', '1W', '1M'].map((period) => (
                            <button
                              key={period}
                              className="px-3 py-1.5 text-xs font-medium rounded-lg bg-[#1A1D2E] text-white/60 
                                hover:bg-[#2A2F42] hover:text-white/90 transition-all duration-200 
                                border border-[#1E2233] hover:border-[#2A2F42]"
                            >
                              {period}
                            </button>
                          ))}
                        </div>
                      </div>
                      <span className="text-xs text-white/40 font-medium">Last 30 days</span>
                    </div>
                  </div>
                  
                  {/* Chart Content Area with Loading State */}
                  <div className="h-[400px] w-full p-4 relative">
                    {/* Loading overlay - visible during loading */}
                    {!showContent && (
                      <div className="absolute inset-0 bg-[#0A0C12]/80 backdrop-blur-sm flex flex-col items-center justify-center z-10">
                        <div className="w-16 h-16 mb-4">
                          <div className="w-full h-full rounded-full border-3 border-t-transparent border-purple-500/30 animate-spin"></div>
                        </div>
                        <div className="text-sm text-white/70 font-medium mb-2">Loading market data</div>
                        <div className="text-xs text-white/40 max-w-[280px] text-center">
                          Retrieving historical prices and analyzing patterns for {symbol}
                        </div>
                      </div>
                    )}
                    
                    {/* Actual chart - only rendered when content is ready */}
                    <div className={`transition-opacity duration-1000 ${showContent ? 'opacity-100' : 'opacity-0'}`}>
                      <AdvancedStockChart 
                        symbol={symbol}
                        showVolume={false}
                        showControls={false}
                        theme="dark"
                        chartType="candle"
                      />
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  // Render AI analysis section
  const renderAIAnalysis = () => {
    if (loadingPhase === 'thinking' || loadingPhase === 'loading') {
      return (
        <div className="mt-5 space-y-3">
          <div className="h-4 w-3/4 bg-white/5 rounded-md animate-pulse"></div>
          <div className="h-4 w-5/6 bg-white/5 rounded-md animate-pulse"></div>
          <div className="h-4 w-2/3 bg-white/5 rounded-md animate-pulse"></div>
          <div className="h-4 w-4/5 bg-white/5 rounded-md animate-pulse"></div>
        </div>
      );
    }
    
    if (!content.text) return null;

    return (
      <div className={`mt-5 ${isMobile ? 'text-base' : 'text-sm'} text-white/80 prose dark:prose-invert transition-opacity duration-500 ${showContent ? 'opacity-100' : 'opacity-0'}`}>
        <div dangerouslySetInnerHTML={{ __html: formatAIResponse(content.text, content.symbols) }} />
      </div>
    );
  };

  const renderContent = () => {
    if (content.chartData && content.chartData.symbol) {
      
      return (
        <div className="space-y-4">
          <div className="prose dark:prose-invert">
            {formatAIResponse(content.aiAnalysis, content.symbols)}
          </div>
          
          <div className="mt-4">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-white/70">Chart for {content.chartData.symbol}</h3>
              <span className="text-xs text-white/50">Powered by FinancialDatasets</span>
            </div>
            
            <AdvancedStockChart 
              symbol={content.chartData.symbol}
              height={400}
              showVolume={true}
              showControls={true}
            />
          </div>
        </div>
      );
    }
    
    return (
      <div className="prose dark:prose-invert">
        {formatAIResponse(content.aiAnalysis, content.symbols)}
      </div>
    );
  };

  const renderLoadingIndicator = () => {
    if (!showThinking) return null;
    
    const currentStepData = loadingSteps.find(step => step.id === currentStep);
    
    return (
      <div className={`flex items-start gap-4 mb-6 transition-all duration-500 ${showContent ? 'opacity-0' : 'opacity-100'}`}>
        <div className="flex-shrink-0 mt-1">
          <div className="relative h-8 w-8">
            {/* Outer rotating ring */}
            <div className="absolute inset-0 rounded-full border-2 border-purple-500/30 animate-[spin_3s_linear_infinite]" />
            
            {/* Inner progress indicator */}
            <div className="absolute inset-0 rounded-full border-2 border-t-purple-500 border-r-purple-500 border-b-transparent border-l-transparent animate-[spin_1.5s_cubic-bezier(0.65,0.25,0.35,0.75)_infinite]" />
            
            {/* Center dot */}
            <div className="absolute inset-2 rounded-full bg-gradient-to-br from-purple-500 to-indigo-600 animate-pulse" />
          </div>
        </div>
        
        <div className="flex-1 space-y-4">
          {/* Main loading message */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-white/90">
              {currentStepData?.message || "Processing..."}
            </h3>
            <p className="text-xs text-white/60">
              {currentStepData?.subMessage || "Please wait..."}
            </p>
          </div>
          
          {/* Progress steps */}
          <div className="flex gap-2">
            {loadingSteps.map((step) => (
              <div
                key={step.id}
                className={`h-1 rounded-full transition-all duration-300 ${
                  step.id === currentStep 
                    ? 'bg-purple-500 w-8' 
                    : step.id < currentStep 
                      ? 'bg-purple-500/50 w-4' 
                      : 'bg-white/10 w-4'
                }`}
              />
            ))}
          </div>
        </div>
      </div>
    );
  };

  const renderLoadingState = () => {
    if (loadingPhase === 'thinking') {
      return <OsisThinking />;
    }
    
    if (loadingPhase === 'market_data') {
      return <MarketDataLoading />;
    }
    
    if (loadingPhase === 'chart_loading') {
      return (
        <>
          <ChartSkeleton />
          <LoadingProgress content={content} />
        </>
      );
    }
    
    if (loadingPhase === 'complete' && content.marketData) {
      return (
        <>
          {Object.entries(content.marketData).map(([symbol, data]) => (
            <div key={symbol} className="mb-6">
              <AdvancedStockChart 
                symbol={symbol}
                height={400}
                showVolume={true}
                showControls={true}
              />
            </div>
          ))}
          <div className="prose dark:prose-invert">
            {isTyping ? typedText : content.text}
          </div>
        </>
      );
    }
    
    return null;
  };

  if (role === 'assistant') {
    return (
      <div className="space-y-6 animate-fade-in px-0">
        {/* Suggested Questions */}
        {loadingPhase === 'complete' && content.text && (
          <div className="flex flex-wrap gap-2 mb-3">
            {isLoadingSuggestions ? (
              <>
                {[1, 2, 3].map((i) => (
                  <div 
                    key={i}
                    className="h-8 bg-white/5 rounded-full px-3 animate-pulse"
                    style={{ width: `${Math.floor(Math.random() * 80) + 100}px` }}
                  />
                ))}
              </>
            ) : (
              suggestedQuestions.map((question, index) => (
                <button
                  key={index}
                  onClick={() => onSuggestedQuestionClick && onSuggestedQuestionClick(question)}
                  className="bg-white/5 hover:bg-white/10 text-white/80 hover:text-white/100 rounded-full px-3 py-1.5 text-xs transition-colors border border-white/10"
                >
                  {question}
                </button>
              ))
            )}
          </div>
        )}
        
        {/* Loading States or Content */}
        {loadingPhase !== 'complete' ? (
          renderLoadingState()
        ) : (
          <>
            {/* Market Data Section */}
            {content.marketData && renderMarketData()}
            
            {/* AI Analysis Section */}
            {renderAIAnalysis()}
          </>
        )}
      </div>
    );
  }

  return (
    <Card className={cn(
      "relative border-0 bg-transparent",
      role === 'user' ? 'message-user' : 'message-ai'
    )}>
      <div className="flex gap-3 p-4">
        <Avatar className="h-8 w-8">
          {role === 'user' ? (
            <AvatarFallback>U</AvatarFallback>
          ) : (
            <AvatarImage src="/grok-avatar.png" alt="Grok" />
          )}
        </Avatar>
        
        <div className="flex-1">
          <div className={`prose prose-invert max-w-none ${isMobile ? 'text-base' : 'prose-lg'}`}>
            {content.text || renderLoadingState()}
          </div>
        </div>
        
        {onEdit && (
          <Button
            variant="ghost"
            size="icon"
            onClick={handleEdit}
            className="shrink-0"
          >
            <Edit2 className="h-4 w-4" />
          </Button>
        )}
      </div>
    </Card>
  );
};

export default MessageCard;
