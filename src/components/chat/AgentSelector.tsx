import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader2, Search, Bot, CheckCircle, AlertCircle, Settings } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { getAgents } from '@/services/agentService';

interface Agent {
  id: string;
  name: string;
  description?: string;
  configuration: {
    blocks: any[];
    entryBlockId: string;
  };
  created_at?: string;
  updated_at?: string;
}

interface AgentSelectorProps {
  onAgentsSelected: (selectedAgentIds: string[]) => void;
  onCancel?: () => void;
  initialSelectedAgents?: string[];
  isVisible: boolean;
  title?: string;
  subtitle?: string;
}

export default function AgentSelector({
  onAgentsSelected,
  onCancel,
  initialSelectedAgents = [],
  isVisible,
  title = "Select Trading Agents",
  subtitle = "Choose which agents will analyze your trading questions during this chat session"
}: AgentSelectorProps) {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [filteredAgents, setFilteredAgents] = useState<Agent[]>([]);
  const [selectedAgentIds, setSelectedAgentIds] = useState<string[]>(initialSelectedAgents);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  // Load agents on component mount
  useEffect(() => {
    if (isVisible) {
      loadAgents();
    }
  }, [isVisible]);

  // Filter agents when search query changes
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredAgents(agents);
      return;
    }

    const filtered = agents.filter(agent =>
      agent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (agent.description && agent.description.toLowerCase().includes(searchQuery.toLowerCase()))
    );
    setFilteredAgents(filtered);
  }, [searchQuery, agents]);

  const loadAgents = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const agentList = await getAgents();
      setAgents(agentList);
      setFilteredAgents(agentList);
    } catch (err) {
      console.error('Error loading agents:', err);
      setError('Failed to load agents. Please try again.');
      toast({
        title: 'Error',
        description: 'Failed to load your trading agents',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAgentToggle = (agentId: string) => {
    setSelectedAgentIds(prev => {
      // Single agent selection - replace current selection
      if (prev.includes(agentId)) {
        return []; // Deselect if already selected
      } else {
        return [agentId]; // Select only this agent
      }
    });
  };

  // Remove select all functionality for single agent selection
  const handleConfirm = () => {
    onAgentsSelected(selectedAgentIds);
  };

  const getAgentCapabilities = (agent: Agent) => {
    const capabilities = new Set<string>();

    agent.configuration.blocks.forEach(block => {
      if (block.type === 'INDICATOR') {
        capabilities.add(block.indicator || 'Technical Analysis');
      } else if (block.type === 'CONDITION') {
        capabilities.add('Conditions');
      } else if (block.type === 'TRIGGER') {
        capabilities.add('Signals');
      } else if (block.type === 'CANDLE_PATTERN') {
        capabilities.add('Candle Patterns');
      }
    });

    return Array.from(capabilities).slice(0, 3); // Show max 3 capabilities
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-4xl max-h-[80vh] bg-[#1A1A1A] border-gray-800">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl text-white flex items-center gap-2">
                <Bot className="h-5 w-5 text-blue-400" />
                {title}
              </CardTitle>
              <p className="text-sm text-gray-400 mt-1">{subtitle}</p>
            </div>
            {onCancel && (
              <Button variant="outline" onClick={onCancel} size="sm">
                Cancel
              </Button>
            )}
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Search */}
          <div className="flex gap-3">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search agents by name or description..."
                className="pl-10 bg-[#2A2A2A] border-gray-700 text-white"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          {/* Agent List */}
          <ScrollArea className="h-[400px] pr-4">
            {isLoading ? (
              <div className="flex items-center justify-center h-32">
                <Loader2 className="h-6 w-6 animate-spin text-blue-400" />
                <span className="ml-2 text-gray-400">Loading agents...</span>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-32 text-red-400">
                <AlertCircle className="h-5 w-5 mr-2" />
                {error}
              </div>
            ) : filteredAgents.length === 0 ? (
              <div className="text-center py-8 text-gray-400">
                {searchQuery ? 'No agents match your search' : 'No agents found. Create your first agent in the Agent Builder.'}
              </div>
            ) : (
              <div className="space-y-3">
                {filteredAgents.map(agent => {
                  const isSelected = selectedAgentIds.includes(agent.id);
                  const capabilities = getAgentCapabilities(agent);

                  return (
                    <div
                      key={agent.id}
                      className={`p-4 rounded-lg border cursor-pointer transition-all ${
                        isSelected
                          ? 'border-blue-500 bg-blue-500/10'
                          : 'border-gray-700 bg-[#2A2A2A] hover:border-gray-600'
                      }`}
                      onClick={() => handleAgentToggle(agent.id)}
                    >
                      <div className="flex items-start gap-3">
                        <Checkbox
                          checked={isSelected}
                          onChange={() => handleAgentToggle(agent.id)}
                          className="mt-1"
                        />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="font-medium text-white truncate">{agent.name}</h3>
                            {isSelected && <CheckCircle className="h-4 w-4 text-blue-400 flex-shrink-0" />}
                          </div>

                          {agent.description && (
                            <p className="text-sm text-gray-400 mb-2 line-clamp-2">
                              {agent.description}
                            </p>
                          )}

                          <div className="flex items-center gap-2 flex-wrap">
                            <span className="text-xs text-gray-500">
                              {agent.configuration.blocks.length} blocks
                            </span>
                            {capabilities.map(capability => (
                              <Badge
                                key={capability}
                                variant="secondary"
                                className="text-xs bg-gray-700 text-gray-300"
                              >
                                {capability}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </ScrollArea>

          {/* Footer */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-700">
            <div className="text-sm text-gray-400">
              {selectedAgentIds.length > 0 ? 'Agent selected' : 'No agent selected'}
            </div>
            <div className="flex gap-2">
              {onCancel && (
                <Button variant="outline" onClick={onCancel}>
                  Cancel
                </Button>
              )}
              <Button
                onClick={handleConfirm}
                disabled={selectedAgentIds.length === 0}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {selectedAgentIds.length > 0 ? 'Continue with Selected Agent' : 'Select an Agent'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
