import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  ChevronDown,
  ChevronUp,
  Bot,
  TrendingUp,
  TrendingDown,
  Minus,
  Clock,
  Target,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { AgentExecutionResult } from './types';

interface AgentResultsCardProps {
  agentResults: AgentExecutionResult[];
  symbols: string[];
  className?: string;
}

interface ConsensusSignal {
  signal: 'bullish' | 'bearish' | 'neutral';
  confidence: number;
  agreementLevel: number;
  agentCount: number;
}

export default function AgentResultsCard({
  agentResults,
  symbols,
  className = ""
}: AgentResultsCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [expandedAgents, setExpandedAgents] = useState<Set<string>>(new Set());



  if (!agentResults || agentResults.length === 0) {
    return null;
  }

  // Group results by symbol
  const resultsBySymbol = agentResults.reduce((acc, result) => {
    if (!acc[result.symbol]) {
      acc[result.symbol] = [];
    }
    acc[result.symbol].push(result);
    return acc;
  }, {} as Record<string, AgentExecutionResult[]>);

  // Calculate consensus for each symbol
  const calculateConsensus = (results: AgentExecutionResult[]): ConsensusSignal => {
    const validResults = results.filter(r => !r.error);
    if (validResults.length === 0) {
      return { signal: 'neutral', confidence: 0, agreementLevel: 0, agentCount: 0 };
    }

    let bullishScore = 0;
    let bearishScore = 0;
    let neutralScore = 0;
    let totalWeight = 0;

    for (const result of validResults) {
      const weight = result.result.confidence / 100;
      totalWeight += weight;

      switch (result.result.signal) {
        case 'bullish':
          bullishScore += weight;
          break;
        case 'bearish':
          bearishScore += weight;
          break;
        case 'neutral':
          neutralScore += weight;
          break;
      }
    }

    let consensusSignal: 'bullish' | 'bearish' | 'neutral';
    const maxScore = Math.max(bullishScore, bearishScore, neutralScore);

    if (maxScore === bullishScore) {
      consensusSignal = 'bullish';
    } else if (maxScore === bearishScore) {
      consensusSignal = 'bearish';
    } else {
      consensusSignal = 'neutral';
    }

    const avgConfidence = validResults.reduce((sum, r) => sum + r.result.confidence, 0) / validResults.length;
    const agreementLevel = totalWeight > 0 ? (maxScore / totalWeight) * 100 : 0;

    return {
      signal: consensusSignal,
      confidence: Math.round(avgConfidence),
      agreementLevel: Math.round(agreementLevel),
      agentCount: validResults.length
    };
  };

  const getSignalIcon = (signal: string) => {
    switch (signal) {
      case 'bullish':
        return <TrendingUp className="h-4 w-4 text-green-400" />;
      case 'bearish':
        return <TrendingDown className="h-4 w-4 text-red-400" />;
      default:
        return <Minus className="h-4 w-4 text-gray-400" />;
    }
  };

  const getSignalColor = (signal: string) => {
    switch (signal) {
      case 'bullish':
        return 'text-green-400 bg-green-400/10 border-green-400/20';
      case 'bearish':
        return 'text-red-400 bg-red-400/10 border-red-400/20';
      default:
        return 'text-gray-400 bg-gray-400/10 border-gray-400/20';
    }
  };

  const toggleAgentExpansion = (agentId: string) => {
    setExpandedAgents(prev => {
      const newSet = new Set(prev);
      if (newSet.has(agentId)) {
        newSet.delete(agentId);
      } else {
        newSet.add(agentId);
      }
      return newSet;
    });
  };

  const successfulExecutions = agentResults.filter(r => !r.error).length;
  const failedExecutions = agentResults.filter(r => r.error).length;
  const totalExecutionTime = agentResults.reduce((sum, r) => sum + r.executionTime, 0);

  return (
    <Card className={`bg-[#1A1A1A] border-gray-800 ${className}`}>
      <CardHeader
        className="cursor-pointer hover:bg-gray-800/50 transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Bot className="h-5 w-5 text-blue-400" />
            <CardTitle className="text-lg text-white">
              Agent Analysis
            </CardTitle>
            <Badge variant="secondary" className="bg-blue-500/20 text-blue-300">
              {successfulExecutions}/{agentResults.length} agent{agentResults.length !== 1 ? 's' : ''}
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            <div className="text-sm text-gray-400">
              {totalExecutionTime}ms
            </div>
            {isExpanded ? (
              <ChevronUp className="h-4 w-4 text-gray-400" />
            ) : (
              <ChevronDown className="h-4 w-4 text-gray-400" />
            )}
          </div>
        </div>

        {/* Quick Summary */}
        <div className="flex flex-wrap gap-2 mt-2">
          {Object.entries(resultsBySymbol).map(([symbol, results]) => {
            const consensus = calculateConsensus(results);
            return (
              <div key={symbol} className="flex items-center gap-1 text-sm">
                <span className="text-gray-300">{symbol}:</span>
                <Badge className={`${getSignalColor(consensus.signal)} text-xs`}>
                  {getSignalIcon(consensus.signal)}
                  <span className="ml-1">{consensus.signal.toUpperCase()}</span>
                  <span className="ml-1">({consensus.confidence}%)</span>
                </Badge>
              </div>
            );
          })}
        </div>
      </CardHeader>

      {isExpanded && (
          <CardContent className="pt-0">
            {/* Execution Summary */}
            <div className="grid grid-cols-3 gap-4 mb-4 p-3 bg-gray-800/30 rounded-lg">
              <div className="text-center">
                <div className="text-lg font-semibold text-green-400">{successfulExecutions}</div>
                <div className="text-xs text-gray-400">Successful</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-red-400">{failedExecutions}</div>
                <div className="text-xs text-gray-400">Failed</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-blue-400">{totalExecutionTime}ms</div>
                <div className="text-xs text-gray-400">Total Time</div>
              </div>
            </div>

            {/* Results by Symbol */}
            {Object.entries(resultsBySymbol).map(([symbol, results]) => {
              const consensus = calculateConsensus(results);
              const validResults = results.filter(r => !r.error);
              const failedResults = results.filter(r => r.error);

              return (
                <div key={symbol} className="mb-6 last:mb-0">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-lg font-semibold text-white">{symbol}</h3>
                    <div className="flex items-center gap-2">
                      <Badge className={`${getSignalColor(consensus.signal)}`}>
                        {getSignalIcon(consensus.signal)}
                        <span className="ml-1">{consensus.signal.toUpperCase()}</span>
                      </Badge>
                      <div className="text-sm text-gray-400">
                        {consensus.confidence}% confidence
                      </div>
                    </div>
                  </div>

                  {/* Consensus Details */}
                  <div className="bg-gray-800/30 rounded-lg p-3 mb-3">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-400">Agreement Level:</span>
                        <span className="ml-2 text-white">{consensus.agreementLevel}%</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Contributing Agents:</span>
                        <span className="ml-2 text-white">{consensus.agentCount}</span>
                      </div>
                    </div>
                  </div>

                  {/* Individual Agent Results */}
                  <div className="space-y-2">
                    {validResults.map((result) => (
                      <div key={`${result.agentId}-${result.symbol}`} className="border border-gray-700 rounded-lg">
                        <div
                          className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-800/30"
                          onClick={() => toggleAgentExpansion(`${result.agentId}-${result.symbol}`)}
                        >
                          <div className="flex items-center gap-3">
                            <CheckCircle className="h-4 w-4 text-green-400" />
                            <span className="font-medium text-white">{result.agentName}</span>
                            <Badge className={`${getSignalColor(result.result.signal)} text-xs`}>
                              {result.result.signal.toUpperCase()} ({result.result.confidence}%)
                            </Badge>
                          </div>
                          <div className="flex items-center gap-2 text-sm text-gray-400">
                            <Clock className="h-3 w-3" />
                            {result.executionTime}ms
                            {expandedAgents.has(`${result.agentId}-${result.symbol}`) ? (
                              <ChevronUp className="h-4 w-4" />
                            ) : (
                              <ChevronDown className="h-4 w-4" />
                            )}
                          </div>
                        </div>

                        {expandedAgents.has(`${result.agentId}-${result.symbol}`) && (
                          <div className="px-3 pb-3 border-t border-gray-700">
                            <div className="mt-2 space-y-2">
                              <div>
                                <span className="text-sm text-gray-400">Reasoning:</span>
                                <p className="text-sm text-gray-300 mt-1">{result.result.reasoning}</p>
                              </div>

                              {Object.keys(result.result.metrics).length > 0 && (
                                <div>
                                  <span className="text-sm text-gray-400">Key Metrics:</span>
                                  <div className="mt-1 grid grid-cols-2 gap-2 text-xs">
                                    {Object.entries(result.result.metrics).slice(0, 4).map(([key, value]) => (
                                      <div key={key} className="flex justify-between">
                                        <span className="text-gray-400">{key}:</span>
                                        <span className="text-gray-300">
                                          {typeof value === 'number' ? value.toFixed(2) : value}
                                        </span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}

                    {/* Failed Results */}
                    {failedResults.map((result) => (
                      <div key={`${result.agentId}-${result.symbol}-failed`} className="border border-red-700/50 rounded-lg">
                        <div className="flex items-center justify-between p-3">
                          <div className="flex items-center gap-3">
                            <AlertCircle className="h-4 w-4 text-red-400" />
                            <span className="font-medium text-white">{result.agentName}</span>
                            <Badge className="text-red-400 bg-red-400/10 border-red-400/20 text-xs">
                              FAILED
                            </Badge>
                          </div>
                          <div className="text-sm text-gray-400">
                            {result.error}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              );
            })}
          </CardContent>
        )}
    </Card>
  );
}
