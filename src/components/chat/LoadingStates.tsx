import React from 'react';
import { Loader2, <PERSON>, BarChart3, TrendingUp } from 'lucide-react';

// OsisThinking component for thinking state
export const OsisThinking: React.FC = () => {
  return (
    <div className="flex items-center gap-3 p-4">
      <div className="relative">
        <div className="w-8 h-8 rounded-full border-2 border-blue-500/30 animate-spin">
          <div className="absolute inset-0 rounded-full border-2 border-t-blue-500 border-r-blue-500 border-b-transparent border-l-transparent animate-spin" />
        </div>
        <Brain className="absolute inset-2 w-4 h-4 text-blue-400" />
      </div>
      <div className="space-y-1">
        <p className="text-sm font-medium text-white/90">Analyzing your request...</p>
        <p className="text-xs text-white/60">Processing query and identifying market symbols</p>
      </div>
    </div>
  );
};

// MarketDataLoading component for market data loading state
export const MarketDataLoading: React.FC = () => {
  return (
    <div className="flex items-center gap-3 p-4">
      <div className="relative">
        <div className="w-8 h-8 rounded-full border-2 border-green-500/30 animate-spin">
          <div className="absolute inset-0 rounded-full border-2 border-t-green-500 border-r-green-500 border-b-transparent border-l-transparent animate-spin" />
        </div>
        <BarChart3 className="absolute inset-2 w-4 h-4 text-green-400" />
      </div>
      <div className="space-y-1">
        <p className="text-sm font-medium text-white/90">Fetching market data...</p>
        <p className="text-xs text-white/60">Retrieving real-time prices and historical data</p>
      </div>
    </div>
  );
};

// ChartSkeleton component for chart loading state
export const ChartSkeleton: React.FC = () => {
  return (
    <div className="w-full h-[400px] bg-gray-800/30 rounded-lg animate-pulse">
      <div className="p-4 space-y-4">
        <div className="flex justify-between items-center">
          <div className="h-4 bg-gray-700/50 rounded w-24"></div>
          <div className="h-4 bg-gray-700/50 rounded w-16"></div>
        </div>
        <div className="h-[300px] bg-gray-700/30 rounded relative">
          <div className="absolute inset-0 flex items-center justify-center">
            <TrendingUp className="w-12 h-12 text-gray-600/50" />
          </div>
        </div>
        <div className="flex justify-between">
          <div className="h-3 bg-gray-700/50 rounded w-20"></div>
          <div className="h-3 bg-gray-700/50 rounded w-20"></div>
          <div className="h-3 bg-gray-700/50 rounded w-20"></div>
        </div>
      </div>
    </div>
  );
};

// LoadingProgress component for showing progress
export const LoadingProgress: React.FC<{ content?: any }> = ({ content }) => {
  return (
    <div className="flex items-center gap-2 p-4">
      <Loader2 className="w-4 h-4 animate-spin text-blue-400" />
      <span className="text-sm text-white/70">
        {content?.symbols?.length > 0 
          ? `Processing ${content.symbols.join(', ')}...` 
          : 'Processing data...'
        }
      </span>
    </div>
  );
};

// EnhancedChart component placeholder
export const EnhancedChart: React.FC<{ symbol?: string; data?: any }> = ({ symbol, data }) => {
  return (
    <div className="w-full h-[400px] bg-gray-900/50 rounded-lg border border-gray-700/50 flex items-center justify-center">
      <div className="text-center space-y-2">
        <TrendingUp className="w-8 h-8 text-gray-500 mx-auto" />
        <p className="text-sm text-gray-400">
          {symbol ? `Chart for ${symbol}` : 'Chart loading...'}
        </p>
      </div>
    </div>
  );
};

// Simple LoadingMessage component for general loading states
export const LoadingMessage: React.FC<{ message?: string }> = ({ message = "Loading..." }) => {
  return (
    <div className="flex items-center gap-2 p-3">
      <Loader2 className="w-4 h-4 animate-spin text-blue-400" />
      <span className="text-sm text-white/70">{message}</span>
    </div>
  );
};

// Default export for backward compatibility
export default LoadingMessage;
