import React from 'react';
import { Target, ArrowUp, ArrowDown, Gauge, Flame, BarChart2, ArrowUpDown, Shield, Clock, DollarSign, Percent } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TradingStrategyProps {
  symbol: string;
  recommendation: 'LONG' | 'SHORT';
  confidence: 'HIGH' | 'MEDIUM' | 'LOW';
  currentPrice: number;
  entry: {
    primary: number;
    secondary: number;
  };
  exit: {
    target1: number;
    target2: number;
    target3: number;
  };
  stopLoss: number;
  riskReward: number;
  support: number[];
  resistance: number[];
  timeframe: string;
}

const TradingStrategyCard: React.FC<TradingStrategyProps> = ({
  symbol,
  recommendation,
  confidence,
  currentPrice,
  entry,
  exit,
  stopLoss,
  riskReward,
  support,
  resistance,
  timeframe
}) => {
  const isBullish = recommendation === 'LONG';
  const confidenceColor = {
    HIGH: 'bg-emerald-500/20 text-emerald-300',
    MEDIUM: 'bg-amber-500/20 text-amber-300',
    LOW: 'bg-rose-500/20 text-rose-300'
  };

  return (
    <div className="relative group mt-6">
      {/* Gradient backdrop */}
      <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/5 to-transparent opacity-50 transition-opacity group-hover:opacity-80 -z-10" />
      
      <div className="border border-[#1A1A1C]/60 bg-gradient-to-b from-[#0A0A0C] to-[#0A0A0C]/50 rounded-2xl p-5 backdrop-blur-xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-[#1A1A1C] border border-[#2A2A2E]">
              <Target className="h-5 w-5 text-purple-400" />
            </div>
            <h3 className="text-lg font-semibold text-white/90">
              Trading Plan for <span className="text-purple-400">{symbol}</span>
            </h3>
          </div>
          <div className={`px-3 py-1 rounded-full ${confidenceColor[confidence]} text-sm font-medium`}>
            {confidence} Confidence
          </div>
        </div>

        {/* Grid Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recommendation Column */}
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-lg ${isBullish ? 'bg-emerald-500/20' : 'bg-rose-500/20'}`}>
                {isBullish ? (
                  <ArrowUp className="h-5 w-5 text-emerald-400" />
                ) : (
                  <ArrowDown className="h-5 w-5 text-rose-400" />
                )}
              </div>
              <div>
                <p className="text-sm text-white/60">Recommendation</p>
                <p className={`text-xl font-semibold ${isBullish ? 'text-emerald-400' : 'text-rose-400'}`}>
                  {recommendation}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-blue-500/20">
                <Gauge className="h-5 w-5 text-blue-400" />
              </div>
              <div>
                <p className="text-sm text-white/60">Current Price</p>
                <p className="text-xl font-semibold text-white/90">
                  ${currentPrice.toFixed(2)}
                </p>
              </div>
            </div>
          </div>

          {/* Entry & Exit Points */}
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-amber-500/20">
                <Flame className="h-5 w-5 text-amber-400" />
              </div>
              <div>
                <p className="text-sm text-white/60">Entry Points</p>
                <div className="flex gap-2">
                  <span className="text-emerald-400">${entry.primary.toFixed(2)}</span>
                  <span className="text-white/40">|</span>
                  <span className="text-amber-400">${entry.secondary.toFixed(2)}</span>
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-purple-500/20">
                <BarChart2 className="h-5 w-5 text-purple-400" />
              </div>
              <div>
                <p className="text-sm text-white/60">Take Profit Targets</p>
                <div className="flex gap-2">
                  <span className="text-purple-400">T1: ${exit.target1.toFixed(2)}</span>
                  <span className="text-white/40">|</span>
                  <span className="text-purple-400">T2: ${exit.target2.toFixed(2)}</span>
                  <span className="text-white/40">|</span>
                  <span className="text-purple-400">T3: ${exit.target3.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Risk Management */}
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-rose-500/20">
                <Shield className="h-5 w-5 text-rose-400" />
              </div>
              <div>
                <p className="text-sm text-white/60">Stop Loss</p>
                <p className="text-rose-400">${stopLoss.toFixed(2)}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-emerald-500/20">
                <ArrowUpDown className="h-5 w-5 text-emerald-400" />
              </div>
              <div>
                <p className="text-sm text-white/60">Risk/Reward</p>
                <p className="text-emerald-400">1:{riskReward.toFixed(2)}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Key Levels */}
        <div className="mt-6 pt-4 border-t border-[#1A1A1C]/60">
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-blue-500/20">
                <ArrowUp className="h-5 w-5 text-blue-400" />
              </div>
              <div>
                <p className="text-sm text-white/60">Key Support</p>
                <div className="flex gap-2 flex-wrap">
                  {support.map((level, i) => (
                    <span key={i} className="text-blue-400">${level.toFixed(2)}</span>
                  ))}
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-rose-500/20">
                <ArrowDown className="h-5 w-5 text-rose-400" />
              </div>
              <div>
                <p className="text-sm text-white/60">Key Resistance</p>
                <div className="flex gap-2 flex-wrap">
                  {resistance.map((level, i) => (
                    <span key={i} className="text-rose-400">${level.toFixed(2)}</span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-4 flex items-center justify-between text-sm text-white/50">
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            <span>Timeframe: {timeframe}</span>
          </div>
          <div className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            <span>Price in USD</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TradingStrategyCard; 