import React from 'react';
import { Bot } from 'lucide-react';

interface AgentExecutionResult {
  agentId: string;
  agentName: string;
  symbol: string;
  result: {
    signal: 'bullish' | 'bearish' | 'neutral';
    confidence: number;
    reasoning: string;
    metrics: Record<string, any>;
    executionPath: string[];
    executionTime: number;
    timestamp: string;
  };
  executionTime: number;
  error?: string;
}

interface SelectedAgentCardProps {
  agentResults: AgentExecutionResult[];
  symbols: string[];
  className?: string;
}

const SelectedAgentCard: React.FC<SelectedAgentCardProps> = ({
  agentResults,
  symbols,
  className = ""
}) => {
  if (!agentResults || agentResults.length === 0) {
    return null;
  }

  // Get the first successful result
  const result = agentResults.find(r => !r.error && r.result);
  if (!result) {
    return null;
  }

  const getSignalColor = (signal: string) => {
    switch (signal.toLowerCase()) {
      case 'bullish':
        return 'text-green-400';
      case 'bearish':
        return 'text-red-400';
      case 'neutral':
        return 'text-gray-400';
      default:
        return 'text-gray-400';
    }
  };

  const getSignalBadgeColor = (signal: string) => {
    switch (signal.toLowerCase()) {
      case 'bullish':
        return 'bg-green-500/20 text-green-300 border-green-500/30';
      case 'bearish':
        return 'bg-red-500/20 text-red-300 border-red-500/30';
      case 'neutral':
        return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
      default:
        return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    }
  };

  // Professional mini chart component matching the expert opinion style
  const MiniTrendChart = ({ signal }: { signal: string }) => {
    const agentId = `agent_${Math.random().toString(36).substr(2, 9)}`;

    // Generate ultra-smooth curve data points
    const generateChartData = () => {
      const points = 40; // More points for smoother curves
      const data = [];

      switch (signal.toLowerCase()) {
        case 'bullish':
          // Ultra-smooth upward trend
          for (let i = 0; i < points; i++) {
            const progress = i / (points - 1);
            const baseValue = 150 - (progress * 80); // Start high, go lower (inverted Y)
            // Multiple harmonics for natural smoothness
            const noise = Math.sin(i * 0.2) * 6 +
                         Math.cos(i * 0.5) * 3 +
                         Math.sin(i * 0.8) * 2;
            data.push(Math.max(30, Math.min(170, baseValue + noise)));
          }
          break;
        case 'bearish':
          // Ultra-smooth downward trend
          for (let i = 0; i < points; i++) {
            const progress = i / (points - 1);
            const baseValue = 50 + (progress * 80); // Start low, go higher (inverted Y)
            // Multiple harmonics for natural smoothness
            const noise = Math.sin(i * 0.25) * 5 +
                         Math.cos(i * 0.6) * 3 +
                         Math.sin(i * 0.9) * 2;
            data.push(Math.max(30, Math.min(170, baseValue + noise)));
          }
          break;
        case 'neutral':
        default:
          // Ultra-smooth volatile sideways movement
          for (let i = 0; i < points; i++) {
            const baseValue = 100; // Middle
            // Complex wave pattern for realistic volatility
            const noise = Math.sin(i * 0.4) * 20 +
                         Math.cos(i * 0.7) * 12 +
                         Math.sin(i * 1.1) * 8 +
                         Math.cos(i * 0.15) * 15;
            data.push(Math.max(30, Math.min(170, baseValue + noise)));
          }
          break;
      }
      return data;
    };

    const chartData = generateChartData();

    // Create ultra-smooth curved path using advanced bezier curves
    const createSmoothPath = () => {
      if (chartData.length < 2) return '';

      const xStep = 1000 / (chartData.length - 1);
      let path = `M 0,${chartData[0]}`;

      for (let i = 1; i < chartData.length; i++) {
        const x = i * xStep;
        const y = chartData[i];
        const prevX = (i - 1) * xStep;
        const prevY = chartData[i - 1];

        // Calculate slopes for smoother curves
        let prevSlope = 0;
        let nextSlope = 0;

        if (i > 1) {
          prevSlope = (chartData[i - 1] - chartData[i - 2]) / xStep;
        }
        if (i < chartData.length - 1) {
          nextSlope = (chartData[i + 1] - chartData[i]) / xStep;
        }

        // Smooth control points based on slopes
        const tension = 0.4; // Smoothness factor
        const cp1x = prevX + xStep * tension;
        const cp1y = prevY + (xStep * tension * prevSlope);
        const cp2x = x - xStep * tension;
        const cp2y = y - (xStep * tension * nextSlope);

        path += ` C ${cp1x},${cp1y} ${cp2x},${cp2y} ${x},${y}`;
      }

      return path;
    };

    // Create filled area path
    const createFilledPath = () => {
      const smoothPath = createSmoothPath();
      return `${smoothPath} L 1000,200 L 0,200 Z`;
    };

    const getChartColor = () => {
      switch (signal.toLowerCase()) {
        case 'bullish':
          return '#22c55e'; // Green
        case 'bearish':
          return '#ff3a50'; // Red like in your example
        case 'neutral':
        default:
          return '#d4d4d8'; // Light gray
      }
    };

    const getGradientColor = () => {
      switch (signal.toLowerCase()) {
        case 'bullish':
          return '#22c55e30'; // Green with transparency
        case 'bearish':
          return '#ff3a5030'; // Red with transparency
        case 'neutral':
        default:
          return '#d4d4d830'; // Gray with transparency
      }
    };

    return (
      <div className="w-full h-20 overflow-hidden relative">
        <svg viewBox="0 0 1000 200" className="w-full h-full relative z-10" preserveAspectRatio="none">
          <defs>
            <linearGradient id={`gradient-${agentId}`} x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor={getGradientColor()}></stop>
              <stop offset="100%" stopColor="transparent"></stop>
            </linearGradient>
          </defs>

          {/* Filled area */}
          <path
            d={createFilledPath()}
            fill={`url(#gradient-${agentId})`}
            strokeWidth="0"
            opacity="1"
          />

          {/* Main line with glow effect */}
          <path
            d={createSmoothPath()}
            fill="none"
            stroke={getChartColor()}
            strokeWidth="3.5"
            strokeLinecap="round"
            strokeLinejoin="round"
            filter={`drop-shadow(0 0 3px ${getChartColor()}80)`}
          />
        </svg>
      </div>
    );
  };

  return (
    <div className={`${className}`}>
      {/* Single agent card matching the expert opinion style */}
      <div className="flex items-center justify-between p-3 bg-[#0A0A0C]/50 rounded-lg border border-[#1A1A1C]/40 hover:border-[#1A1A1C]/60 transition-colors">
        {/* Left side - Avatar and name */}
        <div className="flex items-center gap-3">
          {/* Agent avatar */}
          <div className="w-8 h-8 rounded-full bg-[#1A1A1C] border border-[#2A2A2E] flex items-center justify-center">
            <Bot className="w-4 h-4 text-blue-400" />
          </div>

          {/* Agent name */}
          <span className="text-white/90 text-sm font-medium">
            {result.agentName}
          </span>
        </div>

        {/* Right side - Signal badge */}
        <div className={`px-2 py-1 rounded text-xs font-medium border ${getSignalBadgeColor(result.result.signal)}`}>
          {result.result.signal.toUpperCase()}
        </div>
      </div>

      {/* Mini trend chart below the card */}
      <div className="p-3">
        <MiniTrendChart signal={result.result.signal} />
      </div>
    </div>
  );
};

export default SelectedAgentCard;
