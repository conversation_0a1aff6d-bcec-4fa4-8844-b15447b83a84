/**
 * Lazy-loaded route components for better performance
 * This file handles code splitting and lazy loading of page components
 */

import React, { Suspense, lazy } from 'react';
import { preloadingService } from '@/services/preloadingService';

// Loading component for suspense fallbacks
const RouteLoadingSpinner = () => (
  <div className="flex items-center justify-center h-full min-h-[400px]">
    <div className="flex flex-col items-center space-y-4">
      <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white/30"></div>
      <p className="text-white/60 text-sm">Loading...</p>
    </div>
  </div>
);

// Enhanced loading component with progress indication
const EnhancedRouteLoader = ({ message = "Loading page..." }: { message?: string }) => (
  <div className="flex items-center justify-center h-full min-h-[400px]">
    <div className="flex flex-col items-center space-y-4 max-w-sm text-center">
      <div className="relative">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        <div className="absolute inset-0 rounded-full border-2 border-white/10"></div>
      </div>
      <div className="space-y-2">
        <p className="text-white/80 text-sm font-medium">{message}</p>
        <div className="w-48 h-1 bg-white/10 rounded-full overflow-hidden">
          <div className="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-pulse"></div>
        </div>
      </div>
    </div>
  </div>
);

// Lazy load core pages
export const LazyHome = lazy(() => 
  import('@/pages/Home').then(module => {
    // Preload related resources when Home is loaded
    preloadingService.prefetchRouteData('/');
    return module;
  })
);

export const LazySettings = lazy(() => 
  import('@/pages/UnifiedSettings').then(module => {
    preloadingService.prefetchRouteData('/settings');
    return module;
  })
);

export const LazyModelSettings = lazy(() => import('@/pages/ModelSettings'));

export const LazySubscription = lazy(() => import('@/pages/Subscription'));

export const LazyManageSubscription = lazy(() => import('@/pages/ManageSubscription'));

// Lazy load trading pages
export const LazyTrades = lazy(() => 
  import('@/pages/Trades').then(module => {
    preloadingService.prefetchRouteData('/trades');
    return module;
  })
);

export const LazyPortfolioManager = lazy(() => 
  import('@/pages/PortfolioManager').then(module => {
    preloadingService.prefetchRouteData('/portfolio-manager');
    return module;
  })
);

export const LazyPortfolioNews = lazy(() => import('@/pages/PortfolioNews'));

// Lazy load agent pages
export const LazyAgentBuilder = lazy(() => 
  import('@/pages/AgentBuilder').then(module => {
    preloadingService.prefetchRouteData('/agent-builder');
    return module;
  })
);

export const LazyAgentManagement = lazy(() => 
  import('@/pages/AgentManagement').then(module => {
    preloadingService.prefetchRouteData('/agent-management');
    return module;
  })
);

export const LazyAgentScanner = lazy(() => import('@/pages/AgentScanner'));

export const LazyAgentBacktesting = lazy(() => import('@/pages/AgentBacktesting'));

export const LazyBacktestResults = lazy(() => import('@/pages/BacktestResults'));

export const LazyAgentBuilderDocs = lazy(() => import('@/pages/AgentBuilderDocs'));

// Lazy load discovery and search pages
export const LazyDiscover = lazy(() => 
  import('@/pages/Discover').then(module => {
    preloadingService.prefetchRouteData('/discover');
    return module;
  })
);

export const LazyStockSearch = lazy(() => 
  import('@/pages/StockSearch').then(module => {
    preloadingService.prefetchRouteData('/stock-search');
    return module;
  })
);

export const LazyStockDetail = lazy(() => import('@/pages/StockDetail'));

// Lazy load utility pages
export const LazyApiTest = lazy(() => import('@/pages/ApiTest'));

export const LazyStripeTest = lazy(() => import('@/pages/StripeTest'));

export const LazyScreener = lazy(() => import('@/pages/Screener'));

export const LazyChartGenerator = lazy(() => import('@/pages/ChartGenerator'));

export const LazyTradingDemo = lazy(() => import('@/pages/TradingDemo'));

export const LazyTrading = lazy(() => import('@/pages/Trading'));

// Lazy load auth and legal pages
export const LazyAbout = lazy(() => import('@/pages/About'));

export const LazyTermsOfService = lazy(() => import('@/pages/TermsOfService'));

export const LazyPrivacyPolicy = lazy(() => import('@/pages/PrivacyPolicy'));

export const LazyWhopCallback = lazy(() => import('@/pages/WhopCallback'));

// Higher-order component for wrapping lazy routes with enhanced loading
export const withLazyLoading = (
  LazyComponent: React.LazyExoticComponent<React.ComponentType<any>>,
  loadingMessage?: string
) => {
  return (props: any) => (
    <Suspense fallback={<EnhancedRouteLoader message={loadingMessage} />}>
      <LazyComponent {...props} />
    </Suspense>
  );
};

// Pre-configured lazy route components with custom loading messages
export const LazyRoutes = {
  Home: withLazyLoading(LazyHome, "Loading dashboard..."),
  Settings: withLazyLoading(LazySettings, "Loading settings..."),
  ModelSettings: withLazyLoading(LazyModelSettings, "Loading model settings..."),
  Subscription: withLazyLoading(LazySubscription, "Loading subscription..."),
  ManageSubscription: withLazyLoading(LazyManageSubscription, "Loading subscription management..."),
  
  Trades: withLazyLoading(LazyTrades, "Loading trades..."),
  PortfolioManager: withLazyLoading(LazyPortfolioManager, "Loading portfolio..."),
  PortfolioNews: withLazyLoading(LazyPortfolioNews, "Loading news..."),
  
  AgentBuilder: withLazyLoading(LazyAgentBuilder, "Loading agent builder..."),
  AgentManagement: withLazyLoading(LazyAgentManagement, "Loading agent management..."),
  AgentScanner: withLazyLoading(LazyAgentScanner, "Loading agent scanner..."),
  AgentBacktesting: withLazyLoading(LazyAgentBacktesting, "Loading backtesting..."),
  BacktestResults: withLazyLoading(LazyBacktestResults, "Loading backtest results..."),
  AgentBuilderDocs: withLazyLoading(LazyAgentBuilderDocs, "Loading documentation..."),
  
  Discover: withLazyLoading(LazyDiscover, "Loading marketplace..."),
  StockSearch: withLazyLoading(LazyStockSearch, "Loading stock search..."),
  StockDetail: withLazyLoading(LazyStockDetail, "Loading stock details..."),
  
  ApiTest: withLazyLoading(LazyApiTest, "Loading API test..."),
  StripeTest: withLazyLoading(LazyStripeTest, "Loading Stripe test..."),
  Screener: withLazyLoading(LazyScreener, "Loading stock screener..."),
  ChartGenerator: withLazyLoading(LazyChartGenerator, "Loading chart generator..."),
  TradingDemo: withLazyLoading(LazyTradingDemo, "Loading trading demo..."),
  Trading: withLazyLoading(LazyTrading, "Loading trading interface..."),

  About: withLazyLoading(LazyAbout, "Loading about page..."),
  TermsOfService: withLazyLoading(LazyTermsOfService, "Loading terms..."),
  PrivacyPolicy: withLazyLoading(LazyPrivacyPolicy, "Loading privacy policy..."),
  WhopCallback: withLazyLoading(LazyWhopCallback, "Processing authentication..."),
};

// Route preloading utilities
export const preloadRoute = (routeName: keyof typeof LazyRoutes) => {
  const routeMap = {
    Home: () => import('@/pages/Home'),
    Settings: () => import('@/pages/UnifiedSettings'),
    ModelSettings: () => import('@/pages/ModelSettings'),
    Subscription: () => import('@/pages/Subscription'),
    ManageSubscription: () => import('@/pages/ManageSubscription'),
    
    Trades: () => import('@/pages/Trades'),
    PortfolioManager: () => import('@/pages/PortfolioManager'),
    PortfolioNews: () => import('@/pages/PortfolioNews'),
    
    AgentBuilder: () => import('@/pages/AgentBuilder'),
    AgentManagement: () => import('@/pages/AgentManagement'),
    AgentScanner: () => import('@/pages/AgentScanner'),
    AgentBacktesting: () => import('@/pages/AgentBacktesting'),
    BacktestResults: () => import('@/pages/BacktestResults'),
    AgentBuilderDocs: () => import('@/pages/AgentBuilderDocs'),
    
    Discover: () => import('@/pages/Discover'),
    StockSearch: () => import('@/pages/StockSearch'),
    StockDetail: () => import('@/pages/StockDetail'),
    
    ApiTest: () => import('@/pages/ApiTest'),
    StripeTest: () => import('@/pages/StripeTest'),
    Screener: () => import('@/pages/Screener'),
    ChartGenerator: () => import('@/pages/ChartGenerator'),
    TradingDemo: () => import('@/pages/TradingDemo'),
    Trading: () => import('@/pages/Trading'),

    About: () => import('@/pages/About'),
    TermsOfService: () => import('@/pages/TermsOfService'),
    PrivacyPolicy: () => import('@/pages/PrivacyPolicy'),
    WhopCallback: () => import('@/pages/WhopCallback'),
  };

  const preloadFn = routeMap[routeName];
  if (preloadFn) {
    preloadFn().catch(error => {
      console.warn(`Failed to preload route ${routeName}:`, error);
    });
  }
};

// Preload multiple routes
export const preloadRoutes = (routeNames: (keyof typeof LazyRoutes)[]) => {
  routeNames.forEach(routeName => preloadRoute(routeName));
};

// Auto-preload common routes on idle
export const preloadCommonRoutes = () => {
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      preloadRoutes(['PortfolioManager', 'Trades', 'AgentManagement', 'Discover']);
    });
  } else {
    // Fallback for browsers without requestIdleCallback
    setTimeout(() => {
      preloadRoutes(['PortfolioManager', 'Trades', 'AgentManagement', 'Discover']);
    }, 2000);
  }
};
