import React from 'react';

interface ChartDataPoint {
  date: string;
  value: number;
}

interface MiniPortfolioChartProps {
  performance: number;
  width?: number;
  height?: number;
  showBadge?: boolean;
  className?: string;
  chartData?: ChartDataPoint[];
}

const MiniPortfolioChart: React.FC<MiniPortfolioChartProps> = ({
  performance,
  width = 300,
  height = 160,
  showBadge = true,
  className = "",
  chartData = []
}) => {
  const isPositive = performance >= 0;
  const color = isPositive ? "rgba(78, 184, 151, 0.9)" : "rgba(229, 128, 128, 0.9)";
  const gradientColor = isPositive ? "#4EB897" : "#E58080";

  // Generate a path from real chart data or fallback to a generated path
  const generatePathFromData = () => {
    const svgWidth = 100;
    const svgHeight = 50;

    // If we have real chart data, use it
    if (chartData && chartData.length > 1) {
      // Find min and max values for scaling
      const values = chartData.map(point => point.value);
      const minValue = Math.min(...values);
      const maxValue = Math.max(...values);
      const valueRange = maxValue - minValue;

      // Scale points to fit in the SVG viewBox
      const points = chartData.map((point, index) => {
        // X coordinate scales from 0 to svgWidth based on position in array
        const x = (index / (chartData.length - 1)) * svgWidth;

        // Y coordinate scales from 0 to svgHeight based on value
        // Invert Y axis (SVG 0 is at top)
        let y;
        if (valueRange === 0) {
          // If all values are the same, draw a horizontal line in the middle
          y = svgHeight / 2;
        } else {
          // Scale the value to fit in the SVG height
          // Leave some margin at top and bottom (10%)
          const margin = svgHeight * 0.1;
          const availableHeight = svgHeight - (2 * margin);
          y = svgHeight - margin - ((point.value - minValue) / valueRange) * availableHeight;
        }

        return { x, y };
      });

      // Generate SVG path
      let path = `M${points[0].x},${points[0].y}`;

      // Use a smooth curve through the points
      for (let i = 1; i < points.length; i++) {
        path += ` L${points[i].x},${points[i].y}`;
      }

      return path;
    } else {
      // Fallback to generated path if no data
      const width = svgWidth;
      const height = svgHeight;

      if (isPositive) {
        // Upward trend for positive performance with realistic fluctuations
        return `M0,${height*0.7}
                C${width*0.1},${height*0.8} ${width*0.15},${height*0.6} ${width*0.25},${height*0.65}
                S${width*0.35},${height*0.5} ${width*0.45},${height*0.55}
                S${width*0.55},${height*0.4} ${width*0.65},${height*0.45}
                S${width*0.75},${height*0.3} ${width*0.85},${height*0.35}
                S${width*0.95},${height*0.2} ${width},${height*0.25}`;
      } else {
        // Downward trend for negative performance with realistic fluctuations
        return `M0,${height*0.3}
                C${width*0.1},${height*0.25} ${width*0.15},${height*0.35} ${width*0.25},${height*0.3}
                S${width*0.35},${height*0.45} ${width*0.45},${height*0.4}
                S${width*0.55},${height*0.55} ${width*0.65},${height*0.5}
                S${width*0.75},${height*0.65} ${width*0.85},${height*0.6}
                S${width*0.95},${height*0.75} ${width},${height*0.7}`;
      }
    }
  };

  // Generate fill path that extends all the way to the bottom
  const generateFillPath = (linePath: string) => {
    return `${linePath} L100,50 L0,50 Z`;
  };

  const linePath = generatePathFromData();
  const fillPath = generateFillPath(linePath);

  return (
    <div className={`relative w-full h-full ${className}`}>
        <svg
          viewBox="0 0 100 50"
          className="w-full h-full"
          style={{ filter: 'drop-shadow(0px 1px 2px rgba(0,0,0,0.3))' }}
        >
          <defs>
            <linearGradient id={`chartGradient${isPositive ? 'Positive' : 'Negative'}-${performance}`} x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor={`${gradientColor}90`} />
              <stop offset="60%" stopColor={`${gradientColor}40`} />
              <stop offset="100%" stopColor={`${gradientColor}00`} />
            </linearGradient>
          </defs>

          {/* Fill path */}
          <path
            d={fillPath}
            fill={`url(#chartGradient${isPositive ? 'Positive' : 'Negative'}-${performance})`}
            opacity="0.25"
          />

          {/* Line path */}
          <path
            d={linePath}
            stroke={color}
            strokeWidth="0.75"
            fill="none"
          />
        </svg>

      {/* Performance percentage */}
      {showBadge && (
        <div className="absolute top-2 right-2 bg-[#0D0D0D] backdrop-blur-sm text-sm font-medium px-2.5 py-0.5 rounded-md border border-[#333]/40 shadow-[0_1px_3px_rgba(0,0,0,0.3)]"
          style={{
            color: isPositive ? 'rgba(78, 184, 151, 1)' : 'rgba(229, 128, 128, 1)',
            background: 'linear-gradient(to bottom, rgba(20,20,20,0.95), rgba(10,10,10,0.95))'
          }}>
          <span className="font-bold tracking-wide">{isPositive ? '+' : ''}{performance.toFixed(1)}%</span>
        </div>
      )}
    </div>
  );
};

export default MiniPortfolioChart;
