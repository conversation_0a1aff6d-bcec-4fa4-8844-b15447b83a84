import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Plus, Trash2, Clock } from 'lucide-react';

interface MobileChatHistoryProps {
  isOpen: boolean;
  onClose: () => void;
}

const MobileChatHistory: React.FC<MobileChatHistoryProps> = ({ 
  isOpen, 
  onClose 
}) => {
  // Sample chat history data
  const chatHistory = [
    {
      id: '1',
      title: 'AAPL Analysis',
      timestamp: '2 hours ago',
      preview: 'Analyzing Apple stock performance...'
    },
    {
      id: '2',
      title: 'Portfolio Review',
      timestamp: 'Yesterday',
      preview: 'Reviewing my tech portfolio...'
    },
    {
      id: '3',
      title: 'Market Trends',
      timestamp: '3 days ago',
      preview: 'What are the current market trends...'
    },
    {
      id: '4',
      title: 'TSLA Discussion',
      timestamp: '1 week ago',
      preview: 'Tesla earnings analysis...'
    }
  ];

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50">
          <motion.div
            className="absolute inset-0 bg-black/60 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />
          
          <motion.div
            className="absolute right-0 top-0 h-full w-80 bg-[#121212] border-l border-white/[0.08] shadow-2xl"
            initial={{ x: 320 }}
            animate={{ x: 0 }}
            exit={{ x: 320 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-white/[0.08]">
              <div className="flex items-center gap-2.5">
                <Clock className="w-5 h-5 text-white/60" />
                <span className="text-lg font-medium text-white tracking-tight">Chat History</span>
              </div>
              <button
                onClick={onClose}
                className="p-2 rounded-lg hover:bg-white/[0.08] transition-colors"
              >
                <X className="w-5 h-5 text-white/70" />
              </button>
            </div>

            {/* New Chat Button */}
            <div className="p-4 border-b border-white/[0.08]">
              <button className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-white/[0.06] hover:bg-white/[0.1] border border-white/[0.08] hover:border-white/[0.12] rounded-lg text-white/80 hover:text-white transition-all duration-200">
                <Plus className="w-4 h-4" />
                <span className="font-medium text-sm">New Chat</span>
              </button>
            </div>

            {/* Chat History List */}
            <div className="flex-1 overflow-y-auto p-4 space-y-3">
              {chatHistory.map((chat) => (
                <div
                  key={chat.id}
                  className="group p-4 rounded-lg bg-white/[0.02] hover:bg-white/[0.04] border border-white/[0.06] hover:border-white/[0.08] transition-all duration-200 cursor-pointer"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="text-white/80 text-sm font-medium mb-1 truncate">
                        {chat.title}
                      </div>
                      <div className="text-white/50 text-xs mb-2">
                        {chat.timestamp}
                      </div>
                      <div className="text-white/40 text-xs line-clamp-2">
                        {chat.preview}
                      </div>
                    </div>
                    <button className="opacity-0 group-hover:opacity-100 ml-2 w-6 h-6 rounded-md bg-white/[0.06] hover:bg-red-500/20 border border-white/[0.08] hover:border-red-500/30 flex items-center justify-center transition-all duration-200">
                      <Trash2 className="w-3 h-3 text-white/60 hover:text-red-400" />
                    </button>
                  </div>
                </div>
              ))}
            </div>

            {/* Footer */}
            <div className="p-4 border-t border-white/[0.08]">
              <div className="text-center text-white/40 text-xs">
                {chatHistory.length} conversations
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default MobileChatHistory;
