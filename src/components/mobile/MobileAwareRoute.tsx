import React from 'react';
import { useLocation } from 'react-router-dom';
import { useMobileDetection } from '@/hooks/useMobileDetection';
import MobileBuilderRestriction from './MobileBuilderRestriction';

interface MobileAwareRouteProps {
  children: React.ReactNode;
  restrictOnMobile?: boolean;
}

const MobileAwareRoute: React.FC<MobileAwareRouteProps> = ({ 
  children, 
  restrictOnMobile = false 
}) => {
  const { isMobile } = useMobileDetection();
  const location = useLocation();

  // Check if current path is a builder route
  const isBuilderRoute = location.pathname.includes('/agent-builder');

  // If it's mobile and we're on a builder route, show restriction
  if (isMobile && (restrictOnMobile || isBuilderRoute)) {
    return <MobileBuilderRestriction />;
  }

  // Otherwise, render children normally
  return <>{children}</>;
};

export default MobileAwareRoute;
