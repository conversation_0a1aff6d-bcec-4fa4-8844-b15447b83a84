import React from 'react';
import { Monitor, Smartphone } from 'lucide-react';

const MobileBuilderRestriction: React.FC = () => {
  return (
    <div className="h-full bg-[#0A0A0A] text-white flex flex-col items-center justify-center p-6">
      <div className="max-w-md mx-auto text-center">
        {/* Icon */}
        <div className="relative mb-8">
          <div className="w-20 h-20 mx-auto bg-white/[0.03] rounded-2xl border border-white/[0.08] flex items-center justify-center">
            <Monitor className="w-10 h-10 text-white" />
          </div>
          <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-white/[0.05] rounded-lg border border-white/[0.08] flex items-center justify-center">
            <Smartphone className="w-4 h-4 text-white/40" />
          </div>
        </div>

        {/* Title */}
        <h1 className="text-2xl font-medium text-white mb-4 font-sans">
          Desktop Required
        </h1>

        {/* Description */}
        <p className="text-white/60 text-base font-sans leading-relaxed mb-6">
          The Agent Builder requires a desktop or laptop computer for the best experience.
          Please switch to a larger screen to access this feature.
        </p>

        {/* Features available on mobile */}
        <div className="bg-white/[0.02] rounded-xl p-4 border border-white/[0.06] mb-6">
          <p className="text-white/50 text-sm font-sans mb-3">
            Available on mobile:
          </p>
          <div className="space-y-2 text-left">
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-green-400 rounded-full"></div>
              <span className="text-white/70 text-sm font-sans">Chat Interface</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-green-400 rounded-full"></div>
              <span className="text-white/70 text-sm font-sans">Portfolio Manager</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-green-400 rounded-full"></div>
              <span className="text-white/70 text-sm font-sans">Stock Scanner</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-green-400 rounded-full"></div>
              <span className="text-white/70 text-sm font-sans">Discover Page</span>
            </div>
          </div>
        </div>

        {/* Action */}
        <button
          onClick={() => window.history.back()}
          className="px-6 py-3 bg-white/[0.03] hover:bg-white/[0.06] text-white/80 border border-white/[0.08] rounded-lg transition-colors duration-200 font-sans"
        >
          Go Back
        </button>
      </div>
    </div>
  );
};

export default MobileBuilderRestriction;
