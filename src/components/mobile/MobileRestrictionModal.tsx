import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Monitor, Smartphone } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface MobileRestrictionModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const MobileRestrictionModal: React.FC<MobileRestrictionModalProps> = ({ 
  isOpen, 
  onClose 
}) => {
  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm p-4">
          <motion.div
            className="absolute inset-0 bg-black/60"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />
          
          <motion.div
            className="relative w-full max-w-md bg-[#121212] rounded-2xl shadow-2xl border border-white/[0.08] overflow-hidden"
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ duration: 0.2 }}
          >
            {/* Close Button */}
            <button
              onClick={onClose}
              className="absolute top-4 right-4 z-10 p-2 text-white/60 hover:text-white/80 transition-colors"
            >
              <X className="w-4 h-4" />
            </button>

            {/* Header */}
            <div className="p-6 text-center border-b border-white/[0.08]">
              <div className="relative mx-auto w-16 h-16 mb-4">
                <div className="w-16 h-16 bg-white/[0.03] rounded-2xl border border-white/[0.08] flex items-center justify-center">
                  <Monitor className="w-8 h-8 text-white/60" />
                </div>
                <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-white/[0.05] rounded-lg border border-white/[0.08] flex items-center justify-center">
                  <Smartphone className="w-3 h-3 text-white/40" />
                </div>
              </div>
              <h2 className="text-white text-xl font-semibold mb-2 font-sans">
                Desktop Required
              </h2>
              <p className="text-white/60 text-sm font-sans">
                Agent Builder requires a desktop or laptop for the best experience
              </p>
            </div>

            {/* Content */}
            <div className="p-6">
              <div className="bg-white/[0.02] rounded-xl p-4 border border-white/[0.06] mb-6">
                <p className="text-white/50 text-sm font-sans mb-3">
                  Available on mobile:
                </p>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-green-400 rounded-full"></div>
                    <span className="text-white/70 text-sm font-sans">Chat Interface</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-green-400 rounded-full"></div>
                    <span className="text-white/70 text-sm font-sans">Portfolio Manager</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-green-400 rounded-full"></div>
                    <span className="text-white/70 text-sm font-sans">Stock Scanner</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-green-400 rounded-full"></div>
                    <span className="text-white/70 text-sm font-sans">Discover Page</span>
                  </div>
                </div>
              </div>

              {/* Action Button */}
              <Button
                onClick={onClose}
                className="w-full bg-white/[0.03] hover:bg-white/[0.06] text-white/80 border border-white/[0.08] font-medium py-2.5 rounded-lg transition-all duration-200 font-sans"
              >
                Got it
              </Button>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default MobileRestrictionModal;
