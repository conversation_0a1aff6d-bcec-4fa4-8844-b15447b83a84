import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useSubscription } from '@/hooks/useSubscription';
import { SUBSCRIPTION_TYPES } from '@/hooks/useSubscription';
import { useToast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';
import { createValidatedCheckoutSession } from '@/utils/stripeUtils';

interface SubscribeButtonProps {
  priceId: string;
  planType: keyof typeof SUBSCRIPTION_TYPES;
  isCurrentPlan?: boolean;
  className?: string;
}

export function SubscribeButton({
  priceId,
  planType,
  isCurrentPlan = false,
  className = ''
}: SubscribeButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { createSubscription, subscription } = useSubscription();
  const { toast } = useToast();

  const handleSubscribe = async () => {
    try {
      setIsLoading(true);

      // Create a validated checkout session
      const data = await createValidatedCheckoutSession(priceId, {
        returnUrl: window.location.origin + '/subscription/manage?success=true'
      });

      if (data.error) {
        throw new Error(data.error);
      }

      // ALL REDIRECT CODE REMOVED
      throw new Error('Redirect functionality removed');
    } catch (error) {
      console.error('Error subscribing:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to process subscription. Please try again.',
        variant: 'destructive'
      });
      setIsLoading(false);
    }
  };

  const getButtonText = () => {
    if (isCurrentPlan) {
      return 'Current Plan';
    }

    if (!subscription?.subscription_type) {
      return 'Subscribe';
    }

    switch (planType) {
      case SUBSCRIPTION_TYPES.basic:
        return 'Downgrade';
      case SUBSCRIPTION_TYPES.pro:
        return subscription?.subscription_type === SUBSCRIPTION_TYPES.premium ? 'Downgrade' : 'Upgrade';
      case SUBSCRIPTION_TYPES.premium:
        return 'Upgrade';
      default:
        return 'Subscribe';
    }
  };

  return (
    <Button
      onClick={handleSubscribe}
      disabled={isLoading || isCurrentPlan}
      className={className}
      variant={isCurrentPlan ? 'outline' : 'default'}
    >
      {isLoading ? (
        <>
          <Loader2 className="mr-2 h-3.5 w-3.5 animate-spin" />
          Processing...
        </>
      ) : (
        getButtonText()
      )}
    </Button>
  );
}
