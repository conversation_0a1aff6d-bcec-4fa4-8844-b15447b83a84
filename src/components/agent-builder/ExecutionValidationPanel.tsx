import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Clock, 
  Zap,
  DollarSign,
  X,
  Play
} from 'lucide-react';
import { ExecutionValidationResult } from '@/utils/executionValidation';

interface ExecutionValidationPanelProps {
  validationResult: ExecutionValidationResult | null;
  onClose: () => void;
  onExecute: () => void;
  onFixError: (errorId: string) => void;
  isVisible: boolean;
  isExecuting: boolean;
}

const ExecutionValidationPanel: React.FC<ExecutionValidationPanelProps> = ({
  validationResult,
  onClose,
  onExecute,
  onFixError,
  isVisible,
  isExecuting
}) => {
  if (!isVisible || !validationResult) return null;

  const { canExecute, criticalErrors, warnings, executionPath, estimatedExecutionTime, resourceRequirements } = validationResult;

  const totalCost = resourceRequirements.reduce((sum, req) => sum + req.estimated_cost, 0);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'high':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high':
        return 'text-red-600';
      case 'medium':
        return 'text-yellow-600';
      case 'low':
        return 'text-green-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <Card className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-30 w-96 max-h-[80vh] overflow-hidden bg-white border shadow-xl">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {canExecute ? (
              <CheckCircle className="h-5 w-5 text-green-500" />
            ) : (
              <XCircle className="h-5 w-5 text-red-500" />
            )}
            <CardTitle className="text-lg">
              {canExecute ? 'Ready to Execute' : 'Execution Blocked'}
            </CardTitle>
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6"
            onClick={onClose}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
        
        {canExecute && (
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span>~{estimatedExecutionTime}ms</span>
            </div>
            <div className="flex items-center gap-1">
              <DollarSign className="h-3 w-3" />
              <span>${totalCost.toFixed(4)}</span>
            </div>
            <div className="flex items-center gap-1">
              <Zap className="h-3 w-3" />
              <span>{executionPath.length} blocks</span>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent className="p-4 pt-0 overflow-y-auto max-h-96">
        {/* Critical Errors */}
        {criticalErrors.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-red-600 mb-2 flex items-center gap-1">
              <XCircle className="h-4 w-4" />
              Critical Issues ({criticalErrors.length})
            </h4>
            <div className="space-y-2">
              {criticalErrors.map(error => (
                <div
                  key={error.id}
                  className={`p-3 rounded-lg border ${getSeverityColor(error.severity)}`}
                >
                  <div className="flex items-start justify-between gap-2">
                    <div className="flex-1">
                      <p className="text-sm font-medium">{error.message}</p>
                      {error.suggestedFix && (
                        <p className="text-xs mt-1 opacity-80">
                          💡 {error.suggestedFix}
                        </p>
                      )}
                    </div>
                    {error.blockId && (
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-6 px-2 text-xs"
                        onClick={() => onFixError(error.id)}
                      >
                        Fix
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Warnings */}
        {warnings.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-yellow-600 mb-2 flex items-center gap-1">
              <AlertTriangle className="h-4 w-4" />
              Warnings ({warnings.length})
            </h4>
            <div className="space-y-2">
              {warnings.map(warning => (
                <div
                  key={warning.id}
                  className="p-2 rounded-lg bg-yellow-50 border border-yellow-200"
                >
                  <div className="flex items-start justify-between gap-2">
                    <p className="text-sm text-yellow-800">{warning.message}</p>
                    <Badge 
                      variant="outline" 
                      className={`text-xs ${getImpactColor(warning.impact)}`}
                    >
                      {warning.impact}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Execution Path */}
        {canExecute && executionPath.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">
              Execution Flow
            </h4>
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="flex flex-wrap gap-1">
                {executionPath.map((blockId, index) => (
                  <React.Fragment key={blockId}>
                    <Badge variant="secondary" className="text-xs">
                      {index + 1}
                    </Badge>
                    {index < executionPath.length - 1 && (
                      <span className="text-gray-400 text-xs">→</span>
                    )}
                  </React.Fragment>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Resource Requirements */}
        {resourceRequirements.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">
              Resource Requirements
            </h4>
            <div className="space-y-1">
              {resourceRequirements.map((req, index) => (
                <div key={index} className="flex items-center justify-between text-xs">
                  <span className="text-gray-600">{req.description}</span>
                  <span className="font-mono">${req.estimated_cost.toFixed(4)}</span>
                </div>
              ))}
              <div className="border-t pt-1 flex items-center justify-between text-sm font-medium">
                <span>Total Cost</span>
                <span>${totalCost.toFixed(4)}</span>
              </div>
            </div>
          </div>
        )}

        {/* Execution Button */}
        <div className="flex gap-2">
          <Button
            onClick={onExecute}
            disabled={!canExecute || isExecuting}
            className="flex-1"
            size="sm"
          >
            {isExecuting ? (
              <>
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2" />
                Executing...
              </>
            ) : (
              <>
                <Play className="h-3 w-3 mr-2" />
                Execute Agent
              </>
            )}
          </Button>
          
          <Button
            variant="outline"
            onClick={onClose}
            size="sm"
          >
            Cancel
          </Button>
        </div>

        {/* Execution Progress */}
        {isExecuting && (
          <div className="mt-3">
            <div className="flex items-center justify-between text-xs mb-1">
              <span>Executing blocks...</span>
              <span>Step 1 of {executionPath.length}</span>
            </div>
            <Progress value={20} className="h-1" />
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ExecutionValidationPanel;
