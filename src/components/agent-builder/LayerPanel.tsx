import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Eye, EyeOff, ChevronDown, ChevronRight } from 'lucide-react';
import { AgentBlock } from '@/services/agentService';
import { getBlockCategory, BLOCK_CATEGORIES } from '@/utils/canvasLayoutEngine';
import { BlockType } from '@/hooks/useAgentBuilder';

interface LayerPanelProps {
  blocks: AgentBlock[];
  visibleLayers: Set<string>;
  onToggleLayer: (layer: string) => void;
  onSelectBlock: (blockId: string) => void;
  selectedBlockId?: string;
  isVisible: boolean;
  onClose: () => void;
}

const LayerPanel: React.FC<LayerPanelProps> = ({
  blocks,
  visibleLayers,
  onToggleLayer,
  onSelectBlock,
  selectedBlockId,
  isVisible,
  onClose
}) => {
  const [expandedLayers, setExpandedLayers] = React.useState<Set<string>>(new Set());

  if (!isVisible) return null;

  // Group blocks by category
  const blocksByCategory = blocks.reduce((acc, block) => {
    const category = getBlockCategory(block.type as BlockType);
    if (!acc[category]) acc[category] = [];
    acc[category].push(block);
    return acc;
  }, {} as { [category: string]: AgentBlock[] });

  const toggleLayerExpansion = (layer: string) => {
    const newExpanded = new Set(expandedLayers);
    if (newExpanded.has(layer)) {
      newExpanded.delete(layer);
    } else {
      newExpanded.add(layer);
    }
    setExpandedLayers(newExpanded);
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      ENTRY: 'bg-blue-100 text-blue-800',
      DATA_SOURCE: 'bg-green-100 text-green-800',
      ANALYSIS: 'bg-purple-100 text-purple-800',
      SIGNAL_GENERATION: 'bg-orange-100 text-orange-800',
      LOGIC: 'bg-yellow-100 text-yellow-800',
      RISK_ANALYSIS: 'bg-red-100 text-red-800',
      QUALITY_FILTER: 'bg-emerald-100 text-emerald-800',
      OUTPUT: 'bg-indigo-100 text-indigo-800',
      MISC: 'bg-gray-100 text-gray-800'
    };
    return colors[category as keyof typeof colors] || colors.MISC;
  };

  const getCategoryIcon = (category: string) => {
    const icons = {
      ENTRY: '🚀',
      DATA_SOURCE: '📊',
      ANALYSIS: '🔍',
      SIGNAL_GENERATION: '⚡',
      LOGIC: '🧠',
      RISK_ANALYSIS: '🛡️',
      QUALITY_FILTER: '✅',
      OUTPUT: '🎯',
      MISC: '📦'
    };
    return icons[category as keyof typeof icons] || icons.MISC;
  };

  return (
    <Card className="absolute top-4 left-4 z-20 w-80 max-h-96 overflow-hidden bg-white/95 backdrop-blur-sm border shadow-lg">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium">Block Layers</CardTitle>
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6"
            onClick={onClose}
          >
            <EyeOff className="h-3 w-3" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-3 pt-0 overflow-y-auto max-h-80">
        <div className="space-y-2">
          {Object.entries(blocksByCategory).map(([category, categoryBlocks]) => {
            const isExpanded = expandedLayers.has(category);
            const isVisible = visibleLayers.has(category);
            
            return (
              <div key={category} className="border rounded-lg overflow-hidden">
                <div className="flex items-center justify-between p-2 bg-gray-50 hover:bg-gray-100 transition-colors">
                  <div className="flex items-center gap-2 flex-1">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-4 w-4 p-0"
                      onClick={() => toggleLayerExpansion(category)}
                    >
                      {isExpanded ? (
                        <ChevronDown className="h-3 w-3" />
                      ) : (
                        <ChevronRight className="h-3 w-3" />
                      )}
                    </Button>
                    
                    <span className="text-xs">{getCategoryIcon(category)}</span>
                    
                    <div className="flex items-center gap-2 flex-1">
                      <span className="text-xs font-medium">
                        {category.replace('_', ' ')}
                      </span>
                      <Badge variant="secondary" className="text-xs px-1 py-0">
                        {categoryBlocks.length}
                      </Badge>
                    </div>
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    onClick={() => onToggleLayer(category)}
                  >
                    {isVisible ? (
                      <Eye className="h-3 w-3" />
                    ) : (
                      <EyeOff className="h-3 w-3 opacity-50" />
                    )}
                  </Button>
                </div>
                
                {isExpanded && (
                  <div className="p-2 space-y-1 bg-white">
                    {categoryBlocks.map(block => (
                      <div
                        key={block.id}
                        className={`flex items-center justify-between p-2 rounded text-xs cursor-pointer transition-colors ${
                          selectedBlockId === block.id
                            ? 'bg-blue-100 border border-blue-300'
                            : 'hover:bg-gray-50'
                        }`}
                        onClick={() => onSelectBlock(block.id)}
                      >
                        <div className="flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${isVisible ? 'bg-green-500' : 'bg-gray-300'}`} />
                          <span className="font-medium">
                            {block.type.replace('_', ' ')}
                          </span>
                        </div>
                        
                        {block.parameters?.method && (
                          <Badge variant="outline" className="text-xs px-1 py-0">
                            {block.parameters.method}
                          </Badge>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            );
          })}
          
          {Object.keys(blocksByCategory).length === 0 && (
            <div className="text-center text-gray-500 text-xs py-4">
              No blocks on canvas
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default LayerPanel;
