import React, { useState } from 'react';
import { <PERSON>le, Position } from 'reactflow';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Settings, Trash2, Plus, Minus, TrendingUp, TrendingDown } from 'lucide-react';
import { AgentBlock } from '@/hooks/useAgentBuilder';

interface ConfidenceBoostBlockProps {
  data: {
    id: string;
    boostType: 'bullish' | 'bearish' | 'neutral';
    percentage: number;
    inputConnections: string[];
    outputConnections: string[];
    isEntryBlock: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
  };
  selected: boolean;
}

const ConfidenceBoostBlock: React.FC<ConfidenceBoostBlockProps> = ({ data, selected }) => {
  const [isEditing, setIsEditing] = useState(false);

  // Handle boost type change
  const handleBoostTypeChange = (value: string) => {
    data.onUpdate({ boostType: value as 'bullish' | 'bearish' | 'neutral' });
  };

  // Handle percentage change
  const handlePercentageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value) || 0;
    data.onUpdate({ percentage: Math.max(0, value) });
  };

  // Get icon and color based on boost type
  const getBoostIcon = () => {
    switch (data.boostType) {
      case 'bullish':
        return <TrendingUp className="h-3 w-3 text-green-500" />;
      case 'bearish':
        return <TrendingDown className="h-3 w-3 text-red-500" />;
      case 'neutral':
        return <Minus className="h-3 w-3 text-gray-500" />;
      default:
        return <Plus className="h-3 w-3 text-blue-500" />;
    }
  };

  const getBoostColor = () => {
    switch (data.boostType) {
      case 'bullish':
        return 'text-green-600';
      case 'bearish':
        return 'text-red-600';
      case 'neutral':
        return 'text-gray-600';
      default:
        return 'text-blue-600';
    }
  };

  const getBoostBgColor = () => {
    switch (data.boostType) {
      case 'bullish':
        return 'bg-green-500/10';
      case 'bearish':
        return 'bg-red-500/10';
      case 'neutral':
        return 'bg-gray-500/10';
      default:
        return 'bg-blue-500/10';
    }
  };

  const getHandleColor = () => {
    switch (data.boostType) {
      case 'bullish':
        return '#22c55e';
      case 'bearish':
        return '#ef4444';
      case 'neutral':
        return '#6b7280';
      default:
        return '#3b82f6';
    }
  };

  const getBoostLabel = () => {
    const typeLabel = data.boostType.charAt(0).toUpperCase() + data.boostType.slice(1);
    return `+${data.percentage}% ${typeLabel}`;
  };

  return (
    <div className="relative">
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{
          background: '#3b82f6',
          width: '12px',
          height: '12px',
          border: '2px solid white'
        }}
      />

      {/* Output handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        style={{
          background: getHandleColor(),
          width: '12px',
          height: '12px',
          border: '2px solid white'
        }}
      />

      <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''} ${data.isEntryBlock ? 'border-primary' : ''}`}>
        <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={`w-6 h-6 rounded-full ${getBoostBgColor()} flex items-center justify-center`}>
              {getBoostIcon()}
            </div>
            <CardTitle className="text-sm font-medium">{getBoostLabel()}</CardTitle>
          </div>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={() => setIsEditing(!isEditing)}
              title="Edit block"
            >
              <Settings className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-red-500 hover:text-red-700"
              onClick={data.onRemove}
              title="Remove block"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="p-3 pt-2">
          <div className="space-y-2">
            <div className="text-xs text-muted-foreground">
              Increases {data.boostType} confidence by {data.percentage}%
            </div>

            {isEditing && (
              <div className="space-y-3 pt-2 border-t">
                <div className="space-y-2">
                  <Label htmlFor="boostType" className="text-xs">Boost Type</Label>
                  <Select value={data.boostType} onValueChange={handleBoostTypeChange}>
                    <SelectTrigger className="h-8 text-xs">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="bullish" className="text-xs">
                        <div className="flex items-center gap-2">
                          <TrendingUp className="h-3 w-3 text-green-500" />
                          <span>Bullish</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="bearish" className="text-xs">
                        <div className="flex items-center gap-2">
                          <TrendingDown className="h-3 w-3 text-red-500" />
                          <span>Bearish</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="neutral" className="text-xs">
                        <div className="flex items-center gap-2">
                          <Minus className="h-3 w-3 text-gray-500" />
                          <span>Neutral</span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="percentage" className="text-xs">Confidence Boost Percentage</Label>
                  <Input
                    id="percentage"
                    type="number"
                    min="0"
                    step="0.1"
                    value={data.percentage}
                    onChange={handlePercentageChange}
                    className="h-8 text-xs"
                    placeholder="e.g., 10.0"
                  />
                  <p className="text-xs text-muted-foreground">
                    Enter the percentage to boost {data.boostType} confidence (e.g., 10 for +10%)
                  </p>
                </div>
              </div>
            )}

            {isEditing && (
              <div className="mt-2 p-2 bg-muted/50 rounded-md">
                <p className="text-xs text-muted-foreground">
                  This block adjusts the confidence of the trading signal by the specified percentage.
                  Multiple confidence boost blocks can be chained together to create nuanced signal adjustments.
                </p>
                <div className="flex items-center gap-2 mt-2">
                  {getBoostIcon()}
                  <span className={`text-xs font-medium ${getBoostColor()}`}>
                    Boosts {data.boostType} confidence by +{data.percentage}%
                  </span>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ConfidenceBoostBlock;
