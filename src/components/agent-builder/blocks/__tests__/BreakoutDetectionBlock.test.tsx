import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import BreakoutDetectionBlock from '../BreakoutDetectionBlock';

// Mock the UI components
vi.mock('@/components/ui/card', () => ({
  Card: ({ children, className }: any) => <div className={className}>{children}</div>,
  CardHeader: ({ children }: any) => <div>{children}</div>,
  CardContent: ({ children }: any) => <div>{children}</div>,
  CardTitle: ({ children }: any) => <h3>{children}</h3>,
}));

vi.mock('@/components/ui/select', () => ({
  Select: ({ children, onValueChange, value }: any) => (
    <select onChange={(e) => onValueChange(e.target.value)} value={value}>
      {children}
    </select>
  ),
  SelectContent: ({ children }: any) => <>{children}</>,
  SelectItem: ({ children, value }: any) => <option value={value}>{children}</option>,
  SelectTrigger: ({ children }: any) => <div>{children}</div>,
  SelectValue: () => <span>Select Value</span>,
}));

vi.mock('@/components/ui/input', () => ({
  Input: (props: any) => <input {...props} />,
}));

vi.mock('@/components/ui/label', () => ({
  Label: ({ children, ...props }: any) => <label {...props}>{children}</label>,
}));

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, ...props }: any) => (
    <button onClick={onClick} {...props}>{children}</button>
  ),
}));

vi.mock('@/components/ui/collapsible', () => ({
  Collapsible: ({ children, open }: any) => <div style={{ display: open ? 'block' : 'none' }}>{children}</div>,
  CollapsibleTrigger: ({ children, asChild }: any) => asChild ? children : <div>{children}</div>,
  CollapsibleContent: ({ children }: any) => <div>{children}</div>,
}));

vi.mock('reactflow', () => ({
  Handle: ({ id, type, position, style }: any) => (
    <div data-testid={`handle-${id}`} data-type={type} data-position={position} style={style} />
  ),
  Position: {
    Left: 'left',
    Right: 'right',
  },
}));

vi.mock('lucide-react', () => ({
  ArrowUpDown: () => <span>ArrowUpDown</span>,
  Trash2: () => <span>Trash2</span>,
  ChevronDown: () => <span>ChevronDown</span>,
}));

describe('BreakoutDetectionBlock', () => {
  const mockOnUpdate = vi.fn();
  const mockOnRemove = vi.fn();
  const mockOnSetAsEntry = vi.fn();

  const defaultProps = {
    data: {
      id: 'test-breakout-block',
      breakoutType: 'support_resistance',
      breakoutDirection: 'any',
      timeframe: 'day',
      lookbackPeriod: 20,
      volumeConfirmation: false,
      minBreakoutSize: 2.0,
      inputConnections: [],
      trueConnection: undefined,
      falseConnection: undefined,
      isEntryBlock: false,
      onUpdate: mockOnUpdate,
      onRemove: mockOnRemove,
      onSetAsEntry: mockOnSetAsEntry,
      hasError: false,
      isDisconnected: false,
      errorMessages: [],
    },
    selected: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the breakout detection block with default values', () => {
    render(<BreakoutDetectionBlock {...defaultProps} />);
    
    expect(screen.getByText('Breakout Detection')).toBeInTheDocument();
    expect(screen.getByText('Detects directional price breakouts with bullish/bearish filtering')).toBeInTheDocument();
  });

  it('displays all breakout direction options', () => {
    render(<BreakoutDetectionBlock {...defaultProps} />);
    
    const directionSelect = screen.getByDisplayValue('any');
    expect(directionSelect).toBeInTheDocument();
  });

  it('calls onUpdate when breakout direction is changed', () => {
    render(<BreakoutDetectionBlock {...defaultProps} />);
    
    const directionSelects = screen.getAllByRole('combobox');
    const directionSelect = directionSelects.find(select => 
      select.getAttribute('value') === 'any'
    );
    
    if (directionSelect) {
      fireEvent.change(directionSelect, { target: { value: 'bullish' } });
      expect(mockOnUpdate).toHaveBeenCalledWith({ breakoutDirection: 'bullish' });
    }
  });

  it('displays the correct breakout direction value', () => {
    const propsWithBullishDirection = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        breakoutDirection: 'bullish',
      },
    };
    
    render(<BreakoutDetectionBlock {...propsWithBullishDirection} />);
    
    const directionSelect = screen.getByDisplayValue('bullish');
    expect(directionSelect).toBeInTheDocument();
  });

  it('renders input and output handles', () => {
    render(<BreakoutDetectionBlock {...defaultProps} />);
    
    expect(screen.getByTestId('handle-input')).toBeInTheDocument();
    expect(screen.getByTestId('handle-true')).toBeInTheDocument();
    expect(screen.getByTestId('handle-false')).toBeInTheDocument();
  });

  it('calls onRemove when remove button is clicked', () => {
    render(<BreakoutDetectionBlock {...defaultProps} />);
    
    const removeButton = screen.getByTitle('Remove block');
    fireEvent.click(removeButton);
    
    expect(mockOnRemove).toHaveBeenCalled();
  });

  it('shows help section when expanded', () => {
    render(<BreakoutDetectionBlock {...defaultProps} />);
    
    const helpButton = screen.getByText('How this block works');
    fireEvent.click(helpButton);
    
    expect(screen.getByText(/Breakout Direction.*Filter for bullish, bearish, or any direction/)).toBeInTheDocument();
  });
});
