import React, { useState } from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Target, Trash2, TrendingUp, TrendingDown, Minus, ChevronDown, HelpCircle } from 'lucide-react';
import { AgentBlock } from '@/services/agentService';

interface SignalBlockProps {
  data: {
    id: string;
    signal: 'bullish' | 'bearish' | 'neutral';
    confidence: number;
    inputConnections: string[];
    isEntryBlock: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
  };
  selected: boolean;
}

const SignalBlock: React.FC<SignalBlockProps> = ({ data, selected }) => {
  const [showHelp, setShowHelp] = useState(false); // Help section collapsed by default

  // Handle signal change
  const handleSignalChange = (value: string) => {
    data.onUpdate({ signal: value as 'bullish' | 'bearish' | 'neutral' });
  };

  // Handle confidence change
  const handleConfidenceChange = (value: number[]) => {
    data.onUpdate({ confidence: value[0] });
  };

  // Get signal icon
  const getSignalIcon = () => {
    switch (data.signal) {
      case 'bullish':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'bearish':
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return <Minus className="h-4 w-4 text-gray-500" />;
    }
  };

  // Get signal color
  const getSignalColor = () => {
    switch (data.signal) {
      case 'bullish':
        return 'text-green-500';
      case 'bearish':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  return (
    <div className="relative">
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{
          background: '#6b7280',
          width: '12px',
          height: '12px',
          border: '2px solid white'
        }}
      />

      <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''} ${data.isEntryBlock ? 'border-primary' : ''}`}>
        <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center">
              <Target className="h-3 w-3 text-primary" />
            </div>
            <CardTitle className="text-sm font-medium">Signal</CardTitle>
          </div>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-destructive"
              onClick={data.onRemove}
              title="Remove block"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-3 pt-2">
          <div className="space-y-3">
            <div>
              <label className="text-xs font-medium block mb-1">Signal</label>
              <Select
                value={data.signal}
                onValueChange={handleSignalChange}
              >
                <SelectTrigger className="h-8 text-xs">
                  <SelectValue placeholder="Select signal" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="bullish" className="text-xs">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-3 w-3 text-green-500" />
                      <span>Bullish</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="bearish" className="text-xs">
                    <div className="flex items-center gap-2">
                      <TrendingDown className="h-3 w-3 text-red-500" />
                      <span>Bearish</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="neutral" className="text-xs">
                    <div className="flex items-center gap-2">
                      <Minus className="h-3 w-3 text-gray-500" />
                      <span>Neutral</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <div className="flex justify-between items-center mb-1">
                <label className="text-xs font-medium">Confidence</label>
                <span className={`text-xs font-medium ${getSignalColor()}`}>
                  {data.confidence}%
                </span>
              </div>
              <Slider
                value={[data.confidence]}
                min={0}
                max={100}
                step={5}
                onValueChange={handleConfidenceChange}
                className="py-1"
              />
            </div>

            <Collapsible open={showHelp} onOpenChange={setShowHelp}>
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-between mt-2 h-8 text-xs text-muted-foreground hover:text-foreground"
                >
                  <div className="flex items-center gap-2">
                    <HelpCircle className="h-3 w-3" />
                    <span>How this block works</span>
                  </div>
                  <ChevronDown className={`h-3 w-3 transition-transform ${showHelp ? 'rotate-180' : ''}`} />
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <div className="mt-2 p-3 bg-muted/30 rounded-md text-xs text-muted-foreground">
                  <p className="mb-2">
                    This block generates the final signal for the agent. Connect conditions or other blocks to the input to determine when this signal should be triggered.
                  </p>
                  <div className="flex items-center gap-2 mt-2">
                    {getSignalIcon()}
                    <span className={`font-medium ${getSignalColor()}`}>
                      {data.signal.charAt(0).toUpperCase() + data.signal.slice(1)} with {data.confidence}% confidence
                    </span>
                  </div>
                </div>
              </CollapsibleContent>
            </Collapsible>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SignalBlock;
