import React, { useState } from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Newspaper, Trash2, HelpCircle, ChevronDown } from 'lucide-react';
import { AgentBlock } from '@/services/agentService';

interface StockSentimentBlockProps {
  data: {
    id: string;
    articleLimit?: number;
    daysBack?: number;
    inputConnections: string[];
    bullishConnection?: string;
    bearishConnection?: string;
    neutralConnection?: string;
    isEntryBlock: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
    hasError?: boolean;
    isDisconnected?: boolean;
    errorMessages?: string[];
  };
  selected: boolean;
}

const StockSentimentBlock: React.FC<StockSentimentBlockProps> = ({ data, selected }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [showHelp, setShowHelp] = useState(false);

  // Render error messages
  const renderErrorMessages = () => {
    if (!data.hasError || !data.errorMessages || data.errorMessages.length === 0) {
      return null;
    }

    return (
      <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
        <div className="flex items-center gap-2 text-red-700 font-medium text-xs mb-1">
          <span>⚠️ Errors:</span>
        </div>
        <ul className="text-red-600 text-xs space-y-1">
          {data.errorMessages.map((message, index) => (
            <li key={index}>• {message}</li>
          ))}
        </ul>
      </div>
    );
  };

  return (
    <div className={`relative ${data.isDisconnected ? 'disconnected-block' : ''}`}>
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{
          background: data.hasError ? '#ef4444' : '#6b7280',
          width: '12px',
          height: '12px',
          border: '2px solid white'
        }}
      />

      {/* Bullish output handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="bullish"
        style={{
          background: data.hasError ? '#ef4444' : '#22c55e',
          top: '30%',
          width: '12px',
          height: '12px',
          border: '2px solid white'
        }}
      />

      {/* Bearish output handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="bearish"
        style={{
          background: data.hasError ? '#ef4444' : '#ef4444',
          top: '50%',
          width: '12px',
          height: '12px',
          border: '2px solid white'
        }}
      />

      {/* Neutral output handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="neutral"
        style={{
          background: data.hasError ? '#ef4444' : '#6b7280',
          top: '70%',
          width: '12px',
          height: '12px',
          border: '2px solid white'
        }}
      />

      <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''} ${data.isEntryBlock ? 'border-primary' : ''} ${data.hasError ? 'border-red-500' : ''}`}>
        <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center">
              <Newspaper className="h-3 w-3 text-blue-600" />
            </div>
            <CardTitle className="text-sm font-medium">Stock Sentiment</CardTitle>
          </div>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={data.onRemove}
              title="Remove block"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-3 pt-2">
          <div className="space-y-3">
            {/* Article Limit */}
            <div className="space-y-1">
              <Label htmlFor="articleLimit" className="text-xs font-medium">
                Article Limit
              </Label>
              <Input
                id="articleLimit"
                type="number"
                min="1"
                max="50"
                value={data.articleLimit || 10}
                onChange={(e) => {
                  const value = parseInt(e.target.value) || 10;
                  data.onUpdate({ articleLimit: Math.max(1, Math.min(50, value)) });
                }}
                className="h-8 text-xs"
                placeholder="10"
              />
            </div>

            {/* Days Back */}
            <div className="space-y-1">
              <Label htmlFor="daysBack" className="text-xs font-medium">
                Days Back
              </Label>
              <Input
                id="daysBack"
                type="number"
                min="1"
                max="30"
                value={data.daysBack || 7}
                onChange={(e) => {
                  const value = parseInt(e.target.value) || 7;
                  data.onUpdate({ daysBack: Math.max(1, Math.min(30, value)) });
                }}
                className="h-8 text-xs"
                placeholder="7"
              />
            </div>

            {/* Output Terminal Labels */}
            <div className="space-y-2">
              <div className="text-xs font-medium text-muted-foreground">Output Terminals:</div>
              <div className="flex flex-col gap-1 text-xs">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                  <span>Bullish sentiment</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-red-500"></div>
                  <span>Bearish sentiment</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-gray-500"></div>
                  <span>Neutral sentiment</span>
                </div>
              </div>
            </div>

            {/* Help Section */}
            <Collapsible open={showHelp} onOpenChange={setShowHelp}>
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-between mt-3 h-8 text-xs text-muted-foreground hover:text-foreground"
                >
                  <div className="flex items-center gap-2">
                    <HelpCircle className="h-3 w-3" />
                    <span>How this block works</span>
                  </div>
                  <ChevronDown className={`h-3 w-3 transition-transform ${showHelp ? 'rotate-180' : ''}`} />
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <div className="mt-2 p-3 bg-muted/30 rounded-md text-xs text-muted-foreground">
                  <p>
                    Analyzes recent news articles about the stock to determine market sentiment.
                  </p>
                </div>
              </CollapsibleContent>
            </Collapsible>

            {/* Error Messages */}
            {renderErrorMessages()}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default StockSentimentBlock;
