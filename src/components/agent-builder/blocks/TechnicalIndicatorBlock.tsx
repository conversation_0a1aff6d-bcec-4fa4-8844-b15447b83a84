import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { BarChart2, Trash2, Star } from 'lucide-react';
import { AgentBlock } from '@/services/agentService';

interface TechnicalIndicatorBlockProps {
  data: {
    id: string;
    type: string;
    indicator: string;
    parameters: Record<string, any>;
    isEntryBlock: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
    hasError?: boolean;
    isDisconnected?: boolean;
    errorMessages?: string[];
  };
  selected: boolean;
}

const TechnicalIndicatorBlock: React.FC<TechnicalIndicatorBlockProps> = ({ data, selected }) => {
  
  // Technical indicator configurations
  const getIndicatorConfig = (type: string) => {
    switch (type) {
      case 'TREND_INDICATOR':
        return {
          title: 'Trend Indicator',
          indicators: [
            { value: 'macd', label: 'MACD', params: { fastPeriod: 12, slowPeriod: 26, signalPeriod: 9 } },
            { value: 'adx', label: 'ADX', params: { period: 14 } },
            { value: 'parabolic_sar', label: 'Parabolic SAR', params: { acceleration: 0.02, maximum: 0.2 } },
            { value: 'ichimoku', label: 'Ichimoku Cloud', params: { conversionPeriod: 9, basePeriod: 26, spanBPeriod: 52 } }
          ]
        };
      case 'VOLUME_INDICATOR':
        return {
          title: 'Volume Indicator',
          indicators: [
            { value: 'obv', label: 'On-Balance Volume', params: {} },
            { value: 'volume_profile', label: 'Volume Profile', params: { period: 20 } },
            { value: 'accumulation_distribution', label: 'A/D Line', params: {} },
            { value: 'chaikin_money_flow', label: 'Chaikin Money Flow', params: { period: 20 } }
          ]
        };
      case 'VOLATILITY_INDICATOR':
        return {
          title: 'Volatility Indicator',
          indicators: [
            { value: 'bollinger_bands', label: 'Bollinger Bands', params: { period: 20, stdDev: 2 } },
            { value: 'atr', label: 'Average True Range', params: { period: 14 } },
            { value: 'keltner_channels', label: 'Keltner Channels', params: { period: 20, multiplier: 2 } },
            { value: 'donchian_channels', label: 'Donchian Channels', params: { period: 20 } }
          ]
        };
      default:
        return {
          title: 'Technical Indicator',
          indicators: [
            { value: 'rsi', label: 'RSI', params: { period: 14 } },
            { value: 'macd', label: 'MACD', params: { fastPeriod: 12, slowPeriod: 26, signalPeriod: 9 } }
          ]
        };
    }
  };

  const config = getIndicatorConfig(data.type);
  const currentIndicator = config.indicators.find(ind => ind.value === data.indicator) || config.indicators[0];

  // Ensure parameters exist and have default values
  const parameters = data.parameters || {};

  const handleIndicatorChange = (indicator: string) => {
    const selectedIndicator = config.indicators.find(ind => ind.value === indicator);
    if (selectedIndicator) {
      data.onUpdate({
        indicator,
        parameters: selectedIndicator.params
      });
    }
  };

  const handleParameterChange = (param: string, value: any) => {
    data.onUpdate({
      parameters: {
        ...parameters,
        [param]: parseFloat(value) || value
      }
    });
  };

  const renderParameterInputs = () => {
    if (!currentIndicator) return null;

    return Object.entries(currentIndicator.params).map(([param, defaultValue]) => {
      const currentValue = parameters[param] ?? defaultValue;
      
      return (
        <div key={param} className="space-y-1">
          <label className="text-xs font-medium block capitalize">
            {param.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
          </label>
          <Input
            type="number"
            value={currentValue}
            onChange={e => {
              const value = e.target.value;
              if (value === '') {
                handleParameterChange(param, '');
              } else {
                const isDecimal = param.includes('acceleration') || param.includes('multiplier');
                const numValue = isDecimal ? parseFloat(value) : parseInt(value);
                if (!isNaN(numValue)) {
                  handleParameterChange(param, numValue);
                }
              }
            }}
            className="h-8 text-xs"
            min={param.includes('period') ? 1 : 0}
            step={param.includes('acceleration') || param.includes('multiplier') ? 0.01 : 1}
            placeholder={defaultValue?.toString() || '0'}
          />
        </div>
      );
    });
  };

  return (
    <>
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 bg-gray-400 border-2 border-white"
      />

      <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''} ${data.isEntryBlock ? 'border-primary' : ''} ${data.hasError ? 'border-red-500' : ''}`}>
        <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 rounded-full bg-blue-500/10 flex items-center justify-center">
              <BarChart2 className="h-3 w-3 text-blue-500" />
            </div>
            <CardTitle className="text-sm font-medium">{config.title}</CardTitle>
          </div>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-destructive"
              onClick={(e) => {
                e.stopPropagation();
                data.onRemove();
              }}
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-3 pt-2 space-y-3">
          {/* Indicator Selection */}
          <div className="space-y-1">
            <label className="text-xs font-medium block">Indicator</label>
            <Select value={data.indicator || currentIndicator.value} onValueChange={handleIndicatorChange}>
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {config.indicators.map(indicator => (
                  <SelectItem key={indicator.value} value={indicator.value}>
                    {indicator.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Parameter Inputs */}
          <div className="space-y-2">
            {renderParameterInputs()}
          </div>

          {/* Timeframe Selection */}
          <div className="space-y-1">
            <label className="text-xs font-medium block">Timeframe</label>
            <Select value={parameters.timeframe || 'day'} onValueChange={value => handleParameterChange('timeframe', value)}>
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1minute">1 Minute</SelectItem>
                <SelectItem value="5minute">5 Minutes</SelectItem>
                <SelectItem value="15minute">15 Minutes</SelectItem>
                <SelectItem value="hour">1 Hour</SelectItem>
                <SelectItem value="4hour">4 Hours</SelectItem>
                <SelectItem value="day">Daily</SelectItem>
                <SelectItem value="week">Weekly</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Error Messages */}
          {data.hasError && data.errorMessages && (
            <div className="text-xs text-red-500 bg-red-50 p-2 rounded">
              {data.errorMessages.map((msg, idx) => (
                <div key={idx}>{msg}</div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Output handles with color coding */}
      <Handle
        type="source"
        position={Position.Right}
        id="bullish"
        style={{ top: '30%' }}
        className="w-3 h-3 bg-green-500 border-2 border-white"
      />
      <Handle
        type="source"
        position={Position.Right}
        id="neutral"
        style={{ top: '50%' }}
        className="w-3 h-3 bg-gray-400 border-2 border-white"
      />
      <Handle
        type="source"
        position={Position.Right}
        id="bearish"
        style={{ top: '70%' }}
        className="w-3 h-3 bg-red-500 border-2 border-white"
      />
    </>
  );
};

export default TechnicalIndicatorBlock;
