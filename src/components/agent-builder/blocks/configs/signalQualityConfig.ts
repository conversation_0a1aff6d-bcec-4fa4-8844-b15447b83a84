import { 
  CheckCircle, 
  Gauge, 
  Clock, 
  Calendar
} from 'lucide-react';

// Signal Quality & Filtering Block Configurations
export const signalQualityConfigs = {
  SIGNAL_QUALITY_FILTER: {
    name: 'Signal Quality Filter',
    description: 'Filters signals based on quality criteria',
    icon: CheckCircle,
    category: 'Signal Quality',
    methods: [
      {
        value: 'comprehensive_quality',
        label: 'Comprehensive Quality',
        description: 'Multi-factor signal quality assessment',
        params: {
          min_confidence: 70,
          require_volume_confirmation: true,
          require_trend_alignment: false,
          quality_score_threshold: 75,
          include_momentum_check: true
        }
      },
      {
        value: 'basic_quality',
        label: 'Basic Quality',
        description: 'Simple quality filtering',
        params: {
          min_confidence: 60,
          require_volume_confirmation: false,
          require_trend_alignment: false,
          quality_score_threshold: 60,
          include_momentum_check: false
        }
      },
      {
        value: 'strict_quality',
        label: 'Strict Quality',
        description: 'High-quality signals only',
        params: {
          min_confidence: 85,
          require_volume_confirmation: true,
          require_trend_alignment: true,
          quality_score_threshold: 90,
          include_momentum_check: true
        }
      }
    ]
  },

  CONFIDENCE_THRESHOLD: {
    name: 'Confidence Threshold',
    description: 'Adjusts and filters signals by confidence levels',
    icon: Gauge,
    category: 'Signal Quality',
    methods: [
      {
        value: 'fixed_threshold',
        label: 'Fixed Threshold',
        description: 'Simple minimum confidence requirement',
        params: {
          minimum_confidence: 60,
          maximum_confidence: 95,
          adjustment_factor: 1.0,
          dynamic_threshold: false
        }
      },
      {
        value: 'dynamic_threshold',
        label: 'Dynamic Threshold',
        description: 'Adjusts threshold based on market conditions',
        params: {
          base_confidence: 70,
          volatility_adjustment: true,
          trend_adjustment: true,
          dynamic_threshold: true
        }
      },
      {
        value: 'adaptive_threshold',
        label: 'Adaptive Threshold',
        description: 'Learns optimal thresholds from performance',
        params: {
          initial_confidence: 65,
          learning_rate: 0.1,
          performance_window: 50,
          dynamic_threshold: true
        }
      }
    ]
  },

  SIGNAL_CONFIRMATION_DELAY: {
    name: 'Signal Confirmation',
    description: 'Requires signal confirmation over multiple periods',
    icon: Clock,
    category: 'Signal Quality',
    methods: [
      {
        value: 'consecutive_confirmation',
        label: 'Consecutive Confirmation',
        description: 'Requires consecutive confirming signals',
        params: {
          confirmation_periods: 2,
          require_consecutive: true,
          decay_factor: 0.9,
          max_delay_periods: 5
        }
      },
      {
        value: 'majority_confirmation',
        label: 'Majority Confirmation',
        description: 'Requires majority of recent signals to agree',
        params: {
          confirmation_periods: 3,
          require_consecutive: false,
          majority_threshold: 0.6,
          max_delay_periods: 5
        }
      },
      {
        value: 'weighted_confirmation',
        label: 'Weighted Confirmation',
        description: 'Weights recent signals more heavily',
        params: {
          confirmation_periods: 4,
          require_consecutive: false,
          decay_factor: 0.8,
          max_delay_periods: 7
        }
      }
    ]
  },

  MARKET_HOURS_FILTER: {
    name: 'Market Hours Filter',
    description: 'Filters signals based on market timing',
    icon: Calendar,
    category: 'Signal Quality',
    methods: [
      {
        value: 'regular_hours_only',
        label: 'Regular Hours Only',
        description: 'Only signals during regular market hours',
        params: {
          market_open: '09:30',
          market_close: '16:00',
          timezone: 'EST',
          allow_premarket: false,
          allow_afterhours: false
        }
      },
      {
        value: 'extended_hours',
        label: 'Extended Hours',
        description: 'Includes pre-market and after-hours',
        params: {
          market_open: '04:00',
          market_close: '20:00',
          timezone: 'EST',
          allow_premarket: true,
          allow_afterhours: true
        }
      },
      {
        value: 'opening_hours_only',
        label: 'Opening Hours Only',
        description: 'Only signals during market open period',
        params: {
          market_open: '09:30',
          market_close: '11:00',
          timezone: 'EST',
          allow_premarket: false,
          allow_afterhours: false
        }
      },
      {
        value: 'closing_hours_only',
        label: 'Closing Hours Only',
        description: 'Only signals during market close period',
        params: {
          market_open: '15:00',
          market_close: '16:00',
          timezone: 'EST',
          allow_premarket: false,
          allow_afterhours: false
        }
      }
    ]
  }
};

export function getSignalQualityConfig(blockType: string) {
  return signalQualityConfigs[blockType as keyof typeof signalQualityConfigs];
}
