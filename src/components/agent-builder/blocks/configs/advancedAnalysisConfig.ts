import { 
  Git<PERSON>ranch, 
  TrendingUp, 
  BarChart3, 
  Activity, 
  Filter, 
  Zap,
  Heart,
  Building
} from 'lucide-react';

// Advanced Analysis Block Configurations
export const advancedAnalysisConfigs = {
  CORRELATION_ANALYSIS: {
    name: 'Correlation Analysis',
    description: 'Analyzes correlation with market benchmarks and sectors',
    icon: GitBranch,
    category: 'Advanced Analysis',
    methods: [
      {
        value: 'market_correlation',
        label: 'Market Correlation',
        description: 'Correlation with major market indices',
        params: {
          benchmark: 'SPY',
          lookback_period: 60,
          correlation_threshold: 0.7,
          analysis_type: 'market_correlation'
        }
      },
      {
        value: 'sector_correlation',
        label: 'Sector Correlation',
        description: 'Correlation with sector ETFs',
        params: {
          benchmark: 'auto_detect',
          lookback_period: 60,
          correlation_threshold: 0.6,
          analysis_type: 'sector_correlation'
        }
      }
    ]
  },

  MOMENTUM_SHIFT: {
    name: 'Momentum Shift',
    description: 'Detects changes in price momentum and trend shifts',
    icon: TrendingUp,
    category: 'Advanced Analysis',
    methods: [
      {
        value: 'rate_of_change',
        label: 'Rate of Change',
        description: 'Detects momentum shifts using rate of change',
        params: {
          lookback_period: 20,
          sensitivity: 'medium',
          confirmation_period: 3,
          threshold_percentage: 5
        }
      },
      {
        value: 'macd_divergence',
        label: 'MACD Divergence',
        description: 'Uses MACD divergence to detect momentum shifts',
        params: {
          fast_period: 12,
          slow_period: 26,
          signal_period: 9,
          confirmation_period: 2
        }
      }
    ]
  },

  SUPPORT_RESISTANCE: {
    name: 'Support & Resistance',
    description: 'Identifies key support and resistance levels',
    icon: BarChart3,
    category: 'Advanced Analysis',
    methods: [
      {
        value: 'pivot_points',
        label: 'Pivot Points',
        description: 'Classical pivot point calculation',
        params: {
          lookback_period: 50,
          strength_threshold: 3,
          proximity_percentage: 2,
          include_fibonacci: false
        }
      },
      {
        value: 'swing_highs_lows',
        label: 'Swing Highs/Lows',
        description: 'Support/resistance from swing points',
        params: {
          lookback_period: 100,
          swing_strength: 5,
          proximity_percentage: 1.5,
          include_fibonacci: true
        }
      }
    ]
  },

  TREND_STRENGTH: {
    name: 'Trend Strength',
    description: 'Measures the strength of current trends',
    icon: Activity,
    category: 'Advanced Analysis',
    methods: [
      {
        value: 'adx_based',
        label: 'ADX-Based',
        description: 'Uses Average Directional Index for trend strength',
        params: {
          period: 14,
          strong_trend_threshold: 25,
          weak_trend_threshold: 15,
          very_strong_threshold: 40
        }
      },
      {
        value: 'slope_analysis',
        label: 'Slope Analysis',
        description: 'Analyzes moving average slopes for trend strength',
        params: {
          ma_period: 20,
          lookback_period: 10,
          strong_slope_threshold: 0.5,
          weak_slope_threshold: 0.1
        }
      }
    ]
  },

  VOLATILITY_FILTER: {
    name: 'Volatility Filter',
    description: 'Filters signals based on volatility conditions',
    icon: Filter,
    category: 'Advanced Analysis',
    methods: [
      {
        value: 'atr_percentile',
        label: 'ATR Percentile',
        description: 'Filters based on ATR percentile ranking',
        params: {
          lookback_period: 20,
          low_volatility_threshold: 25,
          high_volatility_threshold: 75,
          filter_mode: 'exclude_extremes'
        }
      },
      {
        value: 'bollinger_squeeze',
        label: 'Bollinger Squeeze',
        description: 'Detects low volatility squeeze conditions',
        params: {
          bb_period: 20,
          bb_std_dev: 2,
          squeeze_threshold: 0.1,
          filter_mode: 'low_volatility_only'
        }
      }
    ]
  },

  FIBONACCI_LEVELS: {
    name: 'Fibonacci Levels',
    description: 'Calculates Fibonacci retracement and extension levels',
    icon: Zap,
    category: 'Advanced Analysis',
    methods: [
      {
        value: 'swing_high_low',
        label: 'Swing High/Low',
        description: 'Fibonacci levels from recent swing points',
        params: {
          lookback_period: 50,
          key_levels: [0.236, 0.382, 0.5, 0.618, 0.786],
          proximity_tolerance: 1,
          include_extensions: false
        }
      },
      {
        value: 'trend_based',
        label: 'Trend-Based',
        description: 'Fibonacci levels based on trend direction',
        params: {
          trend_period: 100,
          key_levels: [0.236, 0.382, 0.5, 0.618, 0.786],
          proximity_tolerance: 1.5,
          include_extensions: true
        }
      }
    ]
  },

  MARKET_SENTIMENT: {
    name: 'Market Sentiment',
    description: 'Analyzes overall market sentiment indicators',
    icon: Heart,
    category: 'Advanced Analysis',
    methods: [
      {
        value: 'vix_analysis',
        label: 'VIX Analysis',
        description: 'Sentiment based on VIX fear/greed levels',
        params: {
          lookback_period: 10,
          bullish_threshold: 20,
          bearish_threshold: 30,
          extreme_fear_threshold: 40
        }
      },
      {
        value: 'put_call_ratio',
        label: 'Put/Call Ratio',
        description: 'Sentiment from options put/call ratios',
        params: {
          lookback_period: 5,
          bullish_threshold: 0.8,
          bearish_threshold: 1.2,
          extreme_threshold: 1.5
        }
      }
    ]
  },

  SECTOR_ANALYSIS: {
    name: 'Sector Analysis',
    description: 'Analyzes sector performance and relative strength',
    icon: Building,
    category: 'Advanced Analysis',
    methods: [
      {
        value: 'relative_strength',
        label: 'Relative Strength',
        description: 'Compares performance to sector benchmark',
        params: {
          sector_benchmark: 'auto_detect',
          lookback_period: 30,
          outperformance_threshold: 5,
          underperformance_threshold: -5
        }
      },
      {
        value: 'sector_rotation',
        label: 'Sector Rotation',
        description: 'Analyzes sector rotation patterns',
        params: {
          rotation_period: 60,
          momentum_threshold: 10,
          relative_strength_period: 20,
          sector_benchmark: 'auto_detect'
        }
      }
    ]
  }
};

export function getAdvancedAnalysisConfig(blockType: string) {
  return advancedAnalysisConfigs[blockType as keyof typeof advancedAnalysisConfigs];
}
