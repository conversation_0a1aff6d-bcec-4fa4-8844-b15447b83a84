import { Shield, Target, TrendingUp, AlertTriangle } from 'lucide-react';

// Risk Analysis Block Configurations
export const riskAnalysisConfigs = {
  RISK_ANALYZER: {
    name: 'Risk Analyzer',
    description: 'Analyzes downside risk and calculates optimal stop loss levels',
    icon: Shield,
    category: 'Risk Analysis',
    methods: [
      {
        value: 'atr_based',
        label: 'ATR-Based',
        description: 'Uses Average True Range for dynamic risk calculation',
        params: {
          atr_multiplier: 2,
          atr_period: 14,
          support_resistance: true,
          risk_tolerance: 'moderate'
        }
      },
      {
        value: 'support_resistance',
        label: 'Support/Resistance',
        description: 'Places stop loss at key support/resistance levels',
        params: {
          lookback_period: 50,
          strength_threshold: 3,
          buffer_percentage: 0.5,
          risk_tolerance: 'moderate'
        }
      },
      {
        value: 'percentage_based',
        label: 'Fixed Percentage',
        description: 'Simple percentage-based stop loss calculation',
        params: {
          percentage: 3,
          max_risk_percentage: 5,
          min_risk_percentage: 1,
          risk_tolerance: 'moderate'
        }
      }
    ]
  },

  TARGET_ANALYZER: {
    name: 'Target Analyzer',
    description: 'Calculates profit targets and upside potential',
    icon: Target,
    category: 'Risk Analysis',
    methods: [
      {
        value: 'fibonacci_resistance',
        label: 'Fibonacci Levels',
        description: 'Uses Fibonacci retracement levels for profit targets',
        params: {
          lookback_period: 50,
          key_levels: [0.236, 0.382, 0.5, 0.618, 0.786],
          target_levels: 3,
          probability_weighting: true
        }
      },
      {
        value: 'risk_reward_ratio',
        label: 'Risk/Reward Ratio',
        description: 'Sets targets based on risk/reward ratios',
        params: {
          risk_reward_ratio: 2,
          target_levels: 2,
          scaling_factor: 1.5,
          probability_weighting: true
        }
      }
    ]
  },

  SIGNAL_ENHANCER: {
    name: 'Signal Enhancer',
    description: 'Enhances signals with risk management guidance',
    icon: TrendingUp,
    category: 'Risk Analysis',
    methods: [
      {
        value: 'risk_guidance',
        label: 'Risk Guidance',
        description: 'Adds comprehensive risk management information',
        params: {
          include_position_sizing: true,
          include_risk_metrics: true,
          include_targets: true,
          educational_mode: true
        }
      }
    ]
  },

  RISK_REWARD_ANALYZER: {
    name: 'Risk/Reward Analyzer',
    description: 'Analyzes overall risk/reward profile of signals',
    icon: AlertTriangle,
    category: 'Risk Analysis',
    methods: [
      {
        value: 'comprehensive_analysis',
        label: 'Comprehensive Analysis',
        description: 'Full risk/reward analysis with quality scoring',
        params: {
          min_risk_reward_ratio: 1.5,
          max_risk_percentage: 5,
          quality_threshold: 70,
          include_probability: true
        }
      }
    ]
  }
};

export function getRiskAnalysisConfig(blockType: string) {
  return riskAnalysisConfigs[blockType as keyof typeof riskAnalysisConfigs];
}
