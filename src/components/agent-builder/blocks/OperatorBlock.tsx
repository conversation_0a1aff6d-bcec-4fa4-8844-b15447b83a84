import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>si<PERSON> } from 'reactflow';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Calculator, Trash2 } from 'lucide-react';
import { AgentBlock } from '@/services/agentService';

interface OperatorBlockProps {
  data: {
    id: string;
    operation: string;
    inputConnections: string[];
    outputConnections: string[];
    isEntryBlock: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
  };
  selected: boolean;
}

const OperatorBlock: React.FC<OperatorBlockProps> = ({ data, selected }) => {
  const [isEditing, setIsEditing] = useState(false);

  // Available operations
  const operations = [
    { value: 'add', label: 'Add (+)', description: 'Sum of all inputs' },
    { value: 'subtract', label: 'Subtract (-)', description: 'First input minus all other inputs' },
    { value: 'multiply', label: 'Multiply (×)', description: 'Product of all inputs' },
    { value: 'divide', label: 'Divide (÷)', description: 'First input divided by second input' },
    { value: 'average', label: 'Average', description: 'Average of all inputs' },
    { value: 'min', label: 'Minimum', description: 'Smallest value among inputs' },
    { value: 'max', label: 'Maximum', description: 'Largest value among inputs' },
    { value: 'abs', label: 'Absolute Value', description: 'Absolute value of the input' }
  ];

  // Handle operation change
  const handleOperationChange = (value: string) => {
    data.onUpdate({ operation: value });
  };

  // Get the current operation
  const currentOperation = operations.find(op => op.value === data.operation) || operations[0];

  // Get operation symbol
  const getOperationSymbol = () => {
    switch (data.operation) {
      case 'add':
        return '+';
      case 'subtract':
        return '-';
      case 'multiply':
        return '×';
      case 'divide':
        return '÷';
      case 'average':
        return 'avg';
      case 'min':
        return 'min';
      case 'max':
        return 'max';
      case 'abs':
        return '|x|';
      default:
        return '';
    }
  };

  return (
    <div className="relative">
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{
          background: '#555',
          width: '12px',
          height: '12px',
          border: '2px solid white'
        }}
      />

      {/* Output handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        style={{
          background: '#555',
          width: '12px',
          height: '12px',
          border: '2px solid white'
        }}
      />

      <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''} ${data.isEntryBlock ? 'border-primary' : ''}`}>
        <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center">
              <Calculator className="h-3 w-3 text-primary" />
            </div>
            <CardTitle className="text-sm font-medium">Operator</CardTitle>
          </div>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={() => setIsEditing(!isEditing)}
              title={isEditing ? "Close editor" : "Edit block"}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-3 w-3"
              >
                <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z" />
                <path d="m15 5 4 4" />
              </svg>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-destructive"
              onClick={data.onRemove}
              title="Remove block"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-3 pt-2">
          <div className="space-y-2">
            <div>
              <label className="text-xs font-medium block mb-1">Operation</label>
              <Select
                value={data.operation}
                onValueChange={handleOperationChange}
              >
                <SelectTrigger className="h-8 text-xs">
                  <SelectValue placeholder="Select operation" />
                </SelectTrigger>
                <SelectContent>
                  {operations.map(op => (
                    <SelectItem key={op.value} value={op.value} className="text-xs">
                      {op.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center justify-center h-10 w-10 mx-auto mt-2 bg-primary/10 rounded-full">
              <span className="text-lg font-bold text-primary">{getOperationSymbol()}</span>
            </div>

            {isEditing && (
              <div className="mt-2 p-2 bg-muted/50 rounded-md">
                <p className="text-xs text-muted-foreground">
                  {currentOperation.description}
                </p>
                <p className="text-xs mt-1 text-muted-foreground">
                  Connect inputs to the left side and use the output on the right side.
                </p>
                <p className="text-xs mt-1 text-muted-foreground">
                  This block performs mathematical operations on numeric inputs.
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default OperatorBlock;
