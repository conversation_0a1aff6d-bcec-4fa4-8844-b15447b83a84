import React, { useState } from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { DollarSign, Trash2 } from 'lucide-react';
import { AgentBlock } from '@/services/agentService';

interface PriceBlockProps {
  data: {
    id: string;
    dataPoint: string;
    timeframe?: string;
    lookback?: number;
    isEntryBlock: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
  };
  selected: boolean;
}

const PriceBlock: React.FC<PriceBlockProps> = ({ data, selected }) => {
  const [isEditing, setIsEditing] = useState(false);

  // Available price data points
  const dataPoints = [
    { value: 'open', label: 'Open Price' },
    { value: 'high', label: 'High Price' },
    { value: 'low', label: 'Low Price' },
    { value: 'close', label: 'Close Price' },
    { value: 'volume', label: 'Volume' },
    { value: 'current', label: 'Current Price' }
  ];

  // Available timeframes
  const timeframes = [
    { value: 'day', label: 'Daily' },
    { value: 'hour', label: 'Hourly' },
    { value: '15minute', label: '15 Minutes' }
  ];

  // Handle data point change
  const handleDataPointChange = (value: string) => {
    data.onUpdate({ dataPoint: value });
  };

  // Handle timeframe change
  const handleTimeframeChange = (value: string) => {
    data.onUpdate({ timeframe: value });
  };

  // Handle lookback change
  const handleLookbackChange = (value: string) => {
    if (value === '') {
      data.onUpdate({ lookback: '' });
    } else {
      const numValue = parseInt(value);
      if (!isNaN(numValue) && numValue >= 0) {
        data.onUpdate({ lookback: numValue });
      }
    }
  };

  return (
    <div className="relative">
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{
          background: '#6b7280',
          width: '12px',
          height: '12px',
          border: '2px solid white'
        }}
      />

      {/* Output handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        style={{
          background: '#3b82f6',
          width: '12px',
          height: '12px',
          border: '2px solid white'
        }}
      />

      <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''} ${data.isEntryBlock ? 'border-primary' : ''}`}>
        <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center">
              <DollarSign className="h-3 w-3 text-primary" />
            </div>
            <CardTitle className="text-sm font-medium">Price Data</CardTitle>
          </div>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={() => setIsEditing(!isEditing)}
              title={isEditing ? "Close editor" : "Edit block"}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-3 w-3"
              >
                <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z" />
                <path d="m15 5 4 4" />
              </svg>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-destructive"
              onClick={data.onRemove}
              title="Remove block"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-3 pt-2">
          <div className="space-y-2">
            <div>
              <label className="text-xs font-medium block mb-1">Data Point</label>
              <Select
                value={data.dataPoint}
                onValueChange={handleDataPointChange}
              >
                <SelectTrigger className="h-8 text-xs">
                  <SelectValue placeholder="Select data point" />
                </SelectTrigger>
                <SelectContent>
                  {dataPoints.map(point => (
                    <SelectItem key={point.value} value={point.value} className="text-xs">
                      {point.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {isEditing && (
              <>
                <div>
                  <label className="text-xs font-medium block mb-1">Timeframe</label>
                  <Select
                    value={data.timeframe || 'day'}
                    onValueChange={handleTimeframeChange}
                  >
                    <SelectTrigger className="h-8 text-xs">
                      <SelectValue placeholder="Select timeframe" />
                    </SelectTrigger>
                    <SelectContent>
                      {timeframes.map(tf => (
                        <SelectItem key={tf.value} value={tf.value} className="text-xs">
                          {tf.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-xs font-medium block mb-1">Lookback Periods</label>
                  <Input
                    type="number"
                    value={data.lookback || 0}
                    onChange={e => handleLookbackChange(e.target.value)}
                    className="h-8 text-xs"
                    min={0}
                    placeholder="0 for current data"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    {data.lookback === 0 || !data.lookback
                      ? "Using current data"
                      : `Looking back ${data.lookback} periods`}
                  </p>
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PriceBlock;
