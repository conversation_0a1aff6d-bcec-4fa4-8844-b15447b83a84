import React from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { TrendingUp, Trash2, ChevronDown } from 'lucide-react';
import { AgentBlock } from '@/types/agent';

interface ChartPatternBlockProps {
  data: {
    id: string;
    pattern: string;
    timeframe?: string;
    lookbackPeriod?: number;
    minPatternSize?: number;
    inputConnections: string[];
    trueConnection?: string;
    falseConnection?: string;
    bullishConnection?: string;
    bearishConnection?: string;
    neutralConnection?: string;
    isEntryBlock: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
    hasError?: boolean;
    isDisconnected?: boolean;
    errorMessages?: string[];
  };
  selected: boolean;
}

const ChartPatternBlock: React.FC<ChartPatternBlockProps> = ({ data, selected }) => {
  // Check if this is "any pattern" mode
  const isAnyPattern = data.pattern === 'any';

  // State for collapsible "How this block works" section
  const [isHelpOpen, setIsHelpOpen] = React.useState(false);

  // Available patterns
  const patterns = [
    { value: 'any', label: 'Any Pattern' },
    { value: 'triangle', label: 'Triangle' },
    { value: 'flag', label: 'Flag' },
    { value: 'pennant', label: 'Pennant' },
    { value: 'head_and_shoulders', label: 'Head and Shoulders' },
    { value: 'double_top', label: 'Double Top' },
    { value: 'double_bottom', label: 'Double Bottom' },
    { value: 'cup_and_handle', label: 'Cup and Handle' },
    { value: 'wedge', label: 'Wedge' },
    { value: 'channel', label: 'Channel' }
  ];

  // Available timeframes
  const timeframes = [
    { value: 'day', label: 'Daily' },
    { value: '4hour', label: '4 Hours' },
    { value: '1hour', label: '1 Hour' },
    { value: '15min', label: '15 Minutes' },
    { value: '5min', label: '5 Minutes' },
    { value: '1min', label: '1 Minute' }
  ];

  // Handle pattern change
  const handlePatternChange = (value: string) => {
    data.onUpdate({ pattern: value });
  };

  // Handle timeframe change
  const handleTimeframeChange = (value: string) => {
    data.onUpdate({ timeframe: value });
  };

  // Handle lookback period change
  const handleLookbackChange = (value: string) => {
    const numValue = parseInt(value) || 50;
    data.onUpdate({ lookbackPeriod: numValue });
  };

  // Handle min pattern size change
  const handleMinSizeChange = (value: string) => {
    const numValue = parseInt(value) || 10;
    data.onUpdate({ minPatternSize: numValue });
  };

  return (
    <div className="relative">
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{
          background: '#3b82f6',
          width: '12px',
          height: '12px',
          border: '2px solid white'
        }}
      />

      {/* Output handles - conditional based on pattern type */}
      {isAnyPattern ? (
        <>
          {/* Bullish output handle */}
          <Handle
            type="source"
            position={Position.Right}
            id="bullish"
            style={{
              background: data.hasError ? '#ef4444' : '#22c55e',
              top: '30%',
              width: '12px',
              height: '12px',
              border: '2px solid white'
            }}
          />

          {/* Bearish output handle */}
          <Handle
            type="source"
            position={Position.Right}
            id="bearish"
            style={{
              background: data.hasError ? '#ef4444' : '#ef4444',
              top: '50%',
              width: '12px',
              height: '12px',
              border: '2px solid white'
            }}
          />

          {/* Neutral output handle */}
          <Handle
            type="source"
            position={Position.Right}
            id="neutral"
            style={{
              background: data.hasError ? '#ef4444' : '#6b7280',
              top: '70%',
              width: '12px',
              height: '12px',
              border: '2px solid white'
            }}
          />
        </>
      ) : (
        <>
          {/* True output handle (pattern found) */}
          <Handle
            type="source"
            position={Position.Right}
            id="true"
            style={{
              background: data.hasError ? '#ef4444' : '#22c55e',
              top: '40%',
              width: '12px',
              height: '12px',
              border: '2px solid white'
            }}
          />

          {/* False output handle (pattern not found) */}
          <Handle
            type="source"
            position={Position.Right}
            id="false"
            style={{
              background: data.hasError ? '#ef4444' : '#ef4444',
              top: '60%',
              width: '12px',
              height: '12px',
              border: '2px solid white'
            }}
          />
        </>
      )}

      <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''} ${data.isEntryBlock ? 'border-primary' : ''} ${data.hasError ? 'border-red-500' : ''}`}>
        <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center">
              <TrendingUp className="h-3 w-3 text-primary" />
            </div>
            <CardTitle className="text-sm font-medium">Chart Pattern</CardTitle>
          </div>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-destructive"
              onClick={(e) => {
                e.stopPropagation();
                data.onRemove();
              }}
              title="Remove block"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-3 pt-2">
          <div className="space-y-2">
            <div className="text-xs text-muted-foreground">
              {isAnyPattern
                ? 'Detects any chart pattern and routes to bullish/bearish/neutral outputs'
                : 'Detects specific chart pattern with true/false outputs'
              }
            </div>

            {/* Pattern selection */}
            <div>
              <Label className="text-xs">Pattern Type</Label>
              <Select value={data.pattern || 'any'} onValueChange={handlePatternChange}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {patterns.map(pattern => (
                    <SelectItem key={pattern.value} value={pattern.value}>
                      {pattern.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Timeframe selection */}
            <div>
              <Label className="text-xs">Timeframe</Label>
              <Select value={data.timeframe || 'day'} onValueChange={handleTimeframeChange}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {timeframes.map(timeframe => (
                    <SelectItem key={timeframe.value} value={timeframe.value}>
                      {timeframe.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Lookback period */}
            <div>
              <Label className="text-xs">Lookback Period</Label>
              <Input
                type="number"
                value={data.lookbackPeriod || 50}
                onChange={(e) => handleLookbackChange(e.target.value)}
                className="h-8"
                min="10"
                max="200"
              />
            </div>

            {/* Min pattern size */}
            <div>
              <Label className="text-xs">Min Pattern Size</Label>
              <Input
                type="number"
                value={data.minPatternSize || 10}
                onChange={(e) => handleMinSizeChange(e.target.value)}
                className="h-8"
                min="5"
                max="50"
              />
            </div>

            {/* How this block works - collapsible */}
            <Collapsible open={isHelpOpen} onOpenChange={setIsHelpOpen}>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" className="w-full justify-between p-0 h-auto text-xs text-muted-foreground hover:text-foreground">
                  <span>How this block works</span>
                  <ChevronDown className={`h-3 w-3 transition-transform ${isHelpOpen ? 'rotate-180' : ''}`} />
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="space-y-2 mt-2">
                <div className="text-xs text-muted-foreground">
                  <div className="mb-2">
                    <div className="font-medium mb-1">Pattern Analysis:</div>
                    <p>Analyzes historical price data to detect chart patterns like triangles, flags, head and shoulders, etc.</p>
                  </div>

                  <div className="mb-2">
                    <div className="font-medium mb-1">Output Terminals:</div>
                    {isAnyPattern ? (
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-green-500"></div>
                          <span>Bullish Pattern - Routes when a bullish pattern is detected</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-red-500"></div>
                          <span>Bearish Pattern - Routes when a bearish pattern is detected</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-gray-500"></div>
                          <span>No Pattern - Routes when no significant pattern is found</span>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-green-500"></div>
                          <span>Pattern Found - Routes when the specific pattern is detected</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-red-500"></div>
                          <span>Pattern Not Found - Routes when the pattern is not detected</span>
                        </div>
                      </div>
                    )}
                  </div>

                  <div>
                    <div className="font-medium mb-1">Configuration:</div>
                    <ul className="space-y-1 text-xs">
                      <li>• <strong>Pattern Type:</strong> Select specific pattern or "Any Pattern"</li>
                      <li>• <strong>Timeframe:</strong> Chart timeframe for analysis</li>
                      <li>• <strong>Lookback Period:</strong> Number of bars to analyze (10-200)</li>
                      <li>• <strong>Min Pattern Size:</strong> Minimum pattern size in bars (5-50)</li>
                    </ul>
                  </div>
                </div>
              </CollapsibleContent>
            </Collapsible>

            {/* Error messages */}
            {data.hasError && data.errorMessages && data.errorMessages.length > 0 && (
              <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
                <div className="flex items-center gap-2 text-red-700 font-medium text-xs mb-1">
                  <span>⚠️ Errors:</span>
                </div>
                <ul className="text-red-600 text-xs space-y-1">
                  {data.errorMessages.map((message, index) => (
                    <li key={index}>• {message}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ChartPatternBlock;
