import React from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Play } from 'lucide-react';
import { AgentBlock } from '@/services/agentService';

interface WhenRunBlockProps {
  data: {
    id: string;
    isEntryBlock: boolean;
    outputConnections: string[];
    hasError?: boolean;
    isDisconnected?: boolean;
    errorMessages?: string[];
  };
  selected: boolean;
}

const WhenRunBlock: React.FC<WhenRunBlockProps> = ({ data, selected }) => {
  // Render error messages
  const renderErrorMessages = () => {
    if (!data.hasError || !data.errorMessages || data.errorMessages.length === 0) {
      return null;
    }

    return (
      <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
        <div className="flex items-center text-red-700 text-xs font-medium mb-1">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          Error
        </div>
        <ul className="list-disc pl-5 text-xs text-red-600">
          {data.errorMessages.map((error, index) => (
            <li key={index}>{error}</li>
          ))}
        </ul>
      </div>
    );
  };

  return (
    <div className={`relative ${data.isDisconnected ? 'disconnected-block' : ''}`}>
      {/* Output handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        style={{
          background: data.hasError ? '#ef4444' : '#22c55e',
          width: '12px',
          height: '12px',
          border: '2px solid white',
          transformOrigin: '50% 50%'
        }}
      />

      <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''} ${data.isEntryBlock ? 'border-primary' : ''} ${data.hasError ? 'border-red-500' : ''}`}>
        <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 rounded-full bg-green-500/20 flex items-center justify-center">
              <Play className="h-3 w-3 text-green-600" />
            </div>
            <CardTitle className="text-sm font-medium">When Run</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="p-3 pt-2">
          <div className="space-y-2">
            <p className="text-xs text-muted-foreground">
              This is the starting point of your agent. Connect this block to the first step in your analysis.
            </p>

            {/* Display error messages */}
            {renderErrorMessages()}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default WhenRunBlock;
