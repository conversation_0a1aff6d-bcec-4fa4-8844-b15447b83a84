import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>T<PERSON>le, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Handle, Position } from 'reactflow';
import { Trash2 } from 'lucide-react';
import { AgentBlock } from '@/services/agentService';
import { getSignalQualityConfig } from './configs/signalQualityConfig';

interface SignalQualityBlockProps {
  data: {
    id: string;
    type: string;
    parameters: Record<string, any>;
    isEntryBlock: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
    hasError?: boolean;
    isDisconnected?: boolean;
    errorMessages?: string[];
  };
  selected: boolean;
}

const SignalQualityBlock: React.FC<SignalQualityBlockProps> = ({ data, selected }) => {
  
  const config = getSignalQualityConfig(data.type);
  
  if (!config) {
    return (
      <Card className="w-64 border-red-500">
        <CardContent className="p-3">
          <div className="text-red-500 text-sm">Unknown block type: {data.type}</div>
        </CardContent>
      </Card>
    );
  }

  const IconComponent = config.icon;
  
  // Ensure parameters exist and have default values
  const parameters = data.parameters || {};
  const currentMethod = config.methods.find(method => method.value === parameters.method) || config.methods[0];

  const handleMethodChange = (method: string) => {
    const selectedMethod = config.methods.find(m => m.value === method);
    if (selectedMethod) {
      data.onUpdate({
        parameters: {
          ...parameters,
          method,
          ...selectedMethod.params
        }
      });
    }
  };

  const handleParameterChange = (param: string, value: any) => {
    data.onUpdate({
      parameters: {
        ...parameters,
        [param]: parseFloat(value) || value
      }
    });
  };

  const renderParameterInputs = () => {
    if (!currentMethod) return null;

    return Object.entries(currentMethod.params).map(([param, defaultValue]) => {
      const currentValue = parameters[param] ?? defaultValue;
      
      // Handle different parameter types
      if (typeof defaultValue === 'boolean') {
        return (
          <div key={param} className="flex items-center justify-between">
            <label className="text-xs font-medium">
              {param.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
            </label>
            <Switch
              checked={currentValue}
              onCheckedChange={value => handleParameterChange(param, value)}
            />
          </div>
        );
      }

      if (param === 'timezone') {
        return (
          <div key={param} className="space-y-1">
            <label className="text-xs font-medium block">Timezone</label>
            <Select value={currentValue} onValueChange={value => handleParameterChange(param, value)}>
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="EST">Eastern (EST)</SelectItem>
                <SelectItem value="PST">Pacific (PST)</SelectItem>
                <SelectItem value="CST">Central (CST)</SelectItem>
                <SelectItem value="MST">Mountain (MST)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );
      }

      if (param.includes('time')) {
        return (
          <div key={param} className="space-y-1">
            <label className="text-xs font-medium block capitalize">
              {param.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
            </label>
            <Input
              type="time"
              value={currentValue}
              onChange={e => handleParameterChange(param, e.target.value)}
              className="h-8 text-xs"
            />
          </div>
        );
      }

      return (
        <div key={param} className="space-y-1">
          <label className="text-xs font-medium block capitalize">
            {param.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
          </label>
          <Input
            type="number"
            value={currentValue}
            onChange={e => {
              const value = e.target.value;
              if (value === '') {
                handleParameterChange(param, '');
              } else {
                const isDecimal = param.includes('factor') || param.includes('threshold');
                const numValue = isDecimal ? parseFloat(value) : parseInt(value);
                if (!isNaN(numValue)) {
                  handleParameterChange(param, numValue);
                }
              }
            }}
            className="h-8 text-xs"
            min={0}
            max={param.includes('confidence') || param.includes('threshold') ? 100 : undefined}
            step={param.includes('factor') || param.includes('threshold') ? 0.1 : 1}
            placeholder={defaultValue?.toString() || '0'}
          />
        </div>
      );
    });
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Signal Quality':
        return { bg: 'bg-emerald-500/10', text: 'text-emerald-500' };
      default:
        return { bg: 'bg-blue-500/10', text: 'text-blue-500' };
    }
  };

  const colorClasses = getCategoryColor(config.category);

  return (
    <>
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 bg-gray-400 border-2 border-white"
      />

      <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''} ${data.isEntryBlock ? 'border-primary' : ''} ${data.hasError ? 'border-red-500' : ''}`}>
        <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={`w-6 h-6 rounded-full ${colorClasses.bg} flex items-center justify-center`}>
              <IconComponent className={`h-3 w-3 ${colorClasses.text}`} />
            </div>
            <CardTitle className="text-sm font-medium">{config.name}</CardTitle>
          </div>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-destructive"
              onClick={(e) => {
                e.stopPropagation();
                data.onRemove();
              }}
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-3 pt-2 space-y-3">
          {/* Method Selection */}
          <div className="space-y-1">
            <label className="text-xs font-medium block">Method</label>
            <Select value={parameters.method || currentMethod.value} onValueChange={handleMethodChange}>
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {config.methods.map(method => (
                  <SelectItem key={method.value} value={method.value}>
                    {method.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Parameter Inputs */}
          <div className="space-y-2">
            {renderParameterInputs()}
          </div>

          {/* Error Messages */}
          {data.hasError && data.errorMessages && (
            <div className="text-xs text-red-500 bg-red-50 p-2 rounded">
              {data.errorMessages.map((msg, idx) => (
                <div key={idx}>{msg}</div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Output handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-3 h-3 bg-gray-400 border-2 border-white"
      />
    </>
  );
};

export default SignalQualityBlock;
