import React, { useState } from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Trash2, Crosshair, ChevronDown, HelpCircle } from 'lucide-react';
import { AgentBlock } from '@/services/agentService';

interface GapAnalysisBlockProps {
  data: {
    id: string;
    minGapSize: number;
    timeframe: string;
    inputConnections: string[];
    gapUpConnection?: string;
    gapDownConnection?: string;
    noGapConnection?: string;
    outputConnections: string[];
    isEntryBlock: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
    hasError?: boolean;
    isDisconnected?: boolean;
    errorMessages?: string[];
  };
  selected: boolean;
}

const GapAnalysisBlock: React.FC<GapAnalysisBlockProps> = ({ data, selected }) => {
  const [showHelp, setShowHelp] = useState(false); // Help section collapsed by default

  const handleUpdate = (field: string, value: any) => {
    data.onUpdate({ [field]: value });
  };

  const timeframeOptions = [
    { value: 'day', label: 'Daily' },
    { value: '1hour', label: '1 Hour' },
    { value: '15min', label: '15 Minutes' },
    { value: '5min', label: '5 Minutes' },
    { value: '1min', label: '1 Minute' }
  ];

  return (
    <div className={`relative ${data.isDisconnected ? 'disconnected-block' : ''}`}>
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{
          background: data.hasError ? '#ef4444' : '#555',
          width: '12px',
          height: '12px',
          border: '2px solid white',
          transformOrigin: '50% 50%'
        }}
      />

      {/* Gap Up output handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="gap_up"
        style={{
          background: data.hasError ? '#ef4444' : '#22c55e',
          top: '25%',
          width: '12px',
          height: '12px',
          border: '2px solid white',
          transformOrigin: '50% 50%'
        }}
      />

      {/* Gap Down output handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="gap_down"
        style={{
          background: data.hasError ? '#ef4444' : '#ef4444',
          top: '50%',
          width: '12px',
          height: '12px',
          border: '2px solid white',
          transformOrigin: '50% 50%'
        }}
      />

      {/* No Gap output handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="no_gap"
        style={{
          background: data.hasError ? '#ef4444' : '#6b7280',
          top: '75%',
          width: '12px',
          height: '12px',
          border: '2px solid white',
          transformOrigin: '50% 50%'
        }}
      />

      <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''} ${data.isEntryBlock ? 'border-primary' : ''} ${data.hasError ? 'border-red-500' : ''}`}>
        <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 rounded-full bg-muted flex items-center justify-center">
              <Crosshair className="h-3 w-3 text-muted-foreground" />
            </div>
            <CardTitle className="text-sm font-medium">Gap Analysis</CardTitle>
          </div>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-red-500 hover:text-red-700"
              onClick={data.onRemove}
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="p-3 pt-2">
          {/* Error display */}
          {data.hasError && data.errorMessages && (
            <div className="mb-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-600">
              {data.errorMessages.map((error, index) => (
                <div key={index}>{error}</div>
              ))}
            </div>
          )}

          {/* Disconnected warning */}
          {data.isDisconnected && (
            <div className="mb-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-600">
              This block is not connected to the main flow
            </div>
          )}

          {/* Configuration */}
          <div className="space-y-3">
            <div>
              <Label className="text-xs font-medium">Minimum Gap Size (%)</Label>
              <Input
                type="number"
                value={data.minGapSize}
                onChange={e => handleUpdate('minGapSize', parseFloat(e.target.value) || 1.0)}
                className="h-8 text-xs mt-1"
                step="0.1"
                min="0.1"
                max="20"
              />
            </div>

            <div>
              <Label className="text-xs font-medium">Timeframe</Label>
              <Select
                value={data.timeframe}
                onValueChange={value => handleUpdate('timeframe', value)}
              >
                <SelectTrigger className="h-8 text-xs mt-1">
                  <SelectValue placeholder="Select timeframe" />
                </SelectTrigger>
                <SelectContent>
                  {timeframeOptions.map(option => (
                    <SelectItem key={option.value} value={option.value} className="text-xs">
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <Collapsible open={showHelp} onOpenChange={setShowHelp}>
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-between mt-3 h-8 text-xs text-muted-foreground hover:text-foreground"
                >
                  <div className="flex items-center gap-2">
                    <HelpCircle className="h-3 w-3" />
                    <span>How this block works</span>
                  </div>
                  <ChevronDown className={`h-3 w-3 transition-transform ${showHelp ? 'rotate-180' : ''}`} />
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <div className="mt-2 p-3 bg-muted/30 rounded-md text-xs text-muted-foreground">
                  <p className="mb-2">
                    Detects price gaps between sessions and routes to appropriate outputs:
                  </p>

                  <div className="flex flex-col gap-2">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                      <span>Gap Up (bullish)</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-red-500"></div>
                      <span>Gap Down (bearish)</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-gray-500"></div>
                      <span>No Gap (normal)</span>
                    </div>
                  </div>

                  <div className="mt-3">
                    <p>
                      A gap occurs when the opening price is outside the previous session's high-low range.
                      Only gaps larger than the minimum size will trigger the gap outputs.
                    </p>
                  </div>
                </div>
              </CollapsibleContent>
            </Collapsible>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default GapAnalysisBlock;
