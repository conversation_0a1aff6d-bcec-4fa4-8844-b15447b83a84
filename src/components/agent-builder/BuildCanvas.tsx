import React, { useCallback, useRef, useState } from 'react';
import React<PERSON>low, {
  Background,
  Controls,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Edge,
  Node,
  NodeTypes,
  EdgeTypes,
  NodeChange,
  EdgeChange,
  ConnectionLineType,
  Panel,
  MiniMap
} from 'reactflow';
import 'reactflow/dist/style.css';
import './agent-builder.css';
import { AgentBlock } from '@/services/agentService';
import { BlockType, ConnectionType } from '@/hooks/useAgentBuilder';
import IndicatorNode from './blocks/IndicatorBlock';
import PriceNode from './blocks/PriceBlock';
import FundamentalNode from './blocks/FundamentalBlock';
import ConditionNode from './blocks/ConditionBlock';
import ComparisonNode from './blocks/ComparisonBlock';
import TriggerNode from './blocks/TriggerBlock';
import OperatorNode from './blocks/OperatorBlock';
import WhenRunNode from './blocks/WhenRunBlock';
import BullishConfidenceBoostNode from './blocks/BullishConfidenceBoostBlock';
import BearishConfidenceBoostNode from './blocks/BearishConfidenceBoostBlock';
import ConfidenceBoostNode from './blocks/ConfidenceBoostBlock';
import CandlePatternNode from './blocks/CandlePatternBlock';
import StockSentimentNode from './blocks/StockSentimentBlock';
import ChartPatternNode from './blocks/ChartPatternBlock';
import BreakoutDetectionNode from './blocks/BreakoutDetectionBlock';
import GapAnalysisNode from './blocks/GapAnalysisBlock';
import ConsoleLogNode from './blocks/ConsoleLogBlock';
import MomentumIndicatorNode from './blocks/MomentumIndicatorBlock';
import MovingAverageNode from './blocks/MovingAverageBlock';
import AndBlock from './blocks/AndBlock';
import OrBlock from './blocks/OrBlock';
import SignalNode from './blocks/SignalBlock';
import GenericNode from './blocks/GenericBlock';
import TechnicalIndicatorBlock from './blocks/TechnicalIndicatorBlock';
import SignalGenerationBlock from './blocks/SignalGenerationBlock';
import LogicFlowBlock from './blocks/LogicFlowBlock';
import MarketAnalysisBlock from './blocks/MarketAnalysisBlock';

import { Button } from '@/components/ui/button';
import { ZoomIn, ZoomOut, Maximize, X } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

// Define custom node types
const nodeTypes: NodeTypes = {
  [BlockType.WHEN_RUN]: WhenRunNode,
  [BlockType.INDICATOR]: IndicatorNode,
  [BlockType.PRICE]: PriceNode,
  [BlockType.FUNDAMENTAL]: FundamentalNode,
  [BlockType.CONDITION]: ConditionNode,
  [BlockType.COMPARISON]: ComparisonNode,
  [BlockType.TRIGGER]: TriggerNode,
  [BlockType.OPERATOR]: OperatorNode,
  [BlockType.BULLISH_CONFIDENCE_BOOST]: BullishConfidenceBoostNode,
  [BlockType.BEARISH_CONFIDENCE_BOOST]: BearishConfidenceBoostNode,
  [BlockType.CONFIDENCE_BOOST]: ConfidenceBoostNode,
  [BlockType.CANDLE_PATTERN]: CandlePatternNode,
  [BlockType.STOCK_SENTIMENT]: StockSentimentNode,
  [BlockType.CHART_PATTERN]: ChartPatternNode,
  [BlockType.BREAKOUT_DETECTION]: BreakoutDetectionNode,
  [BlockType.GAP_ANALYSIS]: GapAnalysisNode,
  [BlockType.CONSOLE_LOG]: ConsoleLogNode,
  [BlockType.MOMENTUM_INDICATOR]: MomentumIndicatorNode,
  [BlockType.MOVING_AVERAGE]: MovingAverageNode,


  // Technical Indicators - Use dedicated components
  [BlockType.TREND_INDICATOR]: TechnicalIndicatorBlock,
  [BlockType.VOLUME_INDICATOR]: TechnicalIndicatorBlock,
  [BlockType.VOLATILITY_INDICATOR]: TechnicalIndicatorBlock,

  // Market Analysis Blocks
  [BlockType.SUPPORT_RESISTANCE]: MarketAnalysisBlock,
  [BlockType.TREND_LINE_ANALYSIS]: MarketAnalysisBlock,
  [BlockType.MARKET_STRUCTURE]: MarketAnalysisBlock,

  // Signal Generation Blocks
  [BlockType.PRICE_ACTION_SIGNAL]: SignalGenerationBlock,
  [BlockType.MULTI_TIMEFRAME_ANALYSIS]: SignalGenerationBlock,
  [BlockType.DIVERGENCE_DETECTION]: SignalGenerationBlock,
  [BlockType.VOLUME_CONFIRMATION]: SignalGenerationBlock,
  [BlockType.MARKET_REGIME]: SignalGenerationBlock,

  // Logic & Flow Control Blocks
  [BlockType.NOT_OPERATOR]: LogicFlowBlock,
  [BlockType.TIME_FILTER]: LogicFlowBlock,
  [BlockType.MARKET_CONDITION_FILTER]: LogicFlowBlock,

  // Logic Blocks
  [BlockType.AND]: AndBlock,
  [BlockType.OR]: OrBlock,

  // Legacy blocks - Use GenericNode for backward compatibility
  [BlockType.RISK_LIMIT]: GenericNode,
  [BlockType.MARKET_BREADTH]: GenericNode,
  [BlockType.MULTI_TIMEFRAME]: GenericNode,
  [BlockType.CONFLUENCE]: GenericNode,
  [BlockType.SCALE_STRATEGY]: GenericNode,
  [BlockType.PARTIAL_PROFIT]: GenericNode
};

interface BuildCanvasProps {
  blocks: AgentBlock[];
  connections: any[];
  entryBlockId: string;
  onBlockUpdate: (id: string, properties: Partial<AgentBlock>) => void;
  onBlockRemove: (id: string) => void;
  onConnectionAdd: (connection: any) => boolean;
  onConnectionRemove: (connection: any) => void;
  onSetEntryBlock: (id: string) => boolean;
  addBlock: (type: string, position: { x: number; y: number }) => string;
  errorDetails?: Record<string, string[]>;
  disconnectedBlocks?: AgentBlock[];
  onFullscreenToggle?: () => void;
  isFullscreen?: boolean;
}

const BuildCanvas: React.FC<BuildCanvasProps> = ({
  blocks,
  connections,
  entryBlockId,
  onBlockUpdate,
  onBlockRemove,
  onConnectionAdd,
  onConnectionRemove,
  onSetEntryBlock,
  addBlock,
  errorDetails = {},
  disconnectedBlocks = [],
  onFullscreenToggle,
  isFullscreen = false
}) => {
  const reactFlowWrapper = useRef<HTMLDivElement>(null);

  // Convert blocks to ReactFlow nodes
  const initialNodes: Node[] = blocks.map((block, index) => {
    const hasError = !!errorDetails[block.id];
    const isDisconnected = disconnectedBlocks.some(b => b.id === block.id);

    // Ensure position is valid with better default spacing
    const position = block.position || { x: 0, y: 0 };
    const validPosition = {
      x: typeof position.x === 'number' && !isNaN(position.x) ? position.x : 150 + (index * 200),
      y: typeof position.y === 'number' && !isNaN(position.y) ? position.y : 150 + (index * 150)
    };

    return {
      id: block.id,
      type: block.type,
      position: validPosition,
      data: {
        ...block,
        isEntryBlock: block.id === entryBlockId,
        onUpdate: (properties: Partial<AgentBlock>) => onBlockUpdate(block.id, properties),
        onRemove: () => onBlockRemove(block.id),
        onSetAsEntry: () => onSetEntryBlock(block.id),
        hasError,
        isDisconnected,
        errorMessages: errorDetails[block.id] || []
      },
      className: hasError ? 'error-node' : ''
    };
  });

  // Convert connections to ReactFlow edges
  const initialEdges: Edge[] = connections.map((conn, index) => ({
    id: `e-${index}`,
    source: conn.sourceId,
    target: conn.targetId,
    sourceHandle: conn.sourceHandle,
    targetHandle: conn.targetHandle,
    type: 'default',
    animated: true
  }));

  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [isDragging, setIsDragging] = useState<boolean>(false);

  // Store the ReactFlow instance
  const [reactFlowInstance, setReactFlowInstance] = React.useState<any>(null);



  // Update nodes when blocks change - more responsive updates
  React.useEffect(() => {
    // Allow updates during dragging for more responsive UI

    // Save current viewport before updating nodes
    let currentViewport = { x: 0, y: 0, zoom: 1 };
    if (reactFlowInstance) {
      currentViewport = reactFlowInstance.getViewport();
    }

    // Only update if blocks have changed
    const updatedNodes = blocks.map(block => {
      const hasError = !!errorDetails[block.id];
      const isDisconnected = disconnectedBlocks.some(b => b.id === block.id);

      // Find the existing node to preserve its position
      const existingNode = nodes.find(node => node.id === block.id);

      return {
        id: block.id,
        type: block.type,
        // Preserve existing node position if available
        position: existingNode?.position || block.position,
        data: {
          ...block,
          isEntryBlock: block.id === entryBlockId,
          onUpdate: (properties: Partial<AgentBlock>) => onBlockUpdate(block.id, properties),
          onRemove: () => onBlockRemove(block.id),
          onSetAsEntry: () => onSetEntryBlock(block.id),
          hasError,
          isDisconnected,
          errorMessages: errorDetails[block.id] || []
        },
        className: hasError ? 'error-node' : ''
      };
    });

    // Only update if there are blocks to show
    if (updatedNodes.length > 0) {
      setNodes(updatedNodes);

      // Restore viewport immediately
      if (reactFlowInstance) {
        reactFlowInstance.setViewport(currentViewport);
      }
    }
  }, [blocks, entryBlockId, onBlockUpdate, onBlockRemove, onSetEntryBlock, errorDetails, disconnectedBlocks, reactFlowInstance]);





  // Handle window resize without auto-rescaling
  React.useEffect(() => {
    // Only add the event listener if reactFlowInstance is initialized
    if (!reactFlowInstance) return;

    const handleResize = () => {
      // Instead of fitView, just update the flow to ensure it renders correctly
      const currentViewport = reactFlowInstance.getViewport();
      reactFlowInstance.setViewport(currentViewport);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [reactFlowInstance]);



  // Update edges when connections change - improved synchronization
  React.useEffect(() => {
    // Create edges with consistent IDs to avoid duplication
    const updatedEdges = connections.map((conn, index) => ({
      id: `e-${conn.sourceId}-${conn.targetId}-${conn.sourceHandle}-${conn.targetHandle}`,
      source: conn.sourceId,
      target: conn.targetId,
      sourceHandle: conn.sourceHandle,
      targetHandle: conn.targetHandle,
      type: 'smoothstep',
      animated: true,
      style: {
        strokeWidth: 2,
        stroke: 'rgba(255, 255, 255, 0.25)',
        strokeLinecap: 'round'
      }
      // No markerEnd - clean lines without arrows
    }));

    // Update edges state
    setEdges(updatedEdges);
  }, [connections, setEdges]);

  // Handle node changes (position, etc.)
  const handleNodesChange = useCallback(
    (changes: NodeChange[]) => {
      // Apply changes to the nodes state immediately
      onNodesChange(changes);

      // Track dragging state and update block positions
      changes.forEach(change => {
        if (change.type === 'position') {
          // Update dragging state
          if (change.dragging !== undefined) {
            setIsDragging(change.dragging);
          }

          // Update block position immediately during and after dragging for responsive feedback
          if (change.position) {
            onBlockUpdate(change.id, { position: change.position });
          }
        }
      });
    },
    [onNodesChange, onBlockUpdate]
  );

  // Handle edge changes - simplified to fix connection issues
  const handleEdgesChange = useCallback(
    (changes: EdgeChange[]) => {
      // Save current viewport before making changes
      let currentViewport = { x: 0, y: 0, zoom: 1 };
      if (reactFlowInstance) {
        currentViewport = reactFlowInstance.getViewport();
      }

      // Apply changes to the edges state
      onEdgesChange(changes);

      // Handle edge removal
      changes.forEach(change => {
        if (change.type === 'remove') {
          // Find the edge that was removed
          const edge = edges.find(e => e.id === change.id);
          if (edge) {
            // Remove the connection from our state
            onConnectionRemove({
              sourceId: edge.source,
              targetId: edge.target,
              sourceHandle: edge.sourceHandle || 'output',
              targetHandle: edge.targetHandle || 'input'
            });
          }
        }
      });

      // Restore viewport immediately
      if (reactFlowInstance) {
        reactFlowInstance.setViewport(currentViewport);
      }
    },
    [edges, onEdgesChange, onConnectionRemove, reactFlowInstance]
  );

  // Enhanced validation with compatibility matrix
  const isValidConnection = useCallback(
    (connection: Connection) => {
      try {
        console.log('🔍 Enhanced validation:', connection);

        // Basic checks
        if (!connection.source || !connection.target) {
          console.log('❌ REJECTED: Missing source or target');
          return false;
        }

        if (connection.source === connection.target) {
          console.log('❌ REJECTED: Self-connection not allowed');
          return false;
        }

        // Find source and target blocks
        const sourceBlock = blocks.find(b => b.id === connection.source);
        const targetBlock = blocks.find(b => b.id === connection.target);

        if (!sourceBlock || !targetBlock) {
          console.log('❌ REJECTED: Source or target block not found');
          return false;
        }

        // Check if connection already exists
        const connectionExists = connections.some(
          conn =>
            conn.sourceId === connection.source &&
            conn.targetId === connection.target &&
            conn.sourceHandle === (connection.sourceHandle || 'output') &&
            conn.targetHandle === (connection.targetHandle || 'input')
        );

        if (connectionExists) {
          console.log('❌ REJECTED: Connection already exists');
          return false;
        }

        // INTELLIGENT: Only prevent multiple output connections for specific block types that should have single outputs
        // Blocks like TRIGGER should only have one output, but data blocks (PRICE, MOVING_AVERAGE, etc.) can have multiple
        const sourceBlockType = sourceBlock.type;
        const singleOutputBlockTypes = ['TRIGGER', 'CONDITION', 'COMPARISON']; // These blocks should only connect to one target

        if (singleOutputBlockTypes.includes(sourceBlockType)) {
          const existingOutputConnections = connections.filter(
            conn => conn.sourceId === connection.source &&
                    conn.sourceHandle === (connection.sourceHandle || 'output')
          );
          if (existingOutputConnections.length > 0) {
            console.log(`❌ REJECTED: ${sourceBlockType} blocks can only have one output connection`);
            return false;
          }
        }

        // Check block type compatibility using the compatibility matrix
        const BLOCK_COMPATIBILITY = {
          'WHEN_RUN': ['PRICE', 'FUNDAMENTAL', 'INDICATOR', 'MOVING_AVERAGE', 'MOMENTUM_INDICATOR', 'TREND_INDICATOR', 'VOLUME_INDICATOR', 'VOLATILITY_INDICATOR'],
          'PRICE': ['CONDITION', 'COMPARISON', 'CANDLE_PATTERN', 'CHART_PATTERN', 'BREAKOUT_DETECTION', 'TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'AND', 'OR'],
          'MOVING_AVERAGE': ['CONDITION', 'COMPARISON', 'CANDLE_PATTERN', 'CHART_PATTERN', 'BREAKOUT_DETECTION', 'TRIGGER', 'SIGNAL', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'AND', 'OR'],
          'MOMENTUM_INDICATOR': ['CONDITION', 'COMPARISON', 'CANDLE_PATTERN', 'CHART_PATTERN', 'BREAKOUT_DETECTION', 'TRIGGER', 'SIGNAL', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'AND', 'OR'],
          'TREND_INDICATOR': ['CONDITION', 'COMPARISON', 'CANDLE_PATTERN', 'CHART_PATTERN', 'BREAKOUT_DETECTION', 'TRIGGER', 'SIGNAL', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'AND', 'OR'],
          'VOLUME_INDICATOR': ['CONDITION', 'COMPARISON', 'CANDLE_PATTERN', 'CHART_PATTERN', 'BREAKOUT_DETECTION', 'TRIGGER', 'SIGNAL', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'AND', 'OR'],
          'VOLATILITY_INDICATOR': ['CONDITION', 'COMPARISON', 'CANDLE_PATTERN', 'CHART_PATTERN', 'BREAKOUT_DETECTION', 'TRIGGER', 'SIGNAL', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'AND', 'OR'],
          'FUNDAMENTAL': ['CONDITION', 'COMPARISON', 'BREAKOUT_DETECTION', 'TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'AND', 'OR'],
          'INDICATOR': ['CONDITION', 'COMPARISON', 'CANDLE_PATTERN', 'CHART_PATTERN', 'BREAKOUT_DETECTION', 'TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'AND', 'OR'],
          'COMPARISON': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
          'CONDITION': ['AND', 'OR', 'TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST'],
          'BREAKOUT_DETECTION': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
          'TRIGGER': [], // Terminal blocks - cannot connect to anything
          'BULLISH_CONFIDENCE_BOOST': ['TRIGGER', 'BREAKOUT_DETECTION', 'CANDLE_PATTERN', 'CHART_PATTERN', 'CONDITION', 'COMPARISON', 'AND', 'OR'],
          'BEARISH_CONFIDENCE_BOOST': ['TRIGGER', 'BREAKOUT_DETECTION', 'CANDLE_PATTERN', 'CHART_PATTERN', 'CONDITION', 'COMPARISON', 'AND', 'OR'],
          'CONFIDENCE_BOOST': ['TRIGGER', 'BREAKOUT_DETECTION', 'CANDLE_PATTERN', 'CHART_PATTERN', 'CONDITION', 'COMPARISON', 'AND', 'OR'],
          // Pattern and Analysis blocks
          'CANDLE_PATTERN': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
          'CHART_PATTERN': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
          'STOCK_SENTIMENT': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
          'GAP_ANALYSIS': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
          // Market Analysis blocks
          'SUPPORT_RESISTANCE': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
          'TREND_LINE_ANALYSIS': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
          'MARKET_STRUCTURE': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
          // Signal Generation blocks
          'PRICE_ACTION_SIGNAL': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
          'MULTI_TIMEFRAME_ANALYSIS': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
          'DIVERGENCE_DETECTION': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
          'VOLUME_CONFIRMATION': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
          'MARKET_REGIME': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
          // Logic & Flow Control blocks
          'NOT_OPERATOR': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
          'TIME_FILTER': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
          'MARKET_CONDITION_FILTER': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
          // Logic blocks
          'AND': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
          'OR': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR']
        };

        const sourceType = sourceBlock.type;
        const targetType = targetBlock.type;
        const allowedTargets = BLOCK_COMPATIBILITY[sourceType];

        // If source type is not in compatibility matrix, reject the connection
        if (!allowedTargets) {
          console.log(`❌ REJECTED: Unknown source block type: ${sourceType}`);
          return false;
        }

        if (!allowedTargets.includes(targetType)) {
          console.log(`❌ REJECTED: ${sourceType} cannot connect to ${targetType}`);
          return false;
        }

        // Prevent illogical condition block connections
        if (sourceBlock.type === 'CONDITION') {
          // Check if both TRUE and FALSE outputs are trying to connect to the same target
          const existingConnections = connections.filter(conn =>
            conn.sourceId === connection.source &&
            conn.targetId === connection.target
          );

          // If there's already a connection from this condition block to the same target
          if (existingConnections.length > 0) {
            const existingHandle = existingConnections[0].sourceHandle;
            const newHandle = connection.sourceHandle;

            // Prevent connecting both TRUE and FALSE to the same target
            if ((existingHandle === 'true' && newHandle === 'false') ||
                (existingHandle === 'false' && newHandle === 'true')) {
              console.log('❌ REJECTED: Cannot connect both TRUE and FALSE outputs to the same target');
              return false;
            }
          }
        }

        console.log(`✅ CONNECTION APPROVED: ${sourceType} → ${targetType}`);
        return true;
      } catch (error) {
        console.error('Error in validation:', error);
        return false;
      }
    },
    [connections, blocks]
  );

  // Handle connection start - provide visual feedback and better logging
  const handleConnectStart = useCallback(
    (event: React.MouseEvent | React.TouchEvent, { nodeId, handleId, handleType }: { nodeId: string; handleId: string; handleType: string }) => {
      console.log('Connection started from:', { nodeId, handleId, handleType });

      // Find the source block to validate it exists
      const sourceBlock = blocks.find(b => b.id === nodeId);
      if (!sourceBlock) {
        console.warn('Connection started from unknown block:', nodeId);
        return;
      }

      console.log('Connection started from block type:', sourceBlock.type);

      // Add visual feedback for connection start
      setNodes(nodes =>
        nodes.map(node => ({
          ...node,
          data: {
            ...node.data,
            isConnecting: node.id === nodeId
          }
        }))
      );
    },
    [setNodes, blocks]
  );

  // Handle connection end - clean up visual feedback with better logging
  const handleConnectEnd = useCallback(
    (event: React.MouseEvent | React.TouchEvent) => {
      console.log('Connection ended - cleaning up visual feedback');
      // Remove visual feedback for connection
      setNodes(nodes =>
        nodes.map(node => ({
          ...node,
          data: {
            ...node.data,
            isConnecting: false
          }
        }))
      );
    },
    [setNodes]
  );

  // Factory settings connection handler - simple and reliable
  const handleConnect = useCallback(
    (connection: Connection) => {
      console.log('🏭 Factory handleConnect called with:', connection);

      // Create the connection object
      const connectionObj = {
        sourceId: connection.source,
        targetId: connection.target,
        sourceHandle: connection.sourceHandle || 'output',
        targetHandle: connection.targetHandle || 'input'
      };

      // Add the connection to our state
      const success = onConnectionAdd(connectionObj);

      console.log('🏭 Factory connection result:', success);

      // Clean up any connection state
      setNodes(nodes =>
        nodes.map(node => ({
          ...node,
          data: {
            ...node.data,
            isConnecting: false
          }
        }))
      );
    },
    [onConnectionAdd, setNodes]
  );

  // Handle dropping a new block onto the canvas
  const handleDrop = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect();
      const dragData = event.dataTransfer.getData('application/reactflow');

      if (!dragData || !reactFlowBounds) {
        return;
      }

      // Parse the drag data
      try {
        // Check if the data is JSON or just a string (for backward compatibility)
        let type, properties = {};

        try {
          const parsedData = JSON.parse(dragData);
          type = parsedData.type;
          properties = parsedData.properties || {};
        } catch {
          // If parsing fails, assume it's just the type string (old format)
          type = dragData;
        }

        if (!type) {
          return;
        }

        // Get the position where the block was dropped
        const position = reactFlowInstance.project({
          x: event.clientX - reactFlowBounds.left,
          y: event.clientY - reactFlowBounds.top
        });

        // Ensure the position is valid
        if (isNaN(position.x) || isNaN(position.y)) {
          position.x = 100;
          position.y = 100;
        }

        // Add the new block with properties
        addBlock(type, position, properties);
      } catch (error) {
        console.error('Error handling block drop:', error);
      }
    },
    [addBlock, reactFlowInstance]
  );

  const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);


  // Fit view to see all nodes
  const handleFitView = useCallback(() => {
    if (reactFlowInstance) {
      reactFlowInstance.fitView({ padding: 0.2 });
    }
  }, [reactFlowInstance]);

  // Zoom in while preserving viewport center
  const handleZoomIn = useCallback(() => {
    if (reactFlowInstance) {
      requestAnimationFrame(() => {
        // Get current viewport
        const { x, y, zoom } = reactFlowInstance.getViewport();
        // Calculate new zoom level
        const newZoom = Math.min(zoom * 1.2, 1.5); // Increase zoom by 20%, max 1.5
        // Set viewport with new zoom but same position
        reactFlowInstance.setViewport({ x, y, zoom: newZoom }, { duration: 0 });
      });
    }
  }, [reactFlowInstance]);

  // Zoom out while preserving viewport center
  const handleZoomOut = useCallback(() => {
    if (reactFlowInstance) {
      requestAnimationFrame(() => {
        // Get current viewport
        const { x, y, zoom } = reactFlowInstance.getViewport();
        // Calculate new zoom level
        const newZoom = Math.max(zoom * 0.8, 0.2); // Decrease zoom by 20%, min 0.2
        // Set viewport with new zoom but same position
        reactFlowInstance.setViewport({ x, y, zoom: newZoom }, { duration: 0 });
      });
    }
  }, [reactFlowInstance]);

  // Initialize ReactFlow when component mounts
  React.useEffect(() => {
    // Force a re-render after a short delay to ensure ReactFlow is properly initialized
    const timer = setTimeout(() => {
      if (nodes.length > 0) {
        setNodes(nodes => [...nodes]);
      }
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="h-full w-full relative" ref={reactFlowWrapper} style={{ minHeight: '500px', minWidth: '100%' }}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={handleNodesChange}
        onEdgesChange={handleEdgesChange}
        onConnect={handleConnect}
        onConnectStart={handleConnectStart}
        onConnectEnd={handleConnectEnd}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onInit={setReactFlowInstance}
        nodeTypes={nodeTypes}
        isValidConnection={isValidConnection}
        connectionLineType={ConnectionLineType.SmoothStep}
        connectionLineStyle={{
          strokeWidth: 2,
          stroke: 'rgba(16, 185, 129, 0.6)',
          strokeLinecap: 'round',
          strokeDasharray: '5 5',
          filter: 'drop-shadow(0 1px 2px rgba(16, 185, 129, 0.3))'
        }}
        connectOnClick={false}
        deleteKeyCode={['Backspace', 'Delete']}
        multiSelectionKeyCode={['Control', 'Meta']}
        selectionKeyCode={['Shift']}
        defaultViewport={{ x: 0, y: 0, zoom: 1 }}
        snapToGrid={true}
        snapGrid={[20, 20]}
        fitViewOptions={{ padding: 0.5 }}
        minZoom={0.2}
        maxZoom={1.5}
        connectionMode="loose"
        proOptions={{ hideAttribution: true }}
      >
        <Background />
        {/* Canvas Controls - Hidden in fullscreen */}
        {!isFullscreen && (
          <Panel position="top-right" className="flex gap-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" onClick={handleZoomIn}>
                    <ZoomIn className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Zoom In</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" onClick={handleZoomOut}>
                    <ZoomOut className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Zoom Out</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" onClick={onFullscreenToggle || handleFitView}>
                    <Maximize className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{onFullscreenToggle ? 'Enter Fullscreen' : 'Fit View'}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </Panel>
        )}

        {/* Fullscreen Exit Button */}
        {isFullscreen && (
          <Panel position="top-left" className="flex gap-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" onClick={onFullscreenToggle}>
                    <X className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Exit Fullscreen</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </Panel>
        )}



      </ReactFlow>
    </div>
  );
};

export default BuildCanvas;
