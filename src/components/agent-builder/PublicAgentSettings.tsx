import React, { useState } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { 
  Globe, 
  Plus, 
  X, 
  Tag,
  Eye,
  EyeOff,
  Info
} from 'lucide-react';

interface PublicAgentSettingsProps {
  isPublic: boolean;
  publicDescription: string;
  tags: string[];
  onIsPublicChange: (isPublic: boolean) => void;
  onPublicDescriptionChange: (description: string) => void;
  onTagsChange: (tags: string[]) => void;
}

const PublicAgentSettings: React.FC<PublicAgentSettingsProps> = ({
  isPublic,
  publicDescription,
  tags,
  onIsPublicChange,
  onPublicDescriptionChange,
  onTagsChange
}) => {
  const [newTag, setNewTag] = useState('');

  // Add a new tag
  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim()) && tags.length < 5) {
      onTagsChange([...tags, newTag.trim()]);
      setNewTag('');
    }
  };

  // Remove a tag
  const handleRemoveTag = (tagToRemove: string) => {
    onTagsChange(tags.filter(tag => tag !== tagToRemove));
  };

  // Handle Enter key for adding tags
  const handleTagKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  return (
    <Card className="bg-[#141414]/40 border border-[#1A1A1A]/20 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)] backdrop-blur-sm">
      <CardHeader className="pb-3">
        <div className="flex items-center gap-2">
          <Globe className="h-4 w-4 text-white/60" />
          <CardTitle className="text-white/90 text-sm font-medium">Public Settings</CardTitle>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Public Toggle */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Label htmlFor="public-toggle" className="text-sm text-white/70">
              Make Public
            </Label>
            <div className="group relative">
              <Info className="h-3 w-3 text-white/40 cursor-help" />
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-[#1A1A1A] text-white/80 text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                Share your agent with the community
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {isPublic ? (
              <Eye className="h-3 w-3 text-green-400" />
            ) : (
              <EyeOff className="h-3 w-3 text-white/40" />
            )}
            <Switch
              id="public-toggle"
              checked={isPublic}
              onCheckedChange={onIsPublicChange}
              className="data-[state=checked]:bg-green-600"
            />
          </div>
        </div>

        {/* Public Description */}
        {isPublic && (
          <>
            <div className="space-y-2">
              <Label htmlFor="public-description" className="text-sm text-white/70">
                Public Description
              </Label>
              <Textarea
                id="public-description"
                value={publicDescription}
                onChange={(e) => onPublicDescriptionChange(e.target.value)}
                placeholder="Describe what your agent does for the community..."
                className="bg-[#0A0A0A]/50 border-[#1A1A1A]/40 text-white placeholder:text-white/30 focus:border-[#232323]/60 focus:ring-0 min-h-[80px] text-sm"
                maxLength={200}
              />
              <div className="text-xs text-white/40 text-right">
                {publicDescription.length}/200
              </div>
            </div>

            {/* Tags */}
            <div className="space-y-2">
              <Label className="text-sm text-white/70">
                Tags (max 5)
              </Label>
              
              {/* Existing Tags */}
              {tags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {tags.map((tag, index) => (
                    <Badge
                      key={index}
                      variant="secondary"
                      className="bg-white/5 text-white/70 text-xs flex items-center gap-1 pr-1"
                    >
                      <Tag className="h-2 w-2" />
                      {tag}
                      <button
                        onClick={() => handleRemoveTag(tag)}
                        className="ml-1 hover:text-red-400 transition-colors"
                      >
                        <X className="h-2 w-2" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}

              {/* Add New Tag */}
              {tags.length < 5 && (
                <div className="flex gap-2">
                  <Input
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    onKeyPress={handleTagKeyPress}
                    placeholder="Add a tag..."
                    className="bg-[#0A0A0A]/50 border-[#1A1A1A]/40 text-white placeholder:text-white/30 focus:border-[#232323]/60 focus:ring-0 text-sm flex-1"
                    maxLength={20}
                  />
                  <Button
                    onClick={handleAddTag}
                    disabled={!newTag.trim() || tags.includes(newTag.trim())}
                    size="sm"
                    className="bg-white/10 hover:bg-white/20 text-white border border-white/20 px-2"
                  >
                    <Plus className="h-3 w-3" />
                  </Button>
                </div>
              )}

              {/* Tag Suggestions */}
              <div className="text-xs text-white/40">
                Suggested: momentum, scalping, swing, breakout, reversal
              </div>
            </div>

            {/* Info */}
            <div className="bg-[#0A0A0A]/30 border border-[#1A1A1A]/20 rounded-lg p-3">
              <div className="flex items-start gap-2">
                <Info className="h-3 w-3 text-blue-400 mt-0.5 flex-shrink-0" />
                <div className="text-xs text-white/60">
                  <p className="font-medium text-white/70 mb-1">Public Agent Guidelines:</p>
                  <ul className="space-y-0.5 text-white/50">
                    <li>• Your agent will be visible in the Discovery page</li>
                    <li>• Others can copy and customize your agent</li>
                    <li>• You'll get credit as the original creator</li>
                    <li>• Use clear descriptions and relevant tags</li>
                  </ul>
                </div>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default PublicAgentSettings;
