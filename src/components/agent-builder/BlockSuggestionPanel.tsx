import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Lightbulb, Plus, Zap, X } from 'lucide-react';
import { BlockSuggestion } from '@/utils/blockSuggestions';
import { BlockType } from '@/hooks/useAgentBuilder';

interface BlockSuggestionPanelProps {
  suggestions: BlockSuggestion[];
  onAddBlock: (blockType: BlockType, parameters?: Record<string, any>) => void;
  onClose: () => void;
  isVisible: boolean;
  selectedBlockId?: string;
}

const BlockSuggestionPanel: React.FC<BlockSuggestionPanelProps> = ({
  suggestions,
  onAddBlock,
  onClose,
  isVisible,
  selectedBlockId
}) => {
  if (!isVisible || suggestions.length === 0) return null;

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-green-600';
    if (confidence >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getCategoryIcon = (category: string) => {
    const icons = {
      ENTRY: '🚀',
      DATA_SOURCE: '📊',
      ANALYSIS: '🔍',
      SIGNAL_GENERATION: '⚡',
      LOGIC: '🧠',
      RISK_ANALYSIS: '🛡️',
      QUALITY_FILTER: '✅',
      OUTPUT: '🎯',
      MISC: '📦'
    };
    return icons[category as keyof typeof icons] || '📦';
  };

  return (
    <Card className="absolute bottom-4 right-4 z-20 w-96 max-h-96 overflow-hidden bg-white/95 backdrop-blur-sm border shadow-lg">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Lightbulb className="h-4 w-4 text-yellow-500" />
            <CardTitle className="text-sm font-medium">Smart Suggestions</CardTitle>
            <Badge variant="secondary" className="text-xs">
              {suggestions.length}
            </Badge>
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6"
            onClick={onClose}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
        {selectedBlockId && (
          <p className="text-xs text-gray-600">
            Suggestions for improving your agent flow
          </p>
        )}
      </CardHeader>
      
      <CardContent className="p-3 pt-0 overflow-y-auto max-h-80">
        <div className="space-y-2">
          {suggestions.map((suggestion, index) => (
            <div
              key={`${suggestion.blockType}-${index}`}
              className="border rounded-lg p-3 hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-start justify-between gap-2">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-sm">
                      {getCategoryIcon(suggestion.category)}
                    </span>
                    <h4 className="text-sm font-medium truncate">
                      {suggestion.title}
                    </h4>
                    <Badge 
                      variant="outline" 
                      className={`text-xs px-1 py-0 ${getPriorityColor(suggestion.priority)}`}
                    >
                      {suggestion.priority}
                    </Badge>
                  </div>
                  
                  <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                    {suggestion.description}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-gray-500">
                        Confidence:
                      </span>
                      <span className={`text-xs font-medium ${getConfidenceColor(suggestion.confidence)}`}>
                        {suggestion.confidence}%
                      </span>
                    </div>
                    
                    <Button
                      size="sm"
                      className="h-6 px-2 text-xs"
                      onClick={() => onAddBlock(suggestion.blockType, suggestion.suggestedParameters)}
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      Add
                    </Button>
                  </div>
                </div>
              </div>
              
              {suggestion.reasoning && (
                <div className="mt-2 pt-2 border-t border-gray-100">
                  <p className="text-xs text-gray-500 italic">
                    💡 {suggestion.reasoning}
                  </p>
                </div>
              )}
            </div>
          ))}
        </div>
        
        {suggestions.length === 0 && (
          <div className="text-center text-gray-500 text-xs py-4">
            <Lightbulb className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No suggestions available</p>
            <p className="text-xs mt-1">
              Add more blocks to get intelligent recommendations
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default BlockSuggestionPanel;
