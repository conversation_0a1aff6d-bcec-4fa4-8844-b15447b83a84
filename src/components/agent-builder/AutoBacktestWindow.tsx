import React, { useState } from 'react';
import { ChevronUp, ChevronDown, TrendingUp, Target, BarChart3, Loader2, X } from 'lucide-react';

interface BacktestResult {
  symbol: string;
  startDate: string;
  endDate: string;
  totalReturn: number;
  buyAndHoldReturn: number;
  numberOfTrades: number;
  winRate: number;
  maxDrawdown: number;
  sharpeRatio: number;
  trades: Array<{
    date: string;
    type: 'buy' | 'sell';
    price: number;
    signal: string;
    confidence: number;
  }>;
  performanceChart: Array<{
    date: string;
    agentValue: number;
    buyHoldValue: number;
  }>;
}

interface AutoBacktestWindowProps {
  isRunning: boolean;
  result: BacktestResult | null;
  error: string | null;
  onClose?: () => void;
}

const AutoBacktestWindow: React.FC<AutoBacktestWindowProps> = ({
  isRunning,
  result,
  error,
  onClose
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [isVisible, setIsVisible] = useState(true);

  // Only show the window if there's something to display
  if (!isVisible || (!isRunning && !result && !error)) return null;

  const handleClose = () => {
    setIsVisible(false);
    onClose?.();
  };

  return (
    <div className="fixed bottom-4 right-4 z-50 w-80 bg-[#0A0A0A] border border-[#1A1A1A]/50 rounded-lg shadow-2xl">
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-[#1A1A1A]/30">
        <div className="flex items-center gap-2">
          <BarChart3 className="h-4 w-4 text-white/70" />
          <span className="text-sm font-medium text-white/90">Auto Backtest</span>
          {isRunning && <Loader2 className="h-3 w-3 animate-spin text-blue-400" />}
        </div>
        <div className="flex items-center gap-1">
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-1 hover:bg-white/[0.05] rounded transition-colors"
          >
            {isExpanded ? (
              <ChevronDown className="h-3 w-3 text-white/60" />
            ) : (
              <ChevronUp className="h-3 w-3 text-white/60" />
            )}
          </button>
          <button
            onClick={handleClose}
            className="p-1 hover:bg-white/[0.05] rounded transition-colors"
          >
            <X className="h-3 w-3 text-white/60" />
          </button>
        </div>
      </div>

      {/* Content */}
      {isExpanded && (
        <div className="p-3">
          {isRunning && (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <Loader2 className="h-6 w-6 animate-spin text-blue-400 mx-auto mb-2" />
                <p className="text-xs text-white/60">Running backtest on SPY...</p>
                <p className="text-xs text-white/40 mt-1">This may take a few moments</p>
              </div>
            </div>
          )}

          {error && (
            <div className="py-4">
              <div className="text-center">
                <div className="text-red-400 text-xs mb-1">Backtest Failed</div>
                <p className="text-xs text-white/60">
                  {error.includes('Agent not found')
                    ? 'Please save your agent first before running backtest'
                    : error.includes('Invalid agent configuration')
                    ? 'Agent configuration is invalid. Please check your blocks and connections.'
                    : error.includes('Unsupported timeframe')
                    ? 'Timeframe configuration error. Please try again.'
                    : error.includes('Edge Function returned a non-2xx status code')
                    ? 'Backtest service temporarily unavailable. Please try again.'
                    : error}
                </p>
              </div>
            </div>
          )}

          {result && !isRunning && (
            <div className="space-y-3">
              {/* Key Metrics */}
              <div className="grid grid-cols-2 gap-2">
                <div className="bg-[#141414]/60 rounded-md p-2">
                  <div className="flex items-center gap-1 mb-1">
                    <TrendingUp className="h-3 w-3 text-green-400" />
                    <span className="text-xs text-white/60">Total Return</span>
                  </div>
                  <div className={`text-sm font-medium ${result.totalReturn >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                    {result.totalReturn >= 0 ? '+' : ''}{result.totalReturn.toFixed(2)}%
                  </div>
                </div>

                <div className="bg-[#141414]/60 rounded-md p-2">
                  <div className="flex items-center gap-1 mb-1">
                    <Target className="h-3 w-3 text-blue-400" />
                    <span className="text-xs text-white/60">Win Rate</span>
                  </div>
                  <div className="text-sm font-medium text-white/90">
                    {result.winRate.toFixed(1)}%
                  </div>
                </div>

                <div className="bg-[#141414]/60 rounded-md p-2">
                  <div className="flex items-center gap-1 mb-1">
                    <BarChart3 className="h-3 w-3 text-purple-400" />
                    <span className="text-xs text-white/60">Trades</span>
                  </div>
                  <div className="text-sm font-medium text-white/90">
                    {result.numberOfTrades}
                  </div>
                </div>

                <div className="bg-[#141414]/60 rounded-md p-2">
                  <div className="flex items-center gap-1 mb-1">
                    <TrendingUp className="h-3 w-3 text-orange-400" />
                    <span className="text-xs text-white/60">Sharpe</span>
                  </div>
                  <div className="text-sm font-medium text-white/90">
                    {result.sharpeRatio.toFixed(2)}
                  </div>
                </div>
              </div>

              {/* Performance vs Buy & Hold */}
              <div className="bg-[#141414]/60 rounded-md p-2">
                <div className="text-xs text-white/60 mb-2">vs Buy & Hold</div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-white/70">Agent:</span>
                  <span className={`text-xs font-medium ${result.totalReturn >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                    {result.totalReturn >= 0 ? '+' : ''}{result.totalReturn.toFixed(2)}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-white/70">Buy & Hold:</span>
                  <span className={`text-xs font-medium ${result.buyAndHoldReturn >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                    {result.buyAndHoldReturn >= 0 ? '+' : ''}{result.buyAndHoldReturn.toFixed(2)}%
                  </span>
                </div>
                <div className="mt-1 pt-1 border-t border-white/[0.06]">
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-white/70">Difference:</span>
                    <span className={`text-xs font-medium ${(result.totalReturn - result.buyAndHoldReturn) >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                      {(result.totalReturn - result.buyAndHoldReturn) >= 0 ? '+' : ''}{(result.totalReturn - result.buyAndHoldReturn).toFixed(2)}%
                    </span>
                  </div>
                </div>
              </div>

              {/* Symbol and Date Range */}
              <div className="text-xs text-white/50 text-center">
                {result.symbol} • {new Date(result.startDate).toLocaleDateString()} - {new Date(result.endDate).toLocaleDateString()}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AutoBacktestWindow;
