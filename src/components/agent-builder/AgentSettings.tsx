import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Settings, 
  Save, 
  X, 
  Eye, 
  EyeOff, 
  Tag, 
  Plus,
  Info,
  Globe,
  Lock
} from 'lucide-react';

interface AgentSettingsProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  agentName: string;
  agentDescription: string;
  isPublic: boolean;
  publicDescription: string;
  tags: string[];
  onSave: (settings: {
    name: string;
    description: string;
    isPublic: boolean;
    publicDescription: string;
    tags: string[];
  }) => void;
}

const AgentSettings: React.FC<AgentSettingsProps> = ({
  open,
  onOpenChange,
  agentName,
  agentDescription,
  isPublic,
  publicDescription,
  tags,
  onSave
}) => {
  const [localName, setLocalName] = useState(agentName);
  const [localDescription, setLocalDescription] = useState(agentDescription);
  const [localIsPublic, setLocalIsPublic] = useState(isPublic);
  const [localPublicDescription, setLocalPublicDescription] = useState(publicDescription);
  const [localTags, setLocalTags] = useState(tags);
  const [newTag, setNewTag] = useState('');

  // Reset local state when dialog opens
  React.useEffect(() => {
    if (open) {
      setLocalName(agentName);
      setLocalDescription(agentDescription);
      setLocalIsPublic(isPublic);
      setLocalPublicDescription(publicDescription);
      setLocalTags(tags);
      setNewTag('');
    }
  }, [open, agentName, agentDescription, isPublic, publicDescription, tags]);

  const handleAddTag = () => {
    if (newTag.trim() && !localTags.includes(newTag.trim()) && localTags.length < 5) {
      setLocalTags([...localTags, newTag.trim()]);
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setLocalTags(localTags.filter(tag => tag !== tagToRemove));
  };

  const handleTagKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  const handleSave = () => {
    onSave({
      name: localName,
      description: localDescription,
      isPublic: localIsPublic,
      publicDescription: localPublicDescription,
      tags: localTags
    });
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md bg-[#0A0A0A] border-white/[0.08] text-white">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-base font-medium">
            <Settings className="w-4 h-4" />
            Agent Settings
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-2">
          {/* Basic Information */}
          <div className="space-y-3">
            <div className="space-y-1">
              <Label htmlFor="agent-name" className="text-xs text-white/60">
                Name
              </Label>
              <Input
                id="agent-name"
                value={localName}
                onChange={(e) => setLocalName(e.target.value)}
                placeholder="Agent name"
                className="bg-white/[0.02] border-white/[0.06] text-white text-sm placeholder:text-white/30 focus:border-white/[0.12] focus:ring-0 h-8"
                maxLength={50}
              />
            </div>

            <div className="space-y-1">
              <Label htmlFor="agent-description" className="text-xs text-white/60">
                Description
              </Label>
              <Textarea
                id="agent-description"
                value={localDescription}
                onChange={(e) => setLocalDescription(e.target.value)}
                placeholder="What does your agent do?"
                className="bg-white/[0.02] border-white/[0.06] text-white text-sm placeholder:text-white/30 focus:border-white/[0.12] focus:ring-0 min-h-[60px] resize-none"
                maxLength={200}
              />
            </div>
          </div>

          {/* Privacy Settings */}
          <div className="space-y-3">
            {/* Public Toggle */}
            <div className="flex items-center justify-between">
              <Label htmlFor="public-toggle" className="text-xs text-white/60">
                Public
              </Label>
              <div className="flex items-center gap-2">
                {localIsPublic ? (
                  <Eye className="h-3 w-3 text-green-400" />
                ) : (
                  <Lock className="h-3 w-3 text-white/40" />
                )}
                <Switch
                  id="public-toggle"
                  checked={localIsPublic}
                  onCheckedChange={setLocalIsPublic}
                  className="data-[state=checked]:bg-green-600 scale-75"
                />
              </div>
            </div>

            {/* Public Description */}
            {localIsPublic && (
              <div className="space-y-1">
                <Label htmlFor="public-description" className="text-xs text-white/60">
                  Public Description
                </Label>
                <Textarea
                  id="public-description"
                  value={localPublicDescription}
                  onChange={(e) => setLocalPublicDescription(e.target.value)}
                  placeholder="Community description"
                  className="bg-white/[0.02] border-white/[0.06] text-white text-sm placeholder:text-white/30 focus:border-white/[0.12] focus:ring-0 min-h-[50px] resize-none"
                  maxLength={200}
                />
              </div>
            )}

            {/* Tags */}
            {localIsPublic && (
              <div className="space-y-1">
                <Label className="text-xs text-white/60">
                  Tags
                </Label>

                {/* Add Tag Input */}
                <div className="flex gap-1">
                  <Input
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    onKeyPress={handleTagKeyPress}
                    placeholder="Add tag"
                    className="bg-white/[0.02] border-white/[0.06] text-white text-sm placeholder:text-white/30 focus:border-white/[0.12] focus:ring-0 h-7"
                    maxLength={20}
                    disabled={localTags.length >= 5}
                  />
                  <Button
                    onClick={handleAddTag}
                    disabled={!newTag.trim() || localTags.includes(newTag.trim()) || localTags.length >= 5}
                    size="sm"
                    className="bg-white/[0.05] hover:bg-white/[0.1] text-white border border-white/[0.08] h-7 px-2 rounded-md"
                  >
                    <Plus className="h-3 w-3" />
                  </Button>
                </div>

                {/* Tags Display */}
                {localTags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-2">
                    {localTags.map((tag, index) => (
                      <Badge
                        key={index}
                        variant="outline"
                        className="text-xs border-white/[0.12] text-white/70 bg-white/[0.02] flex items-center gap-1 h-6 px-2 rounded-md"
                      >
                        {tag}
                        <button
                          onClick={() => handleRemoveTag(tag)}
                          className="hover:text-red-400 transition-colors"
                        >
                          <X className="h-2 w-2" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-2 pt-3 border-t border-white/[0.06]">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="bg-white/[0.03] hover:bg-white/[0.06] text-white/80 border-white/[0.08] h-8 px-3 text-sm"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            className="bg-white text-black hover:bg-white/90 h-8 px-3 text-sm"
          >
            <Save className="w-3 h-3 mr-1" />
            Save
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AgentSettings;
