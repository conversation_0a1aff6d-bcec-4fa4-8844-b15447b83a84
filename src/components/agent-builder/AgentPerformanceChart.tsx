import React, { useState, useRef, useCallback } from 'react';
import ReactECharts from 'echarts-for-react';

interface ChartDataPoint {
  date: string;
  value: number;
}

interface AgentPerformanceChartProps {
  data: ChartDataPoint[];
  height?: number;
  showTrackingDot?: boolean;
  title?: string;
}

const AgentPerformanceChart: React.FC<AgentPerformanceChartProps> = ({
  data,
  height = 200,
  showTrackingDot = true,
  title = 'Agent Performance'
}) => {
  const chartRef = useRef<any>(null);
  const [trackingDot, setTrackingDot] = useState<{
    visible: boolean;
    x: number;
    y: number;
    value: number;
    date: string;
    isPositive: boolean;
  }>({
    visible: false,
    x: 0,
    y: 0,
    value: 0,
    date: '',
    isPositive: true
  });

  if (!data || data.length === 0) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-[#0A0A0A] rounded-lg border border-white/[0.06]">
        <div className="text-white/40 text-sm">No performance data available</div>
      </div>
    );
  }

  // Calculate performance metrics
  const values = data.map(item => item.value);
  const dates = data.map(item => item.date);
  const startValue = values[0];
  const currentValue = values[values.length - 1];
  const percentChange = ((currentValue - startValue) / startValue) * 100;
  const isPositive = percentChange >= 0;

  // Calculate min and max values for y-axis scaling
  const minValue = Math.min(...values) * 0.95;
  const maxValue = Math.max(...values) * 1.05;

  // Handle mouse events for tracking dot
  const handleChartReady = useCallback((chartInstance: any) => {
    chartRef.current = chartInstance;
    
    if (!showTrackingDot) return;

    // Get the chart container
    const chartDom = chartInstance.getDom();
    
    // Mouse move handler
    const handleMouseMove = (event: MouseEvent) => {
      const rect = chartDom.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;
      
      // Convert pixel coordinates to data coordinates
      const pointInPixel = [x, y];
      const pointInGrid = chartInstance.convertFromPixel('grid', pointInPixel);
      
      if (pointInGrid && pointInGrid[0] >= 0 && pointInGrid[0] < data.length) {
        const dataIndex = Math.round(pointInGrid[0]);
        const dataPoint = data[dataIndex];
        
        if (dataPoint) {
          // Convert back to pixel coordinates for precise positioning
          const pixelPoint = chartInstance.convertToPixel('grid', [dataIndex, dataPoint.value]);
          
          setTrackingDot({
            visible: true,
            x: pixelPoint[0],
            y: pixelPoint[1],
            value: dataPoint.value,
            date: dataPoint.date,
            isPositive: isPositive
          });
        }
      }
    };
    
    // Mouse leave handler
    const handleMouseLeave = () => {
      setTrackingDot(prev => ({ ...prev, visible: false }));
    };
    
    // Add event listeners
    chartDom.addEventListener('mousemove', handleMouseMove);
    chartDom.addEventListener('mouseleave', handleMouseLeave);
    
    // Cleanup function
    return () => {
      chartDom.removeEventListener('mousemove', handleMouseMove);
      chartDom.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [data, isPositive, showTrackingDot]);

  const option = {
    backgroundColor: 'transparent',
    grid: {
      left: '3%',
      right: '3%',
      bottom: '3%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: { show: false },
      splitLine: { show: false }
    },
    yAxis: {
      type: 'value',
      min: minValue,
      max: maxValue,
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: { show: false },
      splitLine: { show: false }
    },
    series: [
      {
        type: 'line',
        data: values,
        smooth: true,
        symbol: 'none',
        lineStyle: {
          color: isPositive ? '#10B981' : '#EF4444',
          width: 2,
          shadowColor: isPositive ? 'rgba(16, 185, 129, 0.3)' : 'rgba(239, 68, 68, 0.3)',
          shadowBlur: 4,
          shadowOffsetY: 2
        },
        areaStyle: {
          color: {
            type: 'linear' as const,
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: isPositive ? 'rgba(16, 185, 129, 0.3)' : 'rgba(239, 68, 68, 0.3)'
            }, {
              offset: 0.8,
              color: isPositive ? 'rgba(16, 185, 129, 0.1)' : 'rgba(239, 68, 68, 0.1)'
            }, {
              offset: 1,
              color: isPositive ? 'rgba(16, 185, 129, 0.05)' : 'rgba(239, 68, 68, 0.05)'
            }]
          },
          shadowColor: isPositive ? 'rgba(16, 185, 129, 0.2)' : 'rgba(239, 68, 68, 0.2)',
          shadowBlur: 6,
          shadowOffsetX: 1,
          shadowOffsetY: 1
        }
      }
    ]
  };

  return (
    <div className="w-full h-full relative">
      <ReactECharts
        option={option as any}
        style={{ height: `${height}px`, width: '100%', backgroundColor: 'transparent' }}
        className="w-full"
        onChartReady={handleChartReady}
        opts={{
          renderer: 'canvas',
          width: 'auto',
          height: 'auto',
          devicePixelRatio: window.devicePixelRatio || 1
        }}
      />
      
      {/* Tracking Dot Overlay */}
      {showTrackingDot && trackingDot.visible && (
        <>
          {/* Tracking Dot */}
          <div
            className="absolute pointer-events-none z-10"
            style={{
              left: trackingDot.x - 5,
              top: trackingDot.y - 5,
              transform: 'translate(0, 0)'
            }}
          >
            <div
              className={`w-2.5 h-2.5 rounded-full border border-white shadow-lg ${
                trackingDot.isPositive 
                  ? 'bg-emerald-400 shadow-emerald-400/60' 
                  : 'bg-red-400 shadow-red-400/60'
              }`}
              style={{
                boxShadow: `0 0 8px ${trackingDot.isPositive ? 'rgba(16, 185, 129, 0.8)' : 'rgba(239, 68, 68, 0.8)'}`
              }}
            />
          </div>
          
          {/* Tooltip */}
          <div
            className="absolute pointer-events-none z-20 bg-[#1A1A1A]/90 border border-white/10 rounded-md px-2 py-1 text-xs shadow-lg backdrop-blur-sm"
            style={{
              left: trackingDot.x + 10,
              top: trackingDot.y - 30,
              transform: trackingDot.x > 200 ? 'translateX(-100%)' : 'translateX(0)'
            }}
          >
            <div className="text-white font-medium">
              ${trackingDot.value.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </div>
            <div className="text-white/60 text-xs">
              {new Date(trackingDot.date).toLocaleDateString()}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default AgentPerformanceChart;
