import React from 'react';
import { AlertCircle, Mail, Settings } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';

interface WhopEmailPromptProps {
  title?: string;
  description?: string;
  showSettingsButton?: boolean;
  className?: string;
}

const WhopEmailPrompt: React.FC<WhopEmailPromptProps> = ({
  title = "Email Required for Marketplace",
  description = "To access marketplace features, please add your email address in Settings.",
  showSettingsButton = true,
  className = ""
}) => {
  const navigate = useNavigate();

  const handleGoToSettings = () => {
    navigate('/settings');
  };

  return (
    <Card className={`bg-gradient-to-br from-blue-500/10 to-purple-500/10 border-blue-500/20 ${className}`}>
      <CardContent className="p-6">
        <div className="flex items-start gap-4">
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center">
              <Mail className="w-6 h-6 text-blue-400" />
            </div>
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-white mb-2 flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-blue-400" />
              {title}
            </h3>
            <p className="text-white/70 mb-4">
              {description}
            </p>
            {showSettingsButton && (
              <Button
                onClick={handleGoToSettings}
                className="bg-blue-600 hover:bg-blue-700 text-white border-0 shadow-lg"
              >
                <Settings className="w-4 h-4 mr-2" />
                Go to Settings
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default WhopEmailPrompt;
