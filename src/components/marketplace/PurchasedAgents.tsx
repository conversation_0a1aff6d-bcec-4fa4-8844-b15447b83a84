import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { useNavigate } from 'react-router-dom';
import {
  ShoppingCart,
  Calendar,
  Play,
  DollarSign,
  Shield,
  Key
} from 'lucide-react';
import {
  getPurchasedAgentLicenses,
  executeSecureAgent,
  type AgentLicense
} from '@/services/secureAgentService';
import { formatDistanceToNow } from 'date-fns';

const PurchasedAgents: React.FC = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [agentLicenses, setAgentLicenses] = useState<AgentLicense[]>([]);
  const [loading, setLoading] = useState(true);
  const [executingLicense, setExecutingLicense] = useState<string | null>(null);

  const loadAgentLicenses = async () => {
    setLoading(true);
    try {
      // Add timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Request timeout')), 8000)
      );

      const response = await Promise.race([
        getPurchasedAgentLicenses(),
        timeoutPromise
      ]) as any;

      if (response.success) {
        setAgentLicenses(response.licenses || []);
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: response.error || "Failed to load agent licenses"
        });
      }
    } catch (error) {
      console.error('Error loading agent licenses:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message === 'Request timeout'
          ? "Request timed out. Please try again."
          : "An unexpected error occurred"
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    let isMounted = true;

    const loadDataSafely = async () => {
      if (isMounted) {
        await loadAgentLicenses();
      }
    };

    loadDataSafely();

    return () => {
      isMounted = false;
    };
  }, []); // Empty dependency array is correct here

  // SECURITY: Execute agent securely without exposing configuration
  const handleExecuteAgent = async (license: AgentLicense) => {
    setExecutingLicense(license.license_key);
    try {
      // For demo purposes, execute with AAPL
      const result = await executeSecureAgent(license.license_key, 'AAPL', 'day');

      toast({
        title: "Agent Executed Successfully",
        description: `${result.agent_name}: ${result.signal} (${result.confidence}% confidence)`
      });

      // Refresh licenses to update usage count
      loadAgentLicenses();

    } catch (error) {
      console.error('Error executing agent:', error);
      toast({
        variant: "destructive",
        title: "Execution Failed",
        description: error instanceof Error ? error.message : "Failed to execute agent"
      });
    } finally {
      setExecutingLicense(null);
    }
  };

  // SECURITY: No direct access to agent builder for purchased agents
  const handleViewLicense = (license: AgentLicense) => {
    toast({
      title: "License Information",
      description: `License Key: ${license.license_key.substring(0, 8)}... | Usage: ${license.usage_count}${license.max_usage_limit ? `/${license.max_usage_limit}` : ''}`
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white/20 mx-auto mb-4"></div>
          <p className="text-white/50">Loading purchased agents...</p>
        </div>
      </div>
    );
  }

  if (agentLicenses.length === 0) {
    return (
      <Card className="bg-gradient-to-br from-white/[0.03] to-white/[0.01] border-white/[0.08]">
        <CardContent className="p-8">
          <div className="text-center">
            <ShoppingCart className="w-16 h-16 text-white/20 mx-auto mb-6" />
            <h3 className="text-xl font-medium text-white/60 mb-2">No licensed agents</h3>
            <p className="text-white/40 mb-6">
              Browse the marketplace to discover and purchase AI trading agent licenses
            </p>
            <Button
              onClick={() => navigate('/marketplace')}
              className="bg-white/10 hover:bg-white/20 text-white border border-white/20"
            >
              <ShoppingCart className="w-4 h-4 mr-2" />
              Browse Marketplace
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold text-white">Licensed Agents</h2>
          <p className="text-white/60">
            Agent licenses you've purchased from the marketplace ({agentLicenses.length} total)
          </p>
        </div>
        <Button
          onClick={() => navigate('/marketplace')}
          variant="outline"
          className="border-white/[0.12] text-white/70 hover:text-white hover:bg-white/[0.04]"
        >
          <ShoppingCart className="w-4 h-4 mr-2" />
          Browse More
        </Button>
      </div>

      {/* Licensed Agents Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {agentLicenses.map((license) => (
          <Card
            key={license.id}
            className="bg-gradient-to-br from-white/[0.03] to-white/[0.01] border-white/[0.08] hover:border-white/[0.12] transition-all duration-300 hover:from-white/[0.04] hover:to-white/[0.02] hover:shadow-lg hover:shadow-black/20"
          >
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg text-white line-clamp-1">
                    {license.custom_name || license.agent?.name || 'Licensed Agent'}
                  </CardTitle>
                  <CardDescription className="text-white/60 line-clamp-2 mt-1">
                    {license.agent?.description || 'No description available'}
                  </CardDescription>
                </div>
                <div className="ml-2 flex gap-2">
                  <Badge className="bg-blue-500/20 text-blue-400 border border-blue-500/30">
                    <Key className="w-3 h-3 mr-1" />
                    Licensed
                  </Badge>
                  {license.agent?.is_encrypted && (
                    <Badge className="bg-purple-500/20 text-purple-400 border border-purple-500/30">
                      <Shield className="w-3 h-3 mr-1" />
                      Secure
                    </Badge>
                  )}
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              {/* License Info */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-white/60">License Price:</span>
                  <span className="font-semibold text-green-400">
                    {formatCurrency(license.purchase_price)}
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-white/60">Usage Count:</span>
                  <span className="font-semibold text-blue-400">
                    {license.usage_count}{license.max_usage_limit ? `/${license.max_usage_limit}` : ''}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-xs text-white/40">
                  <Calendar className="w-3 h-3" />
                  <span>Licensed {formatDistanceToNow(new Date(license.created_at), { addSuffix: true })}</span>
                </div>
              </div>

              {/* Tags */}
              {license.agent?.tags && license.agent.tags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {license.agent.tags.slice(0, 3).map((tag: string, index: number) => (
                    <Badge key={index} variant="secondary" className="bg-white/5 text-white/60 text-xs px-2 py-0.5">
                      {tag}
                    </Badge>
                  ))}
                  {license.agent.tags.length > 3 && (
                    <Badge variant="secondary" className="bg-white/5 text-white/60 text-xs px-2 py-0.5">
                      +{license.agent.tags.length - 3}
                    </Badge>
                  )}
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex gap-2 pt-2">
                <Button
                  onClick={() => handleExecuteAgent(license)}
                  disabled={executingLicense === license.license_key}
                  className="flex-1 bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50"
                  size="sm"
                >
                  {executingLicense === license.license_key ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Executing...
                    </>
                  ) : (
                    <>
                      <Play className="w-4 h-4 mr-2" />
                      Execute
                    </>
                  )}
                </Button>
                <Button
                  onClick={() => handleViewLicense(license)}
                  variant="outline"
                  className="border-white/[0.12] text-white/70 hover:text-white hover:bg-white/[0.04]"
                  size="sm"
                >
                  <Key className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Summary Stats */}
      <Card className="bg-gradient-to-br from-white/[0.03] to-white/[0.01] border-white/[0.08]">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-white">License Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 mb-2">
                <Key className="w-5 h-5 text-blue-400" />
                <span className="text-2xl font-bold text-white">{agentLicenses.length}</span>
              </div>
              <p className="text-sm text-white/60">Licenses Owned</p>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 mb-2">
                <DollarSign className="w-5 h-5 text-green-400" />
                <span className="text-2xl font-bold text-white">
                  {formatCurrency(
                    agentLicenses.reduce((sum: number, license: AgentLicense) => sum + license.purchase_price, 0)
                  )}
                </span>
              </div>
              <p className="text-sm text-white/60">Total Spent</p>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 mb-2">
                <Play className="w-5 h-5 text-purple-400" />
                <span className="text-2xl font-bold text-white">
                  {agentLicenses.reduce((sum: number, license: AgentLicense) => sum + license.usage_count, 0)}
                </span>
              </div>
              <p className="text-sm text-white/60">Total Executions</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PurchasedAgents;
