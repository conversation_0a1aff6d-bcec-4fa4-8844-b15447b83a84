import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { ShoppingCart, DollarSign, User, Calendar, Tag, CreditCard } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { type MarketplaceAgent, purchaseAgent } from '@/services/marketplaceService';

interface PurchaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  agent: MarketplaceAgent;
  onSuccess?: () => void;
}

const PurchaseModal: React.FC<PurchaseModalProps> = ({
  isOpen,
  onClose,
  agent,
  onSuccess
}) => {
  const { toast } = useToast();
  const [customName, setCustomName] = useState('');
  const [loading, setLoading] = useState(false);

  // Debug modal rendering
  useEffect(() => {
    console.log('PurchaseModal render state:', { isOpen, agentName: agent?.name });
  }, [isOpen, agent]);

  // Early return if no agent
  if (!agent) {
    console.log('PurchaseModal: No agent provided');
    return null;
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(price);
  };

  const handlePurchase = async () => {
    setLoading(true);

    try {
      // Step 1: Create payment intent
      toast({
        title: "Processing Purchase",
        description: "Creating payment intent...",
        duration: 2000
      });

      const purchaseResponse = await purchaseAgent(agent.id, customName);

      if (!purchaseResponse.success) {
        throw new Error(purchaseResponse.error || 'Failed to create payment intent');
      }

      // For now, we'll simulate the Stripe payment flow
      // In a full implementation, you would redirect to Stripe Checkout or use Stripe Elements
      toast({
        title: "Payment Required",
        description: `Ready to process payment of ${formatPrice(agent.price)}. Stripe integration is set up and ready!`,
        duration: 4000
      });

      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 2000));

      // For demo purposes, we'll show success
      toast({
        title: "Purchase Successful!",
        description: `"${agent.name}" has been added to your library!`,
        duration: 4000
      });

      onSuccess?.();
      onClose();
    } catch (error) {
      console.error('Purchase error:', error);
      toast({
        variant: "destructive",
        title: "Purchase Failed",
        description: error.message || "An error occurred during purchase. Please try again."
      });
    } finally {
      setLoading(false);
    }
  };

  console.log('PurchaseModal rendering with isOpen:', isOpen);

  // Temporary debug overlay
  if (isOpen) {
    console.log('Modal should be visible now!');
  }

  // Simple test - if modal should be open, show alert
  if (isOpen) {
    setTimeout(() => {
      alert(`Purchase modal for ${agent.name} - Price: $${agent.price}`);
    }, 100);
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-[#1A1A1A] border-white/[0.08] text-white max-w-md">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-white flex items-center gap-2">
            <ShoppingCart className="w-5 h-5 text-green-400" />
            Purchase Agent
          </DialogTitle>
          <DialogDescription className="text-white/60">
            You're about to purchase this AI trading agent
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Agent Details */}
          <div className="bg-white/[0.02] border border-white/[0.08] rounded-lg p-4 space-y-3">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h3 className="font-semibold text-white text-lg">{agent.name}</h3>
                <p className="text-white/60 text-sm mt-1 line-clamp-2">
                  {agent.description || 'No description available'}
                </p>
              </div>
              <div className="ml-3">
                <div className="text-right">
                  <div className="flex items-center gap-1 text-green-400 font-bold text-lg">
                    <DollarSign className="w-4 h-4" />
                    {formatPrice(agent.price)}
                  </div>
                </div>
              </div>
            </div>

            {/* Agent Stats */}
            <div className="flex items-center gap-4 text-xs text-white/40">
              <div className="flex items-center gap-1">
                <User className="w-3 h-3" />
                <span>{agent.seller_name || 'Unknown'}</span>
              </div>
              <div className="flex items-center gap-1">
                <ShoppingCart className="w-3 h-3" />
                <span>{agent.sales_count} sales</span>
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="w-3 h-3" />
                <span>{new Date(agent.created_at).toLocaleDateString()}</span>
              </div>
            </div>

            {/* Tags */}
            {agent.tags && agent.tags.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {agent.tags.slice(0, 4).map((tag, index) => (
                  <Badge key={index} variant="secondary" className="bg-white/5 text-white/60 text-xs px-2 py-0.5">
                    {tag}
                  </Badge>
                ))}
                {agent.tags.length > 4 && (
                  <Badge variant="secondary" className="bg-white/5 text-white/60 text-xs px-2 py-0.5">
                    +{agent.tags.length - 4}
                  </Badge>
                )}
              </div>
            )}
          </div>

          {/* Custom Name Input */}
          <div className="space-y-2">
            <Label htmlFor="custom-name" className="text-sm font-medium text-white">
              Custom Name (Optional)
            </Label>
            <Input
              id="custom-name"
              value={customName}
              onChange={(e) => setCustomName(e.target.value)}
              placeholder={`${agent.name} (Purchased)`}
              className="bg-white/[0.02] border-white/[0.08] text-white placeholder:text-white/40 focus:border-white/[0.15]"
            />
            <p className="text-xs text-white/60">
              Give your copy a custom name, or leave blank to use the default
            </p>
          </div>

          {/* Purchase Summary */}
          <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
            <div className="flex items-center gap-2 text-sm font-medium text-green-400 mb-2">
              <DollarSign className="w-4 h-4" />
              Purchase Summary
            </div>
            
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-white/60">Agent Price:</span>
                <span className="text-white">{formatPrice(agent.price)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-white/60">Platform Fee:</span>
                <span className="text-white">Included</span>
              </div>
              <div className="border-t border-green-500/20 pt-2">
                <div className="flex justify-between font-medium">
                  <span className="text-white">Total:</span>
                  <span className="text-green-400">{formatPrice(agent.price)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* What You Get */}
          <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3">
            <div className="text-xs text-blue-200">
              <p className="font-medium mb-1">✅ What you get:</p>
              <ul className="space-y-1 text-blue-200/80">
                <li>• Full copy of the agent in your library</li>
                <li>• Complete trading strategy and configuration</li>
                <li>• Ability to run, backtest, and modify</li>
                <li>• Lifetime access with no recurring fees</li>
              </ul>
            </div>
          </div>

          {/* Payment Status */}
          <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3">
            <div className="text-xs text-yellow-200">
              <p className="font-medium mb-1">🚀 Payment Integration Status:</p>
              <p className="text-yellow-200/80">
                Stripe payment processing is set up and ready! Currently in demo mode -
                clicking "Pay" will simulate the purchase flow and show success messages.
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1 border-white/[0.12] text-white/70 hover:text-white hover:bg-white/[0.04]"
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              onClick={handlePurchase}
              className="flex-1 bg-green-600 hover:bg-green-700 text-white"
              disabled={loading}
            >
              {loading ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Processing Purchase...</span>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <CreditCard className="w-4 h-4" />
                  <span>Pay {formatPrice(agent.price)}</span>
                </div>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PurchaseModal;
