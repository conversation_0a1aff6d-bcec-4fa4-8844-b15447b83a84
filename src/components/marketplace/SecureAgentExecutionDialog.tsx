import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, TrendingUp, TrendingDown, Minus, Shield, Key, AlertTriangle } from 'lucide-react';
import { executeSecureAgent, type AgentLicense, type SecureAgentExecutionResult } from '@/services/secureAgentService';

interface SecureAgentExecutionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  license: AgentLicense | null;
  onExecutionComplete?: (result: SecureAgentExecutionResult) => void;
}

const SecureAgentExecutionDialog: React.FC<SecureAgentExecutionDialogProps> = ({
  open,
  onOpenChange,
  license,
  onExecutionComplete
}) => {
  const [symbol, setSymbol] = useState<string>('AAPL');
  const [timeframe, setTimeframe] = useState<string>('day');
  const [isExecuting, setIsExecuting] = useState<boolean>(false);
  const [result, setResult] = useState<SecureAgentExecutionResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Reset state when dialog opens/closes
  React.useEffect(() => {
    if (!open) {
      setResult(null);
      setError(null);
    }
  }, [open]);

  // Handle secure execution
  const handleExecute = async () => {
    if (!license) {
      setError('No license provided');
      return;
    }

    // Validate inputs
    if (!symbol) {
      setError('Please enter a symbol');
      return;
    }

    // Start execution
    setIsExecuting(true);
    setError(null);
    setResult(null);

    console.log(`Executing secure agent for symbol ${symbol} on ${timeframe} timeframe`);

    try {
      const executionResult = await executeSecureAgent(license.license_key, symbol, timeframe);
      
      setResult(executionResult);
      
      // Notify parent component
      if (onExecutionComplete) {
        onExecutionComplete(executionResult);
      }

    } catch (error) {
      console.error('Secure agent execution error:', error);
      setError(error instanceof Error ? error.message : 'Execution failed');
    } finally {
      setIsExecuting(false);
    }
  };

  const getSignalIcon = (signal: string) => {
    switch (signal) {
      case 'bullish':
        return <TrendingUp className="w-5 h-5 text-green-400" />;
      case 'bearish':
        return <TrendingDown className="w-5 h-5 text-red-400" />;
      default:
        return <Minus className="w-5 h-5 text-gray-400" />;
    }
  };

  const getSignalColor = (signal: string) => {
    switch (signal) {
      case 'bullish':
        return 'text-green-400';
      case 'bearish':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  if (!license) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] bg-gray-900 border-gray-700">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center gap-2">
            <Shield className="w-5 h-5 text-blue-400" />
            Secure Agent Execution
          </DialogTitle>
          <DialogDescription className="text-gray-400">
            Execute your licensed agent securely without exposing its configuration
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* License Information */}
          <Card className="bg-gray-800/50 border-gray-700">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-white">
                  {license.custom_name || license.agent?.name || 'Licensed Agent'}
                </h4>
                <div className="flex gap-2">
                  <Badge className="bg-blue-500/20 text-blue-400 border border-blue-500/30">
                    <Key className="w-3 h-3 mr-1" />
                    Licensed
                  </Badge>
                  {license.agent?.is_encrypted && (
                    <Badge className="bg-purple-500/20 text-purple-400 border border-purple-500/30">
                      <Shield className="w-3 h-3 mr-1" />
                      Secure
                    </Badge>
                  )}
                </div>
              </div>
              <div className="text-sm text-gray-400 space-y-1">
                <div className="flex justify-between">
                  <span>Usage Count:</span>
                  <span className="text-blue-400">
                    {license.usage_count}{license.max_usage_limit ? `/${license.max_usage_limit}` : ''}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>License Key:</span>
                  <span className="font-mono text-xs">
                    {license.license_key.substring(0, 8)}...
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Security Notice */}
          <Alert className="bg-yellow-500/10 border-yellow-500/30">
            <AlertTriangle className="h-4 w-4 text-yellow-500" />
            <AlertDescription className="text-yellow-200">
              This agent will execute securely without exposing its internal configuration. 
              Only the final analysis result will be returned.
            </AlertDescription>
          </Alert>

          {/* Execution Parameters */}
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Symbol
              </label>
              <Input
                value={symbol}
                onChange={(e) => setSymbol(e.target.value.toUpperCase())}
                placeholder="Enter stock symbol (e.g., AAPL)"
                className="bg-gray-800 border-gray-600 text-white"
                disabled={isExecuting}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Timeframe
              </label>
              <Select value={timeframe} onValueChange={setTimeframe} disabled={isExecuting}>
                <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="minute">1 Minute</SelectItem>
                  <SelectItem value="hour">1 Hour</SelectItem>
                  <SelectItem value="day">1 Day</SelectItem>
                  <SelectItem value="week">1 Week</SelectItem>
                  <SelectItem value="month">1 Month</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <Alert className="bg-red-500/10 border-red-500/30">
              <AlertTriangle className="h-4 w-4 text-red-500" />
              <AlertDescription className="text-red-200">
                {error}
              </AlertDescription>
            </Alert>
          )}

          {/* Result Display */}
          {result && (
            <Card className="bg-gray-800/50 border-gray-700">
              <CardContent className="p-4">
                <h4 className="font-medium text-white mb-3">Execution Result</h4>
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Signal:</span>
                    <div className="flex items-center gap-2">
                      {getSignalIcon(result.signal)}
                      <span className={`font-semibold capitalize ${getSignalColor(result.signal)}`}>
                        {result.signal}
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Confidence:</span>
                    <span className="font-semibold text-white">{result.confidence}%</span>
                  </div>
                  
                  <div className="space-y-2">
                    <span className="text-gray-400">Reasoning:</span>
                    <p className="text-sm text-gray-300 bg-gray-800 p-2 rounded">
                      {result.reasoning}
                    </p>
                  </div>
                  
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>Executed at: {new Date(result.timestamp).toLocaleString()}</span>
                    <span>Usage: {result.usage_count}{result.max_usage_limit ? `/${result.max_usage_limit}` : ''}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="border-gray-600 text-gray-300 hover:bg-gray-800"
          >
            Close
          </Button>
          <Button
            onClick={handleExecute}
            disabled={isExecuting || !symbol}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            {isExecuting ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Executing...
              </>
            ) : (
              <>
                <Shield className="w-4 h-4 mr-2" />
                Execute Securely
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SecureAgentExecutionDialog;
