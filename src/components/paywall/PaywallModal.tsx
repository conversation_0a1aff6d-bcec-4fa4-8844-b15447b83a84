import React from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Crown, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';

interface PaywallModalProps {
  isOpen: boolean;
  onClose: () => void;
  featureName?: string;
}

const PaywallModal: React.FC<PaywallModalProps> = ({ 
  isOpen, 
  onClose, 
  featureName = "this feature" 
}) => {
  const navigate = useNavigate();

  const handleUpgrade = () => {
    onClose();
    navigate('/subscription/manage');
  };

  const premiumFeatures = [
    'Access to Agent Builder',
    'Discover Community Agents',
    'Advanced Stock Search',
    'Stock Scanner Tools',
    'Unlimited messages',
    'Unlimited portfolios',
    'Priority Support',
    'Advanced Analytics'
  ];

  return createPortal(
    <AnimatePresence>
      {isOpen && (
        <div
          className="fixed inset-0 flex items-center justify-center backdrop-blur-sm p-4"
          style={{
            zIndex: 2147483647,
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0
          }}
        >
          <motion.div
            className="absolute inset-0 bg-black/70"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            style={{ zIndex: 2147483647 }}
          />

          <motion.div
            className="relative w-full max-w-sm bg-[#0A0A0A] rounded-xl shadow-2xl border border-white/[0.08] overflow-hidden"
            style={{ zIndex: 2147483648 }}
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ duration: 0.2 }}
          >
            {/* Close Button */}
            <button
              onClick={onClose}
              className="absolute top-3 right-3 z-10 p-1.5 text-white/40 hover:text-white/70 transition-colors rounded-md hover:bg-white/[0.05]"
            >
              <X className="w-4 h-4" />
            </button>

            {/* Header */}
            <div className="p-5 text-center">
              {/* Osis Logo */}
              <div className="flex items-center justify-center mb-4">
                <div className="flex-shrink-0 p-2 rounded-xl bg-white/[0.02] border border-white/[0.08]">
                  <img
                    src="http://thecodingkid.oyosite.com/logo_only.png"
                    alt="Osis Logo"
                    className="w-6 h-6 object-contain"
                  />
                </div>
              </div>

              <h2 className="text-white text-lg font-medium mb-2">
                Premium Access
              </h2>
              <p className="text-white/60 text-sm mb-3">
                Access {featureName} with Premium plan
              </p>

              {/* Pricing Information */}
              <div className="bg-white/[0.03] border border-white/[0.08] rounded-lg p-3 mb-1">
                <div className="flex items-center justify-center gap-2 mb-1">
                  <span className="text-white text-lg font-medium">$19.99/week</span>
                </div>
                <p className="text-green-400 text-xs font-medium">
                  Complete access to all features
                </p>
                <p className="text-white/50 text-xs mt-1">
                  Premium Plan
                </p>
              </div>
            </div>

            {/* Features List */}
            <div className="px-5 pb-5">
              <div className="space-y-2 mb-5">
                {premiumFeatures.slice(0, 4).map((feature, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <div className="w-4 h-4 bg-green-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                      <Check className="w-2.5 h-2.5 text-green-400" />
                    </div>
                    <span className="text-white/70 text-xs">{feature}</span>
                  </div>
                ))}
              </div>

              {/* Action Buttons */}
              <div className="space-y-2">
                <Button
                  onClick={handleUpgrade}
                  className="w-full bg-white hover:bg-white/90 text-black font-medium py-2 rounded-lg transition-all duration-200 text-sm h-9"
                >
                  Get Premium Access
                </Button>

                <button
                  onClick={onClose}
                  className="w-full py-2 text-white/50 hover:text-white/70 text-xs transition-colors"
                >
                  Maybe later
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>,
    document.body
  );
};

export default PaywallModal;
