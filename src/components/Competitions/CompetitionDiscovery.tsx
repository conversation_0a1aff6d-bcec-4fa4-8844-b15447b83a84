import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Trophy, Users, Clock, DollarSign, Calendar, Target, Award, Timer, Globe, Crown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useCompetitions, Competition } from '@/hooks/useCompetitions';
import { useToast } from '@/components/ui/use-toast';
import { formatDistanceToNow, isAfter, isBefore } from 'date-fns';
import CompetitionCountdown from './CompetitionCountdown';
import { OFFICIAL_OSIS_BUSINESS_ID, OFFICIAL_OSIS_HANDLE } from '@/types/whopCompetition';

interface CompetitionDiscoveryProps {
  onCompetitionSelect?: (competition: Competition) => void;
}

const CompetitionDiscovery: React.FC<CompetitionDiscoveryProps> = ({
  onCompetitionSelect
}) => {
  const { competitions, userCompetitions, loading, joinCompetition } = useCompetitions();
  const { toast } = useToast();
  const [filter, setFilter] = useState<'all' | 'open' | 'active' | 'completed'>('all');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'active': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'completed': return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getCompetitionScopeBadge = (competition: Competition) => {
    if (!competition.competition_scope || competition.competition_scope === 'public') {
      return (
        <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
          <Globe className="w-3 h-3 mr-1" />
          Public
        </Badge>
      );
    }

    if (competition.competition_scope === 'whop_local') {
      return (
        <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
          <Users className="w-3 h-3 mr-1" />
          Community
        </Badge>
      );
    }

    if (competition.competition_scope === 'whop_cross_community') {
      const isOfficialOsis = competition.whop_business_id === OFFICIAL_OSIS_BUSINESS_ID ||
                            competition.whop_business_handle === OFFICIAL_OSIS_HANDLE;
      return (
        <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
          <Crown className="w-3 h-3 mr-1" />
          {isOfficialOsis ? 'Official Osis' : 'Cross-Community'}
        </Badge>
      );
    }

    return null;
  };

  const getTimeRemaining = (competition: Competition) => {
    const now = new Date();
    const start = new Date(competition.competition_start);
    const end = new Date(competition.competition_end);

    if (isBefore(now, start)) {
      return {
        label: 'Starts in',
        time: formatDistanceToNow(start),
        urgent: false
      };
    } else if (isAfter(now, start) && isBefore(now, end)) {
      const remaining = formatDistanceToNow(end);
      return {
        label: 'Ends in',
        time: remaining,
        urgent: remaining.includes('hour') || remaining.includes('minute')
      };
    } else {
      return {
        label: 'Ended',
        time: formatDistanceToNow(end) + ' ago',
        urgent: false
      };
    }
  };

  const isUserParticipating = (competitionId: string) => {
    return userCompetitions.some(uc => uc.competition_id === competitionId);
  };

  const handleJoinCompetition = async (competitionId: string) => {
    try {
      await joinCompetition(competitionId);
    } catch (error) {
      // Error is already handled in the hook
    }
  };

  const filteredCompetitions = competitions.filter(comp => {
    if (filter === 'all') return true;
    return comp.status === filter;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white flex items-center gap-2">
            <Trophy className="w-6 h-6 text-yellow-400" />
            Trading Competitions
          </h2>
          <p className="text-white/60 mt-1">
            Join paper trading competitions and compete with traders worldwide
          </p>
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="flex gap-2">
        {['all', 'open', 'active', 'completed'].map((status) => (
          <Button
            key={status}
            variant={filter === status ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilter(status as any)}
            className={filter === status ? 'bg-green-600 hover:bg-green-700' : ''}
          >
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </Button>
        ))}
      </div>

      {/* Competitions Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCompetitions.map((competition, index) => {
          const timeInfo = getTimeRemaining(competition);
          const isParticipating = isUserParticipating(competition.id);

          return (
            <motion.div
              key={competition.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="
                bg-[#141414]/80 border-white/[0.08] backdrop-blur-xl shadow-xl
                hover:border-white/[0.12] hover:shadow-2xl hover:shadow-black/20
                transition-all duration-300 cursor-pointer group
                relative overflow-hidden
              "
                    onClick={() => onCompetitionSelect?.(competition)}>
                {/* Subtle gradient overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/[0.02] to-transparent pointer-events-none" />

                <CardHeader className="pb-4 relative z-10">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-3">
                        <CardTitle className="text-white text-xl font-semibold group-hover:text-emerald-400 transition-colors tracking-tight">
                          {competition.name}
                        </CardTitle>
                        {getCompetitionScopeBadge(competition)}
                      </div>
                      <CardDescription className="text-white/70 mt-1 line-clamp-2 text-sm leading-relaxed">
                        {competition.description || 'No description available'}
                      </CardDescription>
                    </div>
                    <Badge className={`
                      ${getStatusColor(competition.status)}
                      px-3 py-1.5 text-xs font-medium rounded-full border
                      shadow-lg transition-all duration-300 hover:scale-105
                    `}>
                      {competition.status}
                    </Badge>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Key Stats */}
                  <div className="grid grid-cols-2 gap-3">
                    <div className="flex items-center gap-2 text-sm">
                      <DollarSign className="w-4 h-4 text-green-400" />
                      <span className="text-white/80">
                        ${competition.starting_balance.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <Users className="w-4 h-4 text-blue-400" />
                      <span className="text-white/80">
                        {competition.participant_count || 0}
                        {competition.max_participants && ` / ${competition.max_participants}`}
                      </span>
                    </div>
                  </div>

                  {/* Competition Countdown */}
                  <CompetitionCountdown
                    competitionStart={competition.competition_start}
                    competitionEnd={competition.competition_end}
                    registrationStart={competition.registration_start}
                    registrationEnd={competition.registration_end}
                    className="mb-2"
                  />

                  {/* Prize Pool */}
                  {competition.prize_pool && competition.prize_pool > 0 && (
                    <div className="flex items-center gap-2 text-sm">
                      <Award className="w-4 h-4 text-yellow-400" />
                      <span className="text-white/80">
                        Prize: ${competition.prize_pool.toLocaleString()}
                      </span>
                    </div>
                  )}

                  {/* Action Button */}
                  <div className="pt-2">
                    {isParticipating ? (
                      <Button 
                        className="w-full bg-blue-600 hover:bg-blue-700"
                        onClick={(e) => {
                          e.stopPropagation();
                          onCompetitionSelect?.(competition);
                        }}
                      >
                        <Trophy className="w-4 h-4 mr-2" />
                        View Competition
                      </Button>
                    ) : competition.status === 'open' ? (
                      <Button 
                        className="w-full bg-green-600 hover:bg-green-700"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleJoinCompetition(competition.id);
                        }}
                      >
                        <Target className="w-4 h-4 mr-2" />
                        Join Competition
                      </Button>
                    ) : (
                      <Button 
                        variant="outline" 
                        className="w-full"
                        onClick={(e) => {
                          e.stopPropagation();
                          onCompetitionSelect?.(competition);
                        }}
                      >
                        <Trophy className="w-4 h-4 mr-2" />
                        View Details
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {/* Empty State */}
      {filteredCompetitions.length === 0 && (
        <div className="text-center py-12">
          <Trophy className="w-16 h-16 text-white/20 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-white/60 mb-2">
            No competitions found
          </h3>
          <p className="text-white/40">
            {filter === 'all' 
              ? 'No competitions are currently available'
              : `No ${filter} competitions found`
            }
          </p>
        </div>
      )}
    </div>
  );
};

export default CompetitionDiscovery;
