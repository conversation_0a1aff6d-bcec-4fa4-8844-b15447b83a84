import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Trophy, Medal, Crown, TrendingUp, TrendingDown, DollarSign, Activity } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useCompetitions, LeaderboardEntry } from '@/hooks/useCompetitions';
import { formatDistanceToNow } from 'date-fns';

interface CompetitionLeaderboardProps {
  competitionId: string;
  refreshInterval?: number; // in milliseconds, default 30 seconds
}

const CompetitionLeaderboard: React.FC<CompetitionLeaderboardProps> = ({
  competitionId,
  refreshInterval = 30000
}) => {
  const { getCompetitionDetails, updateLeaderboard } = useCompetitions();
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [competition, setCompetition] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchLeaderboard = async () => {
    try {
      const details = await getCompetitionDetails(competitionId);
      if (details) {
        setCompetition(details.competition);
        setLeaderboard(details.leaderboard || []);
        setLastUpdated(new Date());
      }
    } catch (error) {
      console.error('Error fetching leaderboard:', error);
    } finally {
      setLoading(false);
    }
  };

  const refreshLeaderboard = async () => {
    try {
      await updateLeaderboard(competitionId);
      await fetchLeaderboard();
    } catch (error) {
      console.error('Error refreshing leaderboard:', error);
    }
  };

  useEffect(() => {
    fetchLeaderboard();

    // Set up auto-refresh interval
    const interval = setInterval(fetchLeaderboard, refreshInterval);
    return () => clearInterval(interval);
  }, [competitionId, refreshInterval]);

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="w-5 h-5 text-yellow-400" />;
      case 2:
        return <Medal className="w-5 h-5 text-gray-300" />;
      case 3:
        return <Medal className="w-5 h-5 text-amber-600" />;
      default:
        return <span className="w-5 h-5 flex items-center justify-center text-white/60 font-bold text-sm">{rank}</span>;
    }
  };

  const getRankBadgeColor = (rank: number) => {
    switch (rank) {
      case 1:
        return 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-black';
      case 2:
        return 'bg-gradient-to-r from-gray-300 to-gray-500 text-black';
      case 3:
        return 'bg-gradient-to-r from-amber-600 to-amber-800 text-white';
      default:
        return 'bg-white/10 text-white/80';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (percent: number) => {
    const sign = percent >= 0 ? '+' : '';
    return `${sign}${percent.toFixed(2)}%`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white flex items-center gap-2">
            <Trophy className="w-6 h-6 text-yellow-400" />
            Leaderboard
          </h2>
          {competition && (
            <p className="text-white/60 mt-1">{competition.name}</p>
          )}
        </div>
        {lastUpdated && (
          <div className="text-right">
            <p className="text-white/40 text-sm">
              Last updated: {formatDistanceToNow(lastUpdated)} ago
            </p>
            <Badge variant="outline" className="mt-1">
              <Activity className="w-3 h-3 mr-1" />
              Live Updates
            </Badge>
          </div>
        )}
      </div>

      {/* Top 3 Podium */}
      {leaderboard.length >= 3 && (
        <div className="grid grid-cols-3 gap-4 mb-8">
          {/* 2nd Place */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="order-1"
          >
            <Card className="bg-gradient-to-br from-gray-500/20 to-gray-600/20 border-gray-400/30">
              <CardContent className="p-4 text-center">
                <div className="flex justify-center mb-2">
                  <Medal className="w-8 h-8 text-gray-300" />
                </div>
                <h3 className="font-bold text-white truncate">{leaderboard[1]?.username}</h3>
                <p className="text-gray-300 font-semibold">{formatCurrency(leaderboard[1]?.portfolio_value || 0)}</p>
                <p className={`text-sm ${leaderboard[1]?.return_percent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {formatPercentage(leaderboard[1]?.return_percent || 0)}
                </p>
              </CardContent>
            </Card>
          </motion.div>

          {/* 1st Place */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0 }}
            className="order-2"
          >
            <Card className="bg-gradient-to-br from-yellow-500/20 to-yellow-600/20 border-yellow-400/30 transform scale-105">
              <CardContent className="p-4 text-center">
                <div className="flex justify-center mb-2">
                  <Crown className="w-10 h-10 text-yellow-400" />
                </div>
                <h3 className="font-bold text-white truncate">{leaderboard[0]?.username}</h3>
                <p className="text-yellow-400 font-semibold">{formatCurrency(leaderboard[0]?.portfolio_value || 0)}</p>
                <p className={`text-sm ${leaderboard[0]?.return_percent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {formatPercentage(leaderboard[0]?.return_percent || 0)}
                </p>
              </CardContent>
            </Card>
          </motion.div>

          {/* 3rd Place */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="order-3"
          >
            <Card className="bg-gradient-to-br from-amber-600/20 to-amber-700/20 border-amber-500/30">
              <CardContent className="p-4 text-center">
                <div className="flex justify-center mb-2">
                  <Medal className="w-8 h-8 text-amber-600" />
                </div>
                <h3 className="font-bold text-white truncate">{leaderboard[2]?.username}</h3>
                <p className="text-amber-400 font-semibold">{formatCurrency(leaderboard[2]?.portfolio_value || 0)}</p>
                <p className={`text-sm ${leaderboard[2]?.return_percent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {formatPercentage(leaderboard[2]?.return_percent || 0)}
                </p>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      )}

      {/* Full Leaderboard */}
      <Card className="bg-white/[0.02] border-white/[0.08]">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Trophy className="w-5 h-5 text-yellow-400" />
            Full Rankings
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="space-y-1">
            {leaderboard.map((entry, index) => (
              <motion.div
                key={entry.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.05 }}
                className="flex items-center justify-between p-4 hover:bg-white/[0.02] transition-colors border-b border-white/[0.05] last:border-b-0"
              >
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    {getRankIcon(entry.current_rank)}
                    <Badge className={getRankBadgeColor(entry.current_rank)}>
                      #{entry.current_rank}
                    </Badge>
                  </div>
                  <div>
                    <h4 className="font-semibold text-white">{entry.username}</h4>
                    <p className="text-white/60 text-sm">{entry.total_trades} trades</p>
                  </div>
                </div>

                <div className="text-right">
                  <div className="flex items-center gap-2">
                    <DollarSign className="w-4 h-4 text-green-400" />
                    <span className="font-semibold text-white">
                      {formatCurrency(entry.portfolio_value)}
                    </span>
                  </div>
                  <div className="flex items-center gap-1 justify-end">
                    {entry.return_percent >= 0 ? (
                      <TrendingUp className="w-3 h-3 text-green-400" />
                    ) : (
                      <TrendingDown className="w-3 h-3 text-red-400" />
                    )}
                    <span className={`text-sm font-medium ${
                      entry.return_percent >= 0 ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {formatPercentage(entry.return_percent)}
                    </span>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Empty State */}
      {leaderboard.length === 0 && (
        <div className="text-center py-12">
          <Trophy className="w-16 h-16 text-white/20 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-white/60 mb-2">
            No participants yet
          </h3>
          <p className="text-white/40">
            Be the first to join this competition!
          </p>
        </div>
      )}
    </div>
  );
};

export default CompetitionLeaderboard;
