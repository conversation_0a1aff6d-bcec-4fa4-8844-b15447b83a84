import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Timer, Clock, Calendar } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { isAfter, isBefore } from 'date-fns';

interface CompetitionCountdownProps {
  competitionStart: string;
  competitionEnd: string;
  registrationStart?: string;
  registrationEnd?: string;
  className?: string;
}

interface TimeRemaining {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
}

const CompetitionCountdown: React.FC<CompetitionCountdownProps> = ({
  competitionStart,
  competitionEnd,
  registrationStart,
  registrationEnd,
  className = ''
}) => {
  const [timeRemaining, setTimeRemaining] = useState<TimeRemaining>({ days: 0, hours: 0, minutes: 0, seconds: 0 });
  const [status, setStatus] = useState<'registration' | 'upcoming' | 'active' | 'completed'>('upcoming');
  const [targetDate, setTargetDate] = useState<Date>(new Date());

  const calculateTimeRemaining = (target: Date): TimeRemaining => {
    const now = new Date().getTime();
    const targetTime = target.getTime();
    const difference = targetTime - now;

    if (difference <= 0) {
      return { days: 0, hours: 0, minutes: 0, seconds: 0 };
    }

    const days = Math.floor(difference / (1000 * 60 * 60 * 24));
    const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((difference % (1000 * 60)) / 1000);

    return { days, hours, minutes, seconds };
  };

  const determineStatus = () => {
    const now = new Date();
    const start = new Date(competitionStart);
    const end = new Date(competitionEnd);
    const regStart = registrationStart ? new Date(registrationStart) : null;
    const regEnd = registrationEnd ? new Date(registrationEnd) : null;

    if (regStart && regEnd && isBefore(now, regStart)) {
      setStatus('registration');
      setTargetDate(regStart);
    } else if (regStart && regEnd && isAfter(now, regStart) && isBefore(now, regEnd)) {
      setStatus('registration');
      setTargetDate(regEnd);
    } else if (isBefore(now, start)) {
      setStatus('upcoming');
      setTargetDate(start);
    } else if (isAfter(now, start) && isBefore(now, end)) {
      setStatus('active');
      setTargetDate(end);
    } else {
      setStatus('completed');
      setTargetDate(end);
    }
  };

  useEffect(() => {
    determineStatus();
  }, [competitionStart, competitionEnd, registrationStart, registrationEnd]);

  useEffect(() => {
    const interval = setInterval(() => {
      setTimeRemaining(calculateTimeRemaining(targetDate));
      determineStatus(); // Re-check status in case it changed
    }, 1000);

    return () => clearInterval(interval);
  }, [targetDate]);

  const getStatusConfig = () => {
    switch (status) {
      case 'registration':
        return {
          label: registrationStart && isBefore(new Date(), new Date(registrationStart)) 
            ? 'Registration opens in' 
            : 'Registration closes in',
          color: 'bg-blue-500/20 text-blue-400 border-blue-500/30',
          icon: <Calendar className="w-4 h-4" />,
          urgent: false
        };
      case 'upcoming':
        return {
          label: 'Competition starts in',
          color: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
          icon: <Clock className="w-4 h-4" />,
          urgent: timeRemaining.days === 0 && timeRemaining.hours < 24
        };
      case 'active':
        return {
          label: 'Competition ends in',
          color: 'bg-green-500/20 text-green-400 border-green-500/30',
          icon: <Timer className="w-4 h-4" />,
          urgent: timeRemaining.days === 0 && timeRemaining.hours < 6
        };
      case 'completed':
        return {
          label: 'Competition ended',
          color: 'bg-gray-500/20 text-gray-400 border-gray-500/30',
          icon: <Timer className="w-4 h-4" />,
          urgent: false
        };
    }
  };

  const statusConfig = getStatusConfig();
  const isUrgent = statusConfig.urgent;
  const hasTimeLeft = timeRemaining.days > 0 || timeRemaining.hours > 0 || timeRemaining.minutes > 0 || timeRemaining.seconds > 0;

  if (status === 'completed') {
    return (
      <Card className={`bg-white/[0.02] border-white/[0.08] ${className}`}>
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              {statusConfig.icon}
              <Badge className={statusConfig.color}>
                Competition Ended
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <motion.div
      animate={isUrgent ? { scale: [1, 1.02, 1] } : {}}
      transition={isUrgent ? { duration: 2, repeat: Infinity } : {}}
      className={className}
    >
      <Card className={`bg-white/[0.02] border-white/[0.08] ${isUrgent ? 'border-red-500/30' : ''}`}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {statusConfig.icon}
              <span className={`text-sm font-medium ${isUrgent ? 'text-red-400' : 'text-white/80'}`}>
                {statusConfig.label}
              </span>
            </div>
            <Badge className={isUrgent ? 'bg-red-500/20 text-red-400 border-red-500/30' : statusConfig.color}>
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </Badge>
          </div>

          {hasTimeLeft && (
            <div className="mt-3 grid grid-cols-4 gap-2">
              {/* Days */}
              <div className="text-center">
                <motion.div
                  key={timeRemaining.days}
                  initial={{ scale: 1.2, opacity: 0.8 }}
                  animate={{ scale: 1, opacity: 1 }}
                  className={`text-2xl font-bold ${isUrgent ? 'text-red-400' : 'text-white'}`}
                >
                  {timeRemaining.days.toString().padStart(2, '0')}
                </motion.div>
                <div className="text-xs text-white/60">Days</div>
              </div>

              {/* Hours */}
              <div className="text-center">
                <motion.div
                  key={timeRemaining.hours}
                  initial={{ scale: 1.2, opacity: 0.8 }}
                  animate={{ scale: 1, opacity: 1 }}
                  className={`text-2xl font-bold ${isUrgent ? 'text-red-400' : 'text-white'}`}
                >
                  {timeRemaining.hours.toString().padStart(2, '0')}
                </motion.div>
                <div className="text-xs text-white/60">Hours</div>
              </div>

              {/* Minutes */}
              <div className="text-center">
                <motion.div
                  key={timeRemaining.minutes}
                  initial={{ scale: 1.2, opacity: 0.8 }}
                  animate={{ scale: 1, opacity: 1 }}
                  className={`text-2xl font-bold ${isUrgent ? 'text-red-400' : 'text-white'}`}
                >
                  {timeRemaining.minutes.toString().padStart(2, '0')}
                </motion.div>
                <div className="text-xs text-white/60">Minutes</div>
              </div>

              {/* Seconds */}
              <div className="text-center">
                <motion.div
                  key={timeRemaining.seconds}
                  initial={{ scale: 1.2, opacity: 0.8 }}
                  animate={{ scale: 1, opacity: 1 }}
                  className={`text-2xl font-bold ${isUrgent ? 'text-red-400' : 'text-white'}`}
                >
                  {timeRemaining.seconds.toString().padStart(2, '0')}
                </motion.div>
                <div className="text-xs text-white/60">Seconds</div>
              </div>
            </div>
          )}

          {!hasTimeLeft && status !== 'completed' && (
            <div className="mt-3 text-center">
              <span className="text-lg font-bold text-green-400">
                {status === 'upcoming' ? 'Competition Starting!' : 'Time\'s Up!'}
              </span>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default CompetitionCountdown;
