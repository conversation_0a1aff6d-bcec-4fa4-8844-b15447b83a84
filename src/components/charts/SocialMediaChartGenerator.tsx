import React, { useRef, useCallback, useState } from 'react';
import ReactECharts from 'echarts-for-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Download, Copy, Share2 } from 'lucide-react';
import domtoimage from 'dom-to-image';

export interface ChartDataPoint {
  name: string;
  value: number;
  date?: string;
  category?: string;
  color?: string;
}

export interface ChartConfig {
  title: string;
  subtitle?: string;
  type: 'bar' | 'stacked-bar' | 'line' | 'area' | 'comparison-bar';
  data: ChartDataPoint[] | ChartDataPoint[][];
  colors?: string[];
  showValues?: boolean;
  showLegend?: boolean;
  showGrid?: boolean;
  backgroundColor?: string;
  textColor?: string;
  brandLogo?: string;
  brandText?: string;
  width?: number;
  height?: number;
  yAxisLabel?: string;
  xAxisLabel?: string;
  valueFormatter?: (value: number) => string;
  legendData?: string[];
  totalChange?: string;
  cagr?: string;
  // Comparison chart specific options
  barWidth?: number;
  barGap?: string; // Gap between different series (e.g., '-30%' for overlap)
  categoryGap?: string; // Gap between categories
}

interface SocialMediaChartGeneratorProps {
  config: ChartConfig;
  onExport?: (imageData: string) => void;
}

const SocialMediaChartGenerator: React.FC<SocialMediaChartGeneratorProps> = ({
  config,
  onExport
}) => {
  const chartRef = useRef<any>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isExporting, setIsExporting] = useState(false);

  // Default colors matching the examples (Fiscal.ai style)
  const defaultColors = [
    '#3B82F6', // Blue
    '#10B981', // Green  
    '#F59E0B', // Yellow/Orange
    '#EF4444', // Red
    '#8B5CF6', // Purple
    '#06B6D4', // Cyan
    '#84CC16', // Lime
    '#F97316', // Orange
    '#EC4899', // Pink
    '#6B7280'  // Gray
  ];

  const formatValue = useCallback((value: number): string => {
    if (config.valueFormatter) {
      return config.valueFormatter(value);
    }
    
    if (value >= 1000000000) {
      return `${(value / 1000000000).toFixed(1)}B`;
    } else if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return value.toString();
  }, [config.valueFormatter]);

  const generateComparisonBarChart = useCallback(() => {
    const colors = config.colors || defaultColors;

    // Expect data to be an array of arrays for comparison charts
    const seriesData = Array.isArray(config.data) && Array.isArray(config.data[0])
      ? config.data as ChartDataPoint[][]
      : [config.data as ChartDataPoint[]];

    if (seriesData.length === 0 || !seriesData[0] || seriesData[0].length === 0) {
      return {
        title: { text: 'NO DATA' },
        xAxis: { data: [] },
        yAxis: {},
        series: []
      };
    }

    // Extract category names from the first series
    const categories = seriesData[0].map((item: any) => item.name || 'Unknown');

    // Create series for each data set - STACKED bars instead of overlapping
    const series = seriesData.map((dataSet: ChartDataPoint[], index: number) => ({
      type: 'bar',
      name: config.legendData?.[index] || `Series ${index + 1}`,
      data: dataSet.map((item: any) => item.value || 0),
      stack: 'comparison', // This creates stacked bars
      itemStyle: {
        color: colors[index] || defaultColors[index % defaultColors.length],
        borderRadius: index === seriesData.length - 1 ? [4, 4, 0, 0] : [0, 0, 0, 0] // Only round top of highest stack
      },
      label: {
        show: config.showValues !== false,
        position: 'inside', // Position labels inside the bars for stacked charts
        color: '#FFFFFF',
        fontSize: 11,
        fontWeight: 'bold',
        formatter: (params: any) => {
          const value = params.value;
          return value > 0 ? formatValue(value) : ''; // Only show label if value > 0
        }
      },
      emphasis: {
        focus: 'series'
      }
    }));

    const baseOption: any = {
      backgroundColor: config.backgroundColor || '#FFFFFF',
      title: {
        text: config.title || 'Comparison Chart',
        left: 'center',
        top: 20,
        textStyle: {
          fontSize: 18,
          fontWeight: 'bold',
          color: config.textColor || '#000000'
        }
      },
      grid: {
        left: 80,
        right: 80,
        top: config.showLegend ? 140 : 80,
        bottom: config.showLegend ? 120 : 60,
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: categories,
        axisLabel: {
          color: '#666666',
          fontSize: 11,
          rotate: categories.length > 8 ? 45 : 0 // Rotate labels if many categories
        },
        axisLine: {
          lineStyle: {
            color: '#E5E7EB'
          }
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: '#666666',
          fontSize: 11,
          formatter: (value: number) => formatValue(value)
        },
        splitLine: {
          show: config.showGrid !== false,
          lineStyle: {
            color: '#F3F4F6',
            type: 'solid'
          }
        },
        axisLine: {
          show: false
        }
      },
      series: series,
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.9)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        borderWidth: 1,
        textStyle: {
          color: '#FFFFFF',
          fontSize: 12
        },
        formatter: (params: any) => {
          let total = 0;
          let result = `<strong style="font-size: 14px;">${params[0].axisValue}</strong><br/>`;

          params.forEach((param: any) => {
            total += param.value;
            result += `<div style="margin: 4px 0;">`;
            result += `${param.marker} <span style="font-weight: 500;">${param.seriesName}:</span> `;
            result += `<span style="font-weight: bold;">${formatValue(param.value)}</span>`;
            result += `</div>`;
          });

          if (params.length > 1) {
            result += `<div style="margin-top: 8px; padding-top: 4px; border-top: 1px solid rgba(255,255,255,0.3);">`;
            result += `<strong>Total: ${formatValue(total)}</strong>`;
            result += `</div>`;
          }

          return result;
        }
      }
    };

    // Add legend if requested - position at bottom like in the examples
    if (config.showLegend && config.legendData) {
      baseOption.legend = {
        data: config.legendData,
        bottom: 20,
        left: 'center',
        orient: 'horizontal',
        itemGap: 20,
        textStyle: {
          color: config.textColor || '#000000',
          fontSize: 11
        },
        itemWidth: 12,
        itemHeight: 12,
        icon: 'circle'
      };
    }

    // Add brand text
    if (config.brandText) {
      baseOption.graphic = {
        type: 'text',
        right: 20,
        bottom: 15,
        style: {
          text: config.brandText,
          fontSize: 14,
          fill: '#666666',
          fontWeight: 'bold'
        }
      };
    }

    return baseOption;
  }, [config, defaultColors, formatValue]);

  const generateChartOption = useCallback(() => {
    console.log('=== CHART DEBUG ===');
    console.log('Config:', config);
    console.log('Data:', config.data);
    console.log('Data type:', typeof config.data);
    console.log('Is array:', Array.isArray(config.data));

    const colors = config.colors || defaultColors;

    // Handle comparison-bar type with multiple data series
    if (config.type === 'comparison-bar') {
      return generateComparisonBarChart();
    }

    // Force simple data structure for regular charts
    const chartData = Array.isArray(config.data) ? config.data : [];
    console.log('Chart data length:', chartData.length);

    if (chartData.length === 0) {
      console.error('NO DATA FOUND!');
      return {
        title: { text: 'NO DATA' },
        xAxis: { data: [] },
        yAxis: {},
        series: []
      };
    }

    // Extract names and values
    const names = chartData.map((item: any) => item.name || 'Unknown');
    const values = chartData.map((item: any) => item.value || 0);

    console.log('Names:', names);
    console.log('Values:', values);

    // Create simple, guaranteed-to-work chart option
    const baseOption: any = {
      backgroundColor: config.backgroundColor || '#FFFFFF',
      title: {
        text: config.title || 'Chart',
        left: 'center',
        top: 20,
        textStyle: {
          fontSize: 18,
          fontWeight: 'bold',
          color: config.textColor || '#000000'
        }
      },
      grid: {
        left: 60,
        right: 60,
        top: 80,
        bottom: 60,
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: names,
        axisLabel: {
          color: '#666666',
          fontSize: 11
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: '#666666',
          fontSize: 11
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#F3F4F6'
          }
        }
      },
      series: [{
        type: 'bar',
        data: values,
        itemStyle: {
          color: colors[0] || '#3B82F6',
          borderRadius: [4, 4, 0, 0]
        },
        label: {
          show: config.showValues !== false,
          position: 'top',
          color: '#000000',
          fontSize: 11
        }
      }],
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        textStyle: {
          color: '#FFFFFF'
        }
      }
    };

    // Add brand text
    if (config.brandText) {
      baseOption.graphic = {
        type: 'text',
        right: 20,
        bottom: 15,
        style: {
          text: config.brandText,
          fontSize: 14,
          fill: '#666666',
          fontWeight: 'bold'
        }
      };
    }

    console.log('Final chart option:', baseOption);
    return baseOption;
  }, [config, defaultColors, formatValue]);

  const exportAsImage = useCallback(async (format: 'png' | 'jpeg' = 'png') => {
    if (!containerRef.current) return;
    
    setIsExporting(true);
    
    try {
      const scale = 2; // High resolution for social media
      const options = {
        width: (config.width || 800) * scale,
        height: (config.height || 600) * scale,
        style: {
          transform: `scale(${scale})`,
          transformOrigin: 'top left',
          width: `${config.width || 800}px`,
          height: `${config.height || 600}px`
        },
        quality: 0.95,
        bgcolor: config.backgroundColor || '#FFFFFF'
      };

      const dataUrl = format === 'png' 
        ? await domtoimage.toPng(containerRef.current, options)
        : await domtoimage.toJpeg(containerRef.current, options);
      
      if (onExport) {
        onExport(dataUrl);
      }
      
      // Auto-download
      const link = document.createElement('a');
      link.download = `chart-${Date.now()}.${format}`;
      link.href = dataUrl;
      link.click();
      
    } catch (error) {
      console.error('Error exporting chart:', error);
    } finally {
      setIsExporting(false);
    }
  }, [config, onExport]);

  const copyToClipboard = useCallback(async () => {
    if (!containerRef.current) return;
    
    try {
      const dataUrl = await domtoimage.toPng(containerRef.current, {
        quality: 0.95,
        bgcolor: config.backgroundColor || '#FFFFFF'
      });
      
      const response = await fetch(dataUrl);
      const blob = await response.blob();
      
      await navigator.clipboard.write([
        new ClipboardItem({ 'image/png': blob })
      ]);
      
      // You could add a toast notification here
      console.log('Chart copied to clipboard');
    } catch (error) {
      console.error('Error copying chart:', error);
    }
  }, [config.backgroundColor]);

  return (
    <Card className="p-6 bg-white">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">Chart Generator</h3>
        <div className="flex gap-2">
          <Button
            variant="secondary"
            size="sm"
            onClick={copyToClipboard}
            disabled={isExporting}
            className="bg-gray-100 hover:bg-gray-200 text-gray-700 border-gray-300"
          >
            <Copy className="h-4 w-4 mr-2" />
            Copy
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => exportAsImage('png')}
            disabled={isExporting}
            className="bg-blue-100 hover:bg-blue-200 text-blue-700 border-blue-300"
          >
            <Download className="h-4 w-4 mr-2" />
            {isExporting ? 'Exporting...' : 'Export PNG'}
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => exportAsImage('jpeg')}
            disabled={isExporting}
            className="bg-green-100 hover:bg-green-200 text-green-700 border-green-300"
          >
            <Share2 className="h-4 w-4 mr-2" />
            Export JPG
          </Button>
        </div>
      </div>
      
      <div 
        ref={containerRef}
        className="bg-white rounded-lg"
        style={{ 
          width: config.width || 800, 
          height: config.height || 600,
          backgroundColor: config.backgroundColor || '#FFFFFF'
        }}
      >
        <ReactECharts
          key={`chart-${config.title}-${config.data?.length || 0}`}
          ref={chartRef}
          option={generateChartOption()}
          style={{
            width: '100%',
            height: '100%'
          }}
          opts={{
            renderer: 'canvas',
            devicePixelRatio: window.devicePixelRatio || 1
          }}
          notMerge={false}
          lazyUpdate={true}
        />
      </div>
    </Card>
  );
};

export default SocialMediaChartGenerator;
