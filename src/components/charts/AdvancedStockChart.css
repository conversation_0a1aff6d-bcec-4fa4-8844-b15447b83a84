/* Tooltip enhancements */
.echarts-tooltip {
  pointer-events: none !important;
  transition: none !important;
  animation: none !important;
  will-change: transform;
  z-index: 9999 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transform: translateZ(0);
  backface-visibility: hidden;
  contain: layout style paint;
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.2));
}

/* Axis pointer (crosshair) styling */
.echarts-axispointer-line {
  pointer-events: none !important;
  transition: none !important;
  animation: none !important;
  will-change: transform;
  contain: strict;
}

/* TP/SL Line styling */
.echarts-mark-line {
  pointer-events: none !important;
  transition: none !important;
  animation: none !important;
  will-change: transform;
  contain: strict;
}

.echarts-mark-line-emphasis .echarts-mark-line-line {
  animation: none !important;
  transition: none !important;
}

.echarts-mark-line-label {
  font-weight: 600 !important;
  font-size: 12px !important;
  user-select: none;
  pointer-events: none;
  transition: none !important;
  will-change: transform;
  contain: strict;
}

/* Disable hover effects on certain elements */
.trading-strategy-area,
.trading-strategy-line,
.echarts-axis-line,
.echarts-axis-tick,
.echarts-axis-label,
.echarts-grid-line {
  pointer-events: none !important;
  transition: none !important;
  animation: none !important;
  will-change: transform;
  contain: strict;
}

/* Trading Strategy Visualization Styles */
.strategy-marker {
  pointer-events: none !important;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3)) !important;
  will-change: transform;
  contain: strict;
}

.strategy-entry-marker {
  fill: #00ff88 !important;
  stroke: #132215 !important;
  stroke-width: 2px !important;
}

.strategy-exit-marker {
  fill: #ff3a50 !important;
  stroke: #221315 !important;
  stroke-width: 2px !important;
}

.strategy-level-line {
  stroke-dasharray: 4 4 !important;
  pointer-events: none !important;
  transition: none !important;
  animation: none !important;
  will-change: transform;
  contain: strict;
}

.strategy-support-level {
  stroke: rgba(0, 255, 136, 0.6) !important;
  stroke-width: 1.5px !important;
}

.strategy-resistance-level {
  stroke: rgba(255, 58, 80, 0.6) !important;
  stroke-width: 1.5px !important;
}

.strategy-zone {
  opacity: 0.08 !important;
  pointer-events: none !important;
  transition: none !important;
  animation: none !important;
  will-change: transform;
  contain: strict;
}

.strategy-buy-zone {
  fill: rgba(0, 255, 136, 0.6) !important;
}

.strategy-sell-zone {
  fill: rgba(255, 58, 80, 0.6) !important;
}

/* Performance optimizations */
.echarts-canvas {
  will-change: transform;
  contain: strict;
  backface-visibility: hidden;
  transform: translateZ(0);
}

/* Disable all animations globally */
.echarts * {
  transition: none !important;
  animation: none !important;
}

/* Add custom animations */
@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* Add additional chart styling */
.screenshot-visible {
  opacity: 0;
}

.screenshot-visible:hover {
  opacity: 0.3;
}

/* Apply during screenshot mode */
:global(.screenshot-mode) .screenshot-visible {
  opacity: 1 !important;
}

/* Add these new styles */
.echarts-axis-pointer {
  transition: 
    transform 0.1s cubic-bezier(0.33, 1, 0.68, 1),
    opacity 0.1s linear !important;
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.echarts-axis-pointer-line {
  stroke-dasharray: none !important;
  transition: 
    stroke 0.2s ease,
    stroke-opacity 0.2s ease,
    stroke-width 0.2s ease !important;
  transform: translateZ(0);
}

.echarts-axis-pointer-label {
  transition: 
    left 0.2s cubic-bezier(0.33, 1, 0.68, 1),
    top 0.2s cubic-bezier(0.33, 1, 0.68, 1),
    opacity 0.2s ease !important;
  will-change: left, top, opacity;
  transform: translateZ(0);
  contain: layout paint;
}

/* Force single instance rendering */
.echarts-axis-pointer,
.echarts-axis-pointer-line,
.echarts-axis-pointer-label {
  transform-style: preserve-3d;
  pointer-events: none !important;
}

/* Add these new transitions */
.echarts-mark-line-label,
.echarts-mark-area-label {
  transition: opacity 0.2s ease !important;
}

@keyframes drawLine {
  from {
    stroke-dashoffset: 10000;
  }
  to {
    stroke-dashoffset: 0;
  }
}

.echarts-line-series path {
  stroke-dasharray: 10000;
  animation: drawLine 200ms linear forwards;
}

/* Disable all ECharts animations */
.echarts * {
  transition: none !important;
  animation: none !important;
}

/* Only allow our custom line animation */
.echarts-line-series path {
  animation: drawLine 200ms linear forwards !important;
} 