import React from 'react';
import { Card } from '@/components/ui/card';
import SocialMediaChartGenerator, { ChartConfig } from './SocialMediaChartGenerator';

const ComparisonStructureTest: React.FC = () => {
  // WRONG: Company names on X-axis (what we had before)
  const wrongStructure: ChartConfig = {
    title: '❌ WRONG: Company Names on X-axis',
    type: 'comparison-bar',
    data: [
      [{ name: 'Apple', value: 391040 }],
      [{ name: 'Microsoft', value: 245122 }]
    ],
    colors: ['#3B82F6', '#EF4444'],
    showValues: true,
    showLegend: true,
    showGrid: true,
    backgroundColor: '#FFFFFF',
    textColor: '#000000',
    width: 800,
    height: 400,
    legendData: ['Apple Total', 'Microsoft Total'],
    brandText: 'Made with Osis.co'
  };

  // RIGHT: Time periods on X-axis with companies stacked
  const rightStructure: ChartConfig = {
    title: '✅ RIGHT: Time Periods on X-axis with Companies Stacked',
    type: 'comparison-bar',
    data: [
      [
        { name: 'Q1 \'23', value: 117154 },
        { name: 'Q2 \'23', value: 81797 },
        { name: 'Q3 \'23', value: 89498 },
        { name: 'Q4 \'23', value: 119575 }
      ],
      [
        { name: 'Q1 \'23', value: 52857 },
        { name: 'Q2 \'23', value: 56189 },
        { name: 'Q3 \'23', value: 61858 },
        { name: 'Q4 \'23', value: 62020 }
      ]
    ],
    colors: ['#3B82F6', '#EF4444'],
    showValues: true,
    showLegend: true,
    showGrid: true,
    backgroundColor: '#FFFFFF',
    textColor: '#000000',
    width: 800,
    height: 400,
    legendData: ['Apple Revenue (Millions)', 'Microsoft Revenue (Millions)'],
    brandText: 'Made with Osis.co'
  };

  // Multi-company example (like advertising revenue)
  const multiCompanyStructure: ChartConfig = {
    title: '✅ PERFECT: Multi-Company Advertising Revenue Style',
    type: 'comparison-bar',
    data: [
      [
        { name: 'Q3 \'20', value: 26.3 },
        { name: 'Q4 \'20', value: 31.9 },
        { name: 'Q1 \'21', value: 31.9 },
        { name: 'Q2 \'21', value: 35.8 }
      ],
      [
        { name: 'Q3 \'20', value: 5.0 },
        { name: 'Q4 \'20', value: 6.9 },
        { name: 'Q1 \'21', value: 6.0 },
        { name: 'Q2 \'21', value: 7.0 }
      ],
      [
        { name: 'Q3 \'20', value: 21.2 },
        { name: 'Q4 \'20', value: 28.1 },
        { name: 'Q1 \'21', value: 25.4 },
        { name: 'Q2 \'21', value: 28.6 }
      ],
      [
        { name: 'Q3 \'20', value: 6.8 },
        { name: 'Q4 \'20', value: 9.7 },
        { name: 'Q1 \'21', value: 6.9 },
        { name: 'Q2 \'21', value: 7.9 }
      ]
    ],
    colors: ['#3B82F6', '#EF4444', '#10B981', '#F59E0B'],
    showValues: true,
    showLegend: true,
    showGrid: true,
    backgroundColor: '#FFFFFF',
    textColor: '#000000',
    width: 1000,
    height: 500,
    legendData: [
      'Google Search Revenue (Billions)',
      'YouTube Ads Revenue (Billions)',
      'Meta Advertising Revenue (Billions)',
      'Amazon Advertising Revenue (Billions)'
    ],
    brandText: 'Made with Osis.co'
  };

  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Comparison Chart Structure Test</h1>
        <p className="text-gray-600">
          Testing the difference between wrong and right data structures
        </p>
      </div>

      <Card className="p-6 border-red-200 bg-red-50">
        <h3 className="text-lg font-semibold text-red-800 mb-4">❌ Wrong Structure</h3>
        <p className="text-red-600 mb-4">
          This creates separate bars for each company (what you saw in the screenshot)
        </p>
        <SocialMediaChartGenerator config={wrongStructure} />
      </Card>

      <Card className="p-6 border-green-200 bg-green-50">
        <h3 className="text-lg font-semibold text-green-800 mb-4">✅ Right Structure</h3>
        <p className="text-green-600 mb-4">
          This creates stacked bars by time period (like your examples)
        </p>
        <SocialMediaChartGenerator config={rightStructure} />
      </Card>

      <Card className="p-6 border-blue-200 bg-blue-50">
        <h3 className="text-lg font-semibold text-blue-800 mb-4">✅ Perfect: Multi-Company Style</h3>
        <p className="text-blue-600 mb-4">
          This matches your advertising revenue example exactly
        </p>
        <SocialMediaChartGenerator config={multiCompanyStructure} />
      </Card>

      <Card className="p-6 bg-gray-50">
        <h3 className="text-lg font-semibold mb-4">Key Differences</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div className="p-4 bg-red-100 rounded">
            <h4 className="font-medium text-red-800 mb-2">❌ Wrong Approach:</h4>
            <ul className="space-y-1 text-red-700">
              <li>X-axis: ["Apple", "Microsoft"]</li>
              <li>Creates separate bars</li>
              <li>Can't show time progression</li>
              <li>Not stackable</li>
            </ul>
          </div>
          <div className="p-4 bg-green-100 rounded">
            <h4 className="font-medium text-green-800 mb-2">✅ Right Approach:</h4>
            <ul className="space-y-1 text-green-700">
              <li>X-axis: ["Q1 '23", "Q2 '23", "Q3 '23"]</li>
              <li>Creates stacked bars per time period</li>
              <li>Shows progression over time</li>
              <li>Companies stack within each period</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ComparisonStructureTest;
