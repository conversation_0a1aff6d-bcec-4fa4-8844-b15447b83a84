import React from 'react';
import SocialMediaChartGenerator, { ChartConfig } from '../SocialMediaChartGenerator';

// Example 1: Simple Revenue Growth Chart
export const RevenueGrowthExample: React.FC = () => {
  const config: ChartConfig = {
    title: 'Company Revenue Growth',
    subtitle: 'Quarterly Revenue (in millions)',
    type: 'bar',
    data: [
      { name: 'Q1 \'23', value: 850 },
      { name: 'Q2 \'23', value: 920 },
      { name: 'Q3 \'23', value: 1050 },
      { name: 'Q4 \'23', value: 1180 },
      { name: 'Q1 \'24', value: 1320 },
      { name: 'Q2 \'24', value: 1450 }
    ],
    colors: ['#3B82F6'],
    showValues: true,
    showGrid: true,
    backgroundColor: '#FFFFFF',
    textColor: '#000000',
    brandText: 'Made with Osis',
    totalChange: '70.6%',
    cagr: '23.5%',
    valueFormatter: (value) => `$${value}M`
  };

  return <SocialMediaChartGenerator config={config} />;
};

// Example 2: Market Share Stacked Chart
export const MarketShareExample: React.FC = () => {
  const config: ChartConfig = {
    title: 'Cloud Computing Market Share',
    subtitle: 'Revenue by Provider (Billions)',
    type: 'stacked-bar',
    data: [
      [
        { name: 'Q1 \'22', value: 18.4 },
        { name: 'Q2 \'22', value: 19.7 },
        { name: 'Q3 \'22', value: 20.5 },
        { name: 'Q4 \'22', value: 21.4 },
        { name: 'Q1 \'23', value: 22.5 },
        { name: 'Q2 \'23', value: 23.8 }
      ],
      [
        { name: 'Q1 \'22', value: 13.5 },
        { name: 'Q2 \'22', value: 14.2 },
        { name: 'Q3 \'22', value: 15.1 },
        { name: 'Q4 \'22', value: 15.8 },
        { name: 'Q1 \'23', value: 16.4 },
        { name: 'Q2 \'23', value: 17.2 }
      ],
      [
        { name: 'Q1 \'22', value: 7.8 },
        { name: 'Q2 \'22', value: 8.2 },
        { name: 'Q3 \'22', value: 8.9 },
        { name: 'Q4 \'22', value: 9.4 },
        { name: 'Q1 \'23', value: 10.1 },
        { name: 'Q2 \'23', value: 10.8 }
      ]
    ],
    colors: ['#FF9500', '#3B82F6', '#10B981'],
    showValues: true,
    showLegend: true,
    legendData: [
      'AWS - Cloud Revenue (Billions)',
      'Microsoft Azure - Cloud Revenue (Billions)',
      'Google Cloud - Cloud Revenue (Billions)'
    ],
    backgroundColor: '#FFFFFF',
    textColor: '#000000',
    brandText: 'Made with Osis',
    valueFormatter: (value) => `$${value.toFixed(1)}B`
  };

  return <SocialMediaChartGenerator config={config} />;
};

// Example 3: Cryptocurrency Performance
export const CryptoPerformanceExample: React.FC = () => {
  const config: ChartConfig = {
    title: 'Bitcoin Price Performance',
    subtitle: 'Monthly Average Price (USD)',
    type: 'bar',
    data: [
      { name: 'Jan \'24', value: 42500 },
      { name: 'Feb \'24', value: 51200 },
      { name: 'Mar \'24', value: 69800 },
      { name: 'Apr \'24', value: 63400 },
      { name: 'May \'24', value: 67900 },
      { name: 'Jun \'24', value: 61200 },
      { name: 'Jul \'24', value: 66800 },
      { name: 'Aug \'24', value: 59300 },
      { name: 'Sep \'24', value: 63700 },
      { name: 'Oct \'24', value: 69200 },
      { name: 'Nov \'24', value: 87400 },
      { name: 'Dec \'24', value: 95600 }
    ],
    colors: ['#F7931A'], // Bitcoin orange
    showValues: true,
    showGrid: true,
    backgroundColor: '#FFFFFF',
    textColor: '#000000',
    brandText: 'Made with Osis',
    totalChange: '125.0%',
    cagr: '125.0%',
    valueFormatter: (value) => `$${(value / 1000).toFixed(0)}K`
  };

  return <SocialMediaChartGenerator config={config} />;
};

// Example 4: Economic Indicator
export const UnemploymentRateExample: React.FC = () => {
  const config: ChartConfig = {
    title: 'US Unemployment Rate',
    subtitle: 'Monthly Rate (%)',
    type: 'bar',
    data: [
      { name: 'Jan \'24', value: 3.7 },
      { name: 'Feb \'24', value: 3.9 },
      { name: 'Mar \'24', value: 3.8 },
      { name: 'Apr \'24', value: 3.9 },
      { name: 'May \'24', value: 4.0 },
      { name: 'Jun \'24', value: 4.0 },
      { name: 'Jul \'24', value: 4.3 },
      { name: 'Aug \'24', value: 4.2 },
      { name: 'Sep \'24', value: 4.1 },
      { name: 'Oct \'24', value: 4.1 },
      { name: 'Nov \'24', value: 4.2 },
      { name: 'Dec \'24', value: 4.1 }
    ],
    colors: ['#EF4444'], // Red for unemployment
    showValues: true,
    showGrid: true,
    backgroundColor: '#FFFFFF',
    textColor: '#000000',
    brandText: 'Made with Osis',
    valueFormatter: (value) => `${value.toFixed(1)}%`
  };

  return <SocialMediaChartGenerator config={config} />;
};

// Example 5: Sector Performance Comparison
export const SectorPerformanceExample: React.FC = () => {
  const config: ChartConfig = {
    title: 'S&P 500 Sector Performance',
    subtitle: 'YTD Returns (%)',
    type: 'bar',
    data: [
      { name: 'Technology', value: 28.5 },
      { name: 'Communication', value: 22.1 },
      { name: 'Consumer Disc.', value: 18.7 },
      { name: 'Industrials', value: 15.3 },
      { name: 'Financials', value: 12.8 },
      { name: 'Healthcare', value: 8.9 },
      { name: 'Materials', value: 6.4 },
      { name: 'Consumer Staples', value: 4.2 },
      { name: 'Utilities', value: -2.1 },
      { name: 'Real Estate', value: -5.8 },
      { name: 'Energy', value: -8.3 }
    ],
    colors: ['#10B981'], // Green for positive performance
    showValues: true,
    showGrid: true,
    backgroundColor: '#FFFFFF',
    textColor: '#000000',
    brandText: 'Made with Osis',
    valueFormatter: (value) => `${value > 0 ? '+' : ''}${value.toFixed(1)}%`
  };

  return <SocialMediaChartGenerator config={config} />;
};

// Demo component that shows all examples
export const AllExamples: React.FC = () => {
  return (
    <div className="space-y-8 p-6">
      <h1 className="text-2xl font-bold text-center mb-8">Chart Generator Examples</h1>
      
      <div className="space-y-8">
        <div>
          <h2 className="text-xl font-semibold mb-4">1. Revenue Growth Chart</h2>
          <RevenueGrowthExample />
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-4">2. Market Share (Stacked)</h2>
          <MarketShareExample />
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-4">3. Cryptocurrency Performance</h2>
          <CryptoPerformanceExample />
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-4">4. Economic Indicator</h2>
          <UnemploymentRateExample />
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-4">5. Sector Performance</h2>
          <SectorPerformanceExample />
        </div>
      </div>
    </div>
  );
};
