import React from 'react';
import SocialMediaChartGenerator, { ChartConfig, ChartDataPoint } from './SocialMediaChartGenerator';

// Template configurations matching the example charts

export const createBrokerageAgentsTemplate = (data: ChartDataPoint[]): ChartConfig => ({
  title: 'The Real Brokerage - Total Number of Agents',
  type: 'bar',
  data: data,
  colors: ['#4B5563'], // Gray bars like in the example
  showValues: true,
  showGrid: false,
  backgroundColor: '#FFFFFF',
  textColor: '#000000',
  width: 800,
  height: 600,
  valueFormatter: (value: number) => {
    if (value >= 1000) {
      return `${(value / 1000).toFixed(0)},${(value % 1000).toString().padStart(3, '0')}`;
    }
    return value.toString();
  },
  brandText: 'Powered by Fiscal.ai',
  totalChange: '1995.94%',
  cagr: '96.6%'
});

export const createAdvertisingRevenueTemplate = (
  googleData: ChartDataPoint[],
  metaData: ChartDataPoint[],
  amazonData: ChartDataPoint[],
  youtubeData: ChartDataPoint[]
): ChartConfig => ({
  title: 'Advertising Revenue (Google Search, Meta, Amazon, & YouTube)',
  type: 'stacked-bar',
  data: [googleData, metaData, amazonData, youtubeData],
  colors: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'], // Blue, Green, Yellow, Red
  showValues: true,
  showGrid: false,
  backgroundColor: '#FFFFFF',
  textColor: '#000000',
  width: 800,
  height: 600,
  legendData: [
    'GOOGL - Google Search and Other Revenue (LTM) (Billions)',
    'META - Advertising Revenue (LTM) (Billions)', 
    'AMZN - Advertising Services Revenue (LTM) (Billions)',
    'GOOGL - YouTube Ads Revenue (LTM) (Billions)'
  ],
  valueFormatter: (value: number) => value.toString(),
  brandText: 'Powered by Fiscal.ai'
});

export const createRevenueByCategory = (
  productsData: ChartDataPoint[],
  healthData: ChartDataPoint[],
  financialData: ChartDataPoint[],
  communicationsData: ChartDataPoint[],
  resourcesData: ChartDataPoint[]
): ChartConfig => ({
  title: 'Accenture - Revenue by Category',
  type: 'stacked-bar',
  data: [productsData, healthData, financialData, communicationsData, resourcesData],
  colors: ['#8B5CF6', '#3B82F6', '#F59E0B', '#10B981', '#EF4444'], // Purple, Blue, Orange, Green, Red
  showValues: true,
  showGrid: false,
  backgroundColor: '#FFFFFF',
  textColor: '#000000',
  width: 800,
  height: 600,
  legendData: [
    'Products Revenue (LTM) (Billions)',
    'Health & Public Service Revenue (LTM) (Billions)',
    'Financial Services Revenue (LTM) (Billions)',
    'Communications, Media & Technology Revenue (LTM) (Billions)',
    'Resources Revenue (LTM) (Billions)'
  ],
  valueFormatter: (value: number) => value.toFixed(1),
  brandText: 'Powered by Fiscal.ai'
});

export const createNetIncomeTemplate = (data: ChartDataPoint[]): ChartConfig => ({
  title: 'NVIDIA - Net Income',
  type: 'bar',
  data: data,
  colors: ['#22C55E'], // Green gradient like NVIDIA
  showValues: true,
  showGrid: false,
  backgroundColor: '#FFFFFF',
  textColor: '#000000',
  width: 800,
  height: 600,
  valueFormatter: (value: number) => value.toString(),
  brandText: 'Powered by FinChat',
  totalChange: '14495.82%',
  cagr: '66.6%'
});

export const createComparisonTemplate = (
  series1Data: ChartDataPoint[],
  series2Data: ChartDataPoint[],
  title: string = 'Company Comparison',
  series1Name: string = 'Company A',
  series2Name: string = 'Company B'
): ChartConfig => ({
  title: title,
  type: 'comparison-bar',
  data: [series1Data, series2Data],
  colors: ['#3B82F6', '#EF4444'], // Blue and Red for comparison
  showValues: true,
  showLegend: true,
  showGrid: true,
  backgroundColor: '#FFFFFF',
  textColor: '#000000',
  width: 800,
  height: 600,
  legendData: [series1Name, series2Name],
  valueFormatter: (value: number) => {
    if (value >= 1000000000) {
      return `${(value / 1000000000).toFixed(1)}B`;
    } else if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return `${value}`;
  },
  brandText: 'Made with Osis.co'
});

// Multi-series comparison template (like the advertising revenue example)
export const createMultiSeriesComparisonTemplate = (
  seriesData: ChartDataPoint[][],
  title: string,
  seriesNames: string[]
): ChartConfig => ({
  title: title,
  type: 'comparison-bar',
  data: seriesData,
  colors: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'], // Multiple distinct colors
  showValues: true,
  showLegend: true,
  showGrid: true,
  backgroundColor: '#FFFFFF',
  textColor: '#000000',
  width: 1000,
  height: 700,
  legendData: seriesNames,
  valueFormatter: (value: number) => {
    if (value >= 1000000000) {
      return `${(value / 1000000000).toFixed(1)}B`;
    } else if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return `${value}`;
  },
  brandText: 'Made with Osis.co'
});

// Utility functions to create sample data

export const createSampleBrokerageData = (): ChartDataPoint[] => [
  { name: 'Sep \'20', value: 1282 },
  { name: 'Dec \'20', value: 1475 },
  { name: 'Mar \'21', value: 1895 },
  { name: 'Jun \'21', value: 2451 },
  { name: 'Sep \'21', value: 2975 },
  { name: 'Dec \'21', value: 3850 },
  { name: 'Mar \'22', value: 4500 },
  { name: 'Jun \'22', value: 5600 },
  { name: 'Sep \'22', value: 6700 },
  { name: 'Dec \'22', value: 8200 },
  { name: 'Mar \'23', value: 10000 },
  { name: 'Jun \'23', value: 11500 },
  { name: 'Sep \'23', value: 12175 },
  { name: 'Dec \'23', value: 13650 },
  { name: 'Mar \'24', value: 16680 },
  { name: 'Jun \'24', value: 19540 },
  { name: 'Sep \'24', value: 21770 },
  { name: 'Dec \'24', value: 24140 },
  { name: 'Mar \'25', value: 26870 }
];

export const createSampleAdvertisingData = () => ({
  google: [
    { name: 'Q3 \'20', value: 90 },
    { name: 'Q4 \'20', value: 104 },
    { name: 'Q1 \'21', value: 111 },
    { name: 'Q2 \'21', value: 126 },
    { name: 'Q3 \'21', value: 138 },
    { name: 'Q4 \'21', value: 149 },
    { name: 'Q1 \'22', value: 157 },
    { name: 'Q2 \'22', value: 162 },
    { name: 'Q3 \'22', value: 163 },
    { name: 'Q4 \'22', value: 162 },
    { name: 'Q1 \'23', value: 163 },
    { name: 'Q2 \'23', value: 166 },
    { name: 'Q3 \'23', value: 170 },
    { name: 'Q4 \'23', value: 175 },
    { name: 'Q1 \'24', value: 181 },
    { name: 'Q2 \'24', value: 187 },
    { name: 'Q3 \'24', value: 192 },
    { name: 'Q4 \'24', value: 198 },
    { name: 'Q1 \'25', value: 203 }
  ],
  meta: [
    { name: 'Q3 \'20', value: 102 },
    { name: 'Q4 \'20', value: 109 },
    { name: 'Q1 \'21', value: 115 },
    { name: 'Q2 \'21', value: 116 },
    { name: 'Q3 \'21', value: 116 },
    { name: 'Q4 \'21', value: 115 },
    { name: 'Q1 \'22', value: 114 },
    { name: 'Q2 \'22', value: 115 },
    { name: 'Q3 \'22', value: 118 },
    { name: 'Q4 \'22', value: 124 },
    { name: 'Q1 \'23', value: 132 },
    { name: 'Q2 \'23', value: 139 },
    { name: 'Q3 \'23', value: 146 },
    { name: 'Q4 \'23', value: 153 },
    { name: 'Q1 \'24', value: 161 },
    { name: 'Q2 \'24', value: 166 }
  ],
  amazon: [
    { name: 'Q3 \'20', value: 26 },
    { name: 'Q4 \'20', value: 27 },
    { name: 'Q1 \'21', value: 29 },
    { name: 'Q2 \'21', value: 31 },
    { name: 'Q3 \'21', value: 33 },
    { name: 'Q4 \'21', value: 34 },
    { name: 'Q1 \'22', value: 36 },
    { name: 'Q2 \'22', value: 38 },
    { name: 'Q3 \'22', value: 39 },
    { name: 'Q4 \'22', value: 41 },
    { name: 'Q1 \'23', value: 44 },
    { name: 'Q2 \'23', value: 47 },
    { name: 'Q3 \'23', value: 49 },
    { name: 'Q4 \'23', value: 51 },
    { name: 'Q1 \'24', value: 54 },
    { name: 'Q2 \'24', value: 56 },
    { name: 'Q3 \'24', value: 58 }
  ],
  youtube: [
    { name: 'Q3 \'20', value: 15 },
    { name: 'Q4 \'20', value: 22 },
    { name: 'Q1 \'21', value: 25 },
    { name: 'Q2 \'21', value: 27 },
    { name: 'Q3 \'21', value: 30 },
    { name: 'Q4 \'21', value: 30 },
    { name: 'Q1 \'22', value: 32 },
    { name: 'Q2 \'22', value: 29 },
    { name: 'Q3 \'22', value: 28 },
    { name: 'Q4 \'22', value: 29 },
    { name: 'Q1 \'23', value: 30 },
    { name: 'Q2 \'23', value: 32 },
    { name: 'Q3 \'23', value: 33 },
    { name: 'Q4 \'23', value: 34 },
    { name: 'Q1 \'24', value: 35 },
    { name: 'Q2 \'24', value: 36 },
    { name: 'Q3 \'24', value: 37 }
  ]
});

export const createSampleNvidiaData = (): ChartDataPoint[] => [
  { name: 'Q2 \'15', value: 1 },
  { name: 'Q3 \'15', value: 1 },
  { name: 'Q4 \'15', value: 1 },
  { name: 'Q1 \'16', value: 1 },
  { name: 'Q2 \'16', value: 1 },
  { name: 'Q3 \'16', value: 1 },
  { name: 'Q4 \'16', value: 2 },
  { name: 'Q1 \'17', value: 2 },
  { name: 'Q2 \'17', value: 2 },
  { name: 'Q3 \'17', value: 3 },
  { name: 'Q4 \'17', value: 3 },
  { name: 'Q1 \'18', value: 4 },
  { name: 'Q2 \'18', value: 4 },
  { name: 'Q3 \'18', value: 5 },
  { name: 'Q4 \'18', value: 4 },
  { name: 'Q1 \'19', value: 3 },
  { name: 'Q2 \'19', value: 3 },
  { name: 'Q3 \'19', value: 2 },
  { name: 'Q4 \'19', value: 3 },
  { name: 'Q1 \'20', value: 3 },
  { name: 'Q2 \'20', value: 4 },
  { name: 'Q3 \'20', value: 4 },
  { name: 'Q4 \'20', value: 5 },
  { name: 'Q1 \'21', value: 7 },
  { name: 'Q2 \'21', value: 8 },
  { name: 'Q3 \'21', value: 10 },
  { name: 'Q4 \'21', value: 9 },
  { name: 'Q1 \'22', value: 8 },
  { name: 'Q2 \'22', value: 6 },
  { name: 'Q3 \'22', value: 4 },
  { name: 'Q4 \'22', value: 5 },
  { name: 'Q1 \'23', value: 10 },
  { name: 'Q2 \'23', value: 19 },
  { name: 'Q3 \'23', value: 30 },
  { name: 'Q4 \'23', value: 43 },
  { name: 'Q1 \'24', value: 53 },
  { name: 'Q2 \'24', value: 63 },
  { name: 'Q3 \'24', value: 73 },
  { name: 'Q4 \'24', value: 77 }
];

// Template component examples
export const BrokerageAgentsChart: React.FC = () => {
  const data = createSampleBrokerageData();
  const config = createBrokerageAgentsTemplate(data);
  
  return <SocialMediaChartGenerator config={config} />;
};

export const AdvertisingRevenueChart: React.FC = () => {
  const { google, meta, amazon, youtube } = createSampleAdvertisingData();
  const config = createAdvertisingRevenueTemplate(google, meta, amazon, youtube);
  
  return <SocialMediaChartGenerator config={config} />;
};

export const NvidiaNetIncomeChart: React.FC = () => {
  const data = createSampleNvidiaData();
  const config = createNetIncomeTemplate(data);

  return <SocialMediaChartGenerator config={config} />;
};

export const createSampleComparisonData = (): {
  appleData: ChartDataPoint[],
  microsoftData: ChartDataPoint[]
} => ({
  appleData: [
    { name: 'Q1 \'23', value: 117154 },
    { name: 'Q2 \'23', value: 81797 },
    { name: 'Q3 \'23', value: 89498 },
    { name: 'Q4 \'23', value: 119575 },
    { name: 'Q1 \'24', value: 119575 },
    { name: 'Q2 \'24', value: 85777 },
    { name: 'Q3 \'24', value: 94933 }
  ],
  microsoftData: [
    { name: 'Q1 \'23', value: 52857 },
    { name: 'Q2 \'23', value: 56189 },
    { name: 'Q3 \'23', value: 61858 },
    { name: 'Q4 \'23', value: 62020 },
    { name: 'Q1 \'24', value: 61858 },
    { name: 'Q2 \'24', value: 64728 },
    { name: 'Q3 \'24', value: 65585 }
  ]
});

// Sample data similar to the advertising revenue example
export const createSampleAdvertisingComparisonData = (): {
  googleSearch: ChartDataPoint[],
  googleYouTube: ChartDataPoint[],
  meta: ChartDataPoint[],
  amazon: ChartDataPoint[]
} => ({
  googleSearch: [
    { name: 'Q3 \'20', value: 26.3 },
    { name: 'Q4 \'20', value: 31.9 },
    { name: 'Q1 \'21', value: 31.9 },
    { name: 'Q2 \'21', value: 35.8 },
    { name: 'Q3 \'21', value: 37.9 },
    { name: 'Q4 \'21', value: 43.3 },
    { name: 'Q1 \'22', value: 39.6 },
    { name: 'Q2 \'22', value: 40.7 },
    { name: 'Q3 \'22', value: 39.5 },
    { name: 'Q4 \'22', value: 42.6 }
  ],
  googleYouTube: [
    { name: 'Q3 \'20', value: 5.0 },
    { name: 'Q4 \'20', value: 6.9 },
    { name: 'Q1 \'21', value: 6.0 },
    { name: 'Q2 \'21', value: 7.0 },
    { name: 'Q3 \'21', value: 7.2 },
    { name: 'Q4 \'21', value: 8.6 },
    { name: 'Q1 \'22', value: 6.9 },
    { name: 'Q2 \'22', value: 7.3 },
    { name: 'Q3 \'22', value: 7.1 },
    { name: 'Q4 \'22', value: 7.9 }
  ],
  meta: [
    { name: 'Q3 \'20', value: 21.2 },
    { name: 'Q4 \'20', value: 28.1 },
    { name: 'Q1 \'21', value: 25.4 },
    { name: 'Q2 \'21', value: 28.6 },
    { name: 'Q3 \'21', value: 28.3 },
    { name: 'Q4 \'21', value: 32.6 },
    { name: 'Q1 \'22', value: 27.9 },
    { name: 'Q2 \'22', value: 28.8 },
    { name: 'Q3 \'22', value: 27.7 },
    { name: 'Q4 \'22', value: 31.3 }
  ],
  amazon: [
    { name: 'Q3 \'20', value: 6.8 },
    { name: 'Q4 \'20', value: 9.7 },
    { name: 'Q1 \'21', value: 6.9 },
    { name: 'Q2 \'21', value: 7.9 },
    { name: 'Q3 \'21', value: 9.6 },
    { name: 'Q4 \'21', value: 9.7 },
    { name: 'Q1 \'22', value: 7.9 },
    { name: 'Q2 \'22', value: 8.8 },
    { name: 'Q3 \'22', value: 9.5 },
    { name: 'Q4 \'22', value: 11.3 }
  ]
});

export const ComparisonChart: React.FC = () => {
  const { appleData, microsoftData } = createSampleComparisonData();
  const config = createComparisonTemplate(
    appleData,
    microsoftData,
    'Apple vs Microsoft - Quarterly Revenue Over Time',
    'Apple Revenue (Millions)',
    'Microsoft Revenue (Millions)'
  );

  return <SocialMediaChartGenerator config={config} />;
};

export const AdvertisingComparisonChart: React.FC = () => {
  const { googleSearch, googleYouTube, meta, amazon } = createSampleAdvertisingComparisonData();
  const config = createMultiSeriesComparisonTemplate(
    [googleSearch, googleYouTube, meta, amazon],
    'Advertising Revenue (Google Search, Meta, Amazon, & YouTube)',
    [
      'Google Search Revenue (Billions)',
      'YouTube Ads Revenue (Billions)',
      'Meta Advertising Revenue (Billions)',
      'Amazon Advertising Revenue (Billions)'
    ]
  );

  return <SocialMediaChartGenerator config={config} />;
};
