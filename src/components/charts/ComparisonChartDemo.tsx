import React from 'react';
import { Card } from '@/components/ui/card';
import SocialMediaChartGenerator from './SocialMediaChartGenerator';
import {
  createComparisonTemplate,
  createSampleComparisonData,
  createMultiSeriesComparisonTemplate,
  createSampleAdvertisingComparisonData
} from './ChartTemplates';

const ComparisonChartDemo: React.FC = () => {
  // Sample data for Apple vs Microsoft comparison
  const { appleData, microsoftData } = createSampleComparisonData();
  
  // Create different comparison chart configurations
  const revenueComparison = createComparisonTemplate(
    appleData,
    microsoftData,
    'Apple vs Microsoft - Quarterly Revenue 2023',
    'Apple Revenue',
    'Microsoft Revenue'
  );

  // Example with different colors
  const colorVariantComparison = {
    ...revenueComparison,
    title: 'Revenue Comparison - Different Colors',
    colors: ['#007AFF', '#FF3B30'] // Apple blue and red
  };

  // Example with multiple series (like advertising revenue)
  const { googleSearch, googleYouTube, meta, amazon } = createSampleAdvertisingComparisonData();
  const multiSeriesComparison = createMultiSeriesComparisonTemplate(
    [googleSearch, googleYouTube, meta, amazon],
    'Multi-Company Advertising Revenue Comparison',
    ['Google Search', 'YouTube Ads', 'Meta Advertising', 'Amazon Advertising']
  );

  // Example with custom styling
  const customStyledComparison = {
    ...revenueComparison,
    title: 'Revenue Comparison - Custom Style',
    backgroundColor: '#1a1a1a',
    textColor: '#ffffff',
    barGap: '-40%',
    colors: ['#00D4AA', '#FF6B6B'], // Teal and coral
    brandText: 'Made with Osis.co'
  };

  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Comparison Bar Chart Demo</h1>
        <p className="text-gray-600">
          Stacked bar charts for comparing data between different series
        </p>
      </div>

      <div className="grid gap-8">
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Standard Stacked Comparison Chart</h3>
          <p className="text-sm text-gray-600 mb-4">
            Two-series stacked comparison with legend and value labels
          </p>
          <SocialMediaChartGenerator config={revenueComparison} />
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Different Color Scheme</h3>
          <p className="text-sm text-gray-600 mb-4">
            Same data with Apple brand colors (blue and red)
          </p>
          <SocialMediaChartGenerator config={colorVariantComparison} />
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Multi-Series Stacked Chart</h3>
          <p className="text-sm text-gray-600 mb-4">
            Four companies stacked together (similar to advertising revenue example)
          </p>
          <SocialMediaChartGenerator config={multiSeriesComparison} />
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Dark Theme with Custom Colors</h3>
          <p className="text-sm text-gray-600 mb-4">
            Dark background with custom color scheme and branding
          </p>
          <SocialMediaChartGenerator config={customStyledComparison} />
        </Card>
      </div>

      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Configuration Options</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <h4 className="font-medium mb-2">Stacked Chart Features:</h4>
            <ul className="space-y-1 text-gray-600">
              <li><code>stack: 'comparison'</code> - Creates stacked bars</li>
              <li><code>position: 'inside'</code> - Labels inside bars</li>
              <li><code>legend: bottom</code> - Legend at bottom</li>
              <li><code>tooltip: enhanced</code> - Shows totals</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium mb-2">Data Structure:</h4>
            <ul className="space-y-1 text-gray-600">
              <li><code>type: 'comparison-bar'</code></li>
              <li><code>data: [series1[], series2[]]</code></li>
              <li><code>legendData: ['Series 1', 'Series 2']</code></li>
              <li><code>showLegend: true</code></li>
            </ul>
          </div>
        </div>
      </Card>

      <Card className="p-6 bg-gray-50">
        <h3 className="text-lg font-semibold mb-4">Expected Behavior</h3>
        <div className="space-y-2 text-sm text-gray-700">
          <p><strong>✓ Comparison Keywords:</strong> When you use words like "vs", "versus", "compare", "comparison", "against", the AI should detect this and use the "comparison-bar" chart type.</p>
          <p><strong>✓ Multiple Data Series:</strong> The AI should generate data as an array of arrays for comparison charts.</p>
          <p><strong>✓ Legend Support:</strong> Comparison charts should include legendData and showLegend: true.</p>
          <p><strong>✓ Stacked Bars:</strong> The chart creates stacked bars where each series stacks on top of others.</p>
          <p><strong>✓ Contrasting Colors:</strong> Different colors should be used for each series being compared.</p>
          <p><strong>✓ Enhanced Tooltips:</strong> Tooltips show individual values and total sums.</p>
        </div>
      </Card>
    </div>
  );
};

export default ComparisonChartDemo;
