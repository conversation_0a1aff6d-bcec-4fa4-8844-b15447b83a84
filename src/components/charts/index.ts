// Social Media Chart Generator System
export { default as SocialMediaChartGenerator } from './SocialMediaChartGenerator';
export type { ChartConfig, ChartDataPoint } from './SocialMediaChartGenerator';

// Chart Templates
export {
  default as ChartTemplates,
  BrokerageAgentsChart,
  AdvertisingRevenueChart,
  NvidiaNetIncomeChart,
  ComparisonChart,
  AdvertisingComparisonChart,
  createBrokerageAgentsTemplate,
  createAdvertisingRevenueTemplate,
  createRevenueByCategory,
  createNetIncomeTemplate,
  createComparisonTemplate,
  createMultiSeriesComparisonTemplate,
  createSampleBrokerageData,
  createSampleAdvertisingData,
  createSampleNvidiaData,
  createSampleComparisonData,
  createSampleAdvertisingComparisonData
} from './ChartTemplates';

// Builder Components
export { default as EconomicChartBuilder } from './EconomicChartBuilder';

// Demo Components
export { default as ComparisonChartDemo } from './ComparisonChartDemo';
export { default as AIComparisonTest } from './AIComparisonTest';

// Existing chart components
export { default as AdvancedStockChart } from './AdvancedStockChart';
export { default as CleanCandlestickChart } from './CleanCandlestickChart';
export { default as FallbackChart } from './FallbackChart';
export { default as FocusedCandlestickChart } from './FocusedCandlestickChart';
export { default as InfiniteCandlestickChart } from './InfiniteCandlestickChart';
export { default as MiniAgentChart } from './MiniAgentChart';
export { default as RobustTradingViewChart } from './RobustTradingViewChart';
export { default as SimpleTradingViewChart } from './SimpleTradingViewChart';
