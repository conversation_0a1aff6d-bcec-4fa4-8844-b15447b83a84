# Social Media Chart Generator

A comprehensive charting system designed to generate professional, social media-ready charts from economic and fundamental stock data. This system is built for internal use to create visual content that matches industry-standard designs like Fiscal.ai and FinChat.

## Features

- **Professional Templates**: Pre-built chart templates matching industry standards
- **Real Financial Data**: Integration with Polygon API for live company financials
- **Custom Styling**: Full control over colors, fonts, branding, and visual elements
- **Export Ready**: High-resolution exports optimized for social media platforms
- **Multiple Chart Types**: Bar charts, stacked bar charts, line charts, and area charts

## Components

### Core Components

#### `SocialMediaChartGenerator`
The main chart generation component that handles rendering and exporting.

**Props:**
- `config: ChartConfig` - Chart configuration object
- `onExport?: (imageData: string) => void` - Callback when chart is exported

#### `ChartTemplates`
Pre-built templates matching the example charts provided:
- Brokerage Agents Growth Chart
- Advertising Revenue Stacked Chart
- NVIDIA Net Income Chart
- Accenture Revenue by Category

#### `EconomicChartBuilder`
Minimal interface that fetches real financial data from Polygon API and generates charts automatically. Features a clean, streamlined design focused on economic data visualization.

### Services

#### `economicDataService`
Service for fetching and processing economic and financial data:
- Company financials from Polygon API
- Dividend data
- Financial ratio calculations
- Growth rate calculations

## Usage

### Basic Usage

```tsx
import { SocialMediaChartGenerator, ChartConfig } from '@/components/charts';

const config: ChartConfig = {
  title: 'Sample Revenue Chart',
  type: 'bar',
  data: [
    { name: 'Q1 2023', value: 1000 },
    { name: 'Q2 2023', value: 1200 },
    { name: 'Q3 2023', value: 1500 },
    { name: 'Q4 2023', value: 1800 }
  ],
  showValues: true,
  brandText: 'Made with Osis'
};

<SocialMediaChartGenerator 
  config={config}
  onExport={(imageData) => console.log('Chart exported:', imageData)}
/>
```

### Using Templates

```tsx
import { BrokerageAgentsChart, createNetIncomeTemplate } from '@/components/charts';

// Use pre-built component
<BrokerageAgentsChart />

// Or create custom template
const data = [/* your data */];
const config = createNetIncomeTemplate(data);
<SocialMediaChartGenerator config={config} />
```

### Creating Comparison Charts

```tsx
import { createComparisonTemplate } from '@/components/charts';

const series1Data = [
  { name: 'Q1', value: 1200 },
  { name: 'Q2', value: 1350 },
  { name: 'Q3', value: 1500 },
  { name: 'Q4', value: 1650 }
];

const series2Data = [
  { name: 'Q1', value: 980 },
  { name: 'Q2', value: 1100 },
  { name: 'Q3', value: 1250 },
  { name: 'Q4', value: 1400 }
];

const config = createComparisonTemplate(
  series1Data,
  series2Data,
  'Revenue Comparison',
  'Company A',
  'Company B'
);

<SocialMediaChartGenerator config={config} />
```

### Fetching Real Data

```tsx
import { fetchCompanyFinancials } from '@/services/economicDataService';

const financials = await fetchCompanyFinancials('AAPL');
// Use financials.revenue, financials.netIncome, etc.
```

## Chart Configuration

### ChartConfig Interface

```typescript
interface ChartConfig {
  title: string;
  subtitle?: string;
  type: 'bar' | 'stacked-bar' | 'line' | 'area' | 'comparison-bar';
  data: ChartDataPoint[] | ChartDataPoint[][];
  colors?: string[];
  showValues?: boolean;
  showLegend?: boolean;
  showGrid?: boolean;
  backgroundColor?: string;
  textColor?: string;
  brandText?: string;
  width?: number;
  height?: number;
  valueFormatter?: (value: number) => string;
  legendData?: string[];
  totalChange?: string;
  cagr?: string;
  // Comparison chart specific options
  barWidth?: number;
  barGap?: string; // Gap between different series (e.g., '-30%' for overlap)
  categoryGap?: string; // Gap between categories
}
```

### ChartDataPoint Interface

```typescript
interface ChartDataPoint {
  name: string;
  value: number;
  date?: string;
  category?: string;
  color?: string;
}
```

## Styling

The charts use a professional color palette by default:
- Primary Blue: `#3B82F6`
- Success Green: `#10B981`
- Warning Orange: `#F59E0B`
- Danger Red: `#EF4444`
- Purple: `#8B5CF6`
- Cyan: `#06B6D4`

Charts are designed to match the visual style of the provided examples with:
- Clean, minimal design
- Professional typography (Inter font family)
- Subtle shadows and borders
- High contrast for readability
- Consistent spacing and alignment

## Export Options

Charts can be exported in multiple formats:
- **PNG**: High-resolution with transparency support
- **JPEG**: Compressed format for smaller file sizes
- **Clipboard**: Direct copy to system clipboard
- **Auto-download**: Automatic file download

All exports are generated at 2x resolution for crisp display on high-DPI screens and social media platforms.

## API Integration

The system integrates with Polygon API for real financial data:
- Company financials (revenue, net income, etc.)
- Dividend data
- Balance sheet information
- Cash flow statements

Requires `VITE_POLYGON_API_KEY` environment variable.

## Accessing the Chart Generator

The chart generator is available at `/chart-generator` route and provides:
- **Minimal Interface**: Clean, focused design for economic data visualization
- **Real Financial Data**: Fetches company financials from Polygon API
- **Multiple Metrics**: Revenue, net income, gross profit, operating income, total assets, total debt, and free cash flow
- **Professional Charts**: High-quality charts with export functionality

## Comparison Bar Charts

The system supports comparison bar charts with stacked bars of different colors, perfect for comparing metrics between different companies, time periods, or categories. This creates charts similar to the advertising revenue examples where multiple series are stacked on top of each other.

### Features

- **Stacked Bars**: Create visually striking comparisons with stacked bars
- **Multiple Data Series**: Support for 2 or more data series in a single chart
- **Smart Stacking**: Each series stacks on top of others to show totals and individual contributions
- **Legend Support**: Automatic legend generation for multiple series positioned at bottom
- **Color Coding**: Each series gets its own color for easy identification
- **Enhanced Tooltips**: Hover to see detailed information for each series plus total values
- **Inside Labels**: Value labels positioned inside bars for better readability

### Configuration Options

- `stack`: All series use the same stack name to create stacked effect
- `position`: Labels positioned 'inside' bars for stacked charts
- `legend`: Positioned at bottom with horizontal orientation
- `tooltip`: Enhanced to show individual values and totals

### Use Cases

- Company revenue comparisons (e.g., Apple vs Microsoft vs Google)
- Multi-platform advertising revenue (e.g., Google Search + YouTube + Meta + Amazon)
- Business segment analysis (e.g., Amazon's different revenue streams)
- Market share analysis across multiple competitors
- Performance metrics over time with multiple categories
- Product feature comparisons across different companies

## Examples

The system includes sample data and templates based on the provided examples:
- Real estate brokerage agent growth
- Tech company advertising revenue (stacked)
- NVIDIA net income growth
- Accenture revenue by business category
- Apple vs Microsoft revenue comparison (stacked bars)
- Multi-company advertising revenue comparison (Google, Meta, Amazon, YouTube)

All templates include proper formatting, growth calculations, and professional styling to match industry standards.
