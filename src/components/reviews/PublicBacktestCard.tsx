import React from 'react';
import { formatDistanceToNow } from 'date-fns';
import { TrendingUp, TrendingDown, Calendar, User, BarChart3 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { PublicBacktest } from '@/services/reviewService';

interface PublicBacktestCardProps {
  backtest: PublicBacktest;
}

const PublicBacktestCard: React.FC<PublicBacktestCardProps> = ({ backtest }) => {
  const { results, backtest_config, user_name, user_email, created_at } = backtest;

  const getUserName = () => {
    if (user_name) {
      return user_name;
    }
    if (user_email) {
      return user_email.split('@')[0];
    }
    return 'Anonymous User';
  };

  const formatPercentage = (value: number) => {
    const sign = value > 0 ? '+' : '';
    return `${sign}${value.toFixed(2)}%`;
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const getPerformanceColor = (value: number) => {
    return value > 0 ? 'text-green-400' : value < 0 ? 'text-red-400' : 'text-white/70';
  };

  return (
    <div className="bg-white/[0.02] border border-white/[0.08] rounded-lg p-4">
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <div className="w-6 h-6 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center text-white text-xs font-medium">
            {getUserName().charAt(0).toUpperCase()}
          </div>
          <div>
            <div className="text-white text-sm font-medium">{getUserName()}</div>
            <div className="text-white/60 text-xs flex items-center gap-1">
              <Calendar className="w-3 h-3" />
              {formatDistanceToNow(new Date(created_at), { addSuffix: true })}
            </div>
          </div>
        </div>
        <Badge className="bg-blue-500/20 text-blue-400 border border-blue-500/30 text-xs">
          <BarChart3 className="w-3 h-3 mr-1" />
          Public Backtest
        </Badge>
      </div>

      {/* Configuration Summary */}
      {backtest_config && (
        <div className="mb-3 p-2 bg-white/[0.02] rounded border border-white/[0.05]">
          <div className="text-white/70 text-xs font-medium mb-1">Configuration</div>
          <div className="grid grid-cols-2 gap-2 text-xs">
            {backtest_config.start_date && (
              <div>
                <span className="text-white/60">Period:</span>
                <span className="ml-1 text-white">
                  {new Date(backtest_config.start_date).getFullYear()} - {new Date(backtest_config.end_date || Date.now()).getFullYear()}
                </span>
              </div>
            )}
            {backtest_config.initial_capital && (
              <div>
                <span className="text-white/60">Capital:</span>
                <span className="ml-1 text-white">
                  {formatCurrency(backtest_config.initial_capital)}
                </span>
              </div>
            )}
            {backtest_config.symbols && (
              <div className="col-span-2">
                <span className="text-white/60">Symbols:</span>
                <span className="ml-1 text-white">
                  {Array.isArray(backtest_config.symbols) 
                    ? backtest_config.symbols.slice(0, 3).join(', ') + (backtest_config.symbols.length > 3 ? '...' : '')
                    : backtest_config.symbols}
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Performance Results */}
      <div className="grid grid-cols-2 gap-3">
        {results.total_return !== undefined && (
          <div className="text-center p-2 bg-white/[0.02] rounded border border-white/[0.05]">
            <div className={`text-lg font-semibold ${getPerformanceColor(results.total_return)}`}>
              {formatPercentage(results.total_return)}
            </div>
            <div className="text-white/60 text-xs">Total Return</div>
          </div>
        )}

        {results.sharpe_ratio !== undefined && (
          <div className="text-center p-2 bg-white/[0.02] rounded border border-white/[0.05]">
            <div className="text-lg font-semibold text-white">
              {results.sharpe_ratio.toFixed(2)}
            </div>
            <div className="text-white/60 text-xs">Sharpe Ratio</div>
          </div>
        )}

        {results.max_drawdown !== undefined && (
          <div className="text-center p-2 bg-white/[0.02] rounded border border-white/[0.05]">
            <div className="text-lg font-semibold text-red-400">
              {formatPercentage(results.max_drawdown)}
            </div>
            <div className="text-white/60 text-xs">Max Drawdown</div>
          </div>
        )}

        {results.win_rate !== undefined && (
          <div className="text-center p-2 bg-white/[0.02] rounded border border-white/[0.05]">
            <div className="text-lg font-semibold text-white">
              {formatPercentage(results.win_rate)}
            </div>
            <div className="text-white/60 text-xs">Win Rate</div>
          </div>
        )}

        {results.total_trades !== undefined && (
          <div className="text-center p-2 bg-white/[0.02] rounded border border-white/[0.05]">
            <div className="text-lg font-semibold text-white">
              {results.total_trades}
            </div>
            <div className="text-white/60 text-xs">Total Trades</div>
          </div>
        )}

        {results.profit_factor !== undefined && (
          <div className="text-center p-2 bg-white/[0.02] rounded border border-white/[0.05]">
            <div className={`text-lg font-semibold ${getPerformanceColor(results.profit_factor - 1)}`}>
              {results.profit_factor.toFixed(2)}
            </div>
            <div className="text-white/60 text-xs">Profit Factor</div>
          </div>
        )}
      </div>

      {/* Performance Indicator */}
      <div className="mt-3 pt-2 border-t border-white/[0.06]">
        <div className="flex items-center justify-center gap-2">
          {results.total_return > 0 ? (
            <>
              <TrendingUp className="w-4 h-4 text-green-400" />
              <span className="text-green-400 text-sm font-medium">Profitable Strategy</span>
            </>
          ) : results.total_return < 0 ? (
            <>
              <TrendingDown className="w-4 h-4 text-red-400" />
              <span className="text-red-400 text-sm font-medium">Loss-Making Strategy</span>
            </>
          ) : (
            <>
              <BarChart3 className="w-4 h-4 text-white/60" />
              <span className="text-white/60 text-sm font-medium">Neutral Performance</span>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default PublicBacktestCard;
