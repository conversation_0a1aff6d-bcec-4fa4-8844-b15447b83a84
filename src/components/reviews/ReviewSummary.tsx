import React from 'react';
import { Star } from 'lucide-react';
import StarRating from '@/components/ui/star-rating';
import { ReviewSummary as ReviewSummaryType } from '@/services/reviewService';

interface ReviewSummaryProps {
  summary: ReviewSummaryType;
  className?: string;
}

const ReviewSummary: React.FC<ReviewSummaryProps> = ({ summary, className = '' }) => {
  const { average_rating, total_reviews, rating_distribution } = summary;

  const getRatingPercentage = (rating: number) => {
    if (total_reviews === 0) return 0;
    return ((rating_distribution[rating.toString()] || 0) / total_reviews) * 100;
  };

  const formatRatingCount = (count: number) => {
    if (count === 0) return '0';
    if (count < 1000) return count.toString();
    if (count < 1000000) return `${(count / 1000).toFixed(1)}k`;
    return `${(count / 1000000).toFixed(1)}m`;
  };

  return (
    <div className={`bg-white/[0.02] border border-white/[0.08] rounded-lg p-4 ${className}`}>
      {/* Overall Rating */}
      <div className="flex items-center gap-4 mb-4">
        <div className="text-center">
          <div className="text-3xl font-bold text-white mb-1">
            {average_rating.toFixed(1)}
          </div>
          <StarRating rating={average_rating} size="md" showValue={false} />
          <div className="text-white/60 text-sm mt-1">
            {formatRatingCount(total_reviews)} {total_reviews === 1 ? 'review' : 'reviews'}
          </div>
        </div>

        {/* Rating Distribution */}
        <div className="flex-1">
          {[5, 4, 3, 2, 1].map((rating) => {
            const count = rating_distribution[rating.toString()] || 0;
            const percentage = getRatingPercentage(count);
            
            return (
              <div key={rating} className="flex items-center gap-2 mb-1">
                <div className="flex items-center gap-1 w-12">
                  <span className="text-white/80 text-sm">{rating}</span>
                  <Star className="w-3 h-3 text-yellow-400 fill-yellow-400" />
                </div>
                
                <div className="flex-1 bg-white/[0.05] rounded-full h-2 overflow-hidden">
                  <div
                    className="h-full bg-yellow-400 transition-all duration-300"
                    style={{ width: `${percentage}%` }}
                  />
                </div>
                
                <span className="text-white/60 text-xs w-8 text-right">
                  {count}
                </span>
              </div>
            );
          })}
        </div>
      </div>

      {/* Quick Stats */}
      {total_reviews > 0 && (
        <div className="grid grid-cols-3 gap-4 pt-3 border-t border-white/[0.06]">
          <div className="text-center">
            <div className="text-lg font-semibold text-white">
              {getRatingPercentage(rating_distribution['5'] + rating_distribution['4']).toFixed(0)}%
            </div>
            <div className="text-white/60 text-xs">Positive</div>
          </div>
          
          <div className="text-center">
            <div className="text-lg font-semibold text-white">
              {getRatingPercentage(rating_distribution['3']).toFixed(0)}%
            </div>
            <div className="text-white/60 text-xs">Neutral</div>
          </div>
          
          <div className="text-center">
            <div className="text-lg font-semibold text-white">
              {getRatingPercentage(rating_distribution['2'] + rating_distribution['1']).toFixed(0)}%
            </div>
            <div className="text-white/60 text-xs">Negative</div>
          </div>
        </div>
      )}

      {/* No Reviews State */}
      {total_reviews === 0 && (
        <div className="text-center py-4">
          <div className="text-white/60 text-sm">No reviews yet</div>
          <div className="text-white/40 text-xs mt-1">
            Be the first to review this agent
          </div>
        </div>
      )}
    </div>
  );
};

export default ReviewSummary;
