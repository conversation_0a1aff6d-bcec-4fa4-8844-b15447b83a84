import React, { useState } from 'react';
import { formatDistanceToNow } from 'date-fns';
import { ThumbsUp, ThumbsDown, Shield, TrendingUp, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import StarRating from '@/components/ui/star-rating';
import { AgentReview, voteOnReviewHelpfulness } from '@/services/reviewService';
import { useToast } from '@/components/ui/use-toast';

interface ReviewCardProps {
  review: AgentReview;
  onHelpfulnessUpdate?: () => void;
}

const ReviewCard: React.FC<ReviewCardProps> = ({ review, onHelpfulnessUpdate }) => {
  const { toast } = useToast();
  const [isVoting, setIsVoting] = useState(false);
  const [userVote, setUserVote] = useState<boolean | null>(review.user_helpfulness_vote || null);

  const handleHelpfulnessVote = async (isHelpful: boolean) => {
    if (isVoting) return;

    try {
      setIsVoting(true);
      
      // If user is clicking the same vote, remove it
      const newVote = userVote === isHelpful ? null : isHelpful;
      
      if (newVote !== null) {
        await voteOnReviewHelpfulness(review.id, newVote);
        setUserVote(newVote);
        toast({
          title: "Vote recorded",
          description: `Marked review as ${newVote ? 'helpful' : 'not helpful'}`
        });
      }
      
      onHelpfulnessUpdate?.();
    } catch (error) {
      console.error('Error voting on review helpfulness:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to record your vote"
      });
    } finally {
      setIsVoting(false);
    }
  };

  const getReviewerName = () => {
    if (review.user_name) {
      return review.user_name;
    }
    if (review.user_email) {
      return review.user_email.split('@')[0];
    }
    return 'Anonymous User';
  };

  const formatBacktestPerformance = (performance: any) => {
    if (!performance) return null;
    
    return (
      <div className="mt-3 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
        <div className="flex items-center gap-2 text-blue-400 text-sm font-medium mb-2">
          <TrendingUp className="w-4 h-4" />
          Backtest Performance
        </div>
        <div className="grid grid-cols-2 gap-2 text-xs">
          {performance.total_return && (
            <div>
              <span className="text-white/60">Total Return:</span>
              <span className={`ml-1 font-medium ${
                performance.total_return > 0 ? 'text-green-400' : 'text-red-400'
              }`}>
                {performance.total_return > 0 ? '+' : ''}{performance.total_return.toFixed(2)}%
              </span>
            </div>
          )}
          {performance.sharpe_ratio && (
            <div>
              <span className="text-white/60">Sharpe Ratio:</span>
              <span className="ml-1 font-medium text-white">
                {performance.sharpe_ratio.toFixed(2)}
              </span>
            </div>
          )}
          {performance.max_drawdown && (
            <div>
              <span className="text-white/60">Max Drawdown:</span>
              <span className="ml-1 font-medium text-red-400">
                {performance.max_drawdown.toFixed(2)}%
              </span>
            </div>
          )}
          {performance.win_rate && (
            <div>
              <span className="text-white/60">Win Rate:</span>
              <span className="ml-1 font-medium text-white">
                {performance.win_rate.toFixed(1)}%
              </span>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white/[0.02] border border-white/[0.08] rounded-lg p-4">
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
            {getReviewerName().charAt(0).toUpperCase()}
          </div>
          <div>
            <div className="flex items-center gap-2">
              <span className="text-white font-medium text-sm">{getReviewerName()}</span>
              {review.is_verified_purchase && (
                <Badge className="bg-green-500/20 text-green-400 border border-green-500/30 text-xs">
                  <Shield className="w-3 h-3 mr-1" />
                  Verified Purchase
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-2 mt-1">
              <StarRating rating={review.rating} size="sm" />
              <span className="text-white/60 text-xs flex items-center gap-1">
                <Calendar className="w-3 h-3" />
                {formatDistanceToNow(new Date(review.created_at), { addSuffix: true })}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Review Title */}
      {review.title && (
        <h4 className="text-white font-medium text-sm mb-2">{review.title}</h4>
      )}

      {/* Review Text */}
      {review.review_text && (
        <p className="text-white/80 text-sm leading-relaxed mb-3">
          {review.review_text}
        </p>
      )}

      {/* Backtest Performance */}
      {formatBacktestPerformance(review.backtest_performance)}

      {/* Helpfulness Voting */}
      <div className="flex items-center justify-between mt-4 pt-3 border-t border-white/[0.06]">
        <div className="flex items-center gap-2">
          <span className="text-white/60 text-xs">Was this helpful?</span>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleHelpfulnessVote(true)}
              disabled={isVoting}
              className={`h-7 px-2 text-xs ${
                userVote === true
                  ? 'bg-green-500/20 text-green-400 hover:bg-green-500/30'
                  : 'text-white/60 hover:text-white hover:bg-white/[0.04]'
              }`}
            >
              <ThumbsUp className="w-3 h-3 mr-1" />
              Yes
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleHelpfulnessVote(false)}
              disabled={isVoting}
              className={`h-7 px-2 text-xs ${
                userVote === false
                  ? 'bg-red-500/20 text-red-400 hover:bg-red-500/30'
                  : 'text-white/60 hover:text-white hover:bg-white/[0.04]'
              }`}
            >
              <ThumbsDown className="w-3 h-3 mr-1" />
              No
            </Button>
          </div>
        </div>

        {review.helpful_count > 0 && (
          <span className="text-white/60 text-xs">
            {review.helpful_count} {review.helpful_count === 1 ? 'person' : 'people'} found this helpful
          </span>
        )}
      </div>
    </div>
  );
};

export default ReviewCard;
