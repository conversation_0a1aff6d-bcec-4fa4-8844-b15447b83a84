import { useEffect, useState } from 'react';

interface WelcomeHeadingProps {
  text: string;
  highlightWord?: string;
  className?: string;
  speed?: number;
}

export function WelcomeHeading({ 
  text = "Hey! I'm Osis. How can I help?", 
  highlightWord = "Osis", 
  className = "",
  speed = 70 
}: WelcomeHeadingProps) {
  const [displayText, setDisplayText] = useState("");
  const [isComplete, setIsComplete] = useState(false);
  
  useEffect(() => {
    let i = 0;
    const timer = setInterval(() => {
      if (i < text.length) {
        setDisplayText(text.substring(0, i + 1));
        i++;
      } else {
        clearInterval(timer);
        setIsComplete(true);
      }
    }, speed);
    
    return () => clearInterval(timer);
  }, [text, speed]);
  
  // Split the text by the highlight word
  const parts = displayText.split(new RegExp(`(${highlightWord})`, 'g'));
  
  return (
    <h1 className={`text-4xl font-normal tracking-tighter font-sf-pro-display leading-tight ${className}`} style={{ letterSpacing: '-0.03em', fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
      {parts.map((part, i) => 
        part === highlightWord ? (
          <span key={i} className="text-white font-normal">{part}</span>
        ) : (
          <span key={i} className="text-white/70 font-normal">{part}</span>
        )
      )}
      {!isComplete && <span className="animate-pulse">|</span>}
    </h1>
  );
} 