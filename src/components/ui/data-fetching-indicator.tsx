import React, { useState, useEffect } from 'react';
import { ChartBar, LineChart, Database, Sparkles, CheckCircle2 } from 'lucide-react';

interface DataFetchingIndicatorProps {
  isLoading: boolean;
  symbol?: string;
  steps?: Array<{
    id: string;
    label: string;
    icon: React.ReactNode;
  }>;
}

export function DataFetchingIndicator({ 
  isLoading, 
  symbol = '', 
  steps
}: DataFetchingIndicatorProps) {
  const [completedSteps, setCompletedSteps] = useState<string[]>([]);
  const [currentStep, setCurrentStep] = useState<string | null>(null);

  // Default steps if none provided
  const defaultSteps = [
    { id: 'market', label: 'Fetching market data', icon: <ChartBar className="w-4 h-4" /> },
    { id: 'historical', label: 'Processing historical prices', icon: <LineChart className="w-4 h-4" /> },
    { id: 'financial', label: 'Analyzing financial metrics', icon: <Database className="w-4 h-4" /> },
    { id: 'ai', label: 'Generating AI insights', icon: <Sparkles className="w-4 h-4" /> }
  ];

  const finalSteps = steps || defaultSteps;

  useEffect(() => {
    if (!isLoading) {
      setCompletedSteps([]);
      setCurrentStep(null);
      return;
    }

    // Simulate the progression through steps
    const stepIds = finalSteps.map(step => step.id);
    setCompletedSteps([]);
    setCurrentStep(stepIds[0]);

    const timers = stepIds.map((stepId, index) => {
      return setTimeout(() => {
        if (index > 0) {
          setCompletedSteps(prev => [...prev, stepIds[index - 1]]);
        }
        setCurrentStep(stepId);
        
        // Mark the last step as completed when done
        if (index === stepIds.length - 1) {
          setTimeout(() => {
            setCompletedSteps(prev => [...prev, stepIds[index]]);
            setCurrentStep(null);
          }, 800);
        }
      }, index * 1200); // Stagger the steps
    });

    return () => timers.forEach(timer => clearTimeout(timer));
  }, [isLoading, finalSteps]);

  if (!isLoading && completedSteps.length === 0) return null;

  return (
    <div className="w-full max-w-md mx-auto bg-gradient-to-b from-[#0A0C12] to-[#12141A] 
      border border-[#1E2233] rounded-xl p-4 my-4 shadow-xl shadow-black/20">
      <div className="flex items-center mb-4">
        <div className="relative">
          <div className="w-2 h-2 bg-[#4F87FF] rounded-full"></div>
          <div className="w-2 h-2 bg-[#4F87FF] rounded-full absolute inset-0 animate-ping opacity-50"></div>
        </div>
        <h3 className="ml-3 text-sm font-medium bg-gradient-to-r from-white/90 to-white/70 bg-clip-text text-transparent">
          {symbol ? `Processing ${symbol} data` : 'Processing data'}
        </h3>
      </div>
      
      <div className="space-y-3">
        {finalSteps.map((step, index) => {
          const isCompleted = completedSteps.includes(step.id);
          const isActive = currentStep === step.id;
          
          return (
            <div 
              key={step.id}
              className={`flex items-center p-2.5 rounded-lg transition-all duration-300 ${
                isActive ? 'bg-[#1A1D2E] shadow-lg shadow-black/10' : ''
              }`}
            >
              <div className={`mr-3 ${isCompleted ? 'text-emerald-500' : isActive ? 'text-[#4F87FF]' : 'text-white/30'}`}>
                {isCompleted ? (
                  <CheckCircle2 className="w-5 h-5" />
                ) : (
                  <div className="p-1 rounded-md bg-[#1E2233]/50">
                    {step.icon}
                  </div>
                )}
              </div>
              
              <div className="flex-1">
                <p className={`text-xs font-medium ${
                  isActive ? 'text-white' : isCompleted ? 'text-white/70' : 'text-white/40'
                }`}>
                  {step.label}
                </p>
              </div>
              
              {isActive && (
                <div className="flex space-x-1">
                  {[0, 1, 2].map((dot) => (
                    <div
                      key={dot}
                      className="w-1 h-1 bg-[#4F87FF] rounded-full animate-pulse"
                      style={{ 
                        animationDelay: `${dot * 0.2}s`,
                        animationDuration: '1.5s'
                      }}
                    />
                  ))}
                </div>
              )}
            </div>
          );
        })}
      </div>
      
      {completedSteps.length === finalSteps.length && (
        <div className="mt-4 text-center">
          <p className="text-xs font-medium text-emerald-500/90">Analysis complete</p>
        </div>
      )}
    </div>
  );
} 