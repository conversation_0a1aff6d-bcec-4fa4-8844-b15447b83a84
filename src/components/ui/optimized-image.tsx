/**
 * Optimized Image Component
 * Handles lazy loading, preloading, WebP support, and performance optimization
 */

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { cn } from '@/lib/utils';

interface OptimizedImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  priority?: boolean; // High priority images load immediately
  lazy?: boolean; // Enable lazy loading (default: true)
  placeholder?: string; // Placeholder image URL
  blurDataURL?: string; // Base64 blur placeholder
  quality?: number; // Image quality (1-100)
  sizes?: string; // Responsive sizes
  onLoadComplete?: () => void;
  onError?: () => void;
  className?: string;
}

// Image format detection
const supportsWebP = (() => {
  if (typeof window === 'undefined') return false;
  
  const canvas = document.createElement('canvas');
  canvas.width = 1;
  canvas.height = 1;
  
  return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
})();

const supportsAvif = (() => {
  if (typeof window === 'undefined') return false;
  
  const canvas = document.createElement('canvas');
  canvas.width = 1;
  canvas.height = 1;
  
  return canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0;
})();

// Generate optimized image URL
const getOptimizedImageUrl = (src: string, width?: number, quality = 75): string => {
  // If it's already an optimized URL or external URL, return as-is
  if (src.includes('?') || src.startsWith('http') || src.startsWith('data:')) {
    return src;
  }
  
  // Add optimization parameters
  const params = new URLSearchParams();
  if (width) params.set('w', width.toString());
  params.set('q', quality.toString());
  
  // Add format preference
  if (supportsAvif) {
    params.set('f', 'avif');
  } else if (supportsWebP) {
    params.set('f', 'webp');
  }
  
  return `${src}?${params.toString()}`;
};

// Generate responsive srcSet
const generateSrcSet = (src: string, quality = 75): string => {
  const widths = [640, 750, 828, 1080, 1200, 1920, 2048, 3840];
  
  return widths
    .map(width => `${getOptimizedImageUrl(src, width, quality)} ${width}w`)
    .join(', ');
};

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  priority = false,
  lazy = true,
  placeholder,
  blurDataURL,
  quality = 75,
  sizes,
  onLoadComplete,
  onError,
  className,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const [isInView, setIsInView] = useState(!lazy || priority);
  const imgRef = useRef<HTMLImageElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!lazy || priority || isInView) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observer.disconnect();
          }
        });
      },
      {
        rootMargin: '50px', // Start loading 50px before the image enters viewport
        threshold: 0.1,
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    observerRef.current = observer;

    return () => {
      observer.disconnect();
    };
  }, [lazy, priority, isInView]);

  // Preload high priority images
  useEffect(() => {
    if (priority && src) {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = getOptimizedImageUrl(src, width, quality);
      
      if (sizes) {
        link.setAttribute('imagesizes', sizes);
        link.setAttribute('imagesrcset', generateSrcSet(src, quality));
      }
      
      document.head.appendChild(link);
      
      return () => {
        if (document.head.contains(link)) {
          document.head.removeChild(link);
        }
      };
    }
  }, [priority, src, width, quality, sizes]);

  // Handle image load
  const handleLoad = useCallback(() => {
    setIsLoaded(true);
    onLoadComplete?.();
  }, [onLoadComplete]);

  // Handle image error
  const handleError = useCallback(() => {
    setIsError(true);
    onError?.();
  }, [onError]);

  // Generate optimized src and srcSet
  const optimizedSrc = getOptimizedImageUrl(src, width, quality);
  const srcSet = generateSrcSet(src, quality);

  // Determine what to show
  const shouldShowImage = isInView && !isError;
  const shouldShowPlaceholder = !isLoaded && (placeholder || blurDataURL);
  const shouldShowBlur = !isLoaded && blurDataURL;

  return (
    <div
      className={cn(
        'relative overflow-hidden',
        width && height && 'inline-block',
        className
      )}
      style={{
        width: width ? `${width}px` : undefined,
        height: height ? `${height}px` : undefined,
      }}
    >
      {/* Blur placeholder */}
      {shouldShowBlur && (
        <img
          src={blurDataURL}
          alt=""
          className={cn(
            'absolute inset-0 w-full h-full object-cover transition-opacity duration-300',
            isLoaded ? 'opacity-0' : 'opacity-100'
          )}
          style={{
            filter: 'blur(20px)',
            transform: 'scale(1.1)', // Slightly scale to hide blur edges
          }}
          aria-hidden="true"
        />
      )}

      {/* Regular placeholder */}
      {shouldShowPlaceholder && !blurDataURL && placeholder && (
        <img
          src={placeholder}
          alt=""
          className={cn(
            'absolute inset-0 w-full h-full object-cover transition-opacity duration-300',
            isLoaded ? 'opacity-0' : 'opacity-100'
          )}
          aria-hidden="true"
        />
      )}

      {/* Main image */}
      {shouldShowImage && (
        <img
          ref={imgRef}
          src={optimizedSrc}
          srcSet={srcSet}
          sizes={sizes || '100vw'}
          alt={alt}
          width={width}
          height={height}
          loading={priority ? 'eager' : 'lazy'}
          decoding="async"
          onLoad={handleLoad}
          onError={handleError}
          className={cn(
            'w-full h-full object-cover transition-opacity duration-300',
            isLoaded ? 'opacity-100' : 'opacity-0'
          )}
          {...props}
        />
      )}

      {/* Error state */}
      {isError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800">
          <div className="text-center text-gray-500 dark:text-gray-400">
            <svg
              className="w-8 h-8 mx-auto mb-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            <p className="text-sm">Failed to load image</p>
          </div>
        </div>
      )}

      {/* Loading state */}
      {!isLoaded && !isError && shouldShowImage && !shouldShowPlaceholder && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-gray-400"></div>
        </div>
      )}
    </div>
  );
};

// Avatar component with optimized image
export const OptimizedAvatar: React.FC<{
  src?: string;
  alt: string;
  size?: number;
  fallback?: string;
  className?: string;
}> = ({ src, alt, size = 40, fallback, className }) => {
  return (
    <div
      className={cn(
        'relative rounded-full overflow-hidden bg-gray-200 dark:bg-gray-700',
        className
      )}
      style={{ width: size, height: size }}
    >
      {src ? (
        <OptimizedImage
          src={src}
          alt={alt}
          width={size}
          height={size}
          className="w-full h-full object-cover"
          placeholder={fallback}
        />
      ) : (
        <div className="w-full h-full flex items-center justify-center text-gray-500 dark:text-gray-400">
          {fallback || alt.charAt(0).toUpperCase()}
        </div>
      )}
    </div>
  );
};

// Background image component
export const OptimizedBackgroundImage: React.FC<{
  src: string;
  alt: string;
  children?: React.ReactNode;
  className?: string;
  overlay?: boolean;
  priority?: boolean;
}> = ({ src, alt, children, className, overlay = false, priority = false }) => {
  const [isLoaded, setIsLoaded] = useState(false);

  return (
    <div className={cn('relative', className)}>
      <OptimizedImage
        src={src}
        alt={alt}
        priority={priority}
        className="absolute inset-0 w-full h-full object-cover"
        onLoadComplete={() => setIsLoaded(true)}
      />
      
      {overlay && (
        <div className="absolute inset-0 bg-black bg-opacity-40" />
      )}
      
      <div className={cn(
        'relative z-10 transition-opacity duration-300',
        isLoaded ? 'opacity-100' : 'opacity-0'
      )}>
        {children}
      </div>
    </div>
  );
};

export default OptimizedImage;
