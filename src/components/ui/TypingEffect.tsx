import { useState, useEffect } from "react";

interface TypingEffectProps {
  text: string;
  speed?: number;
  className?: string;
  highlightWord?: string;
}

const TypingEffect = ({ 
  text, 
  speed = 70,
  className = "",
  highlightWord = "Osis"
}: TypingEffectProps) => {
  const [displayText, setDisplayText] = useState("");
  const [isComplete, setIsComplete] = useState(false);
  
  useEffect(() => {
    let i = 0;
    const timer = setInterval(() => {
      if (i < text.length) {
        setDisplayText(text.substring(0, i + 1));
        i++;
      } else {
        clearInterval(timer);
        setIsComplete(true);
      }
    }, speed);
    
    return () => clearInterval(timer);
  }, [text, speed]);
  
  // Split the text by the highlight word
  const parts = displayText.split(new RegExp(`(${highlightWord})`, 'g'));
  
  return (
    <span className={className}>
      {parts.map((part, i) => 
        part === highlightWord ? 
          <span key={i} className="text-white font-semibold">{part}</span> : 
          <span key={i} className="text-white/70">{part}</span>
      )}
      {!isComplete && <span className="animate-pulse">|</span>}
    </span>
  );
};

export default TypingEffect; 