import React from 'react';
import { Star } from 'lucide-react';
import { cn } from '@/lib/utils';

interface StarRatingProps {
  rating: number;
  maxRating?: number;
  size?: 'sm' | 'md' | 'lg';
  interactive?: boolean;
  onRatingChange?: (rating: number) => void;
  className?: string;
  showValue?: boolean;
  precision?: 'full' | 'half';
}

const StarRating: React.FC<StarRatingProps> = ({
  rating,
  maxRating = 5,
  size = 'md',
  interactive = false,
  onRatingChange,
  className,
  showValue = false,
  precision = 'half'
}) => {
  const [hoverRating, setHoverRating] = React.useState<number | null>(null);

  const sizeClasses = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5'
  };

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  const handleStarClick = (starRating: number) => {
    if (interactive && onRatingChange) {
      onRatingChange(starRating);
    }
  };

  const handleStarHover = (starRating: number) => {
    if (interactive) {
      setHoverRating(starRating);
    }
  };

  const handleMouseLeave = () => {
    if (interactive) {
      setHoverRating(null);
    }
  };

  const getStarFill = (starIndex: number) => {
    const currentRating = hoverRating !== null ? hoverRating : rating;
    const starValue = starIndex + 1;

    if (currentRating >= starValue) {
      return 'full';
    } else if (precision === 'half' && currentRating >= starValue - 0.5) {
      return 'half';
    } else {
      return 'empty';
    }
  };

  const renderStar = (index: number) => {
    const fill = getStarFill(index);
    const starValue = index + 1;

    return (
      <div
        key={index}
        className={cn(
          'relative',
          interactive && 'cursor-pointer',
          sizeClasses[size]
        )}
        onClick={() => handleStarClick(starValue)}
        onMouseEnter={() => handleStarHover(starValue)}
      >
        {/* Background star (empty) */}
        <Star
          className={cn(
            'absolute inset-0 text-gray-300',
            sizeClasses[size]
          )}
        />
        
        {/* Filled star */}
        {fill === 'full' && (
          <Star
            className={cn(
              'absolute inset-0 text-yellow-400 fill-yellow-400',
              sizeClasses[size]
            )}
          />
        )}
        
        {/* Half-filled star */}
        {fill === 'half' && (
          <div className="absolute inset-0 overflow-hidden" style={{ width: '50%' }}>
            <Star
              className={cn(
                'text-yellow-400 fill-yellow-400',
                sizeClasses[size]
              )}
            />
          </div>
        )}
      </div>
    );
  };

  return (
    <div
      className={cn('flex items-center gap-1', className)}
      onMouseLeave={handleMouseLeave}
    >
      <div className="flex items-center">
        {Array.from({ length: maxRating }, (_, index) => renderStar(index))}
      </div>
      
      {showValue && (
        <span className={cn('text-white/70 ml-1', textSizeClasses[size])}>
          {rating.toFixed(1)}
        </span>
      )}
    </div>
  );
};

export default StarRating;
