import React from 'react';
import { Shield, CheckCircle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { formatDistanceToNow } from 'date-fns';

interface VerificationBadgeProps {
  isVerified: boolean;
  verifiedAt?: string;
  verifiedBy?: string;
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
  className?: string;
}

const VerificationBadge: React.FC<VerificationBadgeProps> = ({
  isVerified,
  verifiedAt,
  verifiedBy,
  size = 'md',
  showText = true,
  className = ''
}) => {
  if (!isVerified) return null;

  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-3 py-1',
    lg: 'text-base px-4 py-2'
  };

  const iconSizes = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5'
  };

  const tooltipContent = (
    <div className="text-center">
      <div className="font-semibold text-white">Official OSIS Verified</div>
      <div className="text-white/70 text-xs mt-1">
        This agent has been verified by OSIS administrators
      </div>
      {verifiedAt && (
        <div className="text-white/50 text-xs mt-1">
          Verified {formatDistanceToNow(new Date(verifiedAt), { addSuffix: true })}
        </div>
      )}
    </div>
  );

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge 
            className={`
              bg-gradient-to-r from-blue-600 to-purple-600 
              hover:from-blue-700 hover:to-purple-700
              text-white border-0 
              shadow-lg shadow-blue-500/25
              transition-all duration-200
              cursor-help
              ${sizeClasses[size]}
              ${className}
            `}
          >
            <div className="flex items-center gap-1.5">
              <Shield className={`${iconSizes[size]} fill-current`} />
              {showText && (
                <span className="font-medium">
                  Official OSIS Verified
                </span>
              )}
              <CheckCircle className={`${iconSizes[size]} fill-current`} />
            </div>
          </Badge>
        </TooltipTrigger>
        <TooltipContent 
          side="top" 
          className="bg-gray-900 border-gray-700 max-w-xs"
        >
          {tooltipContent}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default VerificationBadge;
