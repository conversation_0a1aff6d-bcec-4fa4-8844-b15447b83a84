import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/integrations/supabase/client';
import { checkWhopCompetitionPermissions } from '@/services/whopCompetitionService';
import { useToast } from '@/components/ui/use-toast';
import { RefreshCw, User, Crown, Users, Globe } from 'lucide-react';

const WhopUserDebug: React.FC = () => {
  const [userMetadata, setUserMetadata] = useState<any>(null);
  const [permissions, setPermissions] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const fetchUserData = async () => {
    setLoading(true);
    try {
      // Get current user
      const { data: { user }, error } = await supabase.auth.getUser();
      if (error) throw error;

      setUserMetadata(user?.user_metadata || {});

      // Get Whop competition permissions
      const perms = await checkWhopCompetitionPermissions();
      setPermissions(perms);

      console.log('User metadata:', user?.user_metadata);
      console.log('Whop permissions:', perms);
    } catch (error: any) {
      console.error('Error fetching user data:', error);
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const updateUserMetadata = async () => {
    try {
      // This is a temporary fix - in production, this should be done server-side
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('No user found');

      // Update user metadata with Whop owner permissions
      const { error } = await supabase.auth.updateUser({
        data: {
          ...user.user_metadata,
          whop_user_id: user.id, // Set whop_user_id to current user ID
          whop_access_level: 'admin', // Set as admin/owner
          whop_company_id: import.meta.env.VITE_WHOP_COMPANY_ID || import.meta.env.VITE_TRADING_WHOP_COMPANY_ID,
          whop_business_id: 'biz_OGyv6Pz0Le35Fa', // Official Osis business ID
          whop_business_handle: 'tryosis',
          isWhopUser: true // Ensure this is set
        }
      });

      if (error) throw error;

      toast({
        title: "Success",
        description: "User metadata updated successfully",
      });

      // Refresh data
      await fetchUserData();
    } catch (error: any) {
      console.error('Error updating user metadata:', error);
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    fetchUserData();
  }, []);

  // Only show in localhost
  if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
    return null;
  }

  return (
    <Card className="bg-gray-900 border-gray-800 text-white max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="w-5 h-5" />
          Whop User Debug Panel
        </CardTitle>
        <CardDescription>
          Debug information for Whop user permissions and metadata
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Refresh Button */}
        <Button 
          onClick={fetchUserData} 
          disabled={loading}
          className="w-full"
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh Data
        </Button>

        {/* User Metadata */}
        <div className="space-y-3">
          <h3 className="text-lg font-semibold">User Metadata</h3>
          <div className="bg-gray-800 p-4 rounded-lg">
            <pre className="text-sm overflow-auto">
              {JSON.stringify(userMetadata, null, 2)}
            </pre>
          </div>
        </div>

        {/* Whop Permissions */}
        <div className="space-y-3">
          <h3 className="text-lg font-semibold">Whop Competition Permissions</h3>
          {permissions ? (
            <div className="space-y-2">
              <div className="flex flex-wrap gap-2">
                <Badge className={permissions.isWhopOwner ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'}>
                  <Crown className="w-3 h-3 mr-1" />
                  Whop Owner: {permissions.isWhopOwner ? 'Yes' : 'No'}
                </Badge>
                <Badge className={permissions.isOfficialOsis ? 'bg-yellow-500/20 text-yellow-400' : 'bg-gray-500/20 text-gray-400'}>
                  <Crown className="w-3 h-3 mr-1" />
                  Official Osis: {permissions.isOfficialOsis ? 'Yes' : 'No'}
                </Badge>
                <Badge className={permissions.canCreateLocal ? 'bg-blue-500/20 text-blue-400' : 'bg-gray-500/20 text-gray-400'}>
                  <Users className="w-3 h-3 mr-1" />
                  Can Create Local: {permissions.canCreateLocal ? 'Yes' : 'No'}
                </Badge>
                <Badge className={permissions.canCreateCrossCommunity ? 'bg-purple-500/20 text-purple-400' : 'bg-gray-500/20 text-gray-400'}>
                  <Globe className="w-3 h-3 mr-1" />
                  Can Create Cross-Community: {permissions.canCreateCrossCommunity ? 'Yes' : 'No'}
                </Badge>
              </div>
              <div className="bg-gray-800 p-4 rounded-lg">
                <pre className="text-sm overflow-auto">
                  {JSON.stringify(permissions, null, 2)}
                </pre>
              </div>
            </div>
          ) : (
            <div className="bg-gray-800 p-4 rounded-lg text-gray-400">
              Loading permissions...
            </div>
          )}
        </div>

        {/* Quick Fix Button */}
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-yellow-400">Quick Fix (Development Only)</h3>
          <p className="text-sm text-gray-400">
            This button will update your user metadata to have Whop owner permissions. 
            This is for development/testing only.
          </p>
          <Button 
            onClick={updateUserMetadata}
            variant="outline"
            className="w-full border-yellow-500 text-yellow-400 hover:bg-yellow-500/10"
          >
            Set as Whop Owner (Dev Only)
          </Button>
        </div>

        {/* Environment Info */}
        <div className="space-y-3">
          <h3 className="text-lg font-semibold">Environment Variables</h3>
          <div className="bg-gray-800 p-4 rounded-lg text-sm">
            <div>VITE_WHOP_COMPANY_ID: {import.meta.env.VITE_WHOP_COMPANY_ID || 'Not set'}</div>
            <div>VITE_WHOP_APP_ID: {import.meta.env.VITE_WHOP_APP_ID || 'Not set'}</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default WhopUserDebug;
