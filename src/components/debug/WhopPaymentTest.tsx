import React, { useEffect, useState } from 'react';
import { whopIntermediaryClient } from '@/lib/whopIntermediaryClient';
import { iframeSdk } from '@/lib/iframe-sdk';
import { useWhop } from '@/contexts/WhopContext';

/**
 * Test component that automatically creates a charge and opens the payment modal
 * This tests the full payment flow: charge creation via intermediary server + modal via iframe SDK
 */
const WhopPaymentTest: React.FC = () => {
  const [status, setStatus] = useState<'idle' | 'creating-charge' | 'opening-modal' | 'completed' | 'error'>('idle');
  const [logs, setLogs] = useState<string[]>([]);
  const [chargeData, setChargeData] = useState<any>(null);
  const [paymentResult, setPaymentResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Get real user and experience data from Whop context
  const { isWhopUser, whopUser, experienceId, isLoading } = useWhop();

  const addLog = (message: string, data?: any) => {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}`;
    console.log(`🧪 Payment Test: ${message}`, data || '');
    setLogs(prev => [...prev.slice(-10), logEntry]); // Keep last 10 logs
  };

  const runPaymentTest = async () => {
    try {
      setStatus('creating-charge');
      setError(null);
      addLog('🔄 Starting payment test...');

      // Wait for Whop context to load
      if (isLoading) {
        addLog('⏳ Waiting for Whop context to load...');
        setTimeout(() => runPaymentTest(), 1000);
        return;
      }

      // Check if we have real user/experience data from Whop context
      if (!isWhopUser || !whopUser?.id || !experienceId) {
        addLog('⚠️ Missing required Whop user or experience data', {
          isWhopUser,
          userId: whopUser?.id,
          username: whopUser?.username,
          experienceId,
          currentPath: window.location.pathname
        });
        addLog('💡 Make sure you are in a Whop experience context with a logged-in user');
        setError('Missing user or experience data - not in Whop context');
        setStatus('error');
        return;
      }

      const userId = whopUser.id;

      addLog('👤 Using real user and experience info:', {
        userId,
        experienceId,
        currentPath: window.location.pathname
      });

      // Step 1: Create charge via intermediary server
      addLog('📡 Creating charge via whop-intermediary-server...');
      const chargeResponse = await whopIntermediaryClient.createCharge(
        1000, // $10.00 test charge (in cents)
        'usd',
        'Test Payment - Iframe SDK Integration'
      );

      addLog('📡 Charge creation response received', {
        success: chargeResponse.success,
        hasData: !!chargeResponse.data,
        hasInAppPurchase: !!chargeResponse.data?.inAppPurchase
      });

      if (!chargeResponse.success) {
        throw new Error(chargeResponse.error || 'Failed to create charge');
      }

      if (!chargeResponse.data?.inAppPurchase) {
        throw new Error('No inAppPurchase data received from charge creation');
      }

      setChargeData(chargeResponse.data);
      addLog('✅ Charge created successfully', {
        chargeId: chargeResponse.data.inAppPurchase?.id,
        planId: chargeResponse.data.inAppPurchase?.planId,
        chargeData: chargeResponse.data.inAppPurchase
      });

      // Step 2: Open payment modal via iframe SDK
      setStatus('opening-modal');
      addLog('🖼️ Opening payment modal via iframe SDK...');

      if (!iframeSdk) {
        throw new Error('Iframe SDK not available');
      }

      if (typeof (iframeSdk as any).inAppPurchase !== 'function') {
        throw new Error('inAppPurchase method not available on iframe SDK');
      }

      addLog('🖼️ Calling iframeSdk.inAppPurchase with charge data...');
      const result = await (iframeSdk as any).inAppPurchase(chargeResponse.data.inAppPurchase);

      addLog('🖼️ Payment modal result received', {
        status: result?.status,
        hasReceiptId: !!result?.receiptId,
        error: result?.error
      });

      setPaymentResult(result);

      if (result?.status === 'ok') {
        addLog('✅ Payment completed successfully!', {
          receiptId: result.receiptId,
          status: result.status
        });
        setStatus('completed');
      } else if (result?.status === 'user_cancelled_purchase') {
        addLog('⚠️ User cancelled the payment');
        setStatus('completed');
      } else {
        throw new Error(result?.error || 'Payment failed with unknown status');
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      addLog('❌ Payment test failed', { error: errorMessage });
      setError(errorMessage);
      setStatus('error');
    }
  };

  // Auto-run test on component mount
  useEffect(() => {
    addLog('🚀 Auto-starting payment test...');
    // Small delay to ensure everything is initialized
    setTimeout(() => {
      runPaymentTest();
    }, 1000);
  }, []);

  // Only show in development mode for now (can be removed later)
  if (!import.meta.env.DEV) {
    return null;
  }

  return (
    <div className="fixed top-4 left-4 w-96 max-h-96 bg-gray-900 text-white p-4 rounded-lg shadow-lg overflow-auto z-50 text-xs">
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-bold text-sm">Whop Payment Test</h3>
        <div className={`w-3 h-3 rounded-full ${
          status === 'completed' ? 'bg-green-500' : 
          status === 'error' ? 'bg-red-500' : 
          status === 'idle' ? 'bg-gray-500' : 'bg-yellow-500'
        }`} />
      </div>
      
      <div className="mb-3">
        <div className="text-xs text-gray-400 mb-1">Status: {status}</div>
        <div className="text-xs text-gray-400 mb-1">
          Iframe SDK: {iframeSdk ? '✅' : '❌'}
        </div>
        <div className="text-xs text-gray-400 mb-1">
          inAppPurchase: {typeof (iframeSdk as any)?.inAppPurchase === 'function' ? '✅' : '❌'}
        </div>
      </div>

      {error && (
        <div className="mb-3 p-2 bg-red-900 rounded text-xs">
          <div className="font-bold text-red-300">Error:</div>
          <div className="text-red-200">{error}</div>
        </div>
      )}

      {chargeData && (
        <div className="mb-3 p-2 bg-blue-900 rounded text-xs">
          <div className="font-bold text-blue-300">Charge Created:</div>
          <div className="text-blue-200">ID: {chargeData.inAppPurchase?.id}</div>
          <div className="text-blue-200">Plan ID: {chargeData.inAppPurchase?.planId}</div>
        </div>
      )}

      {paymentResult && (
        <div className="mb-3 p-2 bg-green-900 rounded text-xs">
          <div className="font-bold text-green-300">Payment Result:</div>
          <div className="text-green-200">Status: {paymentResult.status}</div>
          {paymentResult.receiptId && (
            <div className="text-green-200">Receipt: {paymentResult.receiptId}</div>
          )}
        </div>
      )}

      <div className="mb-3">
        <button 
          onClick={runPaymentTest}
          className="w-full bg-purple-600 hover:bg-purple-700 px-2 py-1 rounded text-xs"
          disabled={status === 'creating-charge' || status === 'opening-modal'}
        >
          {status === 'creating-charge' ? 'Creating Charge...' :
           status === 'opening-modal' ? 'Opening Modal...' :
           'Run Payment Test'}
        </button>
      </div>

      <div className="border-t border-gray-700 pt-2">
        <div className="text-xs text-gray-400 mb-1">Recent Logs:</div>
        <div className="space-y-1 max-h-32 overflow-y-auto">
          {logs.slice(-8).map((log, index) => (
            <div key={index} className="text-xs text-gray-300 break-words">
              {log}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default WhopPaymentTest;
