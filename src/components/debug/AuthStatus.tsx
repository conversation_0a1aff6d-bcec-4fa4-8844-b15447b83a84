import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useWhop } from '@/contexts/WhopContext';
import { useSubscription } from '@/hooks/useSubscription';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { RefreshCw, User, Shield, Database, Crown } from 'lucide-react';

interface AuthStatusProps {
  showDetails?: boolean;
}

const AuthStatus: React.FC<AuthStatusProps> = ({ showDetails = false }) => {
  const [supabaseUser, setSupabaseUser] = useState<any>(null);
  const [supabaseSession, setSupabaseSession] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const { isWhopUser, whopUser, hasSupabaseSession, isLoading: whopLoading } = useWhop();
  const { subscription, isLoadingSubscription } = useSubscription();

  const checkAuthStatus = async () => {
    setLoading(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      const { data: { session } } = await supabase.auth.getSession();
      
      setSupabaseUser(user);
      setSupabaseSession(session);
    } catch (error) {
      console.error('Error checking auth status:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const refreshAuth = async () => {
    await checkAuthStatus();
    // Also refresh Whop auth if available
    if (isWhopUser) {
      window.location.reload(); // Simple refresh for now
    }
  };

  if (!showDetails) {
    return (
      <div className="flex items-center gap-2 text-sm">
        <div className="flex items-center gap-1">
          <Database className="h-3 w-3" />
          <Badge variant={supabaseUser ? "default" : "destructive"}>
            {supabaseUser ? "Supabase ✓" : "Supabase ✗"}
          </Badge>
        </div>
        {isWhopUser && (
          <div className="flex items-center gap-1">
            <User className="h-3 w-3" />
            <Badge variant={whopUser ? "default" : "destructive"}>
              {whopUser ? "Whop ✓" : "Whop ✗"}
            </Badge>
          </div>
        )}
        <div className="flex items-center gap-1">
          <Crown className="h-3 w-3" />
          <Badge variant={
            (isWhopUser && whopUser) || subscription?.subscription_type === 'premium'
              ? "default"
              : "secondary"
          }>
            {(isWhopUser && whopUser) || subscription?.subscription_type === 'premium'
              ? "Premium ✓"
              : "Basic"
            }
          </Badge>
        </div>
        <Button
          size="sm"
          variant="ghost"
          onClick={refreshAuth}
          disabled={loading || whopLoading}
        >
          <RefreshCw className={`h-3 w-3 ${loading || whopLoading ? 'animate-spin' : ''}`} />
        </Button>
      </div>
    );
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium flex items-center gap-2">
          <Shield className="h-4 w-4" />
          Authentication Status
        </CardTitle>
        <Button
          size="sm"
          variant="outline"
          onClick={refreshAuth}
          disabled={loading || whopLoading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading || whopLoading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Supabase Auth Status */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            <span className="font-medium">Supabase Authentication</span>
            <Badge variant={supabaseUser ? "default" : "destructive"}>
              {supabaseUser ? "Authenticated" : "Not Authenticated"}
            </Badge>
          </div>
          {supabaseUser && (
            <div className="ml-6 text-sm text-muted-foreground space-y-1">
              <div>User ID: {supabaseUser.id}</div>
              <div>Email: {supabaseUser.email}</div>
              <div>Is Whop User: {supabaseUser.user_metadata?.isWhopUser ? 'Yes' : 'No'}</div>
              {supabaseUser.user_metadata?.whop_user_id && (
                <div>Whop User ID: {supabaseUser.user_metadata.whop_user_id}</div>
              )}
              <div>Has Session: {supabaseSession ? 'Yes' : 'No'}</div>
              {supabaseSession && (
                <div>Session Expires: {new Date(supabaseSession.expires_at * 1000).toLocaleString()}</div>
              )}
            </div>
          )}
        </div>

        {/* Whop Auth Status */}
        {isWhopUser && (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4" />
              <span className="font-medium">Whop Authentication</span>
              <Badge variant={whopUser ? "default" : "destructive"}>
                {whopUser ? "Connected" : "Not Connected"}
              </Badge>
            </div>
            {whopUser && (
              <div className="ml-6 text-sm text-muted-foreground space-y-1">
                <div>Username: {whopUser.username}</div>
                <div>Whop ID: {whopUser.id}</div>
                <div>Email: {whopUser.email || 'Not available'}</div>
                <div>Has Supabase Session: {hasSupabaseSession ? 'Yes' : 'No'}</div>
              </div>
            )}
          </div>
        )}

        {/* Subscription Status */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Crown className="h-4 w-4" />
            <span className="font-medium">Subscription Status</span>
            <Badge variant={
              (isWhopUser && whopUser) || subscription?.subscription_type === 'premium'
                ? "default"
                : "secondary"
            }>
              {(isWhopUser && whopUser) || subscription?.subscription_type === 'premium'
                ? "Premium"
                : subscription?.subscription_type || "Basic"
              }
            </Badge>
          </div>
          <div className="ml-6 text-sm text-muted-foreground space-y-1">
            {isWhopUser && whopUser ? (
              <div>✅ Whop users get premium access automatically</div>
            ) : (
              <>
                <div>Subscription Type: {subscription?.subscription_type || 'None'}</div>
                <div>Status: {subscription?.status || 'No active subscription'}</div>
                {subscription?.current_period_end && (
                  <div>Expires: {new Date(subscription.current_period_end * 1000).toLocaleDateString()}</div>
                )}
              </>
            )}
          </div>
        </div>

        {/* Overall Status */}
        <div className="pt-2 border-t">
          <div className="flex items-center gap-2">
            <span className="font-medium">Overall Status:</span>
            <Badge variant={
              (supabaseUser && supabaseSession) || (isWhopUser && whopUser && hasSupabaseSession)
                ? "default"
                : "destructive"
            }>
              {(supabaseUser && supabaseSession) || (isWhopUser && whopUser && hasSupabaseSession)
                ? "Ready for API calls"
                : "Authentication issues detected"
              }
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AuthStatus;
