import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { checkWhopCompetitionPermissions, WhopCompetitionPermissions } from '@/services/whopCompetitionService';
import { whopIntermediaryClient } from '@/lib/whopIntermediaryClient';
import CompetitionCreator from '@/components/Competitions/CompetitionCreator';

/**
 * Debug component to test and display Whop competition permissions
 * This helps debug permission issues by showing the raw API responses
 */
export function WhopPermissionsDebug() {
  const [permissions, setPermissions] = useState<WhopCompetitionPermissions | null>(null);
  const [rawApiResponse, setRawApiResponse] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const experienceId = 'exp_ThljdpAF70d4Af'; // Trading app experience ID

  const testPermissions = async () => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('🧪 Testing Whop permissions...');
      
      // Test direct API call
      const apiResponse = await whopIntermediaryClient.checkCompetitionPermissions(experienceId);
      setRawApiResponse(apiResponse);
      console.log('🧪 Raw API response:', apiResponse);
      
      // Test service function
      const serviceResponse = await checkWhopCompetitionPermissions(experienceId);
      setPermissions(serviceResponse);
      console.log('🧪 Service response:', serviceResponse);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('🧪 Test error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    testPermissions();
  }, []);

  const getStatusIcon = (value: boolean | undefined) => {
    if (value === true) return <CheckCircle className="h-4 w-4 text-green-500" />;
    if (value === false) return <XCircle className="h-4 w-4 text-red-500" />;
    return <AlertCircle className="h-4 w-4 text-yellow-500" />;
  };

  const getStatusColor = (value: boolean | undefined) => {
    if (value === true) return 'bg-green-100 text-green-800 border-green-200';
    if (value === false) return 'bg-red-100 text-red-800 border-red-200';
    return 'bg-yellow-100 text-yellow-800 border-yellow-200';
  };

  return (
    <div className="space-y-6 p-6 max-w-4xl mx-auto">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-white">Whop Permissions Debug</h1>
        <Button 
          onClick={testPermissions} 
          disabled={loading}
          variant="outline"
          size="sm"
        >
          {loading ? (
            <RefreshCw className="h-4 w-4 animate-spin mr-2" />
          ) : (
            <RefreshCw className="h-4 w-4 mr-2" />
          )}
          Refresh
        </Button>
      </div>

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800 flex items-center gap-2">
              <XCircle className="h-5 w-5" />
              Error
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-700">{error}</p>
          </CardContent>
        </Card>
      )}

      {/* Raw API Response */}
      <Card>
        <CardHeader>
          <CardTitle>Raw API Response</CardTitle>
          <CardDescription>
            Direct response from whopIntermediaryClient.checkCompetitionPermissions()
          </CardDescription>
        </CardHeader>
        <CardContent>
          {rawApiResponse ? (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <span className="font-medium">Success:</span>
                <Badge variant="outline" className={getStatusColor(rawApiResponse.success)}>
                  {getStatusIcon(rawApiResponse.success)}
                  <span className="ml-1">{String(rawApiResponse.success)}</span>
                </Badge>
              </div>
              
              {rawApiResponse.permissions && (
                <div className="space-y-2">
                  <h4 className="font-medium">Permissions:</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center gap-2">
                      <span className="text-sm">Can Create:</span>
                      <Badge variant="outline" className={getStatusColor(rawApiResponse.permissions.canCreateCompetitions)}>
                        {getStatusIcon(rawApiResponse.permissions.canCreateCompetitions)}
                        <span className="ml-1">{String(rawApiResponse.permissions.canCreateCompetitions)}</span>
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm">Access Level:</span>
                      <Badge variant="outline">
                        {rawApiResponse.permissions.accessLevel}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm">Has Access:</span>
                      <Badge variant="outline" className={getStatusColor(rawApiResponse.permissions.hasAccess)}>
                        {getStatusIcon(rawApiResponse.permissions.hasAccess)}
                        <span className="ml-1">{String(rawApiResponse.permissions.hasAccess)}</span>
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm">App:</span>
                      <Badge variant="outline">
                        {rawApiResponse.permissions.appName || 'N/A'}
                      </Badge>
                    </div>
                  </div>
                </div>
              )}
              
              <details className="mt-4">
                <summary className="cursor-pointer text-sm font-medium">Full Response</summary>
                <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto">
                  {JSON.stringify(rawApiResponse, null, 2)}
                </pre>
              </details>
            </div>
          ) : (
            <p className="text-gray-500">No API response yet</p>
          )}
        </CardContent>
      </Card>

      {/* Service Response */}
      <Card>
        <CardHeader>
          <CardTitle>Service Response</CardTitle>
          <CardDescription>
            Response from checkWhopCompetitionPermissions() service function
          </CardDescription>
        </CardHeader>
        <CardContent>
          {permissions ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <span className="text-sm">Can Create Local:</span>
                  <Badge variant="outline" className={getStatusColor(permissions.canCreateLocal)}>
                    {getStatusIcon(permissions.canCreateLocal)}
                    <span className="ml-1">{String(permissions.canCreateLocal)}</span>
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm">Can Create Cross-Community:</span>
                  <Badge variant="outline" className={getStatusColor(permissions.canCreateCrossCommunity)}>
                    {getStatusIcon(permissions.canCreateCrossCommunity)}
                    <span className="ml-1">{String(permissions.canCreateCrossCommunity)}</span>
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm">Is Whop Owner:</span>
                  <Badge variant="outline" className={getStatusColor(permissions.isWhopOwner)}>
                    {getStatusIcon(permissions.isWhopOwner)}
                    <span className="ml-1">{String(permissions.isWhopOwner)}</span>
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm">Is Official Osis:</span>
                  <Badge variant="outline" className={getStatusColor(permissions.isOfficialOsis)}>
                    {getStatusIcon(permissions.isOfficialOsis)}
                    <span className="ml-1">{String(permissions.isOfficialOsis)}</span>
                  </Badge>
                </div>
              </div>
              
              <details className="mt-4">
                <summary className="cursor-pointer text-sm font-medium">Full Permissions Object</summary>
                <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto">
                  {JSON.stringify(permissions, null, 2)}
                </pre>
              </details>
            </div>
          ) : (
            <p className="text-gray-500">No service response yet</p>
          )}
        </CardContent>
      </Card>

      {/* UI Condition Check */}
      <Card>
        <CardHeader>
          <CardTitle>UI Condition Check</CardTitle>
          <CardDescription>
            This shows whether the competition creator UI should be visible
          </CardDescription>
        </CardHeader>
        <CardContent>
          {permissions ? (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <span className="text-sm">Show Whop Competition Creator:</span>
                <Badge variant="outline" className={getStatusColor(permissions.isWhopOwner || permissions.canCreateLocal)}>
                  {getStatusIcon(permissions.isWhopOwner || permissions.canCreateLocal)}
                  <span className="ml-1">{String(permissions.isWhopOwner || permissions.canCreateLocal)}</span>
                </Badge>
              </div>
              
              <div className="text-sm text-gray-600">
                <p>Condition: <code>permissions.isWhopOwner || permissions.canCreateLocal</code></p>
                <p>Result: <code>{permissions.isWhopOwner} || {permissions.canCreateLocal} = {permissions.isWhopOwner || permissions.canCreateLocal}</code></p>
              </div>
            </div>
          ) : (
            <p className="text-gray-500">No permissions data available</p>
          )}
        </CardContent>
      </Card>

      {/* Live Competition Creator Test */}
      <Card>
        <CardHeader>
          <CardTitle>Live Competition Creator Test</CardTitle>
          <CardDescription>
            This shows the actual CompetitionCreator component to test visibility
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="relative min-h-[200px] border-2 border-dashed border-gray-300 rounded-lg p-4">
            <p className="text-sm text-gray-600 mb-4">
              The competition creator should appear below if permissions are correct:
            </p>
            <div className="relative">
              <CompetitionCreator />
            </div>
            <p className="text-xs text-gray-500 mt-4">
              Note: The creator appears as a floating button in the bottom-right corner
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default WhopPermissionsDebug;
