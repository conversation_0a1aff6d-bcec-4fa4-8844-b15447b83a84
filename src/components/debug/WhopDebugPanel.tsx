import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useWhopUser, useWhopAccess } from '@/contexts/WhopContext';
import { canDistributeToWhopMembers } from '@/services/whopDistributionService';
import { getAuthenticatedUser } from '@/services/authService';

const WhopDebugPanel: React.FC = () => {
  const { isWhopUser, user: whopUser } = useWhopUser();
  const { hasAccess, accessLevel, isAdmin, isCustomer, isLoading } = useWhopAccess();
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [distributionCheck, setDistributionCheck] = useState<any>(null);

  const runDebugCheck = async () => {
    console.log('🔍 Running comprehensive Whop debug check...');
    
    try {
      // Get authenticated user
      const authUser = await getAuthenticatedUser();
      
      // Get environment variables
      const envVars = {
        VITE_WHOP_APP_ID: import.meta.env.VITE_WHOP_APP_ID,
        VITE_WHOP_COMPANY_ID: import.meta.env.VITE_WHOP_COMPANY_ID,
        VITE_WHOP_AGENT_USER_ID: import.meta.env.VITE_WHOP_AGENT_USER_ID,
        VITE_SUPABASE_URL: import.meta.env.VITE_SUPABASE_URL
      };

      // Check distribution permissions
      let distributionResult = null;
      if (envVars.VITE_WHOP_COMPANY_ID) {
        distributionResult = await canDistributeToWhopMembers(envVars.VITE_WHOP_COMPANY_ID);
      }

      const info = {
        timestamp: new Date().toISOString(),
        whopContext: {
          isWhopUser,
          hasAccess,
          accessLevel,
          isAdmin,
          isCustomer,
          isLoading
        },
        whopUser: whopUser ? {
          id: whopUser.id,
          username: whopUser.username,
          email: whopUser.email,
          profilePicUrl: whopUser.profilePicUrl
        } : null,
        authUser: authUser ? {
          id: authUser.id,
          email: authUser.email,
          userMetadata: authUser.user_metadata,
          whopUserId: authUser.user_metadata?.whop_user_id
        } : null,
        environment: envVars,
        distributionCheck: distributionResult,
        url: {
          pathname: window.location.pathname,
          search: window.location.search,
          hash: window.location.hash,
          href: window.location.href
        },
        iframe: {
          isInIframe: window !== window.parent,
          referrer: document.referrer
        }
      };

      console.log('📋 Debug info collected:', info);
      setDebugInfo(info);
      setDistributionCheck(distributionResult);
    } catch (error) {
      console.error('❌ Error running debug check:', error);
      setDebugInfo({ error: error.message });
    }
  };

  useEffect(() => {
    runDebugCheck();
  }, [isWhopUser, hasAccess, accessLevel]);

  const getStatusBadge = (condition: boolean, label: string) => (
    <Badge 
      variant={condition ? "default" : "destructive"}
      className={condition ? "bg-green-500/20 text-green-400" : "bg-red-500/20 text-red-400"}
    >
      {condition ? "✅" : "❌"} {label}
    </Badge>
  );

  return (
    <Card className="bg-[#1A1A1A] border-[#2A2A2A] text-white">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          🔍 Whop Debug Panel
          <Button onClick={runDebugCheck} size="sm" variant="outline">
            Refresh
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Status Overview */}
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-white/80">Status Overview</h3>
          <div className="flex flex-wrap gap-2">
            {getStatusBadge(isWhopUser, "Whop User")}
            {getStatusBadge(hasAccess, "Has Access")}
            {getStatusBadge(isAdmin, "Admin")}
            {getStatusBadge(distributionCheck?.canDistribute, "Can Distribute")}
          </div>
        </div>

        {/* Access Level */}
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-white/80">Access Level</h3>
          <Badge variant="outline" className="text-white">
            {accessLevel || 'no_access'}
          </Badge>
        </div>

        {/* Environment Variables */}
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-white/80">Environment Variables</h3>
          <div className="text-xs space-y-1">
            {debugInfo?.environment && Object.entries(debugInfo.environment).map(([key, value]) => (
              <div key={key} className="flex justify-between">
                <span className="text-white/60">{key}:</span>
                <span className={value ? "text-green-400" : "text-red-400"}>
                  {value ? "✅ Set" : "❌ Missing"}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* User Information */}
        {whopUser && (
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-white/80">Whop User</h3>
            <div className="text-xs space-y-1">
              <div>ID: {whopUser.id}</div>
              <div>Username: {whopUser.username}</div>
              <div>Email: {whopUser.email}</div>
            </div>
          </div>
        )}

        {/* Distribution Check */}
        {distributionCheck && (
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-white/80">Distribution Check</h3>
            <div className="text-xs space-y-1">
              <div>Can Distribute: {distributionCheck.canDistribute ? "✅ Yes" : "❌ No"}</div>
              {distributionCheck.reason && (
                <div>Reason: {distributionCheck.reason}</div>
              )}
            </div>
          </div>
        )}

        {/* Raw Debug Data */}
        <details className="space-y-2">
          <summary className="text-sm font-medium text-white/80 cursor-pointer">
            Raw Debug Data
          </summary>
          <pre className="text-xs bg-black/20 p-2 rounded overflow-auto max-h-40">
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
        </details>

        {/* Console Logs */}
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-white/80">Console Logs</h3>
          <p className="text-xs text-white/60">
            Check the browser console for detailed debug logs. All Whop-related logs are prefixed with emojis.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default WhopDebugPanel;
