import React, { useEffect, useState } from 'react';
import { iframeSdk } from '../../lib/iframe-sdk';

/**
 * Debug component to test and log Whop iframe SDK functionality
 * This component provides comprehensive logging and testing capabilities
 */
const WhopIframeSdkDebug: React.FC = () => {
  const [sdkStatus, setSdkStatus] = useState<'loading' | 'ready' | 'error'>('loading');
  const [debugLogs, setDebugLogs] = useState<string[]>([]);
  const [testResults, setTestResults] = useState<Record<string, any>>({});

  const addLog = (message: string, data?: any) => {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}`;
    console.log(`🔧 Whop SDK Debug: ${message}`, data || '');
    setDebugLogs(prev => [...prev.slice(-20), logEntry]); // Keep last 20 logs
  };

  useEffect(() => {
    addLog('WhopIframeSdkDebug component mounted');

    // Test iframe SDK availability
    if (iframeSdk) {
      addLog('✅ iframe SDK is available', {
        sdkType: typeof iframeSdk,
        methods: Object.keys(iframeSdk || {}),
        hasInAppPurchase: typeof (iframeSdk as any).inAppPurchase === 'function',
        hasOpenExternalUrl: typeof (iframeSdk as any).openExternalUrl === 'function'
      });
      setSdkStatus('ready');

      // Test SDK methods availability
      const methodTests = {
        inAppPurchase: typeof (iframeSdk as any).inAppPurchase === 'function',
        openExternalUrl: typeof (iframeSdk as any).openExternalUrl === 'function',
        // Add other methods as needed
      };

      setTestResults(methodTests);
      addLog('SDK method availability test completed', methodTests);
    } else {
      addLog('❌ iframe SDK is not available');
      setSdkStatus('error');
    }

    // Test window context
    const windowContext = {
      isInIframe: window.parent !== window,
      origin: window.location.origin,
      pathname: window.location.pathname,
      referrer: document.referrer,
      userAgent: navigator.userAgent,
      hasPostMessage: typeof window.postMessage === 'function',
      parentOrigin: window.parent !== window ? document.referrer : 'same-window'
    };

    addLog('Window context analysis', windowContext);

    // Test for Whop-specific environment variables
    const whopEnv = {
      hasWhopAppId: !!import.meta.env.VITE_WHOP_APP_ID,
      hasWhopTradingAppId: !!import.meta.env.VITE_WHOP_TRADING_APP_ID,
      isDev: import.meta.env.DEV,
      mode: import.meta.env.MODE
    };

    addLog('Whop environment check', whopEnv);

  }, [iframeSdk]);

  const testInAppPurchase = async () => {
    if (!(iframeSdk as any)?.inAppPurchase) {
      addLog('❌ inAppPurchase method not available');
      return;
    }

    addLog('🧪 Testing inAppPurchase method (will fail without valid charge)');

    try {
      // This will fail without a valid charge, but we can test if the method exists
      const result = await (iframeSdk as any).inAppPurchase({
        // Mock data for testing
        id: 'test-charge-id',
        // Add other required properties
      });
      addLog('✅ inAppPurchase method executed', result);
    } catch (error) {
      addLog('⚠️ inAppPurchase method failed (expected without valid charge)', {
        error: error instanceof Error ? error.message : String(error),
        errorType: error instanceof Error ? error.constructor.name : typeof error
      });
    }
  };

  const testOpenExternalUrl = async () => {
    if (!(iframeSdk as any)?.openExternalUrl) {
      addLog('❌ openExternalUrl method not available');
      return;
    }

    addLog('🧪 Testing openExternalUrl method');

    try {
      await (iframeSdk as any).openExternalUrl({ url: 'https://example.com' });
      addLog('✅ openExternalUrl method executed successfully');
    } catch (error) {
      addLog('❌ openExternalUrl method failed', {
        error: error instanceof Error ? error.message : String(error),
        errorType: error instanceof Error ? error.constructor.name : typeof error
      });
    }
  };

  // Only show in development mode
  if (!import.meta.env.DEV) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 w-96 max-h-96 bg-gray-900 text-white p-4 rounded-lg shadow-lg overflow-auto z-50 text-xs">
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-bold text-sm">Whop iframe SDK Debug</h3>
        <div className={`w-3 h-3 rounded-full ${
          sdkStatus === 'ready' ? 'bg-green-500' : 
          sdkStatus === 'error' ? 'bg-red-500' : 'bg-yellow-500'
        }`} />
      </div>
      
      <div className="mb-3">
        <div className="text-xs text-gray-400 mb-1">Status: {sdkStatus}</div>
        <div className="text-xs text-gray-400 mb-1">
          SDK Available: {iframeSdk ? '✅' : '❌'}
        </div>
        {Object.entries(testResults).map(([method, available]) => (
          <div key={method} className="text-xs text-gray-400">
            {method}: {available ? '✅' : '❌'}
          </div>
        ))}
      </div>

      <div className="mb-3 space-y-1">
        <button
          onClick={testInAppPurchase}
          className="w-full bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded text-xs"
          disabled={!(iframeSdk as any)?.inAppPurchase}
        >
          Test inAppPurchase
        </button>
        <button
          onClick={testOpenExternalUrl}
          className="w-full bg-green-600 hover:bg-green-700 px-2 py-1 rounded text-xs"
          disabled={!(iframeSdk as any)?.openExternalUrl}
        >
          Test openExternalUrl
        </button>
      </div>

      <div className="border-t border-gray-700 pt-2">
        <div className="text-xs text-gray-400 mb-1">Recent Logs:</div>
        <div className="space-y-1 max-h-32 overflow-y-auto">
          {debugLogs.slice(-10).map((log, index) => (
            <div key={index} className="text-xs text-gray-300 break-words">
              {log}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default WhopIframeSdkDebug;
