/* TradingChart.css */
.trading-chart-container {
  width: 100%;
  height: 100%;
  min-height: 600px; /* Ensure minimum height for proper chart rendering */
  background-color: #0A0A0A;
  color: #d1d4dc;
  border-radius: 12px;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* Toolbar left section with search and timeframes */
.toolbar-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

/* Clean scanner search */
.futuristic-scanner-search {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, rgba(20, 20, 20, 0.95) 0%, rgba(15, 15, 15, 0.9) 100%);
  border: 1px solid rgba(0, 231, 182, 0.3);
  border-radius: 8px;
  padding: 6px 10px;
  backdrop-filter: blur(20px);
  box-shadow:
    0 0 15px rgba(0, 231, 182, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.08);
  height: 32px;
  margin-left: 0px;
  width: 280px;
  max-width: calc(100vw - 60px);
  position: relative;
  flex-shrink: 0;
}

.futuristic-scanner-search::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 231, 182, 0.3), transparent);
  animation: scanner-sweep 3s infinite;
}

@keyframes scanner-sweep {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}

.scanner-input-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.scanner-input {
  background: transparent;
  border: none;
  color: #ffffff;
  font-size: 13px;
  font-weight: 600;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.02em;
  text-transform: uppercase;
  outline: none;
  flex: 1;
  padding: 0;
  height: 20px;
}

.scanner-input::placeholder {
  color: rgba(255, 255, 255, 0.3);
  font-weight: 400;
  letter-spacing: 0.3px;
  text-transform: none;
}

.scanner-loading {
  color: #00e7b6;
  font-size: 14px;
  animation: spin 1s linear infinite;
  filter: drop-shadow(0 0 8px rgba(0, 231, 182, 0.8));
}

.scanner-status {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  background: rgba(0, 231, 182, 0.08);
  border: 1px solid rgba(0, 231, 182, 0.2);
  border-radius: 4px;
  backdrop-filter: blur(10px);
  white-space: nowrap;
  flex-shrink: 0;
}

.status-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #00e7b6;
  box-shadow: 0 0 8px rgba(0, 231, 182, 0.8);
  animation: status-pulse 1.5s infinite;
}

@keyframes status-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.2);
  }
}

@keyframes scanning-progress {
  0% {
    width: 20%;
    transform: translateX(-100%);
  }
  50% {
    width: 80%;
    transform: translateX(0%);
  }
  100% {
    width: 60%;
    transform: translateX(50%);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

.ripple-effect {
  position: relative;
  overflow: hidden;
}

.ripple-effect::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple-effect:active::before {
  width: 300px;
  height: 300px;
}

.status-text {
  font-size: 9px;
  font-weight: 700;
  color: #00e7b6;
  letter-spacing: 0.5px;
  text-shadow: 0 0 6px rgba(0, 231, 182, 0.4);
}

.search-input {
  width: 100%;
  padding: 8px 35px 8px 45px;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4));
  border: 1px solid rgba(0, 231, 182, 0.3);
  border-radius: 20px;
  color: #ffffff;
  font-size: 13px;
  font-weight: 500;
  letter-spacing: 0.3px;
  box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.3),
              0 0 15px rgba(0, 231, 182, 0.15);
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: rgba(0, 231, 182, 0.7);
  box-shadow: 0 0 25px rgba(0, 231, 182, 0.25),
              inset 0 2px 5px rgba(0, 0, 0, 0.3);
  background: linear-gradient(to right, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5));
}

.search-input::placeholder {
  color: rgba(0, 231, 182, 0.8);
  opacity: 0.9;
  font-weight: 500;
}

.seamless-search-input {
  width: 100%;
  height: 48px;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  padding: 0 40px 0 0;
  outline: none;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 500;
  letter-spacing: 0.3px;
}

.seamless-search-input:focus {
  color: #ffffff;
}

.seamless-search-input::placeholder {
  color: rgba(255, 255, 255, 0.3);
  font-size: 13px;
  font-weight: 400;
  letter-spacing: 0.2px;
}

.search-spinner {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(0, 231, 182, 0.8);
  font-size: 16px;
  animation: spin 1s linear infinite;
  filter: drop-shadow(0 0 8px rgba(0, 231, 182, 0.5));
}

.aura-logo {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.loading-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(0, 231, 182, 0.9);
  font-size: 16px;
  animation: spin 1s linear infinite;
  filter: drop-shadow(0 0 8px rgba(0, 231, 182, 0.6));
  z-index: 3;
}

@keyframes spin {
  0% { transform: translateY(-50%) rotate(0deg); }
  100% { transform: translateY(-50%) rotate(360deg); }
}

.error-message {
  padding: 10px 16px;
  background-color: rgba(255, 82, 82, 0.08);
  color: #ff5252;
  font-size: 14px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 82, 82, 0.15);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(5px);
  font-weight: 500;
}

/* Top toolbar */
.chart-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.06);
  height: 48px;
  background: linear-gradient(180deg, rgba(15, 15, 15, 0.98) 0%, rgba(10, 10, 10, 0.95) 100%);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.03);
  position: relative;
  z-index: 10;
}

/* Timeframe selector styling */
.timeframe-selector {
  display: flex;
  gap: 8px;
}

.timeframe-selector button {
  background: rgba(20, 20, 20, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #787b86;
  padding: 4px 10px;
  border-radius: 15px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.03);
}

.timeframe-selector button:hover {
  background-color: rgba(0, 231, 182, 0.08);
  color: #d1d4dc;
  border-color: rgba(0, 231, 182, 0.3);
  box-shadow: 0 0 10px rgba(0, 231, 182, 0.15);
}

.timeframe-selector button.active {
  background-color: rgba(0, 231, 182, 0.15);
  color: #00e7b6;
  border-color: rgba(0, 231, 182, 0.4);
  box-shadow: 0 0 15px rgba(0, 231, 182, 0.2), inset 0 1px 0 rgba(0, 231, 182, 0.1);
  text-shadow: 0 0 10px rgba(0, 231, 182, 0.4);
}

/* Chart controls and filters */
.chart-controls {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-left: auto;
}

.chart-button {
  background: linear-gradient(145deg, rgba(20, 20, 20, 0.95) 0%, rgba(10, 10, 10, 0.9) 100%);
  border: 1px solid rgba(255, 255, 255, 0.12);
  color: #a8abb4;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 11px;
  display: flex;
  align-items: center;
  gap: 7px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.08),
    inset 0 -1px 0 rgba(0, 0, 0, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.4),
    0 1px 2px rgba(0, 0, 0, 0.2);
  letter-spacing: -0.01em;
  height: 32px;
  position: relative;
  overflow: hidden;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  backdrop-filter: blur(8px);
}

.chart-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, rgba(0, 231, 182, 0), rgba(0, 231, 182, 0.3), rgba(0, 231, 182, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.chart-button:hover::before {
  opacity: 1;
}

.chart-button svg {
  font-size: 13px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.4));
  transition: all 0.3s ease;
}

.chart-button:hover svg {
  filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.5)) drop-shadow(0 0 8px rgba(255, 255, 255, 0.1));
}

.chart-button.active svg {
  filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.5)) drop-shadow(0 0 12px rgba(0, 231, 182, 0.3));
}

/* Filters dropdown styling */
.filters-dropdown {
  position: relative;
  display: inline-block;
}

.filters-button {
  background: linear-gradient(145deg, rgba(20, 20, 20, 0.95) 0%, rgba(10, 10, 10, 0.9) 100%);
  border: 1px solid rgba(0, 231, 182, 0.25);
  color: #00e7b6;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 11px;
  display: flex;
  align-items: center;
  gap: 7px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.08),
    inset 0 -1px 0 rgba(0, 0, 0, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.4),
    0 1px 2px rgba(0, 0, 0, 0.2),
    0 0 15px rgba(0, 231, 182, 0.05);
  letter-spacing: -0.01em;
  height: 32px;
  position: relative;
  overflow: hidden;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  backdrop-filter: blur(8px);
}

.filters-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, rgba(0, 231, 182, 0), rgba(0, 231, 182, 0.3), rgba(0, 231, 182, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.filters-button:hover::before {
  opacity: 1;
}

.filters-button svg {
  font-size: 14px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.4)) drop-shadow(0 0 8px rgba(0, 231, 182, 0.2));
  transition: all 0.3s ease;
}

.filters-button:hover svg {
  filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.5)) drop-shadow(0 0 15px rgba(0, 231, 182, 0.4));
}

.filters-content {
  display: none;
  position: absolute;
  right: 0;
  top: 100%;
  margin-top: 5px;
  background-color: rgba(0, 0, 0, 0.95);
  min-width: 200px;
  border-radius: 8px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(0, 231, 182, 0.1);
  z-index: 20;
  overflow: hidden;
  padding: 12px;
}

.filters-dropdown:hover .filters-content,
.filters-content:hover {
  display: block;
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-5px); }
  to { opacity: 1; transform: translateY(0); }
}

.filters-title {
  font-size: 12px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 12px;
  letter-spacing: -0.02em;
  text-transform: uppercase;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 231, 182, 0.1);
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
}

.filters-description {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.5);
  margin-bottom: 15px;
  line-height: 1.4;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.02em;
}

.filter-options-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.filter-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 10px;
  color: #d1d4dc;
  font-size: 12px;
  transition: all 0.2s ease;
  cursor: pointer;
  border-radius: 6px;
  background-color: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 231, 182, 0.05);
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.02em;
}

.filter-option:hover {
  background-color: rgba(0, 231, 182, 0.05);
  border-color: rgba(0, 231, 182, 0.1);
}

.filter-option.active {
  background-color: rgba(0, 231, 182, 0.08);
  border-color: rgba(0, 231, 182, 0.15);
  box-shadow: 0 0 10px rgba(0, 231, 182, 0.1);
}

.filter-option-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.filter-option-icon {
  color: rgba(0, 231, 182, 0.7);
  font-size: 14px;
  filter: drop-shadow(0 0 2px rgba(0, 231, 182, 0.3));
  transition: all 0.2s ease;
}

.filter-option.active .filter-option-icon {
  color: #00e7b6;
  filter: drop-shadow(0 0 3px rgba(0, 231, 182, 0.5));
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 36px;
  height: 20px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(0, 231, 182, 0.2);
  transition: .3s;
  border-radius: 20px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 14px;
  width: 14px;
  left: 2px;
  bottom: 2px;
  background-color: rgba(255, 255, 255, 0.8);
  transition: .3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: rgba(0, 231, 182, 0.2);
  border-color: rgba(0, 231, 182, 0.4);
}

input:checked + .toggle-slider:before {
  transform: translateX(16px);
  background-color: #00e7b6;
  box-shadow: 0 0 8px rgba(0, 231, 182, 0.5);
}

.chart-button:hover {
  background: linear-gradient(145deg, rgba(25, 25, 25, 0.98) 0%, rgba(15, 15, 15, 0.95) 100%);
  color: #ffffff;
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.12),
    inset 0 -1px 0 rgba(0, 0, 0, 0.4),
    0 4px 16px rgba(0, 0, 0, 0.5),
    0 2px 4px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(255, 255, 255, 0.03);
  transform: translateY(-1px);
}

.chart-button.active {
  background: linear-gradient(145deg, rgba(0, 231, 182, 0.12) 0%, rgba(0, 180, 140, 0.08) 100%);
  color: #00e7b6;
  border-color: rgba(0, 231, 182, 0.4);
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.3),
    0 4px 20px rgba(0, 231, 182, 0.2),
    0 2px 8px rgba(0, 0, 0, 0.4),
    0 0 30px rgba(0, 231, 182, 0.1);
  transform: translateY(-1px);
}

.chart-button.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 15%;
  right: 15%;
  height: 1px;
  background: linear-gradient(90deg, rgba(0, 231, 182, 0), rgba(0, 231, 182, 0.5), rgba(0, 231, 182, 0));
}

.chart-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.chart-button:disabled:hover {
  transform: none;
  background: linear-gradient(145deg, rgba(20, 20, 20, 0.95) 0%, rgba(10, 10, 10, 0.9) 100%);
  color: #a8abb4;
  border-color: rgba(255, 255, 255, 0.12);
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.08),
    inset 0 -1px 0 rgba(0, 0, 0, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.4),
    0 1px 2px rgba(0, 0, 0, 0.2);
}

.filters-button:hover {
  background: linear-gradient(145deg, rgba(0, 231, 182, 0.15) 0%, rgba(0, 180, 140, 0.1) 100%);
  color: #00f5c4;
  border-color: rgba(0, 231, 182, 0.4);
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.12),
    inset 0 -1px 0 rgba(0, 0, 0, 0.4),
    0 4px 16px rgba(0, 231, 182, 0.15),
    0 2px 8px rgba(0, 0, 0, 0.4),
    0 0 25px rgba(0, 231, 182, 0.1);
  transform: translateY(-1px);
}

.filters-button.active {
  background-color: rgba(0, 231, 182, 0.08);
  color: #00e7b6;
  border-color: rgba(0, 231, 182, 0.3);
  box-shadow: 0 0 20px rgba(0, 231, 182, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.filters-button.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 15%;
  right: 15%;
  height: 1px;
  background: linear-gradient(90deg, rgba(0, 231, 182, 0), rgba(0, 231, 182, 0.5), rgba(0, 231, 182, 0));
}

.chart-controls-divider {
  width: 1px;
  height: 20px;
  background: linear-gradient(to bottom, rgba(0, 231, 182, 0.01), rgba(0, 231, 182, 0.15), rgba(0, 231, 182, 0.01));
  margin: 0 8px;
  opacity: 0.5;
}

.dropdown-arrow {
  font-size: 8px;
  margin-left: 3px;
  color: rgba(0, 231, 182, 0.7);
}

.chart-controls {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-left: auto;
  height: 100%;
  padding: 0 5px;
}

/* Chart content area - responsive to container */
.chart-content {
  display: flex;
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 400px;
  background-color: #0A0A0A;
}

/* Delete mode styling */
.chart-container.delete-mode {
  cursor: crosshair !important;
}

.chart-container.delete-mode * {
  cursor: crosshair !important;
}

/* Drawing Tools Dropdown */
.drawing-tools-dropdown-container {
  position: relative;
}

.drawing-tools-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  background: linear-gradient(180deg, rgba(15, 15, 15, 0.98) 0%, rgba(10, 10, 10, 0.95) 100%);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(20px);
  z-index: 1000;
  min-width: 200px;
  animation: dropdownSlideIn 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-header {
  padding: 12px 16px;
  border-bottom: 1px solid rgba(0, 231, 182, 0.15);
  background: linear-gradient(180deg, rgba(0, 231, 182, 0.08) 0%, rgba(0, 0, 0, 0.8) 100%);
}

.dropdown-header span {
  color: #00e7b6;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-shadow: 0 0 8px rgba(0, 231, 182, 0.4);
}

.dropdown-tools {
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.close-button {
  position: absolute;
  top: 8px;
  right: 8px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  font-size: 16px;
  cursor: pointer;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

/* Dropdown Tool Buttons */
.dropdown-tool-button {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: transparent;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  color: rgba(255, 255, 255, 0.7);
  font-size: 13px;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  width: 100%;
  text-align: left;
}

.dropdown-tool-button:hover {
  background: rgba(255, 255, 255, 0.08);
  color: #ffffff;
}

.dropdown-tool-button.active {
  background: rgba(0, 231, 182, 0.15);
  color: #00e7b6;
  border: 1px solid rgba(0, 231, 182, 0.3);
}

.dropdown-tool-icon-image {
  width: 16px;
  height: 16px;
  filter: brightness(0) saturate(100%) invert(70%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-tool-button:hover .dropdown-tool-icon-image {
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
}

.dropdown-tool-button.active .dropdown-tool-icon-image {
  filter: brightness(0) saturate(100%) invert(85%) sepia(15%) saturate(1000%) hue-rotate(120deg) brightness(95%) contrast(95%);
}

.dropdown-tool-icon,
.dropdown-tool-icon-text {
  color: inherit;
  font-size: 14px;
  font-weight: 600;
}

.dropdown-tool-name {
  flex: 1;
  text-align: left;
  font-weight: 500;
}

.drawing-tools-content {
  flex: 1;
  padding: 8px 4px;
  overflow: hidden;
}

.tool-category {
  margin-bottom: 16px;
}

.category-header {
  font-size: 8px;
  color: rgba(255, 255, 255, 0.5);
  font-weight: 600;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  padding: 0 4px;
  text-align: center;
}

.tool-grid {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
  padding: 8px 0;
}

.tool-button {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, rgba(30, 30, 30, 0.85) 0%, rgba(20, 20, 20, 0.7) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.08),
    0 1px 4px rgba(0, 0, 0, 0.6),
    0 0 0 0 rgba(255, 255, 255, 0);
  backdrop-filter: blur(12px);
}

.tool-button:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%);
  border-color: rgba(255, 255, 255, 0.25);
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.7),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

.tool-button.active {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.12),
    0 2px 10px rgba(0, 0, 0, 0.8),
    0 0 0 1px rgba(255, 255, 255, 0.3);
}

.tool-icon {
  color: rgba(255, 255, 255, 0.7);
  font-size: 18px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.1));
}

.tool-icon-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
  font-weight: 600;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.1));
  text-shadow: 0 0 3px rgba(255, 255, 255, 0.2);
}

.tool-icon-image {
  width: 20px;
  height: 20px;
  filter: brightness(0) saturate(100%) invert(70%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%) drop-shadow(0 0 2px rgba(255, 255, 255, 0.1));
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.tool-button:hover .tool-icon,
.tool-button:hover .tool-icon-text {
  color: #ffffff;
  filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.3));
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.tool-button:hover .tool-icon-image {
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%) drop-shadow(0 0 6px rgba(255, 255, 255, 0.3));
  transform: scale(1.1);
}

.tool-button.active .tool-icon,
.tool-button.active .tool-icon-text {
  color: #ffffff;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));
  text-shadow: 0 0 12px rgba(255, 255, 255, 0.3);
  transform: scale(1.15);
}

.tool-button.active .tool-icon-image {
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%) drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));
  transform: scale(1.15);
}

/* Tooltip for tool buttons */
.tool-button::after {
  content: attr(title);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(20, 20, 20, 0.95);
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  margin-left: 8px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  z-index: 1000;
  backdrop-filter: blur(12px);
}

.tool-button:hover::after {
  opacity: 1;
}

/* Adjust chart area when sidebar is open */
.chart-content .chart-area {
  flex: 1;
}

/* Drawing mode cursor - improved */
.chart-container.drawing-mode {
  cursor: crosshair !important;
  position: relative;
}

.chart-container.drawing-mode * {
  cursor: crosshair !important;
}

/* Improved drawing mode overlay for better interaction */
.chart-container.drawing-mode::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  pointer-events: none;
  border: 2px solid rgba(0, 231, 182, 0.3);
  border-radius: 4px;
  animation: drawing-mode-pulse 2s infinite;
}

@keyframes drawing-mode-pulse {
  0%, 100% {
    border-color: rgba(0, 231, 182, 0.2);
    box-shadow: inset 0 0 20px rgba(0, 231, 182, 0.1);
  }
  50% {
    border-color: rgba(0, 231, 182, 0.4);
    box-shadow: inset 0 0 30px rgba(0, 231, 182, 0.2);
  }
}

.drawing-instruction {
  color: rgba(0, 231, 182, 0.8);
  font-style: italic;
  font-size: 11px;
}

/* Drawing counter badge */
.drawings-counter {
  background-color: #00e7b6;
  color: #000000;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  font-size: 9px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 8px rgba(0, 231, 182, 0.5);
  animation: pulse-badge 2s infinite;
  margin-top: 2px;
}

.drawing-count {
  position: absolute;
  top: -6px;
  right: -6px;
  background-color: #00e7b6;
  color: #000000;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 10px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 8px rgba(0, 231, 182, 0.5);
  animation: pulse-badge 2s infinite;
}

@keyframes pulse-badge {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 8px rgba(0, 231, 182, 0.5);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 12px rgba(0, 231, 182, 0.8);
  }
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .drawing-tools-sidebar {
    width: 50px;
  }

  .tool-button {
    width: 42px;
    height: 42px;
  }

  .tool-icon,
  .tool-icon-text {
    font-size: 14px;
  }

  .tool-icon-image {
    width: 14px;
    height: 14px;
  }

  .tool-button::after {
    display: none; /* Hide tooltips on mobile */
  }
}

/* Compact mode for smaller screens */
@media (max-width: 480px) {
  .drawing-tools-sidebar {
    width: 44px;
  }

  .tool-button {
    width: 36px;
    height: 36px;
  }

  .tool-icon,
  .tool-icon-text {
    font-size: 12px;
  }

  .tool-icon-image {
    width: 12px;
    height: 12px;
  }

  .drawing-tools-content {
    padding: 4px 2px;
  }

  .tool-category {
    margin-bottom: 8px;
  }
}

.chart-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #0A0A0A;
}

.chart-header {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding: 10px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.06);
  font-size: 13px;
  background: linear-gradient(180deg, rgba(15, 15, 15, 0.95) 0%, rgba(10, 10, 10, 0.98) 100%);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.03);
  position: relative;
  z-index: 5;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Drawing Status Indicator */
.drawing-status {
  display: flex;
  align-items: center;
  gap: 12px;
  background-color: rgba(0, 231, 182, 0.1);
  border: 1px solid rgba(0, 231, 182, 0.3);
  border-radius: 6px;
  padding: 6px 12px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    box-shadow: 0 0 8px rgba(0, 231, 182, 0.3);
  }
  50% {
    box-shadow: 0 0 16px rgba(0, 231, 182, 0.5);
  }
}

.drawing-status-text {
  color: #00e7b6;
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 0.3px;
}

.cancel-drawing {
  background: rgba(255, 82, 82, 0.2);
  border: 1px solid rgba(255, 82, 82, 0.4);
  color: #ff5252;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.cancel-drawing:hover {
  background: rgba(255, 82, 82, 0.3);
  border-color: rgba(255, 82, 82, 0.6);
  box-shadow: 0 0 8px rgba(255, 82, 82, 0.3);
}

/* Keyboard Shortcuts Panel */
.shortcuts-panel {
  position: absolute;
  left: 100%;
  top: 0;
  width: 200px;
  background-color: rgba(0, 0, 0, 0.95);
  border: 1px solid rgba(0, 231, 182, 0.2);
  border-radius: 8px;
  padding: 12px;
  margin-left: 8px;
  z-index: 1000;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
}

.shortcuts-header {
  color: #00e7b6;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid rgba(0, 231, 182, 0.2);
  padding-bottom: 6px;
}

.shortcuts-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.shortcut-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.shortcut-key {
  background-color: rgba(0, 231, 182, 0.1);
  border: 1px solid rgba(0, 231, 182, 0.3);
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 10px;
  font-weight: 600;
  color: #00e7b6;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  min-width: 45px;
  text-align: center;
}

.shortcut-desc {
  color: rgba(255, 255, 255, 0.8);
  font-size: 11px;
  flex: 1;
  text-align: right;
}

/* Hide shortcuts panel on mobile */
@media (max-width: 768px) {
  .shortcuts-panel {
    display: none;
  }
}

.symbol-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.symbol {
  font-weight: 600;
  color: #ffffff;
  letter-spacing: -0.02em;
  font-size: 15px;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
}

.price-info {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-right: 30px;
  position: relative;
}

.price-info::after {
  content: '';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  height: 20px;
  width: 1px;
  background: linear-gradient(to bottom, rgba(0, 231, 182, 0.01), rgba(0, 231, 182, 0.2), rgba(0, 231, 182, 0.01));
}

.price-label {
  color: #787b86;
  font-size: 14px;
  font-weight: 500;
  margin-right: 5px;
}

.price-value {
  color: #d1d4dc;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.02em;
  font-weight: 500;
  font-size: 14px;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.1);
}

.volume-info {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(0, 0, 0, 0.3);
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid rgba(0, 231, 182, 0.1);
  margin-left: 8px;
}

.volume-label {
  color: #787b86;
  font-size: 10px;
  font-weight: 500;
  margin-right: 2px;
}

.volume-value {
  color: #d1d4dc;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 500;
  letter-spacing: -0.02em;
  font-size: 10px;
}

.volume-change {
  padding: 1px 4px;
  border-radius: 2px;
  font-size: 9px;
  font-weight: 600;
  letter-spacing: 0px;
  margin-left: 2px;
}

.volume-change.positive {
  color: #00e7b6;
  background-color: rgba(0, 231, 182, 0.1);
  border: 1px solid rgba(0, 231, 182, 0.2);
  text-shadow: 0 0 5px rgba(0, 231, 182, 0.3);
}

.volume-change.negative {
  color: #ffffff;
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
}

.chart-container {
  flex: 1;
  height: 100%;
  min-height: 400px;
  background-color: #0A0A0A;
  position: relative;
}



/* Hide TradingView watermark */
.chart-container .tv-lightweight-charts > div > div > div:last-child,
.chart-container div[id^="tradingview_"] > div > div > div:last-child,
.chart-container canvas + div,
.chart-container div[class*="watermark"],
.chart-container div[class*="attribution"] {
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
  pointer-events: none !important;
}





/* Style chart axis text */
.chart-container text {
  font-family: 'Roboto Mono', monospace !important;
  font-size: 10px !important;
  font-weight: 500 !important;
  fill: rgba(0, 231, 182, 0.7) !important;
  text-shadow: 0 0 5px rgba(0, 231, 182, 0.3);
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .future-header h1 {
    font-size: 28px;
  }

  .chart-toolbar {
    flex-wrap: wrap;
    height: auto;
    padding: 15px;
    gap: 15px;
  }

  .toolbar-left {
    width: 100%;
    order: 1;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .chart-search-bar {
    width: 60%;
  }

  .timeframe-selector {
    width: auto;
  }

  .chart-controls {
    order: 2;
    margin: 0;
    width: 100%;
    justify-content: flex-end;
  }
}

@media (max-width: 768px) {
  .future-header h1 {
    font-size: 24px;
  }

  .toolbar-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .chart-search-bar {
    width: 100%;
  }

  .timeframe-selector {
    width: 100%;
    justify-content: flex-start;
  }

  .chart-button span,
  .filters-button span {
    display: none;
  }

  .chart-header {
    flex-direction: column;
    gap: 15px;
    padding: 15px;
  }

  .symbol-info, .price-info, .volume-info {
    margin-right: 0;
    width: 100%;
  }

  .price-info {
    justify-content: space-between;
  }

  .price-info::after {
    display: none;
  }

  .side-toolbar {
    padding: 8px 4px;
  }

  .side-button {
    width: 32px;
    height: 32px;
    margin-bottom: 12px;
  }

  .chart-container {
    min-height: 350px;
  }
}

@media (max-width: 480px) {
  .future-header h1 {
    font-size: 20px;
  }

  .timeframe-selector {
    justify-content: space-between;
    width: 100%;
  }

  .chart-controls {
    justify-content: center;
  }

  .price-info {
    flex-wrap: wrap;
    gap: 10px;
  }

  .price-value {
    margin-right: 5px;
  }

  .chart-container {
    min-height: 300px;
  }
}

/* Delete Panel Styles */
.delete-panel {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.95);
  border: 1px solid rgba(0, 231, 182, 0.3);
  border-radius: 12px;
  padding: 20px;
  min-width: 300px;
  max-width: 500px;
  max-height: 400px;
  z-index: 1000;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.8),
    0 0 30px rgba(0, 231, 182, 0.2);
  backdrop-filter: blur(20px);
}

.delete-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(0, 231, 182, 0.2);
}

.delete-panel-header h3 {
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.close-delete-panel {
  background: none;
  border: none;
  color: #ffffff;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-delete-panel:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ff6b6b;
}

.drawings-list {
  max-height: 300px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.drawing-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: rgba(0, 231, 182, 0.05);
  border: 1px solid rgba(0, 231, 182, 0.1);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.drawing-item:hover {
  background: rgba(0, 231, 182, 0.1);
  border-color: rgba(0, 231, 182, 0.2);
}

.drawing-info {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

.delete-drawing-btn {
  background: #ff4444;
  border: none;
  color: #ffffff;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.delete-drawing-btn:hover {
  background: #ff6666;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 68, 68, 0.3);
}

/* Star button styling */
.star-button {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 6px;
  padding: 4px 8px;
  margin-left: 8px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.star-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.25);
  color: rgba(255, 255, 255, 0.8);
  transform: scale(1.05);
}

.star-button.starred {
  background: rgba(255, 193, 7, 0.2);
  border-color: rgba(255, 193, 7, 0.4);
  color: #ffc107;
}

.star-button.starred:hover {
  background: rgba(255, 193, 7, 0.3);
  border-color: rgba(255, 193, 7, 0.5);
}
