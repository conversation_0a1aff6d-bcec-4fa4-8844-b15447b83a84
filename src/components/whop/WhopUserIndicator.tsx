import React from 'react';
import { useWhopUser, useWhopAccess } from '@/contexts/WhopContext';
import { Badge } from '@/components/ui/badge';
import { Crown, Users, Shield } from 'lucide-react';
import { cn } from '@/lib/utils';

interface WhopUserIndicatorProps {
  className?: string;
  showDetails?: boolean;
  variant?: 'badge' | 'full' | 'minimal';
}

const WhopUserIndicator: React.FC<WhopUserIndicatorProps> = ({ 
  className,
  showDetails = false,
  variant = 'badge'
}) => {
  const { isWhopUser, user, username } = useWhopUser();
  const { hasAccess, accessLevel, isAdmin, isCustomer } = useWhopAccess();

  // Don't render anything if not a Whop user
  if (!isWhopUser || !user) {
    return null;
  }

  const getAccessIcon = () => {
    if (isAdmin) return <Crown className="h-3 w-3" />;
    if (isCustomer) return <Users className="h-3 w-3" />;
    return <Shield className="h-3 w-3" />;
  };

  const getAccessColor = () => {
    if (isAdmin) return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
    if (isCustomer) return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
    return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
  };

  const getAccessLabel = () => {
    if (isAdmin) return 'Admin';
    if (isCustomer) return 'Customer';
    return 'No Access';
  };

  if (variant === 'minimal') {
    return (
      <div className={cn("flex items-center space-x-1", className)}>
        {getAccessIcon()}
        <span className="text-xs text-gray-400">Whop</span>
      </div>
    );
  }

  if (variant === 'badge') {
    return (
      <Badge 
        variant="outline" 
        className={cn(
          "flex items-center space-x-1 text-xs",
          getAccessColor(),
          className
        )}
      >
        {getAccessIcon()}
        <span>Whop {hasAccess ? getAccessLabel() : 'User'}</span>
      </Badge>
    );
  }

  if (variant === 'full') {
    return (
      <div className={cn(
        "flex items-center space-x-3 p-3 rounded-lg border",
        getAccessColor(),
        className
      )}>
        <div className="flex items-center space-x-2">
          {getAccessIcon()}
          <div>
            <div className="text-sm font-medium">
              {username || 'Whop User'}
            </div>
            {showDetails && (
              <div className="text-xs opacity-75">
                {getAccessLabel()} • {hasAccess ? 'Access Granted' : 'No Access'}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  return null;
};

export default WhopUserIndicator;
