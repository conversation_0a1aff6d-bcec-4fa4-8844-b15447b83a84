import React from 'react';
import Who<PERSON><PERSON>ottomNavigation from './WhopBottomNavigation';
import { useWhopUser } from '@/contexts/WhopContext';

interface WhopPageLayoutProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * Layout wrapper for Whop users that provides consistent mobile-style navigation
 * across all pages. This component should be used to wrap page content for Whop users.
 */
const WhopPageLayout: React.FC<WhopPageLayoutProps> = ({ children, className = '' }) => {
  const { isWhopUser } = useWhopUser();

  // If not a Whop user, just render children without the layout
  if (!isWhopUser) {
    return <>{children}</>;
  }

  return (
    <div className={`h-screen bg-[#0A0A0A] relative overflow-hidden ${className}`}>
      {/* Scrollable main content area with bottom padding to prevent overlap with navigation */}
      <div className="h-full overflow-auto pb-20 whop-scrollable">
        {children}
      </div>

      {/* Whop-specific bottom navigation - Fixed at bottom */}
      <WhopBottomNavigation />

      {/* Custom scrollbar styles for Whop layout */}
      <style>{`
        .whop-scrollable::-webkit-scrollbar {
          width: 6px;
        }

        .whop-scrollable::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.02);
        }

        .whop-scrollable::-webkit-scrollbar-thumb {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 3px;
        }

        .whop-scrollable::-webkit-scrollbar-thumb:hover {
          background: rgba(255, 255, 255, 0.15);
        }
      `}</style>
    </div>
  );
};

export default WhopPageLayout;
