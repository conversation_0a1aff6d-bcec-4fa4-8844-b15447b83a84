import React, { useEffect, useState } from 'react';
import { useParams, Navigate } from 'react-router-dom';
import { useWhop, useWhopAccess } from '@/contexts/WhopContext';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, Shield, AlertCircle } from 'lucide-react';

interface WhopExperiencePageProps {
  children?: React.ReactNode;
}

const WhopExperiencePage: React.FC<WhopExperiencePageProps> = ({ children }) => {
  const { experienceId } = useParams<{ experienceId: string }>();
  const { isWhopUser, whopUser, isLoading, error, refreshWhopAuth } = useWhop();
  const { hasAccess } = useWhopAccess();
  const [isRetrying, setIsRetrying] = useState(false);

  // Log Whop user access for debugging
  useEffect(() => {
    if (!isLoading) {
      console.log('🔍 Whop Experience Access Check:', {
        experienceId,
        isWhopUser,
        username: whopUser?.username,
        hasAccess,
        error
      });
    }
  }, [experienceId, isWhopUser, whopUser, hasAccess, isLoading, error]);

  const handleRetry = async () => {
    setIsRetrying(true);
    await refreshWhopAuth();
    setIsRetrying(false);
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-[#0A0A0A]">
        <Card className="p-8 bg-gray-900/50 border-gray-800">
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
            <div className="text-center">
              <h3 className="text-lg font-semibold text-white">Verifying Access</h3>
              <p className="text-gray-400 mt-1">Checking your Whop permissions...</p>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-[#0A0A0A]">
        <Card className="p-8 bg-gray-900/50 border-gray-800 max-w-md">
          <div className="flex flex-col items-center space-y-4">
            <AlertCircle className="h-12 w-12 text-red-500" />
            <div className="text-center">
              <h3 className="text-lg font-semibold text-white">Authentication Error</h3>
              <p className="text-gray-400 mt-1">{error}</p>
            </div>
            <Button 
              onClick={handleRetry} 
              disabled={isRetrying}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isRetrying ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Retrying...
                </>
              ) : (
                'Try Again'
              )}
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  // Redirect non-Whop users to home
  if (!isWhopUser) {
    console.log('❌ Non-Whop user trying to access experience, redirecting to home');
    return <Navigate to="/" replace />;
  }

  // Show access denied for users without access
  if (!hasAccess) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-[#0A0A0A]">
        <Card className="p-8 bg-gray-900/50 border-gray-800 max-w-md">
          <div className="flex flex-col items-center space-y-4">
            <Shield className="h-12 w-12 text-yellow-500" />
            <div className="text-center">
              <h3 className="text-lg font-semibold text-white">Access Denied</h3>
              <p className="text-gray-400 mt-1">
                You don't have access to this experience.
              </p>
              <p className="text-sm text-gray-500 mt-2">
                Experience ID: {experienceId}
              </p>
            </div>
            <Button 
              onClick={handleRetry}
              variant="outline"
              className="border-gray-600 text-gray-300 hover:bg-gray-800"
            >
              Refresh Access
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  // Render children directly without any custom Whop UI
  return <>{children}</>;
};

export default WhopExperiencePage;
