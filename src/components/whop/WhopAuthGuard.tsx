import React from 'react';
import { useWhop, useWhopAccess } from '@/contexts/WhopContext';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, Shield, AlertCircle, Crown, Users } from 'lucide-react';

interface WhopAuthGuardProps {
  children: React.ReactNode;
  requireAccess?: boolean;
  requireAdmin?: boolean;
  loadingComponent?: React.ReactNode;
}

const WhopAuthGuard: React.FC<WhopAuthGuardProps> = ({
  children,
  requireAccess = true,
  requireAdmin = false,
  loadingComponent
}) => {
  const { isWhopUser, whopUser, isLoading, error, refreshWhopAuth } = useWhop();
  const { hasAccess, accessLevel, isAdmin } = useWhopAccess();

  // Show loading state
  if (isLoading) {
    if (loadingComponent) {
      return <>{loadingComponent}</>;
    }

    return (
      <div className="flex items-center justify-center p-8">
        <Card className="p-6 bg-gray-900/50 border-gray-800">
          <div className="flex flex-col items-center space-y-3">
            <Loader2 className="h-6 w-6 animate-spin text-blue-500" />
            <p className="text-sm text-gray-400">Verifying Whop access...</p>
          </div>
        </Card>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex items-center justify-center p-8">
        <Card className="p-6 bg-gray-900/50 border-gray-800 max-w-md">
          <div className="flex flex-col items-center space-y-4">
            <AlertCircle className="h-8 w-8 text-red-500" />
            <div className="text-center">
              <h3 className="text-lg font-semibold text-white">Authentication Error</h3>
              <p className="text-gray-400 text-sm mt-1">{error}</p>
            </div>
            <Button 
              onClick={refreshWhopAuth}
              size="sm"
              className="bg-blue-600 hover:bg-blue-700"
            >
              Retry
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  // Check if user is a Whop user (if we're in a Whop context)
  if (!isWhopUser) {
    // If we're not in a Whop context, just render children
    return <>{children}</>;
  }

  // Check access requirements
  if (requireAccess && !hasAccess) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="flex items-center justify-center p-8">
        <Card className="p-6 bg-gray-900/50 border-gray-800 max-w-md">
          <div className="flex flex-col items-center space-y-4">
            <Shield className="h-8 w-8 text-yellow-500" />
            <div className="text-center">
              <h3 className="text-lg font-semibold text-white">Access Required</h3>
              <p className="text-gray-400 text-sm mt-1">
                You need access to this experience to view this content.
              </p>
              {whopUser && (
                <p className="text-xs text-gray-500 mt-2">
                  Logged in as: {whopUser.username}
                </p>
              )}
            </div>
            <Button 
              onClick={refreshWhopAuth}
              size="sm"
              variant="outline"
              className="border-gray-600 text-gray-300 hover:bg-gray-800"
            >
              Refresh Access
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  // Check admin requirements
  if (requireAdmin && !isAdmin) {
    return (
      <div className="flex items-center justify-center p-8">
        <Card className="p-6 bg-gray-900/50 border-gray-800 max-w-md">
          <div className="flex flex-col items-center space-y-4">
            <Crown className="h-8 w-8 text-yellow-500" />
            <div className="text-center">
              <h3 className="text-lg font-semibold text-white">Admin Access Required</h3>
              <p className="text-gray-400 text-sm mt-1">
                This content is only available to administrators.
              </p>
              <div className="flex items-center justify-center space-x-2 mt-2">
                <Users className="h-4 w-4 text-blue-400" />
                <span className="text-xs text-blue-400">
                  Current access: {accessLevel}
                </span>
              </div>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  // All checks passed, render children
  return <>{children}</>;
};

export default WhopAuthGuard;
