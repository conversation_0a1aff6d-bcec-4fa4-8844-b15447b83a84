import React, { useState } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Loader2, Users, AlertTriangle, CheckCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { distributeAgentToWhopMembers, WhopDistributionRequest } from '@/services/whopDistributionService';
import { useWhopUser } from '@/contexts/WhopContext';

interface WhopDistributionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  agentId: string;
  agentName: string;
  onSuccess?: () => void;
}

const WhopDistributionDialog: React.FC<WhopDistributionDialogProps> = ({
  isO<PERSON>,
  onClose,
  agentId,
  agentName,
  onSuccess
}) => {
  const [isDistributing, setIsDistributing] = useState(false);
  const { toast } = useToast();
  const { user: whopUser } = useWhopUser();

  const handleDistribute = async () => {
    if (!whopUser) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Whop user information not available"
      });
      return;
    }

    console.log('🚀 Starting agent distribution for Whop user:', {
      whopUserId: whopUser.id,
      username: whopUser.username,
      agentId,
      agentName
    });

    setIsDistributing(true);

    try {
      // Check authentication state before proceeding
      console.log('🔍 Checking authentication state...');
      const { supabase } = await import('@/integrations/supabase/client');
      const { data: { session } } = await supabase.auth.getSession();
      const { data: { user } } = await supabase.auth.getUser();

      console.log('🔐 Current auth state:', {
        hasSession: !!session,
        hasAccessToken: !!session?.access_token,
        hasUser: !!user,
        isWhopUser: user?.user_metadata?.isWhopUser,
        whopUserId: user?.user_metadata?.whop_user_id
      });

      // Get company ID from environment or user context
      const whopCompanyId = import.meta.env.VITE_WHOP_COMPANY_ID;

      if (!whopCompanyId) {
        throw new Error('Whop company ID not configured');
      }

      const request: WhopDistributionRequest = {
        agentId,
        whopCompanyId,
        // Optional: include experience ID if available
        whopExperienceId: undefined
      };

      console.log('📤 Sending distribution request:', request);

      const result = await distributeAgentToWhopMembers(request);

      if (result.success) {
        toast({
          title: "Distribution Successful",
          description: result.message || `Agent "${agentName}" has been distributed to all Whop members`,
          duration: 5000,
        });

        // Show additional info if there were any errors
        if (result.errors && result.errors.length > 0) {
          console.warn('Distribution completed with some errors:', result.errors);
          toast({
            variant: "default",
            title: "Partial Success",
            description: `Distributed to ${result.membersAdded} members. Some members could not be processed.`,
            duration: 7000,
          });
        }

        onSuccess?.();
        onClose();
      } else {
        throw new Error(result.error || 'Distribution failed');
      }
    } catch (error) {
      console.error('❌ Distribution error:', error);
      
      const errorMessage = error instanceof Error ? error.message : 'Failed to distribute agent';
      
      toast({
        variant: "destructive",
        title: "Distribution Failed",
        description: errorMessage,
        duration: 7000,
      });
    } finally {
      setIsDistributing(false);
    }
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent className="bg-[#1A1A1A] border-[#2A2A2A] text-white max-w-md">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2 text-white">
            <Users className="h-5 w-5 text-blue-400" />
            Distribute to All Whop Members
          </AlertDialogTitle>
          <AlertDialogDescription className="text-white/70 space-y-3">
            <div>
              Are you sure you want to make the agent <strong>"{agentName}"</strong> available 
              to all current and future members of your Whop?
            </div>
            
            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3">
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-blue-400 mt-0.5 flex-shrink-0" />
                <div className="text-sm">
                  <div className="font-medium text-blue-400 mb-1">This will:</div>
                  <ul className="space-y-1 text-white/60">
                    <li>• Add the agent to all current members' libraries</li>
                    <li>• Automatically add it to new members who join</li>
                    <li>• Allow members to use the agent immediately</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3">
              <div className="flex items-start gap-2">
                <AlertTriangle className="h-4 w-4 text-yellow-400 mt-0.5 flex-shrink-0" />
                <div className="text-sm">
                  <div className="font-medium text-yellow-400 mb-1">Note:</div>
                  <div className="text-white/60">
                    Only Whop owners and admins can distribute agents to all members.
                  </div>
                </div>
              </div>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <AlertDialogFooter>
          <AlertDialogCancel 
            onClick={onClose}
            disabled={isDistributing}
            className="bg-transparent border-[#2A2A2A] text-white/70 hover:bg-[#2A2A2A] hover:text-white"
          >
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDistribute}
            disabled={isDistributing}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            {isDistributing ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Distributing...
              </>
            ) : (
              <>
                <Users className="h-4 w-4 mr-2" />
                Distribute to All Members
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default WhopDistributionDialog;
