import React from 'react';
import { useWhopCompetitionPermissions } from '@/hooks/useWhopCompetitionPermissions';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { AlertCircle, CheckCircle, Shield, Users, Crown } from 'lucide-react';

interface CompetitionPermissionCheckerProps {
  experienceId: string;
  onCanCreate?: () => void;
  children?: React.ReactNode;
}

/**
 * Component that checks and displays competition creation permissions for a Whop experience
 * 
 * @example
 * ```tsx
 * <CompetitionPermissionChecker 
 *   experienceId="exp_ThljdpAF70d4Af"
 *   onCanCreate={() => setShowCreateForm(true)}
 * >
 *   <CreateCompetitionForm />
 * </CompetitionPermissionChecker>
 * ```
 */
export function CompetitionPermissionChecker({ 
  experienceId, 
  onCanCreate,
  children 
}: CompetitionPermissionCheckerProps) {
  const { permissions, loading, error, refetch } = useWhopCompetitionPermissions(experienceId);

  if (loading) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardContent className="pt-6">
          <div className="flex items-center justify-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span className="text-sm text-gray-600">Checking permissions...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full max-w-md mx-auto border-red-200">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-red-600">
            <AlertCircle className="h-5 w-5" />
            <span>Permission Check Failed</span>
          </CardTitle>
          <CardDescription className="text-red-500">
            {error}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button 
            onClick={refetch} 
            variant="outline" 
            size="sm"
            className="w-full"
          >
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!permissions) {
    return (
      <Card className="w-full max-w-md mx-auto border-gray-200">
        <CardContent className="pt-6">
          <div className="text-center text-gray-500">
            No permission data available
          </div>
        </CardContent>
      </Card>
    );
  }

  const getAccessLevelInfo = (accessLevel: string) => {
    switch (accessLevel) {
      case 'admin':
        return {
          icon: <Crown className="h-4 w-4" />,
          label: 'Admin',
          color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
          description: 'Full administrative access'
        };
      case 'customer':
        return {
          icon: <Users className="h-4 w-4" />,
          label: 'Member',
          color: 'bg-blue-100 text-blue-800 border-blue-200',
          description: 'Standard member access'
        };
      case 'no_access':
        return {
          icon: <AlertCircle className="h-4 w-4" />,
          label: 'No Access',
          color: 'bg-red-100 text-red-800 border-red-200',
          description: 'No access to this experience'
        };
      default:
        return {
          icon: <Shield className="h-4 w-4" />,
          label: 'Unknown',
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          description: 'Unknown access level'
        };
    }
  };

  const accessInfo = getAccessLevelInfo(permissions.accessLevel);

  if (!permissions.hasAccess) {
    return (
      <Card className="w-full max-w-md mx-auto border-red-200">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-red-600">
            <AlertCircle className="h-5 w-5" />
            <span>Access Denied</span>
          </CardTitle>
          <CardDescription>
            You do not have access to this Whop experience.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Badge variant="outline" className={accessInfo.color}>
            {accessInfo.icon}
            <span className="ml-1">{accessInfo.label}</span>
          </Badge>
        </CardContent>
      </Card>
    );
  }

  if (!permissions.canCreateCompetitions) {
    return (
      <Card className="w-full max-w-md mx-auto border-orange-200">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-orange-600">
            <Shield className="h-5 w-5" />
            <span>Insufficient Permissions</span>
          </CardTitle>
          <CardDescription>
            Only admins can create competitions in this experience.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Your access level:</span>
            <Badge variant="outline" className={accessInfo.color}>
              {accessInfo.icon}
              <span className="ml-1">{accessInfo.label}</span>
            </Badge>
          </div>
          <div className="text-xs text-gray-500">
            Contact an admin to request competition creation permissions.
          </div>
        </CardContent>
      </Card>
    );
  }

  // User can create competitions
  return (
    <div className="space-y-4">
      <Card className="w-full max-w-md mx-auto border-green-200">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-green-600">
            <CheckCircle className="h-5 w-5" />
            <span>Competition Creation Allowed</span>
          </CardTitle>
          <CardDescription>
            You have permission to create competitions in this experience.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Your access level:</span>
            <Badge variant="outline" className={accessInfo.color}>
              {accessInfo.icon}
              <span className="ml-1">{accessInfo.label}</span>
            </Badge>
          </div>
          
          {permissions.appName && (
            <div className="text-xs text-gray-500">
              App: {permissions.appName}
            </div>
          )}

          {onCanCreate && (
            <Button 
              onClick={onCanCreate} 
              className="w-full bg-green-600 hover:bg-green-700"
            >
              Create Competition
            </Button>
          )}
        </CardContent>
      </Card>

      {/* Render children (like the competition creation form) if user can create */}
      {children}
    </div>
  );
}

export default CompetitionPermissionChecker;
