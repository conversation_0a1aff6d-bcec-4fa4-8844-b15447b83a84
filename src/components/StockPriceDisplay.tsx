import React, { useState, useEffect } from 'react';
import { isCryptoCurrency, formatPolygonSymbol } from '@/utils/symbolUtils';
import { fetchLatestPriceData } from '@/services/polygonService';

interface StockPriceDisplayProps {
  symbol: string;
  timeframe?: string;
  render?: (price: string, percentChange: string) => React.ReactNode;
}

// Helper function to format number with commas
const formatNumberWithCommas = (num: number | string): string => {
  // Convert to number first
  const numValue = typeof num === 'string' ? parseFloat(num) : num;

  // For very small numbers (like crypto prices under $0.01)
  if (numValue < 0.01) {
    // Find the first non-zero digit after decimal and show a few more digits
    const priceStr = numValue.toString();
    const firstNonZero = priceStr.match(/\.0*[1-9]/)?.[0].length - 1 || 8;
    return numValue.toFixed(firstNonZero + 2); // Show 2 more digits after first non-zero
  }

  // For numbers >= 1000, add commas
  if (numValue >= 1000) {
    const parts = numValue.toFixed(2).split('.');
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    return parts.join('.');
  }

  // For numbers between 0.01 and 999.99, just show appropriate decimal places
  if (numValue >= 0.01) {
    // If it's a whole number, don't show decimals
    if (Number.isInteger(numValue)) {
      return numValue.toString();
    }
    // Otherwise show up to 2 decimal places
    return numValue.toFixed(2);
  }

  // Fallback
  return numValue.toString();
};

const StockPriceDisplay: React.FC<StockPriceDisplayProps> = ({ symbol, timeframe = '1D', render }) => {
  const [stockPrice, setStockPrice] = useState<string | null>(null);
  const [volume, setVolume] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isCrypto, setIsCrypto] = useState(false);
  const [dataType, setDataType] = useState<string | null>(null);
  const [activeTimeframe, setActiveTimeframe] = useState<string>(timeframe);

  useEffect(() => {
    if (!symbol) {
      setIsLoading(false);
      return;
    }

    let isMounted = true;

    const fetchStockData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Wait for the async isCryptoCurrency check
        const cryptoCheck = await isCryptoCurrency(symbol);
        if (!isMounted) return;
        setIsCrypto(cryptoCheck);

        // Use the new fetchLatestPriceData function with timeframe
        const priceData = await fetchLatestPriceData(symbol, activeTimeframe);
        if (!isMounted) return;

        if (priceData && priceData.length > 0) {
          const result = priceData[0];

          // Format volume
          const formattedVolume = result.volume >= 1000000
            ? `${(result.volume/1000000).toFixed(1)}M`
            : result.volume.toString();

          // For very small numbers, preserve all significant digits
          const price = result.price;
          let formattedPrice;
          if (price < 0.01) {
            // Find the first non-zero digit after decimal and show a few more digits
            const priceStr = price.toString();
            const firstNonZero = priceStr.match(/\.0*[1-9]/)?.[0].length - 1 || 8;
            formattedPrice = price.toFixed(firstNonZero + 2); // Show 2 more digits after first non-zero
          } else {
            // Use original decimal place logic for larger numbers
            formattedPrice = formatNumberWithCommas(price);
          }

          setStockPrice(formattedPrice);
          setVolume(formattedVolume);
          setDataType(result.dataType || 'unknown');
        } else {
          setError('No data available for this symbol');
        }
      } catch (error) {
        if (!isMounted) return;
        console.error(`Error fetching data for ${symbol}:`, error);
        setError('Failed to load price data');
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    fetchStockData();

    return () => {
      isMounted = false;
    };
  }, [symbol, activeTimeframe]);

  if (isLoading) {
    return <div data-testid="loading" className=""></div>;
  }

  if (error) {
    return <div className="text-red-500 text-sm">{error}</div>;
  }

  // If render prop is provided, use it
  if (render && stockPrice) {
    return <>{render(stockPrice, '')}</>;
  }

  // Default display
  return (
    <div className="flex flex-col">
      <div className="flex items-baseline gap-2">
        <span className="text-2xl font-bold">${stockPrice || "0"}</span>
        {isCrypto && (
          <span className="text-xs bg-blue-500/20 text-blue-400 px-2 py-0.5 rounded-full">
            Crypto
          </span>
        )}
        {dataType === 'live' && (
          <span className="text-xs bg-green-500/20 text-green-400 px-2 py-0.5 rounded-full">
            Live
          </span>
        )}
        {dataType === 'crypto_live' && (
          <span className="text-xs bg-purple-500/20 text-purple-400 px-2 py-0.5 rounded-full">
            Live
          </span>
        )}
        {dataType === 'last_market_close' && (
          <span className="text-xs bg-blue-500/20 text-blue-400 px-2 py-0.5 rounded-full">
            Last Close
          </span>
        )}
      </div>
    </div>
  );
};

export default StockPriceDisplay;