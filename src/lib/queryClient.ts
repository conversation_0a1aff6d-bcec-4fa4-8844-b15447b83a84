/**
 * Optimized React Query Client Configuration
 * Handles caching, background updates, and performance optimizations
 */

import { QueryClient, QueryCache, MutationCache } from '@tanstack/react-query';
import { preloadingService } from '@/services/preloadingService';

// Custom error handler for queries
const handleQueryError = (error: unknown, query: any) => {
  console.error('Query error:', {
    queryKey: query.queryKey,
    error: error instanceof Error ? error.message : 'Unknown error',
    meta: query.meta,
  });

  // Log to analytics if available
  if (typeof window !== 'undefined' && (window as any).posthog) {
    (window as any).posthog.capture('query_error', {
      queryKey: query.queryKey,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

// Custom success handler for queries
const handleQuerySuccess = (data: unknown, query: any) => {
  // Cache successful responses in our preloading service
  if (query.queryKey && Array.isArray(query.queryKey)) {
    const cacheKey = query.queryKey.join('-');
    preloadingService.setCache?.(cacheKey, data, 5 * 60 * 1000); // 5 minutes
  }
};

// Create optimized query cache
const queryCache = new QueryCache({
  onError: handleQueryError,
  onSuccess: handleQuerySuccess,
});

// Create optimized mutation cache
const mutationCache = new MutationCache({
  onError: (error, variables, context, mutation) => {
    console.error('Mutation error:', {
      mutationKey: mutation.options.mutationKey,
      error: error instanceof Error ? error.message : 'Unknown error',
      variables,
    });

    // Log to analytics if available
    if (typeof window !== 'undefined' && (window as any).posthog) {
      (window as any).posthog.capture('mutation_error', {
        mutationKey: mutation.options.mutationKey,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  },
  onSuccess: (data, variables, context, mutation) => {
    // Invalidate related queries on successful mutations
    if (mutation.options.mutationKey) {
      const mutationKey = Array.isArray(mutation.options.mutationKey) 
        ? mutation.options.mutationKey[0] 
        : mutation.options.mutationKey;
      
      // Define related query keys to invalidate
      const invalidationMap: Record<string, string[]> = {
        'updateUser': ['user', 'profile'],
        'updateWatchlist': ['watchlist', 'portfolio'],
        'createAgent': ['agents', 'user-agents'],
        'updateAgent': ['agents', 'user-agents'],
        'deleteAgent': ['agents', 'user-agents'],
        'createTrade': ['trades', 'portfolio'],
        'updateTrade': ['trades', 'portfolio'],
      };

      const keysToInvalidate = invalidationMap[mutationKey as string];
      if (keysToInvalidate) {
        keysToInvalidate.forEach(key => {
          queryClient.invalidateQueries({ queryKey: [key] });
        });
      }
    }
  },
});

// Create the optimized query client
export const queryClient = new QueryClient({
  queryCache,
  mutationCache,
  defaultOptions: {
    queries: {
      // Caching strategy
      staleTime: 5 * 60 * 1000, // 5 minutes - data is fresh for 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes - keep in cache for 10 minutes
      
      // Retry strategy
      retry: (failureCount, error) => {
        // Don't retry on 4xx errors (client errors)
        if (error instanceof Error && 'status' in error) {
          const status = (error as any).status;
          if (status >= 400 && status < 500) {
            return false;
          }
        }
        
        // Retry up to 3 times for other errors
        return failureCount < 3;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
      
      // Background updates
      refetchOnWindowFocus: false, // Disable refetch on window focus for better performance
      refetchOnReconnect: true, // Refetch when reconnecting to internet
      refetchOnMount: true, // Refetch when component mounts
      
      // Network mode
      networkMode: 'online', // Only run queries when online
      
      // Suspense and error boundaries
      throwOnError: false, // Don't throw errors, handle them gracefully
      
      // Performance optimizations
      structuralSharing: true, // Enable structural sharing for better performance
      
      // Meta information for debugging
      meta: {
        errorMessage: 'Failed to fetch data',
      },
    },
    mutations: {
      // Retry strategy for mutations
      retry: (failureCount, error) => {
        // Don't retry mutations on client errors
        if (error instanceof Error && 'status' in error) {
          const status = (error as any).status;
          if (status >= 400 && status < 500) {
            return false;
          }
        }
        
        // Retry once for network errors
        return failureCount < 1;
      },
      retryDelay: 1000, // 1 second delay for mutation retries
      
      // Network mode
      networkMode: 'online',
      
      // Error handling
      throwOnError: false,
      
      // Meta information
      meta: {
        errorMessage: 'Failed to update data',
      },
    },
  },
});

// Prefetch commonly used queries
export const prefetchCommonQueries = async () => {
  try {
    // Prefetch user data
    await queryClient.prefetchQuery({
      queryKey: ['user'],
      queryFn: () => preloadingService.preloadUserData(),
      staleTime: 5 * 60 * 1000,
    });

    // Prefetch watchlist data
    await queryClient.prefetchQuery({
      queryKey: ['watchlist'],
      queryFn: () => preloadingService.preloadWatchlistData(),
      staleTime: 3 * 60 * 1000,
    });

    // Prefetch popular market data
    const popularSymbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'NVDA'];
    await Promise.allSettled(
      popularSymbols.map(symbol =>
        queryClient.prefetchQuery({
          queryKey: ['market-data', symbol],
          queryFn: () => preloadingService.preloadSymbolData(symbol),
          staleTime: 2 * 60 * 1000,
        })
      )
    );
  } catch (error) {
    console.warn('Failed to prefetch common queries:', error);
  }
};

// Utility functions for query management
export const invalidateUserQueries = () => {
  queryClient.invalidateQueries({ queryKey: ['user'] });
  queryClient.invalidateQueries({ queryKey: ['profile'] });
  queryClient.invalidateQueries({ queryKey: ['watchlist'] });
};

export const invalidateMarketQueries = () => {
  queryClient.invalidateQueries({ queryKey: ['market-data'] });
  queryClient.invalidateQueries({ queryKey: ['chart-data'] });
};

export const invalidateAgentQueries = () => {
  queryClient.invalidateQueries({ queryKey: ['agents'] });
  queryClient.invalidateQueries({ queryKey: ['user-agents'] });
};

// Clear all caches (useful for logout)
export const clearAllCaches = () => {
  queryClient.clear();
  preloadingService.clearExpiredCache?.();
};

// Background sync for offline support
export const syncOfflineData = async () => {
  try {
    // Get all cached queries
    const queryCache = queryClient.getQueryCache();
    const queries = queryCache.getAll();
    
    // Refetch stale queries
    const staleQueries = queries.filter(query => 
      query.isStale() && query.state.status === 'success'
    );
    
    await Promise.allSettled(
      staleQueries.map(query => query.fetch())
    );
    
    console.log(`Synced ${staleQueries.length} stale queries`);
  } catch (error) {
    console.warn('Failed to sync offline data:', error);
  }
};

// Performance monitoring
export const getQueryCacheStats = () => {
  const cache = queryClient.getQueryCache();
  const queries = cache.getAll();
  
  return {
    totalQueries: queries.length,
    successQueries: queries.filter(q => q.state.status === 'success').length,
    errorQueries: queries.filter(q => q.state.status === 'error').length,
    loadingQueries: queries.filter(q => q.state.status === 'pending').length,
    staleQueries: queries.filter(q => q.isStale()).length,
    cacheSize: JSON.stringify(queries.map(q => q.state.data)).length,
  };
};

// Initialize background processes
if (typeof window !== 'undefined') {
  // Prefetch common queries after a short delay
  setTimeout(() => {
    prefetchCommonQueries();
  }, 2000);
  
  // Set up periodic cache cleanup
  setInterval(() => {
    queryClient.getQueryCache().clear();
  }, 30 * 60 * 1000); // Every 30 minutes
  
  // Set up offline sync
  window.addEventListener('online', () => {
    syncOfflineData();
  });
  
  // Performance monitoring in development
  if (process.env.NODE_ENV === 'development') {
    setInterval(() => {
      const stats = getQueryCacheStats();
      console.log('Query Cache Stats:', stats);
    }, 60 * 1000); // Every minute in development
  }
}
