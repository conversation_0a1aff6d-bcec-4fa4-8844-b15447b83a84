/**
 * Client library for communicating with the Whop Intermediary Server
 * This replaces direct Whop SDK usage in the main Vite app
 */

import { detectCurrentApp, getIntermediaryUrl } from './whop-app-config';

// Configuration
const getIntermediaryServerUrl = () => {
  // Always use production URL since user runs in production through Whop iframe
  const configuredUrl = import.meta.env.VITE_WHOP_INTERMEDIARY_URL || 'https://whop-intermediary-server.vercel.app';
  console.log('🔧 Using production intermediary server URL:', configuredUrl);
  return configuredUrl;
};

const INTERMEDIARY_SERVER_URL = getIntermediaryServerUrl();

// Determine if we should use the proxy or direct calls
// For standalone trade-app deployment, always use direct calls to intermediary server
const isStandaloneTradeApp = window.location.hostname.includes('vercel.app') ||
                            window.location.hostname === 'localhost';
const USE_PROXY = !isStandaloneTradeApp && window.parent !== window;

console.log('🔧 Whop client configuration:', {
  isStandaloneTradeApp,
  useProxy: USE_PROXY,
  hostname: window.location.hostname,
  hasParent: window.parent !== window
});

interface WhopUser {
  id: string;
  username: string;
  email?: string;
  profilePicUrl?: string;
  discordId?: string;
  twitterUsername?: string;
}

interface WhopAccessResult {
  hasAccess: boolean;
  accessLevel: 'no_access' | 'customer' | 'admin';
  userId: string;
  companyId: string;
  membershipInfo?: any;
  timestamp: string;
}

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  user?: WhopUser;
  access?: WhopAccessResult;
  error?: string;
  message?: string;
}

/**
 * Base API call function with error handling and Whop token forwarding
 */
async function apiCall<T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  try {
    let url: string;
    const currentApp = detectCurrentApp();

    if (USE_PROXY) {
      // Use the main app's proxy endpoint to forward requests with Whop tokens
      // Remove leading slash from endpoint to avoid double encoding
      const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
      // Add app parameter to the endpoint
      const endpointWithApp = cleanEndpoint.includes('?')
        ? `${cleanEndpoint}&app=${currentApp}`
        : `${cleanEndpoint}?app=${currentApp}`;
      url = `/api/whop-proxy?endpoint=${encodeURIComponent(endpointWithApp)}`;
    } else {
      // Direct call to intermediary server (won't have Whop tokens)
      const endpointWithApp = endpoint.includes('?')
        ? `${endpoint}&app=${currentApp}`
        : `${endpoint}?app=${currentApp}`;
      url = `${INTERMEDIARY_SERVER_URL}/api${endpointWithApp}`;
    }

    console.log('🚀 Making API call:', {
      endpoint,
      url,
      currentApp,
      useProxy: USE_PROXY,
      intermediaryServerUrl: INTERMEDIARY_SERVER_URL,
      method: options.method || 'GET'
    });

    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      credentials: 'include', // Include cookies for Whop proxy
    });

    // Get response text first to handle both JSON and HTML responses
    const responseText = await response.text();

    console.log('📥 Raw response received:', {
      status: response.status,
      statusText: response.statusText,
      contentType: response.headers.get('content-type'),
      url: response.url,
      responseLength: responseText.length,
      responsePreview: responseText.substring(0, 200) + (responseText.length > 200 ? '...' : '')
    });

    // Try to parse as JSON
    let data;
    try {
      data = JSON.parse(responseText);
    } catch (parseError) {
      console.error('❌ Failed to parse response as JSON:', {
        parseError: parseError.message,
        responseText: responseText.substring(0, 1000), // Log first 1000 chars
        contentType: response.headers.get('content-type'),
        status: response.status,
        url: response.url
      });

      // If it's HTML, it's likely a 404 or error page
      if (responseText.includes('<!DOCTYPE') || responseText.includes('<html')) {
        return {
          success: false,
          error: `Received HTML instead of JSON (HTTP ${response.status})`,
          message: `The API endpoint returned an HTML page instead of JSON. This usually means the endpoint doesn't exist or there's a routing issue.`,
          details: {
            status: response.status,
            statusText: response.statusText,
            url: response.url,
            htmlPreview: responseText.substring(0, 500)
          }
        };
      }

      return {
        success: false,
        error: 'Invalid JSON response',
        message: parseError.message,
        details: {
          responseText: responseText.substring(0, 500)
        }
      };
    }

    if (!response.ok) {
      console.error(`❌ API call failed [${response.status}]:`, data);
      return {
        success: false,
        error: data.error || `HTTP ${response.status}`,
        message: data.message
      };
    }

    return data;
  } catch (error) {
    console.error('❌ Network error in API call:', error);
    return {
      success: false,
      error: 'Network error',
      message: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Whop Intermediary Client
 */
export const whopIntermediaryClient = {
  /**
   * Test connection to the intermediary server and Whop API
   */
  async testConnection(): Promise<ApiResponse> {
    console.log('🧪 Testing connection to Whop intermediary server...');
    return apiCall('/test-connection');
  },

  /**
   * Health check for the intermediary server
   */
  async healthCheck(): Promise<ApiResponse> {
    return apiCall('/health');
  },

  /**
   * Verify a Whop user token
   */
  async verifyUserToken(token: string): Promise<ApiResponse<{ user: WhopUser; verified: boolean }>> {
    console.log('🔍 Verifying Whop user token...');
    return apiCall('/auth/verify', {
      method: 'POST',
      body: JSON.stringify({ token })
    });
  },

  /**
   * Get current Whop user (requires x-whop-user-token header from Whop proxy)
   */
  async getCurrentUser(): Promise<ApiResponse<{ user: WhopUser }>> {
    console.log('🔍 Getting current Whop user from intermediary server...');
    return apiCall('/user/current');
  },

  /**
   * Test user endpoint (uses agent user for testing without Whop context)
   */
   async getTestUser(): Promise<ApiResponse<{ user: WhopUser }>> {
    console.log('🧪 Getting test user from intermediary server...');
    return apiCall('/test-user');
  },

  /**
   * Debug headers endpoint to see what headers are being forwarded
   */
  async debugHeaders(): Promise<ApiResponse> {
    console.log('🔍 Debugging headers forwarding...');
    return apiCall('/debug-headers');
  },

  /**
   * Test with simulated Whop token (shows what would happen in real Whop context)
   */
  async testWithSimulatedToken(): Promise<ApiResponse> {
    console.log('🧪 Testing with simulated Whop token...');
    return apiCall('/test-with-token');
  },

  /**
   * Test both direct and proxy modes
   */
  async testProxyMode(): Promise<{
    direct: ApiResponse;
    proxy: ApiResponse;
  }> {
    console.log('🧪 Testing both direct and proxy modes...');

    // Test direct mode
    const originalUseProxy = USE_PROXY;
    (window as any).USE_PROXY = false;
    const directResult = await this.healthCheck();

    // Test proxy mode
    (window as any).USE_PROXY = true;
    const proxyResult = await this.healthCheck();

    // Restore original setting
    (window as any).USE_PROXY = originalUseProxy;

    return {
      direct: directResult,
      proxy: proxyResult
    };
  },

  /**
   * Check user access level
   */
  async checkUserAccess(): Promise<ApiResponse<{ access: WhopAccessResult }>> {
    console.log('🔍 Checking user access level...');
    return apiCall('/user/access');
  },

  /**
   * Check user competition creation permissions
   */
  async checkCompetitionPermissions(experienceId: string): Promise<ApiResponse & {
    permissions?: {
      canCreateCompetitions: boolean;
      accessLevel: 'admin' | 'customer' | 'no_access';
      hasAccess: boolean;
      userId?: string;
      experienceId?: string;
      appKey?: string;
      appName?: string;
    }
  }> {
    console.log('🏆 Checking competition creation permissions for experience:', experienceId);
    return apiCall(`/user/competition-permissions?experienceId=${encodeURIComponent(experienceId)}`);
  },

  /**
   * Create/authenticate Supabase user for Whop user
   */
  async createSupabaseUser(whopUser: WhopUser, accessResult?: WhopAccessResult): Promise<ApiResponse<{
    user: any;
    credentials: { email: string; password: string };
    isNewUser: boolean;
  }>> {
    console.log('🔐 Creating Supabase user for Whop user...');
    return apiCall('/auth/supabase', {
      method: 'POST',
      body: JSON.stringify({ whopUser, accessResult })
    });
  },

  /**
   * Get multi-app configuration status
   */
  async getMultiAppStatus(): Promise<ApiResponse> {
    return apiCall('/multi-app-status');
  },

  /**
   * Initialize Whop authentication (combines user verification and access check)
   */
  async initializeAuth(): Promise<{
    isWhopUser: boolean;
    user: WhopUser | null;
    accessResult: WhopAccessResult | null;
    error?: string;
  }> {
    try {
      console.log('🔄 Initializing Whop authentication via intermediary server...');

      // For standalone trade-app deployment, check if we have an experience ID
      // If not, we might not be in a Whop context
      const isStandalone = !USE_PROXY;
      if (isStandalone) {
        console.log('🔍 Standalone deployment detected, checking for experience context...');

        // Try to get experience ID from URL or storage
        const experienceId = this.getExperienceIdFromContext();
        if (!experienceId) {
          console.warn('⚠️ No experience ID found in standalone deployment - may not be in Whop context');
          // For standalone trade-app, we can still allow access without Whop auth
          // This enables the app to work independently
          return {
            isWhopUser: false,
            user: null,
            accessResult: null,
            error: 'No Whop context detected in standalone deployment'
          };
        }
        console.log('✅ Experience ID found in standalone deployment:', experienceId);
      }

      // First, try to get current user
      const userResponse = await this.getCurrentUser();
      
      if (!userResponse.success || !userResponse.user) {
        console.log('ℹ️ No Whop user found or not in Whop context');

        // For standalone deployment, this might be expected
        if (isStandalone) {
          console.log('ℹ️ Standalone deployment - Whop user not found is acceptable');
        }

        return {
          isWhopUser: false,
          user: null,
          accessResult: null,
          error: userResponse.error
        };
      }

      const user = userResponse.user;
      console.log('✅ Whop user found:', user.username);

      // Check user access
      const accessResponse = await this.checkUserAccess();
      
      if (!accessResponse.success) {
        console.warn('⚠️ Could not check user access:', accessResponse.error);
        // Still return user data even if access check fails
        return {
          isWhopUser: true,
          user,
          accessResult: null,
          error: accessResponse.error
        };
      }

      const accessResult = accessResponse.access!;
      console.log('✅ Access check completed:', {
        hasAccess: accessResult.hasAccess,
        accessLevel: accessResult.accessLevel
      });

      return {
        isWhopUser: true,
        user,
        accessResult
      };

    } catch (error) {
      console.error('❌ Error initializing Whop auth:', error);

      // For standalone deployment, network errors are more acceptable
      const isStandalone = !USE_PROXY;
      if (isStandalone) {
        console.log('ℹ️ Standalone deployment - network error during Whop auth is acceptable');
      }

      return {
        isWhopUser: false,
        user: null,
        accessResult: null,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },

  /**
   * Helper method to get experience ID from various sources
   */
  getExperienceIdFromContext(): string | null {
    if (typeof window === 'undefined') return null;

    // Method 1: URL path (original Whop iframe)
    const pathMatch = window.location.pathname.match(/\/experiences\/([^\/]+)/);
    if (pathMatch) return pathMatch[1];

    // Method 2: URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const experienceParam = urlParams.get('experience') || urlParams.get('experienceId') || urlParams.get('exp');
    if (experienceParam) return experienceParam;

    // Method 3: localStorage
    try {
      return localStorage.getItem('whop_experience_id');
    } catch {
      return null;
    }
  },

  /**
   * Create a Whop charge for a user (server-side operation via intermediary)
   */
  async createCharge(amount: number, currency: string = 'usd', description?: string): Promise<ApiResponse<{
    inAppPurchase: {
      id: string;
      planId: string;
    };
    status: 'needs_action' | 'success';
  }>> {
    console.log('💳 Creating Whop charge via intermediary server...', { amount, currency, description });
    return apiCall('/charge', {
      method: 'POST',
      body: JSON.stringify({ amount, currency, description })
    });
  },

  /**
   * Generic API call method for making requests to the intermediary server
   */
  async apiCall<T = any>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    return apiCall<T>(endpoint, options);
  }
};

// Export types for use in other files
export type { WhopUser, WhopAccessResult, ApiResponse };
