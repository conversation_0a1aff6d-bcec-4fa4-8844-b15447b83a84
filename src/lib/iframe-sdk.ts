/**
 * Official Whop iframe SDK integration
 * Uses the official @whop/iframe package
 */

import { createSdk } from "@whop/iframe";

// Get the appropriate app ID based on current path
const getAppId = () => {
  if (typeof window === 'undefined') return import.meta.env.VITE_WHOP_APP_ID;

  const path = window.location.pathname;
  if (path.includes('/trade')) {
    return import.meta.env.VITE_TRADING_WHOP_APP_ID || import.meta.env.VITE_WHOP_APP_ID;
  }
  return import.meta.env.VITE_WHOP_APP_ID;
};

// Create the official iframe SDK instance
export const iframeSdk = createSdk({
  appId: getAppId(),
});

console.log('🔧 Official Whop iframe SDK initialized:', {
  appId: getAppId(),
  timestamp: new Date().toISOString(),
  windowContext: typeof window !== 'undefined' ? {
    isInIframe: window.parent !== window,
    origin: window.location.origin,
    pathname: window.location.pathname,
    referrer: document.referrer
  } : 'SSR'
});

// Check if SDK is properly initialized
console.log('🔧 Whop iframe SDK status:', {
  sdkExists: !!iframeSdk,
  hasInAppPurchase: !!(iframeSdk as any)?.inAppPurchase,
  hasOpenExternalUrl: !!(iframeSdk as any)?.openExternalUrl,
  sdkMethods: iframeSdk ? Object.keys(iframeSdk) : []
});

// Auto-test payment flow after SDK initialization (only for trading app)
if (typeof window !== 'undefined') {
  // Wait a bit for everything to initialize
  setTimeout(async () => {
    try {
      // Check if we're in the trading app context using the proper detection logic
      const { detectCurrentApp } = await import('./whop-app-config');
      const currentApp = detectCurrentApp();

      if (currentApp !== 'trading') {
        console.log('🚫 Skipping payment auto-test - not in trading app context:', {
          currentApp,
          currentPath: window.location.pathname,
          hostname: window.location.hostname,
          fullUrl: window.location.href
        });
        return;
      }

      console.log('🧪 Auto-testing payment flow for trading app...');

      // Get real user and experience info using the proper Whop context
      const getExperienceIdFromContext = () => {
        // Method 1: Try to get from window context (set by React app)
        const whopContext = (window as any).__WHOP_USER_CONTEXT__;
        if (whopContext?.experienceId) {
          console.log('✅ Found experience ID from window context:', whopContext.experienceId);
          return whopContext.experienceId;
        }

        // Method 2: Extract from URL
        const path = window.location.pathname;
        const match = path.match(/\/experiences\/([^\/]+)/);
        if (match) {
          return match[1];
        }

        // No fallbacks - we must have real experience data
        console.warn('⚠️ No experience ID found in context or URL');
        return null;
      };

      const getUserIdFromWhopContext = () => {
        // Method 1: Try to get from window context (set by React app)
        const whopContext = (window as any).__WHOP_USER_CONTEXT__;
        if (whopContext?.userId) {
          console.log('✅ Found Whop user from window context:', whopContext.userId);
          return whopContext.userId;
        }

        // Method 2: Try to get user ID from Whop auth storage (same keys as WhopContext)
        try {
          const whopAuthData = localStorage.getItem('whop_auth_state');
          const whopUserData = localStorage.getItem('whop_user_data');

          if (whopAuthData && whopUserData) {
            const authState = JSON.parse(whopAuthData);
            const userData = JSON.parse(whopUserData);

            if (authState.isWhopUser && userData.id) {
              console.log('✅ Found Whop user from localStorage:', {
                userId: userData.id,
                username: userData.username
              });
              return userData.id;
            }
          }
        } catch (e) {
          console.log('Could not parse Whop auth data from localStorage');
        }

        // No fallbacks - we must have real user data
        console.warn('⚠️ No real Whop user found - cannot proceed with payment');
        return null;
      };

      const experienceId = getExperienceIdFromContext();
      const userId = getUserIdFromWhopContext();

      // Don't proceed if we don't have real user/experience data
      if (!userId || !experienceId) {
        console.warn('⚠️ Missing required user or experience data:', {
          userId,
          experienceId,
          currentPath: window.location.pathname
        });
        console.log('💡 Make sure you are in a Whop experience context with a logged-in user');
        return;
      }

      console.log('👤 Using real user and experience info:', {
        userId,
        experienceId,
        currentPath: window.location.pathname,
        timestamp: new Date().toISOString()
      });

      // 1. Create charge via intermediary server
      console.log('📡 Creating charge via whop-intermediary-server...');

      // Import the whop intermediary client
      const { whopIntermediaryClient } = await import('./whopIntermediaryClient');

      const chargeResponse = await whopIntermediaryClient.createCharge(
        1000, // $10.00 test charge (in cents)
        'usd',
        'Test Payment - Auto-test Flow'
      );

      console.log('📡 Charge creation response received', {
        success: chargeResponse.success,
        hasData: !!chargeResponse.data,
        hasInAppPurchase: !!chargeResponse.data?.inAppPurchase,
        fullResponse: chargeResponse
      });

      if (!chargeResponse.success) {
        console.error('❌ Charge creation failed:', {
          success: chargeResponse.success,
          error: chargeResponse.error,
          fullResponse: chargeResponse
        });
        throw new Error(chargeResponse.error || 'Failed to create charge');
      }

      if (!chargeResponse.data?.inAppPurchase) {
        console.error('❌ No inAppPurchase data in response:', {
          hasData: !!chargeResponse.data,
          dataKeys: chargeResponse.data ? Object.keys(chargeResponse.data) : [],
          fullResponse: chargeResponse
        });
        throw new Error('No inAppPurchase data in response');
      }

      const inAppPurchase = chargeResponse.data.inAppPurchase;
      console.log('✅ Charge created successfully:', {
        inAppPurchase,
        hasId: !!inAppPurchase.id,
        hasPlanId: !!inAppPurchase.planId
      });

        // 2. Open payment modal
        console.log('🖼️ Opening payment modal with iframe SDK...', {
          iframeSdkAvailable: !!(iframeSdk as any),
          inAppPurchaseMethod: !!(iframeSdk as any)?.inAppPurchase,
          inAppPurchaseData: inAppPurchase
        });

        try {
          console.log('🔄 Calling iframeSdk.inAppPurchase...');

          // Add timeout to prevent hanging
          const paymentPromise = (iframeSdk as any).inAppPurchase(inAppPurchase);
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Payment modal timeout after 30 seconds')), 30000);
          });

          const res = await Promise.race([paymentPromise, timeoutPromise]);

          console.log('💳 Payment modal result received:', {
            status: res?.status,
            hasData: !!res?.data,
            resultType: typeof res,
            fullResult: res
          });

          if (res?.status === "ok") {
            console.log('✅ Payment completed successfully:', {
              receiptId: res.data?.receipt_id,
              status: res.status,
              fullData: res.data
            });
          } else if (res?.status === "error") {
            console.log('❌ Payment failed:', {
              status: res.status,
              error: res.error,
              fullResult: res
            });
          } else if (res?.status === "cancelled") {
            console.log('⚠️ Payment cancelled by user:', {
              status: res.status,
              fullResult: res
            });
          } else {
            console.log('❓ Unknown payment result:', {
              status: res?.status,
              fullResult: res
            });
          }
        } catch (modalError) {
          console.error('❌ Payment modal error:', {
            error: modalError instanceof Error ? modalError.message : String(modalError),
            stack: modalError instanceof Error ? modalError.stack : undefined,
            errorType: typeof modalError,
            inAppPurchaseData: inAppPurchase
          });
          throw modalError;
        }

    } catch (error) {
      console.error('❌ Auto-test payment flow failed:', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
    }
  }, 2000); // 2 second delay to ensure everything is ready
}


