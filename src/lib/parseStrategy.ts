export const extractTradingStrategy = (text: string) => {
  if (!text) return null;
  
  try {
    // Find the Trading Strategy section
    const tradingStrategySection = text.match(/### Trading Strategy[\s\S]*?(?=###|$)/i)?.[0] || '';
    if (!tradingStrategySection) return null;
    
    // Extract recommendation (LONG/SHORT)
    const recommendationMatch = tradingStrategySection.match(/RECOMMENDATION:\s*(LONG|SHORT|BULLISH|BEARISH)/i) || 
                              tradingStrategySection.match(/Market Position:\s*(LONG|SHORT|BULLISH|BEARISH)/i) ||
                              text.match(/RECOMMENDATION:\s*(LONG|SHORT|BULLISH|BEARISH)/i) ||
                              text.match(/Market Position:\s*(LONG|SHORT|BULLISH|BEARISH)/i) ||
                              text.match(/\b(LONG|SHORT|BULLISH|BEARISH)\b.*?(?:signal|position|trade|recommendation)/i) ||
                              text.match(/(?:signal|position|trade|recommendation).*?\b(LONG|SHORT|BULLISH|BEARISH)\b/i);
    
    // Default to searching for SHORT/BEARISH signals first
    let recommendation = 'LONG'; // Default to LONG only if no clear signal found
    
    if (recommendationMatch) {
        recommendation = recommendationMatch[1].toUpperCase();
    } else {
        // If no explicit recommendation found, try to infer from the text
        const shortSignals = text.match(/\b(short|bearish|downtrend|downside|sell)\b/i);
        const longSignals = text.match(/\b(long|bullish|uptrend|upside|buy)\b/i);
        
        if (shortSignals && (!longSignals || text.indexOf(shortSignals[0]) < text.indexOf(longSignals[0]))) {
            recommendation = 'SHORT';
        } else if (longSignals) {
            recommendation = 'LONG';
        }
    }
    
    // Convert BULLISH/BEARISH to LONG/SHORT
    if (recommendation === 'BULLISH') recommendation = 'LONG';
    if (recommendation === 'BEARISH') recommendation = 'SHORT';
    
    // Extract confidence level
    const confidenceMatch = tradingStrategySection.match(/Confidence:\s*(HIGH|MEDIUM|LOW|\d+%)/i) || 
                          text.match(/Confidence:\s*(HIGH|MEDIUM|LOW|\d+%)/i);
    
    let confidence = confidenceMatch?.[1] || 'MEDIUM';
    // If confidence is a percentage, convert to HIGH/MEDIUM/LOW
    if (confidence.includes('%')) {
      const percent = parseInt(confidence);
      if (percent >= 70) confidence = 'HIGH';
      else if (percent >= 40) confidence = 'MEDIUM';
      else confidence = 'LOW';
    }
    
    // Extract current price
    const priceMatch = tradingStrategySection.match(/Current Price:\s*\$?([\d,.]+)/i) || 
                      text.match(/Current Price:\s*\$?([\d,.]+)/i) ||
                      text.match(/trading at\s*\$?([\d,.]+)/i);
    const currentPrice = priceMatch ? parseFloat(priceMatch[1].replace(/,/g, '')) : 0;
    
    // Extract entry points with more robust regex
    const entryMatch = tradingStrategySection.match(/Entry\s*Points?:?\s*Primary\s*\$?([\d,.]+)\s*\|?\s*Secondary\s*\$?([\d,.]+)/i) ||
                      tradingStrategySection.match(/Entry\s*:?\s*\$?([\d,.]+)/i) ||
                      tradingStrategySection.match(/Entry Point:?\s*\$?([\d,.]+)/i) ||
                      text.match(/Entry\s*Points?:?\s*Primary\s*\$?([\d,.]+)\s*\|?\s*Secondary\s*\$?([\d,.]+)/i) ||
                      text.match(/Entry\s*:?\s*\$?([\d,.]+)/i) ||
                      text.match(/Entry Point:?\s*\$?([\d,.]+)/i);
    
    // Enhanced TP extraction - look for TP, Take Profit Target, and take profit patterns
    const tpPatterns = [
      // Exact TP value patterns
      /Take Profit Target:?\s*\$?([\d,.]+)/i,
      /Take Profit:?\s*\$?([\d,.]+)/i,
      /Take Profit Targets?:?\s*\$?([\d,.]+)/i,
      /TP:?\s*\$?([\d,.]+)/i,
      /TP\s+(\d+):?\s*\$?([\d,.]+)/i,
      /TP Target:?\s*\$?([\d,.]+)/i,
      // Look for the TP ${value} pattern (common format)
      /TP\s+\$?(\d+\.?\d*)/i,
      // Try to find any price with $ after "take profit"
      /take profit[^$]*\$(\d+\.?\d*)/i,
      // Look for any line with TP and a number
      /tp.*?\$?(\d+\.?\d*)/i,
      // Generic patterns in numbered lists
      /\d+\.\s*Take Profit.*?\$?(\d+\.?\d*)/i,
      // Try to find any full number on a line with "take profit" (case insensitive)
      /take profit.*?(\d+\.?\d*)/i
    ];
    
    let tpValue = null;
    
    // First try the Trading Strategy section
    for (const pattern of tpPatterns) {
      const match = tradingStrategySection.match(pattern);
      if (match) {
        // For some patterns, the number is in group 2, for others in group 1
        tpValue = parseFloat((match[2] || match[1]).replace(/,/g, ''));
        break;
      }
    }
    
    // If not found in Trading Strategy section, try the full text
    if (!tpValue) {
      for (const pattern of tpPatterns) {
        const match = text.match(pattern);
        if (match) {
          tpValue = parseFloat((match[2] || match[1]).replace(/,/g, ''));
          break;
        }
      }
    }
    
    // Enhanced SL extraction - look for SL, Stop Loss, and stop loss patterns
    const slPatterns = [
      // Exact SL value patterns
      /Stop Loss:?\s*\$?([\d,.]+)/i,
      /SL:?\s*\$?([\d,.]+)/i,
      // Look for the SL ${value} pattern (common format)
      /SL\s+\$?(\d+\.?\d*)/i,
      // Try to find any price with $ after "stop loss"
      /stop loss[^$]*\$(\d+\.?\d*)/i,
      // Look for any line with SL and a number
      /sl.*?\$?(\d+\.?\d*)/i,
      // Generic patterns in numbered lists
      /\d+\.\s*Stop Loss.*?\$?(\d+\.?\d*)/i,
      // Try to find any full number on a line with "stop loss" (case insensitive)
      /stop loss.*?(\d+\.?\d*)/i
    ];
    
    let slValue = null;
    
    // First try the Trading Strategy section
    for (const pattern of slPatterns) {
      const match = tradingStrategySection.match(pattern);
      if (match) {
        slValue = parseFloat(match[1].replace(/,/g, ''));
        break;
      }
    }
    
    // If not found in Trading Strategy section, try the full text
    if (!slValue) {
      for (const pattern of slPatterns) {
        const match = text.match(pattern);
        if (match) {
          slValue = parseFloat(match[1].replace(/,/g, ''));
          break;
        }
      }
    }
    
    // Search for lines with numeric values that might be TP and SL
    if (!tpValue || !slValue) {
      // Split the text into lines and look for potential TP/SL values
      const lines = text.split('\n');
      for (const line of lines) {
        // Skip lines without numbers
        if (!/\d/.test(line)) continue;
        
        // Check if the line talks about take profit
        if (!tpValue && /tak?e\s*prof?it|tp/i.test(line)) {
          const numberMatch = line.match(/\$?(\d+\.?\d*)/i);
          if (numberMatch) {
            tpValue = parseFloat(numberMatch[1]);
          }
        }
        
        // Check if the line talks about stop loss
        if (!slValue && /stop\s*los?s|sl/i.test(line)) {
          const numberMatch = line.match(/\$?(\d+\.?\d*)/i);
          if (numberMatch) {
            slValue = parseFloat(numberMatch[1]);
          }
        }
      }
    }
    
    // Extract risk/reward ratio
    const rrMatch = tradingStrategySection.match(/Risk\/Reward:?\s*1:(\d+\.?\d*)/i) || 
                   text.match(/Risk\/Reward:?\s*1:(\d+\.?\d*)/i) ||
                   tradingStrategySection.match(/R\/R:?\s*1:(\d+\.?\d*)/i) || 
                   text.match(/R\/R:?\s*1:(\d+\.?\d*)/i);
    
    // Extract support and resistance levels
    const supportMatch = tradingStrategySection.match(/Support:?\s*\$?([\d,.]+)/i) || 
                        text.match(/Support:?\s*\$?([\d,.]+)/i) ||
                        tradingStrategySection.match(/Support Levels?:?\s*\$?([\d,.]+)/i) || 
                        text.match(/Support Levels?:?\s*\$?([\d,.]+)/i);
    
    const resistanceMatch = tradingStrategySection.match(/Resistance:?\s*\$?([\d,.]+)/i) || 
                           text.match(/Resistance:?\s*\$?([\d,.]+)/i) ||
                           tradingStrategySection.match(/Resistance Levels?:?\s*\$?([\d,.]+)/i) || 
                           text.match(/Resistance Levels?:?\s*\$?([\d,.]+)/i);
    
    // Use fallbacks when specific values aren't found
    const primaryEntry = entryMatch ? parseFloat(entryMatch[1]?.replace(/,/g, '') || '0') : currentPrice;
    const secondaryEntry = entryMatch?.[2] ? parseFloat(entryMatch[2].replace(/,/g, '')) : primaryEntry * 0.98;
    
    // For Take Profit targets, use the extracted value or fallbacks
    let target1 = tpValue || 0;
    
    // For Stop Loss, use the extracted value or fallbacks
    let stopLoss = slValue || 0;
    
    // Calculate risk/reward if not explicitly provided
    let riskReward = rrMatch ? parseFloat(rrMatch[1]) : 0;
    if (!riskReward && primaryEntry > 0 && stopLoss > 0 && target1 > 0) {
      const risk = Math.abs(primaryEntry - stopLoss);
      const reward = Math.abs(primaryEntry - target1);
      riskReward = risk > 0 ? parseFloat((reward / risk).toFixed(2)) : 0;
    }
    
    // Set default targets for T2 and T3
    let target2 = 0;
    let target3 = 0;
    if (target1 > 0) {
      const isLong = recommendation === 'LONG';
      target2 = isLong ? target1 * 1.05 : target1 * 0.95;
      target3 = isLong ? target2 * 1.05 : target2 * 0.95;
    }
    
    return {
      recommendation: recommendation,
      confidence: confidence,
      currentPrice: currentPrice,
      entry: {
        primary: primaryEntry,
        secondary: secondaryEntry
      },
      exit: {
        target1: target1,
        target2: target2,
        target3: target3
      },
      stopLoss: stopLoss,
      riskReward: riskReward,
      support: supportMatch ? [parseFloat(supportMatch[1].replace(/,/g, ''))] : [],
      resistance: resistanceMatch ? [parseFloat(resistanceMatch[1].replace(/,/g, ''))] : [],
    };
  } catch (error) {
    console.error('Error parsing trading strategy:', error);
    return null;
  }
}; 