--- a/src/components/TradingChart/TradingChart.tsx
+++ b/src/components/TradingChart/TradingChart.tsx
@@ -406,32 +406,16 @@
   // Force chart resize on component mount to prevent zoom/layout issues
   useEffect(() => {
     const forceResize = () => {
       if (chartRef.current) {
         try {
           chartRef.current.resize();
           console.log('✅ Force resize on mount completed');
         } catch (error) {
           console.error('Force resize on mount failed:', error);
         }
       }
     };
 
-    // Simulate the dropdown toggle effect that fixes the chart
-    const simulateDropdownEffect = () => {
-      console.log('🔧 Simulating dropdown effect to fix chart layout...');
-
-      // Temporarily toggle the dropdown state to trigger layout recalculation
-      setShowDrawingToolsDropdown(true);
-
-      setTimeout(() => {
-        setShowDrawingToolsDropdown(false);
-
-        // Force resize after the dropdown effect
-        setTimeout(() => {
-          if (chartRef.current) {
-            chartRef.current.resize();
-            console.log('✅ Dropdown effect simulation completed');
-          }
-        }, 100);
-      }, 50);
-    };
-
     // Multiple resize attempts to ensure proper initialization
     const timers = [
       setTimeout(forceResize, 100),
       setTimeout(forceResize, 300),
-      setTimeout(simulateDropdownEffect, 500), // Simulate dropdown effect
+      setTimeout(forceResize, 500),
       setTimeout(forceResize, 800)
     ];
 
     return () => {
       timers.forEach(timer => clearTimeout(timer));
     };
   }, []); // Run only once on mount
