# Trading Mode Development

This document explains how to use the trading mode development setup that automatically redirects port 3000 to the `/trading` route.

## Overview

Trading mode is designed for developing the standalone trading Whop app. When enabled, visiting `http://localhost:3000` automatically redirects to `http://localhost:3000/trading`, making it easier to develop and test the trading interface.

## Quick Start

### Enable Trading Mode
```bash
npm run start:trading
```
This starts both the Vite dev server and the trading proxy.

### Access the Trading Interface
- **Auto-redirect**: http://localhost:3000 → automatically redirects to `/trading`
- **Direct access**: http://localhost:3000/trading
- **Vite dev server**: http://localhost:8082 (direct access to all routes)

## Available Commands

### Main Commands
```bash
# Start trading mode (recommended)
npm run start:trading

# Start only the trading proxy (requires Vite to be running separately)
npm run dev:trading

# Regular development (no trading mode)
npm run dev
```

### Configuration Commands
```bash
# Enable trading mode configuration
npm run trading:on

# Disable trading mode configuration  
npm run trading:off

# Check current trading mode status
npm run trading:status
```

## How It Works

1. **Vite Dev Server**: Runs on port 8082 with full HMR support
2. **Trading Proxy**: Runs on port 3000 and proxies requests to Vite
3. **Auto-redirect**: Root requests (`/`) are redirected to `/trading`
4. **Pass-through**: All other requests are proxied to the Vite server

## Configuration

The trading mode configuration is stored in `package.json`:

```json
{
  "config": {
    "tradingMode": false
  }
}
```

You can toggle this with the convenience commands:
- `npm run trading:on` - Sets `tradingMode` to `true`
- `npm run trading:off` - Sets `tradingMode` to `false`

## Development Workflow

### For Trading App Development
1. Run `npm run start:trading`
2. Open http://localhost:3000 (auto-redirects to trading)
3. Develop with full HMR support
4. All trading-specific features are immediately accessible

### For Regular App Development
1. Run `npm run dev`
2. Open http://localhost:8082
3. Access all routes normally

## Technical Details

### Proxy Server
- **File**: `scripts/dev-trading-proxy.cjs`
- **Port**: 3000 (proxy) → 8082 (Vite)
- **Features**: Auto-redirect, request proxying, error handling

### Supported Features
- ✅ Hot Module Replacement (HMR)
- ✅ WebSocket proxying for dev tools
- ✅ Request/response logging
- ✅ Error handling and recovery
- ✅ Graceful shutdown

### File Structure
```
scripts/
├── dev-trading-proxy.cjs     # Main proxy server
└── trading-mode-info.html    # Info page (accessible at /trading-mode-info)

package.json                  # Scripts and configuration
```

## Troubleshooting

### Port 3000 Already in Use
```bash
# Find and kill processes using port 3000
lsof -ti:3000 | xargs kill -9

# Then restart trading mode
npm run start:trading
```

### Proxy Not Working
1. Check that Vite is running on port 8082
2. Verify the proxy target port in `scripts/dev-trading-proxy.cjs`
3. Check console logs for error messages

### Trading Route Not Loading
1. Ensure `/trading` route is properly configured in `src/App.tsx`
2. Check that `LazyRoutes.Trading` is properly exported
3. Verify the trading page component exists

## Benefits

1. **Faster Development**: Direct access to trading interface
2. **Whop App Simulation**: Mimics how the app will work when embedded
3. **Easy Testing**: Quick access for manual testing
4. **Flexible**: Can be toggled on/off as needed
5. **Full HMR**: Maintains all Vite development features

## Integration with Whop

When deployed as a Whop app, the trading interface will be embedded in an iframe. The trading mode development setup helps simulate this environment by:

- Focusing on the `/trading` route
- Testing standalone functionality
- Validating Whop API integration
- Ensuring proper app context detection
