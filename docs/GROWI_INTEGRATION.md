# Growi Stripe Affiliate Integration

This document describes the implementation of Growi affiliate tracking integration with our Stripe payment system.

## Overview

The Growi integration allows us to track affiliate referrals and attribute Stripe payments to specific affiliates. When users visit our site with affiliate parameters, the Growi tracking script captures the affiliate code and passes it to Stripe metadata for commission tracking.

## Implementation Details

### 1. Tracking Script

The Growi tracking script is loaded dynamically using `src/utils/growiLoader.ts`. This approach allows us to use Vite's environment variable injection and handle cases where the Growi Public ID is not set.

The script is automatically loaded when the app initializes in `src/App.tsx`.

### 2. Environment Variables

Add the following environment variable to your `.env` file:

```bash
VITE_GROWI_PUBLIC_ID=your-growi-public-id-here
```

### 3. Frontend Integration

#### Utility Functions

`src/utils/growiUtils.ts` provides utility functions to capture affiliate codes:

- `getAffiliateCode()`: Gets the affiliate code from Growi script or URL parameters
- `waitForAffiliateCode()`: Waits for the Growi script to load and capture the code

`src/utils/growiLoader.ts` provides script loading functionality:

- `loadGrowiScript()`: Dynamically loads the Growi script with the correct environment variable
- `initializeGrowiTracking()`: Initializes Growi tracking (called automatically in App.tsx)

#### Frontend Components

The following components have been updated to capture and pass affiliate codes:

- `src/App.tsx`
- `src/components/DirectCheckout.tsx`
- `src/components/Onboarding.tsx`
- `src/pages/Subscription.tsx`
- `src/pages/ManageSubscription.tsx`
- `src/utils/stripeUtils.ts`
- `src/components/marketplace/MarketplaceAgentCard.tsx`
- `src/services/marketplaceService.ts`

### 4. Backend Integration

#### Stripe Checkout Function

`supabase/functions/stripe-checkout/index.ts` has been updated to:

1. Accept `affiliateCode` parameter in request body
2. Add `growi_affiliate_code` to Stripe metadata for all checkout sessions
3. Include affiliate code in payment links

#### Marketplace Purchase Function

`supabase/functions/marketplace-purchase/index.ts` has been updated to:

1. Accept `affiliateCode` parameter in purchase requests
2. Add `growi_affiliate_code` to Stripe metadata for marketplace purchases

### 5. Stripe Metadata

When an affiliate code is present, it's added to Stripe metadata as:

```javascript
metadata: {
  // ... other metadata
  growi_affiliate_code: affiliateCode
}
```

This applies to:
- Checkout sessions
- Payment intents
- Payment links
- Subscription updates

## Usage

### For Affiliates

Affiliates can use URLs with affiliate parameters:

```
https://yoursite.com?affiliate=AFFILIATE_CODE
https://yoursite.com?ref=AFFILIATE_CODE
https://yoursite.com?aff=AFFILIATE_CODE
```

The Growi script will automatically capture these parameters.

### For Developers

When creating checkout sessions or processing payments, the affiliate code is automatically captured and included in Stripe metadata. No additional code is required in most cases.

## Testing

To test the integration:

1. Visit your site with an affiliate parameter: `?affiliate=TEST123`
2. Complete a purchase or subscription
3. Check the Stripe dashboard for the payment
4. Verify that `growi_affiliate_code: TEST123` appears in the metadata

## Build Process

The build process includes a script that replaces environment variable placeholders in `index.html`:

```bash
npm run build  # Automatically runs replace-env-vars.js before building
```

## Troubleshooting

### Affiliate Code Not Captured

1. Check that `VITE_GROWI_PUBLIC_ID` is set correctly
2. Verify the Growi script is loading (check browser dev tools)
3. Ensure affiliate parameters are in the URL
4. Check browser console for any JavaScript errors

### Affiliate Code Not in Stripe Metadata

1. Verify the frontend is capturing the affiliate code
2. Check that the backend functions are receiving the `affiliateCode` parameter
3. Ensure the affiliate code is being added to the metadata object

### Environment Variable Not Replaced

1. Check that the build script is running: `node scripts/replace-env-vars.js`
2. Verify the environment variable is set during build
3. Check that the placeholder `%VITE_GROWI_PUBLIC_ID%` exists in `index.html`

## Security Considerations

- Affiliate codes are passed through client-side JavaScript and should not contain sensitive information
- The Growi public ID is safe to expose in client-side code
- Affiliate tracking does not affect payment security or user data privacy

## Future Enhancements

Potential improvements to consider:

1. Server-side affiliate code validation
2. Affiliate dashboard integration
3. Commission calculation automation
4. Advanced attribution tracking
5. A/B testing for affiliate campaigns
