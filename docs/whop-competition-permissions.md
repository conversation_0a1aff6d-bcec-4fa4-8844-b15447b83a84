# Whop Competition Permissions System

This document explains how to use the Whop SDK to determine whether a user can create competitions based on their access level in a Whop experience.

## Overview

The system uses the Whop SDK's `checkIfUserHasAccessToExperience` method to determine user permissions:

- **Admin users** (`accessLevel: 'admin'`) can create competitions
- **Customer users** (`accessLevel: 'customer'`) can only participate in competitions  
- **No access users** (`accessLevel: 'no_access'`) cannot access the experience

## Core Implementation

### 1. Server-Side API Endpoint

The main logic is implemented in the intermediary server:

```typescript
// whop-intermediary-server/app/api/user/competition-permissions/route.ts

import { whopSdk } from "@/lib/whop-sdk";
import { headers } from "next/headers";

export async function GET(request: NextRequest) {
  const headersList = await headers();
  const experienceId = request.nextUrl.searchParams.get('experienceId');
  
  // Verify user token
  const { userId } = await whopSdk.verifyUserToken(headersList);
  
  // Check access level
  const result = await whopSdk.access.checkIfUserHasAccessToExperience({
    userId,
    experienceId,
  });
  
  return NextResponse.json({
    success: true,
    permissions: {
      canCreateCompetitions: result.accessLevel === 'admin',
      accessLevel: result.accessLevel, // 'admin' | 'customer' | 'no_access'
      hasAccess: result.hasAccess,
      userId,
      experienceId
    }
  });
}
```

### 2. Client-Side Service

The client calls the intermediary server:

```typescript
// src/lib/whopIntermediaryClient.ts

export const whopIntermediaryClient = {
  async checkCompetitionPermissions(experienceId: string) {
    return apiCall(`/user/competition-permissions?experienceId=${experienceId}`);
  }
};
```

### 3. React Hook

Easy-to-use React hook for components:

```typescript
// src/hooks/useWhopCompetitionPermissions.ts

export function useWhopCompetitionPermissions(experienceId: string) {
  const [permissions, setPermissions] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchPermissions = async () => {
      const response = await whopIntermediaryClient.checkCompetitionPermissions(experienceId);
      setPermissions(response.data?.permissions);
    };
    
    fetchPermissions();
  }, [experienceId]);

  return { permissions, loading, error };
}
```

## Usage Examples

### Basic Permission Check

```tsx
import { useWhopCompetitionPermissions } from '@/hooks/useWhopCompetitionPermissions';

function CompetitionCreator({ experienceId }: { experienceId: string }) {
  const { permissions, loading, error } = useWhopCompetitionPermissions(experienceId);

  if (loading) return <div>Checking permissions...</div>;
  if (error) return <div>Error: {error}</div>;
  
  if (!permissions?.canCreateCompetitions) {
    return (
      <div>
        You need admin access to create competitions. 
        Your current level: {permissions?.accessLevel}
      </div>
    );
  }

  return <CreateCompetitionForm />;
}
```

### Using the Permission Checker Component

```tsx
import { CompetitionPermissionChecker } from '@/components/Whop/CompetitionPermissionChecker';

function CompetitionPage() {
  const [showCreateForm, setShowCreateForm] = useState(false);

  return (
    <CompetitionPermissionChecker 
      experienceId="exp_ThljdpAF70d4Af"
      onCanCreate={() => setShowCreateForm(true)}
    >
      {showCreateForm && <CreateCompetitionForm />}
    </CompetitionPermissionChecker>
  );
}
```

### One-off Permission Check

```typescript
import { checkCanCreateCompetitions } from '@/hooks/useWhopCompetitionPermissions';

async function handleCreateCompetition() {
  const { canCreate, reason } = await checkCanCreateCompetitions('exp_ThljdpAF70d4Af');
  
  if (!canCreate) {
    alert(`Cannot create competition: ${reason}`);
    return;
  }
  
  // Proceed with competition creation
}
```

## Access Levels Explained

### Admin (`accessLevel: 'admin'`)
- **Who**: Whop community owners, moderators
- **Permissions**: Can create competitions, manage community
- **UI**: Shows "Create Competition" button, admin badges

### Customer (`accessLevel: 'customer'`)  
- **Who**: Regular community members
- **Permissions**: Can participate in competitions, view content
- **UI**: Shows "Join Competition" options, member badges

### No Access (`accessLevel: 'no_access'`)
- **Who**: Users without access to the experience
- **Permissions**: Cannot access any content
- **UI**: Shows access denied message

## Integration with Existing System

The new system integrates with the existing competition service:

```typescript
// src/services/whopCompetitionService.ts

export const checkWhopCompetitionPermissions = async (experienceId?: string) => {
  // If experience ID provided, use new Whop SDK checking
  if (experienceId) {
    const response = await whopIntermediaryClient.checkCompetitionPermissions(experienceId);
    const isWhopOwner = response.data?.permissions.accessLevel === 'admin';
    
    return {
      canCreateLocal: response.data?.permissions.canCreateCompetitions,
      canCreateCrossCommunity: isWhopOwner && isOfficialOsis,
      isWhopOwner,
      isOfficialOsis
    };
  }
  
  // Fall back to legacy metadata checking
  // ...
};
```

## Error Handling

The system gracefully handles various error scenarios:

1. **No Whop Token**: Returns `no_access` permissions
2. **Invalid Experience ID**: Returns error with helpful message  
3. **Network Errors**: Shows retry option
4. **API Failures**: Falls back to legacy permission checking

## Testing

### Development Environment

```typescript
// For localhost testing, you can simulate different access levels
const mockPermissions = {
  admin: { accessLevel: 'admin', canCreateCompetitions: true },
  customer: { accessLevel: 'customer', canCreateCompetitions: false },
  no_access: { accessLevel: 'no_access', canCreateCompetitions: false }
};
```

### Production

The system automatically detects the correct Whop app and experience based on the URL and context.

## Security Notes

- All permission checks happen server-side using the Whop SDK
- User tokens are verified before checking permissions
- No sensitive data is exposed to the client
- Permissions are re-checked on every competition creation attempt
