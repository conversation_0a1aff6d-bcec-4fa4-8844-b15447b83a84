<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Mode Development</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0A0A0A 0%, #1A1A1A 100%);
            color: white;
            margin: 0;
            padding: 40px 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            max-width: 600px;
            text-align: center;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }
        h1 {
            font-size: 2.5rem;
            margin-bottom: 16px;
            background: linear-gradient(135deg, #00C853 0%, #4CAF50 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .subtitle {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 32px;
        }
        .status {
            background: rgba(0, 200, 83, 0.1);
            border: 1px solid rgba(0, 200, 83, 0.3);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 32px;
        }
        .status-title {
            font-weight: 600;
            color: #00C853;
            margin-bottom: 8px;
        }
        .button {
            display: inline-block;
            background: linear-gradient(135deg, #00C853 0%, #4CAF50 100%);
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            margin: 8px;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 200, 83, 0.3);
        }
        .commands {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 20px;
            margin-top: 32px;
            text-align: left;
        }
        .commands h3 {
            margin-top: 0;
            color: #00C853;
        }
        .command {
            background: rgba(0, 0, 0, 0.3);
            padding: 8px 12px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            margin: 8px 0;
            border-left: 3px solid #00C853;
        }
        .description {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.6);
            margin-top: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Trading Mode</h1>
        <p class="subtitle">Development proxy for standalone trading app</p>
        
        <div class="status">
            <div class="status-title">✅ Trading Mode Active</div>
            <div>Port 3000 automatically redirects to /trading</div>
        </div>
        
        <a href="/trading" class="button">🎯 Go to Trading Interface</a>
        <a href="http://localhost:8082" class="button">🔧 Vite Dev Server</a>
        
        <div class="commands">
            <h3>📋 Available Commands</h3>
            
            <div class="command">npm run start:trading</div>
            <div class="description">Start both Vite dev server and trading proxy</div>
            
            <div class="command">npm run dev:trading</div>
            <div class="description">Start only the trading proxy (requires Vite to be running)</div>
            
            <div class="command">npm run trading:on</div>
            <div class="description">Enable trading mode configuration</div>
            
            <div class="command">npm run trading:off</div>
            <div class="description">Disable trading mode configuration</div>
            
            <div class="command">npm run trading:status</div>
            <div class="description">Check current trading mode status</div>
        </div>
        
        <div class="description">
            <strong>How it works:</strong> This proxy server runs on port 3000 and automatically redirects 
            all root requests to /trading. The Vite dev server runs on port 8082 with full HMR support.
        </div>
    </div>
</body>
</html>
