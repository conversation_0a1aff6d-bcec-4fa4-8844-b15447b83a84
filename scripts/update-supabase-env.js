#!/usr/bin/env node

/**
 * This script updates the environment variables for Supabase edge functions.
 * It adds the ENCRYPTION_KEY environment variable to all functions.
 */

import { execSync } from 'child_process';
import * as dotenv from 'dotenv';
import * as fs from 'fs';
import * as path from 'path';

// Load environment variables from .env file
dotenv.config();

// Check if the encryption key is set
if (!process.env.VITE_ENCRYPTION_KEY) {
  console.error('Error: VITE_ENCRYPTION_KEY is not set in your .env file');
  process.exit(1);
}

try {
  // Check if the Supabase CLI is installed
  try {
    execSync('supabase --version', { stdio: 'inherit' });
  } catch (error) {
    throw new Error('Supabase CLI is not installed. Please install it using: npm install -g supabase');
  }
  
  // Get a list of all functions in the supabase/functions directory
  const functionsDir = path.join(process.cwd(), 'supabase', 'functions');
  const functions = fs.readdirSync(functionsDir)
    .filter(file => fs.statSync(path.join(functionsDir, file)).isDirectory());
  
  console.log(`Found ${functions.length} Supabase edge functions`);
  
  // Update the environment variables for each function
  for (const func of functions) {
    console.log(`Updating environment variables for ${func}...`);
    
    try {
      // Set the ENCRYPTION_KEY environment variable
      execSync(`supabase secrets set ENCRYPTION_KEY="${process.env.VITE_ENCRYPTION_KEY}" --env-file .env`, { 
        stdio: 'inherit' 
      });
      
      console.log(`✅ Successfully updated environment variables for ${func}`);
    } catch (error) {
      console.error(`❌ Failed to update environment variables for ${func}:`, error.message);
    }
  }
  
  console.log('✅ All environment variables updated successfully');
} catch (error) {
  console.error('❌ Error updating environment variables:', error.message);
  process.exit(1);
}
