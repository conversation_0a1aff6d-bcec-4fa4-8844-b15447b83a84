-- Seed data for the Discover page feature
-- This script adds sample published agents for testing

-- First, let's create a test user (you can replace this with your actual user ID)
-- INSERT INTO auth.users (id, email, created_at, updated_at) VALUES 
-- ('00000000-0000-0000-0000-000000000001', '<EMAIL>', now(), now())
-- ON CONFLICT (id) DO NOTHING;

-- Create a sample agent configuration
INSERT INTO public.agents (id, user_id, name, description, configuration, created_at, updated_at) VALUES 
(
  '11111111-1111-1111-1111-111111111111',
  '00000000-0000-0000-0000-000000000001', -- Replace with actual user ID
  'RSI Momentum Strategy',
  'A powerful day trading strategy using RSI and moving averages to identify momentum shifts',
  '{
    "blocks": [
      {
        "id": "when-run-1",
        "type": "when_run",
        "position": {"x": 100, "y": 100},
        "outputConnections": ["rsi-1", "sma-1"]
      },
      {
        "id": "rsi-1",
        "type": "indicator",
        "indicator": "rsi",
        "parameters": {"period": 14},
        "position": {"x": 300, "y": 50},
        "inputConnections": ["when-run-1"],
        "outputConnections": ["condition-1"]
      },
      {
        "id": "sma-1",
        "type": "indicator",
        "indicator": "sma",
        "parameters": {"period": 20},
        "position": {"x": 300, "y": 150},
        "inputConnections": ["when-run-1"],
        "outputConnections": ["condition-2"]
      },
      {
        "id": "condition-1",
        "type": "condition",
        "operator": "<",
        "value": 30,
        "position": {"x": 500, "y": 50},
        "inputConnections": ["rsi-1"],
        "outputConnections": ["trigger-1"]
      },
      {
        "id": "condition-2",
        "type": "condition",
        "operator": ">",
        "compareToBlock": "price-1",
        "position": {"x": 500, "y": 150},
        "inputConnections": ["sma-1"],
        "outputConnections": ["trigger-1"]
      },
      {
        "id": "price-1",
        "type": "price",
        "dataPoint": "close",
        "position": {"x": 300, "y": 250},
        "inputConnections": ["when-run-1"],
        "outputConnections": ["condition-2"]
      },
      {
        "id": "trigger-1",
        "type": "trigger",
        "signal": "bullish",
        "confidence": 85,
        "position": {"x": 700, "y": 100},
        "inputConnections": ["condition-1", "condition-2"]
      }
    ],
    "entryBlockId": "when-run-1"
  }',
  now(),
  now()
)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  configuration = EXCLUDED.configuration,
  updated_at = now();

-- Create more sample agents
INSERT INTO public.agents (id, user_id, name, description, configuration, created_at, updated_at) VALUES 
(
  '22222222-2222-2222-2222-222222222222',
  '00000000-0000-0000-0000-000000000001',
  'Bollinger Bands Breakout',
  'Identifies breakout opportunities using Bollinger Bands and volume confirmation',
  '{
    "blocks": [
      {
        "id": "when-run-1",
        "type": "when_run",
        "position": {"x": 100, "y": 100},
        "outputConnections": ["bb-1", "volume-1"]
      },
      {
        "id": "bb-1",
        "type": "indicator",
        "indicator": "bollinger_bands",
        "parameters": {"period": 20, "stdDev": 2},
        "position": {"x": 300, "y": 50},
        "inputConnections": ["when-run-1"],
        "outputConnections": ["condition-1"]
      },
      {
        "id": "volume-1",
        "type": "price",
        "dataPoint": "volume",
        "position": {"x": 300, "y": 150},
        "inputConnections": ["when-run-1"],
        "outputConnections": ["condition-2"]
      },
      {
        "id": "condition-1",
        "type": "condition",
        "operator": ">",
        "compareToIndicator": "upper_band",
        "position": {"x": 500, "y": 50},
        "inputConnections": ["bb-1"],
        "outputConnections": ["trigger-1"]
      },
      {
        "id": "condition-2",
        "type": "condition",
        "operator": ">",
        "value": 1000000,
        "position": {"x": 500, "y": 150},
        "inputConnections": ["volume-1"],
        "outputConnections": ["trigger-1"]
      },
      {
        "id": "trigger-1",
        "type": "trigger",
        "signal": "bullish",
        "confidence": 75,
        "position": {"x": 700, "y": 100},
        "inputConnections": ["condition-1", "condition-2"]
      }
    ],
    "entryBlockId": "when-run-1"
  }',
  now(),
  now()
),
(
  '33333333-3333-3333-3333-333333333333',
  '00000000-0000-0000-0000-000000000001',
  'MACD Divergence Scanner',
  'Advanced strategy that detects MACD divergences for swing trading opportunities',
  '{
    "blocks": [
      {
        "id": "when-run-1",
        "type": "when_run",
        "position": {"x": 100, "y": 100},
        "outputConnections": ["macd-1"]
      },
      {
        "id": "macd-1",
        "type": "indicator",
        "indicator": "macd",
        "parameters": {"fastPeriod": 12, "slowPeriod": 26, "signalPeriod": 9},
        "position": {"x": 300, "y": 100},
        "inputConnections": ["when-run-1"],
        "outputConnections": ["condition-1"]
      },
      {
        "id": "condition-1",
        "type": "condition",
        "operator": ">",
        "compareToIndicator": "signal_line",
        "position": {"x": 500, "y": 100},
        "inputConnections": ["macd-1"],
        "outputConnections": ["trigger-1"]
      },
      {
        "id": "trigger-1",
        "type": "trigger",
        "signal": "bullish",
        "confidence": 70,
        "position": {"x": 700, "y": 100},
        "inputConnections": ["condition-1"]
      }
    ],
    "entryBlockId": "when-run-1"
  }',
  now(),
  now()
)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  configuration = EXCLUDED.configuration,
  updated_at = now();

-- Publish these agents to the marketplace
INSERT INTO public.published_agents (id, agent_id, publisher_id, name, description, category, tags, is_featured, download_count, average_rating, total_reviews, created_at, updated_at) VALUES 
(
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  '11111111-1111-1111-1111-111111111111',
  '00000000-0000-0000-0000-000000000001',
  'RSI Momentum Strategy',
  'A powerful day trading strategy using RSI and moving averages to identify momentum shifts in the market. Perfect for scalping and short-term trades.',
  'Day Trading',
  ARRAY['RSI', 'Moving Average', 'Momentum', 'Scalping', 'Technical Analysis'],
  true,
  156,
  4.2,
  23,
  now() - interval '5 days',
  now()
),
(
  'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb',
  '22222222-2222-2222-2222-222222222222',
  '00000000-0000-0000-0000-000000000001',
  'Bollinger Bands Breakout',
  'Identifies breakout opportunities using Bollinger Bands and volume confirmation. Great for catching strong trending moves.',
  'Technical Analysis',
  ARRAY['Bollinger Bands', 'Breakout', 'Volume', 'Trend Following'],
  false,
  89,
  3.8,
  15,
  now() - interval '3 days',
  now()
),
(
  'cccccccc-cccc-cccc-cccc-cccccccccccc',
  '33333333-3333-3333-3333-333333333333',
  '00000000-0000-0000-0000-000000000001',
  'MACD Divergence Scanner',
  'Advanced strategy that detects MACD divergences for swing trading opportunities. Excellent for finding reversal points.',
  'Swing Trading',
  ARRAY['MACD', 'Divergence', 'Swing Trading', 'Reversal'],
  false,
  67,
  4.5,
  8,
  now() - interval '1 day',
  now()
)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  category = EXCLUDED.category,
  tags = EXCLUDED.tags,
  is_featured = EXCLUDED.is_featured,
  download_count = EXCLUDED.download_count,
  average_rating = EXCLUDED.average_rating,
  total_reviews = EXCLUDED.total_reviews,
  updated_at = now();

-- Add some sample reviews
INSERT INTO public.agent_reviews (id, published_agent_id, reviewer_id, rating, review_text, created_at, updated_at) VALUES 
(
  'rrrrrrrr-rrrr-rrrr-rrrr-rrrrrrrrrrrr',
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  '00000000-0000-0000-0000-000000000001',
  5,
  'Excellent strategy! Made consistent profits using this RSI approach. The signals are very accurate.',
  now() - interval '2 days',
  now()
),
(
  'ssssssss-ssss-ssss-ssss-ssssssssssss',
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  '00000000-0000-0000-0000-000000000001',
  4,
  'Good strategy overall, but requires some fine-tuning for different market conditions.',
  now() - interval '1 day',
  now()
),
(
  'tttttttt-tttt-tttt-tttt-tttttttttttt',
  'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb',
  '00000000-0000-0000-0000-000000000001',
  4,
  'The Bollinger Bands breakout works well in trending markets. Volume confirmation is key.',
  now() - interval '3 hours',
  now()
)
ON CONFLICT (id) DO UPDATE SET
  rating = EXCLUDED.rating,
  review_text = EXCLUDED.review_text,
  updated_at = now();
