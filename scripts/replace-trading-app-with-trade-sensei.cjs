#!/usr/bin/env node

/**
 * <PERSON>rip<PERSON> to replace trading-app directory with trade-sensei directory
 * while keeping the /trade route as the main page
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const TRADING_APP_DIR = 'trading-app';
const TRADE_SENSEI_DIR = 'trade-sensei';
const BACKUP_DIR = 'trading-app-backup';

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function runCommand(command, options = {}) {
  try {
    const result = execSync(command, { 
      stdio: 'inherit', 
      cwd: process.cwd(),
      ...options 
    });
    return result;
  } catch (error) {
    log(`❌ Command failed: ${command}`, 'red');
    throw error;
  }
}

function checkPrerequisites() {
  log('🔍 Checking prerequisites...', 'blue');
  
  // Check if we're in the right directory
  if (!fs.existsSync('package.json')) {
    log('❌ package.json not found. Make sure you\'re in the project root directory.', 'red');
    process.exit(1);
  }

  // Check if trading-app directory exists
  if (!fs.existsSync(TRADING_APP_DIR)) {
    log(`❌ ${TRADING_APP_DIR} directory not found.`, 'red');
    process.exit(1);
  }

  log('✅ Prerequisites check passed', 'green');
}

function backupTradingApp() {
  log('📦 Creating backup of trading-app directory...', 'blue');
  
  if (fs.existsSync(BACKUP_DIR)) {
    log(`⚠️  Backup directory ${BACKUP_DIR} already exists. Removing it...`, 'yellow');
    runCommand(`rm -rf ${BACKUP_DIR}`);
  }

  runCommand(`cp -r ${TRADING_APP_DIR} ${BACKUP_DIR}`);
  log(`✅ Backup created at ${BACKUP_DIR}`, 'green');
}

function removeTradingApp() {
  log('🗑️  Removing trading-app directory...', 'blue');
  runCommand(`rm -rf ${TRADING_APP_DIR}`);
  log('✅ trading-app directory removed', 'green');
}

function setupTradeSensei(sourcePath) {
  log('📁 Setting up trade-sensei directory...', 'blue');
  
  if (sourcePath) {
    // Copy from provided source path
    if (!fs.existsSync(sourcePath)) {
      log(`❌ Source path ${sourcePath} does not exist.`, 'red');
      process.exit(1);
    }
    
    log(`📋 Copying from ${sourcePath} to ${TRADE_SENSEI_DIR}...`, 'blue');
    runCommand(`cp -r "${sourcePath}" ${TRADE_SENSEI_DIR}`);
  } else {
    // Create placeholder directory structure
    log('📁 Creating placeholder trade-sensei directory structure...', 'blue');
    fs.mkdirSync(TRADE_SENSEI_DIR, { recursive: true });
    fs.mkdirSync(path.join(TRADE_SENSEI_DIR, 'src'), { recursive: true });
    fs.mkdirSync(path.join(TRADE_SENSEI_DIR, 'public'), { recursive: true });
    
    // Create basic package.json
    const packageJson = {
      name: 'trade-sensei',
      version: '1.0.0',
      description: 'Trade Sensei Trading Application',
      main: 'index.js',
      scripts: {
        dev: 'vite',
        build: 'vite build',
        preview: 'vite preview'
      },
      dependencies: {},
      devDependencies: {}
    };
    
    fs.writeFileSync(
      path.join(TRADE_SENSEI_DIR, 'package.json'),
      JSON.stringify(packageJson, null, 2)
    );
    
    // Create basic README
    const readme = `# Trade Sensei

This directory contains the Trade Sensei trading application.

## Setup

1. Add your trade-sensei source code to this directory
2. Install dependencies: \`npm install\`
3. Start development: \`npm run dev\`

## Sync with Trade Sensei Repository

Use the sync script to keep this directory up to date:
\`\`\`bash
npm run sync:trade-sensei
\`\`\`
`;
    
    fs.writeFileSync(path.join(TRADE_SENSEI_DIR, 'README.md'), readme);
    
    log('⚠️  Placeholder structure created. You need to add your trade-sensei source code.', 'yellow');
  }
  
  log('✅ trade-sensei directory setup complete', 'green');
}

function updatePackageJson() {
  log('📝 Updating package.json scripts...', 'blue');
  
  const packageJsonPath = 'package.json';
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // Add sync script for trade-sensei
  packageJson.scripts = packageJson.scripts || {};
  packageJson.scripts['sync:trade-sensei'] = 'node scripts/sync-trade-sensei.js';
  packageJson.scripts['update:trade-sensei'] = 'node scripts/sync-trade-sensei.js';
  
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
  log('✅ package.json updated with sync scripts', 'green');
}

function ensureTradeRouteIsDefault() {
  log('🔄 Ensuring /trade route is the default route...', 'blue');
  
  // The routing is already configured in src/App.tsx to redirect to /trade
  // Let's verify this is still the case
  const appTsxPath = 'src/App.tsx';
  if (fs.existsSync(appTsxPath)) {
    const appContent = fs.readFileSync(appTsxPath, 'utf8');
    
    if (appContent.includes('Navigate to="/trade"')) {
      log('✅ /trade route is already set as default', 'green');
    } else {
      log('⚠️  /trade route may not be set as default. Please check src/App.tsx', 'yellow');
    }
  }
}

function createSyncScript() {
  log('📝 Creating sync script for trade-sensei updates...', 'blue');
  
  const syncScriptContent = `#!/usr/bin/env node

/**
 * Script to sync trade-sensei directory with external repository
 */

const fs = require('fs');
const { execSync } = require('child_process');

const TRADE_SENSEI_DIR = 'trade-sensei';

function log(message, color = 'reset') {
  const colors = {
    red: '\\x1b[31m',
    green: '\\x1b[32m',
    yellow: '\\x1b[33m',
    blue: '\\x1b[34m',
    reset: '\\x1b[0m'
  };
  console.log(\`\${colors[color]}\${message}\${colors.reset}\`);
}

function runCommand(command) {
  try {
    execSync(command, { stdio: 'inherit' });
  } catch (error) {
    log(\`❌ Command failed: \${command}\`, 'red');
    throw error;
  }
}

function syncTradeSensei(sourceRepo) {
  if (!sourceRepo) {
    log('❌ Please provide the trade-sensei repository URL or path', 'red');
    log('Usage: npm run sync:trade-sensei <repository-url-or-path>', 'yellow');
    process.exit(1);
  }

  log('🔄 Syncing trade-sensei directory...', 'blue');
  
  // Create temporary directory for cloning
  const tempDir = 'temp-trade-sensei';
  
  try {
    // Remove existing temp directory if it exists
    if (fs.existsSync(tempDir)) {
      runCommand(\`rm -rf \${tempDir}\`);
    }
    
    // Clone or copy the source
    if (sourceRepo.startsWith('http') || sourceRepo.startsWith('git@')) {
      log(\`📥 Cloning from repository: \${sourceRepo}\`, 'blue');
      runCommand(\`git clone \${sourceRepo} \${tempDir}\`);
    } else {
      log(\`📋 Copying from local path: \${sourceRepo}\`, 'blue');
      runCommand(\`cp -r "\${sourceRepo}" \${tempDir}\`);
    }
    
    // Remove existing trade-sensei directory
    if (fs.existsSync(TRADE_SENSEI_DIR)) {
      runCommand(\`rm -rf \${TRADE_SENSEI_DIR}\`);
    }
    
    // Move temp directory to trade-sensei
    runCommand(\`mv \${tempDir} \${TRADE_SENSEI_DIR}\`);
    
    // Remove .git directory if it exists (to avoid nested git repos)
    const gitDir = \`\${TRADE_SENSEI_DIR}/.git\`;
    if (fs.existsSync(gitDir)) {
      runCommand(\`rm -rf \${gitDir}\`);
    }
    
    log('✅ trade-sensei directory synced successfully', 'green');
    
  } catch (error) {
    log('❌ Sync failed', 'red');
    // Cleanup temp directory
    if (fs.existsSync(tempDir)) {
      runCommand(\`rm -rf \${tempDir}\`);
    }
    process.exit(1);
  }
}

// Get source repository from command line arguments
const sourceRepo = process.argv[2];
syncTradeSensei(sourceRepo);
`;

  fs.writeFileSync('scripts/sync-trade-sensei.js', syncScriptContent);
  runCommand('chmod +x scripts/sync-trade-sensei.js');
  log('✅ Sync script created at scripts/sync-trade-sensei.js', 'green');
}

function main() {
  const args = process.argv.slice(2);
  const sourcePath = args[0]; // Optional source path for trade-sensei
  
  log('🚀 Starting trade-app to trade-sensei replacement...', 'blue');
  
  try {
    checkPrerequisites();
    backupTradingApp();
    removeTradingApp();
    setupTradeSensei(sourcePath);
    updatePackageJson();
    ensureTradeRouteIsDefault();
    createSyncScript();
    
    log('🎉 Replacement completed successfully!', 'green');
    log('', 'reset');
    log('📋 Next steps:', 'blue');
    log('1. Add your trade-sensei source code to the trade-sensei directory', 'yellow');
    log('2. Use "npm run sync:trade-sensei <repo-url>" to sync with external repository', 'yellow');
    log('3. The /trade route will continue to work as the main page', 'yellow');
    log('4. Your original trading-app is backed up in trading-app-backup/', 'yellow');
    
  } catch (error) {
    log('❌ Replacement failed', 'red');
    console.error(error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { main };
