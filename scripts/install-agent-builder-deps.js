#!/usr/bin/env node

/**
 * This script installs the dependencies required for the AI agent builder feature.
 */

import { execSync } from 'child_process';

try {
  console.log('Installing dependencies for the AI agent builder feature...');
  
  // Install reactflow and uuid
  execSync('npm install reactflow uuid', { stdio: 'inherit' });
  
  // Install type definitions for uuid
  execSync('npm install -D @types/uuid', { stdio: 'inherit' });
  
  console.log('Dependencies installed successfully!');
} catch (error) {
  console.error('Error installing dependencies:', error);
  process.exit(1);
}
