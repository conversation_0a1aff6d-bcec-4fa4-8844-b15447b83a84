#!/usr/bin/env node

/**
 * Comprehensive Test Runner for AI Trading Agent System
 * Runs different test suites with proper configuration and reporting
 */

import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Test suite configurations
const TEST_SUITES = {
  unit: {
    name: 'Unit Tests',
    pattern: 'src/test/components/blocks',
    timeout: 10000
  },
  integration: {
    name: 'Integration Tests',
    pattern: 'src/test/integration',
    timeout: 30000
  },
  performance: {
    name: 'Performance Tests',
    pattern: 'src/test/performance',
    timeout: 60000
  },
  blocks: {
    name: 'Block Component Tests',
    pattern: 'src/test/components/blocks',
    timeout: 15000
  },
  ui: {
    name: 'UI Component Tests',
    pattern: 'src/test/components/AgentBuilder.test.tsx',
    timeout: 15000
  }
};

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function log(message, color = 'reset') {
  console.log(colorize(message, color));
}

function logHeader(message) {
  console.log('\n' + '='.repeat(60));
  log(message, 'bright');
  console.log('='.repeat(60));
}

function logSection(message) {
  console.log('\n' + '-'.repeat(40));
  log(message, 'cyan');
  console.log('-'.repeat(40));
}

async function runCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve(code);
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

async function runTestSuite(suiteName, options = {}) {
  const suite = TEST_SUITES[suiteName];
  if (!suite) {
    throw new Error(`Unknown test suite: ${suiteName}`);
  }

  logSection(`Running ${suite.name}`);

  const vitestArgs = [
    'run',
    '--config', 'vitest.config.ts'
  ];

  // Add pattern matching
  if (suite.pattern) {
    vitestArgs.push(suite.pattern);
  }

  // Add coverage if requested
  if (options.coverage) {
    vitestArgs.push('--coverage');
  }

  // Add watch mode if requested
  if (options.watch) {
    vitestArgs.push('--watch');
  }

  // Add reporter options
  if (options.reporter) {
    vitestArgs.push('--reporter', options.reporter);
  }

  // Set timeout
  if (suite.timeout) {
    vitestArgs.push('--testTimeout', suite.timeout.toString());
  }

  // Add verbose output
  if (options.verbose) {
    vitestArgs.push('--verbose');
  }

  try {
    await runCommand('npx', ['vitest', ...vitestArgs]);
    log(`✅ ${suite.name} completed successfully`, 'green');
    return true;
  } catch (error) {
    log(`❌ ${suite.name} failed: ${error.message}`, 'red');
    return false;
  }
}

async function runAllTests(options = {}) {
  logHeader('Running All Test Suites');
  
  const results = {};
  const suiteOrder = ['unit', 'blocks', 'ui', 'integration', 'performance'];
  
  for (const suiteName of suiteOrder) {
    try {
      const success = await runTestSuite(suiteName, options);
      results[suiteName] = success;
      
      if (!success && options.bail) {
        log('Stopping due to test failure (--bail option)', 'yellow');
        break;
      }
    } catch (error) {
      results[suiteName] = false;
      log(`Error running ${suiteName}: ${error.message}`, 'red');
      
      if (options.bail) {
        break;
      }
    }
  }

  // Print summary
  logHeader('Test Results Summary');
  
  let totalSuites = 0;
  let passedSuites = 0;
  
  for (const [suiteName, passed] of Object.entries(results)) {
    totalSuites++;
    if (passed) {
      passedSuites++;
      log(`✅ ${TEST_SUITES[suiteName].name}`, 'green');
    } else {
      log(`❌ ${TEST_SUITES[suiteName].name}`, 'red');
    }
  }
  
  console.log('\n');
  if (passedSuites === totalSuites) {
    log(`🎉 All ${totalSuites} test suites passed!`, 'green');
  } else {
    log(`⚠️  ${passedSuites}/${totalSuites} test suites passed`, 'yellow');
  }
  
  return passedSuites === totalSuites;
}

async function runCoverageReport() {
  logHeader('Generating Coverage Report');
  
  try {
    await runCommand('npx', [
      'vitest',
      'run',
      '--coverage',
      '--reporter=verbose',
      '--reporter=json',
      '--reporter=html'
    ]);
    
    log('✅ Coverage report generated successfully', 'green');
    log('📊 Open coverage/index.html to view detailed report', 'cyan');
    return true;
  } catch (error) {
    log(`❌ Coverage report failed: ${error.message}`, 'red');
    return false;
  }
}

async function runPerformanceBenchmarks() {
  logHeader('Running Performance Benchmarks');
  
  try {
    await runCommand('npx', [
      'vitest',
      'bench',
      'src/test/performance/**/*.test.ts',
      '--reporter=verbose'
    ]);
    
    log('✅ Performance benchmarks completed', 'green');
    return true;
  } catch (error) {
    log(`❌ Performance benchmarks failed: ${error.message}`, 'red');
    return false;
  }
}

async function validateTestEnvironment() {
  logSection('Validating Test Environment');
  
  const checks = [
    {
      name: 'Node.js version',
      check: () => {
        const version = process.version;
        const major = parseInt(version.slice(1).split('.')[0]);
        return major >= 16;
      },
      message: 'Node.js 16+ required'
    },
    {
      name: 'Test setup file',
      check: () => fs.existsSync(path.join(process.cwd(), 'src/test/setup.ts')),
      message: 'src/test/setup.ts must exist'
    },
    {
      name: 'Vitest config',
      check: () => fs.existsSync(path.join(process.cwd(), 'vitest.config.ts')),
      message: 'vitest.config.ts must exist'
    },
    {
      name: 'Test utilities',
      check: () => fs.existsSync(path.join(process.cwd(), 'src/test/utils')),
      message: 'Test utilities directory must exist'
    }
  ];
  
  let allPassed = true;
  
  for (const check of checks) {
    try {
      const passed = check.check();
      if (passed) {
        log(`✅ ${check.name}`, 'green');
      } else {
        log(`❌ ${check.name}: ${check.message}`, 'red');
        allPassed = false;
      }
    } catch (error) {
      log(`❌ ${check.name}: ${error.message}`, 'red');
      allPassed = false;
    }
  }
  
  return allPassed;
}

// CLI argument parsing
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {
    suite: null,
    coverage: false,
    watch: false,
    bail: false,
    verbose: false,
    reporter: 'default',
    benchmark: false,
    validate: false
  };
  
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    switch (arg) {
      case '--coverage':
        options.coverage = true;
        break;
      case '--watch':
        options.watch = true;
        break;
      case '--bail':
        options.bail = true;
        break;
      case '--verbose':
        options.verbose = true;
        break;
      case '--benchmark':
        options.benchmark = true;
        break;
      case '--validate':
        options.validate = true;
        break;
      case '--reporter':
        options.reporter = args[++i];
        break;
      case '--suite':
        options.suite = args[++i];
        break;
      case '--help':
        printHelp();
        process.exit(0);
        break;
      default:
        if (!arg.startsWith('--')) {
          options.suite = arg;
        }
    }
  }
  
  return options;
}

function printHelp() {
  console.log(`
${colorize('AI Trading Agent Test Runner', 'bright')}

Usage: node scripts/test-runner.js [options] [suite]

Test Suites:
  unit         Run unit tests only
  integration  Run integration tests only
  performance  Run performance tests only
  blocks       Run block component tests only
  ui           Run UI component tests only
  all          Run all test suites (default)

Options:
  --coverage   Generate coverage report
  --watch      Run tests in watch mode
  --bail       Stop on first test failure
  --verbose    Verbose output
  --benchmark  Run performance benchmarks
  --validate   Validate test environment
  --reporter   Test reporter (default, verbose, json, html)
  --help       Show this help message

Examples:
  node scripts/test-runner.js unit --coverage
  node scripts/test-runner.js --watch
  node scripts/test-runner.js all --bail --verbose
  node scripts/test-runner.js --benchmark
`);
}

// Main execution
async function main() {
  const options = parseArgs();
  
  logHeader('AI Trading Agent Test Runner');
  
  // Validate environment if requested
  if (options.validate) {
    const valid = await validateTestEnvironment();
    if (!valid) {
      log('❌ Environment validation failed', 'red');
      process.exit(1);
    }
    log('✅ Environment validation passed', 'green');
    return;
  }
  
  // Run benchmarks if requested
  if (options.benchmark) {
    const success = await runPerformanceBenchmarks();
    process.exit(success ? 0 : 1);
  }
  
  // Generate coverage report if requested without running tests
  if (options.coverage && !options.suite) {
    const success = await runCoverageReport();
    process.exit(success ? 0 : 1);
  }
  
  // Run specific test suite or all tests
  let success = false;
  
  if (options.suite && options.suite !== 'all') {
    if (!TEST_SUITES[options.suite]) {
      log(`❌ Unknown test suite: ${options.suite}`, 'red');
      log(`Available suites: ${Object.keys(TEST_SUITES).join(', ')}`, 'yellow');
      process.exit(1);
    }
    success = await runTestSuite(options.suite, options);
  } else {
    success = await runAllTests(options);
  }
  
  process.exit(success ? 0 : 1);
}

// Run the main function
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    log(`❌ Unexpected error: ${error.message}`, 'red');
    console.error(error.stack);
    process.exit(1);
  });
}

export {
  runTestSuite,
  runAllTests,
  runCoverageReport,
  runPerformanceBenchmarks,
  validateTestEnvironment
};
