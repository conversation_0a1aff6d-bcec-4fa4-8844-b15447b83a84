#!/usr/bin/env node

/**
 * This script deploys the API edge functions to Supabase.
 */

import { execSync } from 'child_process';

try {
  // Check if the Supabase CLI is installed
  try {
    execSync('supabase --version', { stdio: 'inherit' });
  } catch (error) {
    throw new Error('Supabase CLI is not installed. Please install it using: npm install -g supabase');
  }

  // Deploy the chart-processor function
  execSync('supabase functions deploy chart-processor', { stdio: 'inherit' });

  // Deploy the Osis-api function
  execSync('supabase functions deploy Osis-api', { stdio: 'inherit' });

  // Deploy the api-processor function
  execSync('supabase functions deploy api-processor', { stdio: 'inherit' });

  // Deploy the market-data function
  execSync('supabase functions deploy market-data', { stdio: 'inherit' });

  // Deploy the chart-data function
  execSync('supabase functions deploy chart-data', { stdio: 'inherit' });

} catch (error) {
  process.exit(1);
}
