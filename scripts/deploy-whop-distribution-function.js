#!/usr/bin/env node

/**
 * <PERSON>ript to deploy the Whop Agent Distribution Supabase Edge Function
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m',    // Cyan
    success: '\x1b[32m', // Green
    warning: '\x1b[33m', // Yellow
    error: '\x1b[31m',   // Red
    reset: '\x1b[0m'     // Reset
  };
  
  console.log(`${colors[type]}${message}${colors.reset}`);
}

function runCommand(command, description) {
  try {
    log(`🔄 ${description}...`, 'info');
    const output = execSync(command, { 
      encoding: 'utf8', 
      stdio: 'pipe',
      cwd: path.join(__dirname, '..')
    });
    log(`✅ ${description} completed`, 'success');
    return output;
  } catch (error) {
    log(`❌ ${description} failed: ${error.message}`, 'error');
    throw error;
  }
}

function checkSupabaseInstalled() {
  try {
    execSync('supabase --version', { stdio: 'pipe' });
    return true;
  } catch (error) {
    return false;
  }
}

function checkSupabaseLoggedIn() {
  try {
    const output = execSync('supabase projects list', { encoding: 'utf8', stdio: 'pipe' });
    return !output.includes('You are not logged in');
  } catch (error) {
    return false;
  }
}

function setSupabaseSecrets() {
  log('🔧 Setting Supabase secrets for Whop distribution...', 'info');
  
  // Read environment variables from .env file
  const envPath = path.join(__dirname, '..', '.env');
  if (!fs.existsSync(envPath)) {
    throw new Error('.env file not found. Please run npm run setup:whop first.');
  }
  
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envVars = {};
  
  envContent.split('\n').forEach(line => {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, ...valueParts] = trimmed.split('=');
      if (key && valueParts.length > 0) {
        envVars[key.trim()] = valueParts.join('=').trim();
      }
    }
  });
  
  // Set required secrets
  const requiredSecrets = {
    'WHOP_APP_ID': envVars['VITE_WHOP_APP_ID'],
    'WHOP_API_KEY': envVars['WHOP_API_KEY'],
    'WHOP_AGENT_USER_ID': envVars['VITE_WHOP_AGENT_USER_ID'],
    'WHOP_COMPANY_ID': envVars['VITE_WHOP_COMPANY_ID']
  };
  
  for (const [secretName, secretValue] of Object.entries(requiredSecrets)) {
    if (!secretValue) {
      log(`⚠️ Warning: ${secretName} not found in .env file`, 'warning');
      continue;
    }
    
    try {
      runCommand(
        `supabase secrets set ${secretName}="${secretValue}"`,
        `Setting secret ${secretName}`
      );
    } catch (error) {
      log(`⚠️ Warning: Failed to set secret ${secretName}`, 'warning');
    }
  }
}

function deployFunction() {
  log('🚀 Deploying Whop distribution function...', 'info');
  
  // Check if function directory exists
  const functionPath = path.join(__dirname, '..', 'supabase', 'functions', 'whop-distribute-agent');
  if (!fs.existsSync(functionPath)) {
    throw new Error('Whop distribution function directory not found');
  }
  
  // Deploy the function
  runCommand(
    'supabase functions deploy whop-distribute-agent --no-verify-jwt',
    'Deploying whop-distribute-agent function'
  );
  
  log('✅ Whop distribution function deployed successfully!', 'success');
}

function runMigration() {
  log('🗄️ Running database migration for Whop distribution system...', 'info');
  
  try {
    // Check if migration file exists
    const migrationPath = path.join(__dirname, '..', 'supabase', 'migrations', '20250702000000_create_whop_distribution_system.sql');
    if (!fs.existsSync(migrationPath)) {
      throw new Error('Migration file not found');
    }
    
    // Run the migration
    runCommand(
      'supabase db push',
      'Applying database migration'
    );
    
    log('✅ Database migration completed successfully!', 'success');
  } catch (error) {
    log(`⚠️ Migration failed: ${error.message}`, 'warning');
    log('You may need to apply the migration manually', 'warning');
  }
}

function testFunction() {
  log('🧪 Testing Whop distribution function...', 'info');
  
  try {
    // Get the function URL
    const output = runCommand(
      'supabase status',
      'Getting Supabase status'
    );
    
    // Extract the API URL from status output
    const apiUrlMatch = output.match(/API URL: (https?:\/\/[^\s]+)/);
    if (!apiUrlMatch) {
      throw new Error('Could not find API URL in Supabase status');
    }
    
    const apiUrl = apiUrlMatch[1];
    const functionUrl = `${apiUrl}/functions/v1/whop-distribute-agent`;
    
    log(`📡 Function URL: ${functionUrl}`, 'info');
    log('🔗 Function is ready to receive POST requests for agent distribution', 'info');
    
    return functionUrl;
  } catch (error) {
    log(`⚠️ Could not test function automatically: ${error.message}`, 'warning');
    return null;
  }
}

function main() {
  try {
    log('🚀 Starting Whop distribution function deployment...', 'info');
    
    // Check prerequisites
    if (!checkSupabaseInstalled()) {
      throw new Error('Supabase CLI is not installed. Please install it first: https://supabase.com/docs/guides/cli');
    }
    
    if (!checkSupabaseLoggedIn()) {
      throw new Error('You are not logged in to Supabase. Please run: supabase login');
    }
    
    // Run migration first
    runMigration();
    
    // Set secrets
    setSupabaseSecrets();
    
    // Deploy function
    deployFunction();
    
    // Test function
    const functionUrl = testFunction();
    
    log('', 'info');
    log('🎉 Whop distribution deployment completed!', 'success');
    log('', 'info');
    log('📋 What was deployed:', 'info');
    log('1. Database tables for Whop agent distribution', 'info');
    log('2. Edge function for distributing agents to Whop members', 'info');
    log('3. Required environment variables and secrets', 'info');
    log('', 'info');
    log('🔧 Next steps:', 'info');
    log('1. Test the feature in your application', 'info');
    log('2. Verify Whop owner permissions work correctly', 'info');
    log('3. Check that agents are distributed to all members', 'info');
    log('', 'info');
    
    if (functionUrl) {
      log(`🔗 Function URL: ${functionUrl}`, 'info');
    }
    
  } catch (error) {
    log(`❌ Deployment failed: ${error.message}`, 'error');
    process.exit(1);
  }
}

// Run the deployment
main();
