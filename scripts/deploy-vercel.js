#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Vercel deployment process...');

// Check if vercel.json exists
const vercelConfigPath = path.join(process.cwd(), 'vercel.json');
if (!fs.existsSync(vercelConfigPath)) {
  console.error('❌ vercel.json not found. Please ensure it exists in the project root.');
  process.exit(1);
}

// Check if API functions exist
const apiFunctionsDir = path.join(process.cwd(), 'api');
if (!fs.existsSync(apiFunctionsDir)) {
  console.error('❌ API functions directory not found. Please ensure /api directory exists.');
  process.exit(1);
}

// List required environment variables
const requiredEnvVars = [
  'VITE_WHOP_APP_ID',
  'WHOP_API_KEY',
  'VITE_WHOP_AGENT_USER_ID',
  'VITE_WHOP_COMPANY_ID'
];

console.log('📋 Required environment variables for Vercel:');
requiredEnvVars.forEach(envVar => {
  console.log(`   - ${envVar}`);
});

console.log('\n💡 To set environment variables in Vercel:');
console.log('   1. Go to your Vercel dashboard');
console.log('   2. Select your project');
console.log('   3. Go to Settings > Environment Variables');
console.log('   4. Add each variable with the appropriate value');

console.log('\n🔧 Or use Vercel CLI:');
requiredEnvVars.forEach(envVar => {
  console.log(`   vercel env add ${envVar}`);
});

console.log('\n📦 Building project...');
try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('✅ Build completed successfully');
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}

console.log('\n🚀 Ready for Vercel deployment!');
console.log('Run: vercel --prod');

console.log('\n📝 Deployment checklist:');
console.log('   ✅ vercel.json configured');
console.log('   ✅ API functions created');
console.log('   ✅ Build completed');
console.log('   ⚠️  Environment variables (set in Vercel dashboard)');
console.log('   ⚠️  Domain configuration (if needed)');
