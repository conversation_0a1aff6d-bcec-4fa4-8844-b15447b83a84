#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to replace environment variable placeholders in index.html
 * This is needed because Vite doesn't process environment variables in index.html
 */

const fs = require('fs');
const path = require('path');

// Read environment variables
const growiPublicId = process.env.VITE_GROWI_PUBLIC_ID || '';

// Path to index.html
const indexPath = path.join(__dirname, '..', 'index.html');

try {
  // Read the index.html file
  let content = fs.readFileSync(indexPath, 'utf8');
  
  // Replace the placeholder with the actual environment variable
  content = content.replace('%VITE_GROWI_PUBLIC_ID%', growiPublicId);
  
  // Write the file back
  fs.writeFileSync(indexPath, content, 'utf8');
  
  console.log('✅ Environment variables replaced in index.html');
  if (growiPublicId) {
    console.log(`   VITE_GROWI_PUBLIC_ID: ${growiPublicId}`);
  } else {
    console.log('   ⚠️  VITE_GROWI_PUBLIC_ID is not set');
  }
} catch (error) {
  console.error('❌ Error replacing environment variables:', error);
  process.exit(1);
}
