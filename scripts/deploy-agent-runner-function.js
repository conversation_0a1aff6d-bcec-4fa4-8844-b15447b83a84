#!/usr/bin/env node

/**
 * This script deploys the Agent Runner edge function to Supabase.
 */

import { execSync } from 'child_process';

try {
  // Check if the Supabase CLI is installed
  try {
    execSync('supabase --version', { stdio: 'inherit' });
  } catch (error) {
    throw new Error('Supabase CLI is not installed. Please install it using: npm install -g supabase');
  }
  
  // Deploy the function
  execSync('supabase functions deploy agent-runner', { stdio: 'inherit' });
  console.log('Agent Runner edge function deployed successfully!');
} catch (error) {
  console.error('Error deploying Agent Runner edge function:', error);
  process.exit(1);
}
