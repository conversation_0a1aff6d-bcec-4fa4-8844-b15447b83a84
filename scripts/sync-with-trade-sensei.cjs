#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to completely replace the trade-app directory with the current trade-sensei directory
 * while keeping the /trade route as the main page/default/home page
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const TRADING_APP_DIR = 'trading-app';
const BACKUP_DIR = 'trading-app-backup';

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function runCommand(command, options = {}) {
  try {
    const result = execSync(command, { 
      stdio: 'inherit', 
      cwd: process.cwd(),
      ...options 
    });
    return result;
  } catch (error) {
    log(`❌ Command failed: ${command}`, 'red');
    throw error;
  }
}

function checkPrerequisites() {
  log('🔍 Checking prerequisites...', 'blue');
  
  // Check if we're in the right directory
  if (!fs.existsSync('package.json')) {
    log('❌ package.json not found. Make sure you\'re in the project root directory.', 'red');
    process.exit(1);
  }

  // Check if trading-app directory exists
  if (!fs.existsSync(TRADING_APP_DIR)) {
    log(`❌ ${TRADING_APP_DIR} directory not found.`, 'red');
    process.exit(1);
  }

  log('✅ Prerequisites check passed', 'green');
}

function backupTradingApp() {
  log('📦 Creating backup of trading-app directory...', 'blue');
  
  if (fs.existsSync(BACKUP_DIR)) {
    log(`⚠️  Backup directory ${BACKUP_DIR} already exists. Removing it...`, 'yellow');
    runCommand(`rm -rf ${BACKUP_DIR}`);
  }

  runCommand(`cp -r ${TRADING_APP_DIR} ${BACKUP_DIR}`);
  log(`✅ Backup created at ${BACKUP_DIR}`, 'green');
}

function removeTradingApp() {
  log('🗑️  Removing trading-app directory...', 'blue');
  runCommand(`rm -rf ${TRADING_APP_DIR}`);
  log('✅ trading-app directory removed', 'green');
}

function copyTradeSenseiToTradingApp() {
  log('📋 Copying current trade-sensei directory to trading-app...', 'blue');
  
  // Create the trading-app directory
  fs.mkdirSync(TRADING_APP_DIR, { recursive: true });
  
  // Copy all files from current directory to trading-app, excluding certain directories
  const excludeDirs = [
    'node_modules',
    '.git',
    'dist',
    'trading-app',
    'trading-app-backup',
    'trade-sensei',
    '.next',
    'whop-intermediary-server',
    'whop-app-template'
  ];
  
  const excludeFiles = [
    '.DS_Store',
    'Thumbs.db'
  ];
  
  // Get all items in current directory
  const items = fs.readdirSync('.');
  
  for (const item of items) {
    if (excludeDirs.includes(item) || excludeFiles.includes(item)) {
      continue;
    }
    
    const sourcePath = path.join('.', item);
    const destPath = path.join(TRADING_APP_DIR, item);
    
    try {
      if (fs.statSync(sourcePath).isDirectory()) {
        runCommand(`cp -r "${sourcePath}" "${destPath}"`);
      } else {
        runCommand(`cp "${sourcePath}" "${destPath}"`);
      }
    } catch (error) {
      log(`⚠️  Warning: Could not copy ${item}: ${error.message}`, 'yellow');
    }
  }
  
  log('✅ Trade-sensei content copied to trading-app directory', 'green');
}

function updateTradingAppRouting() {
  log('🔄 Updating routing to make /trade the default route...', 'blue');
  
  const appTsxPath = path.join(TRADING_APP_DIR, 'src', 'App.tsx');
  
  if (fs.existsSync(appTsxPath)) {
    let appContent = fs.readFileSync(appTsxPath, 'utf8');
    
    // Ensure the default route redirects to /trade
    if (!appContent.includes('Navigate to="/trade"')) {
      // Replace the default route to redirect to /trade
      appContent = appContent.replace(
        /Route path="\/" element={[^}]+}/g,
        'Route path="/" element={<Navigate to="/trade" replace />}'
      );
      
      // Also replace catch-all route
      appContent = appContent.replace(
        /Route path="\*" element={[^}]+}/g,
        'Route path="*" element={<Navigate to="/trade" replace />}'
      );
      
      fs.writeFileSync(appTsxPath, appContent);
      log('✅ Updated routing to make /trade the default route', 'green');
    } else {
      log('✅ /trade route is already set as default', 'green');
    }
  } else {
    log('⚠️  App.tsx not found, routing may need manual adjustment', 'yellow');
  }
}

function updatePackageJson() {
  log('📝 Updating trading-app package.json...', 'blue');
  
  const packageJsonPath = path.join(TRADING_APP_DIR, 'package.json');
  
  if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    // Update name to reflect it's the trading app
    packageJson.name = 'trade-app';
    packageJson.description = 'Trade Sensei Trading Application - Synced from trade-sensei-chat';
    
    // Add sync script
    packageJson.scripts = packageJson.scripts || {};
    packageJson.scripts['sync:from-trade-sensei'] = 'cd .. && node scripts/sync-with-trade-sensei.js';
    
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    log('✅ Updated trading-app package.json', 'green');
  }
}

function createSyncScript() {
  log('📝 Creating sync script for future updates...', 'blue');
  
  const syncScriptPath = path.join(TRADING_APP_DIR, 'sync-from-trade-sensei.js');
  const syncScriptContent = `#!/usr/bin/env node

/**
 * Script to sync this trading-app with the parent trade-sensei-chat repository
 * Run this from within the trading-app directory
 */

const { execSync } = require('child_process');

console.log('🔄 Syncing with parent trade-sensei-chat repository...');

try {
  // Go to parent directory and run the sync script
  execSync('cd .. && node scripts/sync-with-trade-sensei.js', { stdio: 'inherit' });
  console.log('✅ Sync completed successfully!');
} catch (error) {
  console.error('❌ Sync failed:', error.message);
  process.exit(1);
}
`;

  fs.writeFileSync(syncScriptPath, syncScriptContent);
  runCommand(`chmod +x "${syncScriptPath}"`);
  log('✅ Sync script created in trading-app directory', 'green');
}

function main() {
  log('🚀 Starting trade-sensei to trading-app sync...', 'blue');
  log('This will completely replace trading-app with current trade-sensei content', 'yellow');
  
  try {
    checkPrerequisites();
    backupTradingApp();
    removeTradingApp();
    copyTradeSenseiToTradingApp();
    updateTradingAppRouting();
    updatePackageJson();
    createSyncScript();
    
    log('🎉 Sync completed successfully!', 'green');
    log('', 'reset');
    log('📋 Summary:', 'blue');
    log('✅ trading-app directory replaced with trade-sensei content', 'green');
    log('✅ /trade route configured as default/home page', 'green');
    log('✅ Original trading-app backed up to trading-app-backup/', 'green');
    log('✅ Sync script created for future updates', 'green');
    log('', 'reset');
    log('📋 Next steps:', 'blue');
    log('1. cd trading-app && npm install', 'yellow');
    log('2. Test the application: npm run dev', 'yellow');
    log('3. Deploy when ready: npm run build', 'yellow');
    log('4. To sync again in the future: npm run sync:from-trade-sensei', 'yellow');
    
  } catch (error) {
    log('❌ Sync failed', 'red');
    console.error(error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { main };
