#!/usr/bin/env node

/**
 * Script to help set up Whop environment variables
 * This script guides users through setting up their Whop integration
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Whop credentials from memories
const WHOP_CREDENTIALS = {
  API_KEY: 'v3Kff2BHovdicGf9c4zAOtlx-Y7AdYyi0YsWGIF20EE',
  APP_ID: 'app_VFK6Os0L6NKH0i',
  AGENT_USER_ID: 'user_CsaZOGen5WUoE',
  COMPANY_ID: 'biz_OGyv6Pz0Le35Fa'
};

const ENV_FILE_PATH = path.join(__dirname, '..', '.env');
const ENV_EXAMPLE_PATH = path.join(__dirname, '..', '.env.example');

function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m',    // Cyan
    success: '\x1b[32m', // Green
    warning: '\x1b[33m', // Yellow
    error: '\x1b[31m',   // Red
    reset: '\x1b[0m'     // Reset
  };
  
  console.log(`${colors[type]}${message}${colors.reset}`);
}

function readEnvFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return {};
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const env = {};
  
  content.split('\n').forEach(line => {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, ...valueParts] = trimmed.split('=');
      if (key && valueParts.length > 0) {
        env[key.trim()] = valueParts.join('=').trim();
      }
    }
  });
  
  return env;
}

function writeEnvFile(filePath, env) {
  const lines = [];
  
  // Add header
  lines.push('# Trade Sensei Environment Variables');
  lines.push('# Generated by setup-whop-env.js');
  lines.push('');
  
  // Group variables by category
  const categories = {
    'Supabase': ['VITE_SUPABASE_URL', 'VITE_SUPABASE_ANON_KEY'],
    'Encryption': ['VITE_ENCRYPTION_KEY'],
    'Stripe': ['VITE_STRIPE_PUBLISHABLE_KEY'],
    'Growi': ['VITE_GROWI_PUBLIC_ID'],
    'Whop': ['VITE_WHOP_APP_ID', 'WHOP_API_KEY', 'VITE_WHOP_AGENT_USER_ID', 'VITE_WHOP_COMPANY_ID']
  };
  
  Object.entries(categories).forEach(([category, keys]) => {
    lines.push(`# ${category} Configuration`);
    keys.forEach(key => {
      const value = env[key] || '';
      lines.push(`${key}=${value}`);
    });
    lines.push('');
  });
  
  // Add any other variables not in categories
  Object.entries(env).forEach(([key, value]) => {
    const isInCategory = Object.values(categories).some(keys => keys.includes(key));
    if (!isInCategory) {
      lines.push(`${key}=${value}`);
    }
  });
  
  fs.writeFileSync(filePath, lines.join('\n'));
}

function setupWhopEnvironment() {
  log('🚀 Setting up Whop integration environment...', 'info');
  
  // Read existing .env file or create from example
  let env = {};
  
  if (fs.existsSync(ENV_FILE_PATH)) {
    log('📖 Reading existing .env file...', 'info');
    env = readEnvFile(ENV_FILE_PATH);
  } else if (fs.existsSync(ENV_EXAMPLE_PATH)) {
    log('📋 Creating .env from .env.example...', 'info');
    env = readEnvFile(ENV_EXAMPLE_PATH);
  } else {
    log('📝 Creating new .env file...', 'info');
  }
  
  // Set Whop credentials
  log('🔧 Setting up Whop credentials...', 'info');
  
  env['VITE_WHOP_APP_ID'] = WHOP_CREDENTIALS.APP_ID;
  env['WHOP_API_KEY'] = WHOP_CREDENTIALS.API_KEY;
  env['VITE_WHOP_AGENT_USER_ID'] = WHOP_CREDENTIALS.AGENT_USER_ID;
  env['VITE_WHOP_COMPANY_ID'] = WHOP_CREDENTIALS.COMPANY_ID;
  
  // Write updated .env file
  writeEnvFile(ENV_FILE_PATH, env);
  
  log('✅ Whop environment setup complete!', 'success');
  log('', 'info');
  log('📋 Whop Configuration:', 'info');
  log(`   App ID: ${WHOP_CREDENTIALS.APP_ID}`, 'info');
  log(`   Agent User ID: ${WHOP_CREDENTIALS.AGENT_USER_ID}`, 'info');
  log(`   Company ID: ${WHOP_CREDENTIALS.COMPANY_ID}`, 'info');
  log(`   API Key: ${WHOP_CREDENTIALS.API_KEY.substring(0, 10)}...`, 'info');
  log('', 'info');
  log('🔄 Please restart your development server to apply changes.', 'warning');
  log('', 'info');
  log('🧪 To test the integration:', 'info');
  log('   1. Start the dev server: npm run dev', 'info');
  log('   2. Open browser console', 'info');
  log('   3. Run: whopDev.enableWhopDevMode()', 'info');
  log('   4. Navigate to: /experiences/exp_test_123', 'info');
}

function validateWhopSetup() {
  log('🔍 Validating Whop setup...', 'info');
  
  if (!fs.existsSync(ENV_FILE_PATH)) {
    log('❌ .env file not found', 'error');
    return false;
  }
  
  const env = readEnvFile(ENV_FILE_PATH);
  const requiredVars = [
    'VITE_WHOP_APP_ID',
    'WHOP_API_KEY',
    'VITE_WHOP_AGENT_USER_ID',
    'VITE_WHOP_COMPANY_ID'
  ];
  
  let isValid = true;
  
  requiredVars.forEach(varName => {
    if (!env[varName] || env[varName].trim() === '') {
      log(`❌ Missing or empty: ${varName}`, 'error');
      isValid = false;
    } else {
      log(`✅ ${varName}: Set`, 'success');
    }
  });
  
  if (isValid) {
    log('✅ Whop setup is valid!', 'success');
  } else {
    log('❌ Whop setup has issues. Run setup again.', 'error');
  }
  
  return isValid;
}

function showHelp() {
  log('🛠️  Whop Environment Setup Script', 'info');
  log('', 'info');
  log('Usage:', 'info');
  log('  node scripts/setup-whop-env.js [command]', 'info');
  log('', 'info');
  log('Commands:', 'info');
  log('  setup     Set up Whop environment variables (default)', 'info');
  log('  validate  Validate existing Whop setup', 'info');
  log('  help      Show this help message', 'info');
  log('', 'info');
  log('Examples:', 'info');
  log('  node scripts/setup-whop-env.js', 'info');
  log('  node scripts/setup-whop-env.js setup', 'info');
  log('  node scripts/setup-whop-env.js validate', 'info');
}

// Main execution
const command = process.argv[2] || 'setup';

switch (command) {
  case 'setup':
    setupWhopEnvironment();
    break;
  case 'validate':
    validateWhopSetup();
    break;
  case 'help':
  case '--help':
  case '-h':
    showHelp();
    break;
  default:
    log(`❌ Unknown command: ${command}`, 'error');
    log('Run with "help" to see available commands.', 'info');
    process.exit(1);
}
