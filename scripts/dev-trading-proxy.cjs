#!/usr/bin/env node

/**
 * Development proxy server for trading mode
 * Automatically redirects port 3000 to localhost:3000/trading
 */

const http = require('http');
const url = require('url');
const fs = require('fs');
const path = require('path');

const PORT = 3000;
const TARGET_PORT = 8080; // Vite dev server port
const TRADING_PATH = '/trading';

console.log('🚀 Starting Trading Mode Development Proxy...');
console.log(`📍 Proxy: http://localhost:${PORT}`);
console.log(`🎯 Target: http://localhost:${TARGET_PORT}`);
console.log(`🔄 Auto-redirect: / → ${TRADING_PATH}`);

// Create HTTP server
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);

  // Special route to show trading mode info
  if (parsedUrl.pathname === '/trading-mode-info') {
    const infoPath = path.join(__dirname, 'trading-mode-info.html');
    fs.readFile(infoPath, 'utf8', (err, data) => {
      if (err) {
        res.writeHead(500, { 'Content-Type': 'text/plain' });
        res.end('Error loading info page');
        return;
      }
      res.writeHead(200, { 'Content-Type': 'text/html' });
      res.end(data);
    });
    return;
  }

  // If accessing root path, redirect to /trading
  if (parsedUrl.pathname === '/' || parsedUrl.pathname === '') {
    console.log(`🔄 Redirecting ${parsedUrl.pathname} → ${TRADING_PATH}`);
    res.writeHead(302, { 'Location': TRADING_PATH });
    res.end();
    return;
  }

  // For all other paths, proxy to the Vite dev server
  console.log(`📡 Proxying: ${req.method} ${parsedUrl.pathname} → http://localhost:${TARGET_PORT}${parsedUrl.pathname}`);

  // Create proxy request
  const proxyReq = http.request({
    hostname: 'localhost',
    port: TARGET_PORT,
    path: req.url,
    method: req.method,
    headers: req.headers
  }, (proxyRes) => {
    // Copy response headers
    res.writeHead(proxyRes.statusCode, proxyRes.headers);
    // Pipe response data
    proxyRes.pipe(res);
  });

  // Handle proxy request errors
  proxyReq.on('error', (err) => {
    console.error('❌ Proxy error:', err.message);
    if (!res.headersSent) {
      res.writeHead(500, { 'Content-Type': 'text/plain' });
      res.end('Proxy error: ' + err.message);
    }
  });

  // Pipe request data to proxy
  req.pipe(proxyReq);
});

// Start the proxy server
server.listen(PORT, () => {
  console.log('✅ Trading Mode Development Proxy started successfully!');
  console.log('');
  console.log('📋 Usage:');
  console.log(`   • Open http://localhost:${PORT} (auto-redirects to /trading)`);
  console.log(`   • Direct access: http://localhost:${PORT}/trading`);
  console.log(`   • Vite dev server: http://localhost:${TARGET_PORT}`);
  console.log('');
  console.log('🔧 To disable trading mode:');
  console.log('   npm run dev (regular development)');
  console.log('');
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down Trading Mode Development Proxy...');
  server.close(() => {
    process.exit(0);
  });
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down Trading Mode Development Proxy...');
  server.close(() => {
    process.exit(0);
  });
});
