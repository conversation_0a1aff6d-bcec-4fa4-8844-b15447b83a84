#!/usr/bin/env node

/**
 * <PERSON>ript to sync trade-sensei-chat to the separate trade-app repository
 * FROM: /Users/<USER>/Documents/GitHub/trade-sensei-chat
 * TO: /Users/<USER>/Documents/GitHub/trade-app
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Paths
const TRADE_SENSEI_PATH = '/Users/<USER>/Documents/GitHub/trade-sensei-chat';
const TRADE_APP_PATH = '/Users/<USER>/Documents/GitHub/trade-app';

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function runCommand(command, options = {}) {
  try {
    execSync(command, { stdio: 'inherit', ...options });
  } catch (error) {
    log(`❌ Command failed: ${command}`, 'red');
    throw error;
  }
}

function checkPaths() {
  log('🔍 Checking paths...', 'blue');
  
  if (!fs.existsSync(TRADE_SENSEI_PATH)) {
    log(`❌ Trade Sensei path not found: ${TRADE_SENSEI_PATH}`, 'red');
    process.exit(1);
  }
  
  if (!fs.existsSync(TRADE_APP_PATH)) {
    log(`❌ Trade App repository not found: ${TRADE_APP_PATH}`, 'red');
    log('Please clone the trade-app repository first:', 'yellow');
    log(`git clone <trade-app-repo-url> ${TRADE_APP_PATH}`, 'yellow');
    process.exit(1);
  }
  
  log('✅ Both repositories exist', 'green');
}

function ensureTradeRouteIsDefault() {
  log('🔄 Ensuring /trade route is the default route in trade-app only...', 'blue');

  const appTsxPath = path.join(TRADE_APP_PATH, 'src', 'App.tsx');

  if (fs.existsSync(appTsxPath)) {
    let appContent = fs.readFileSync(appTsxPath, 'utf8');

    // Ensure the default route redirects to /trade ONLY in the trade-app
    if (!appContent.includes('Navigate to="/trade"')) {
      // Replace the default route to redirect to /trade
      appContent = appContent.replace(
        /Route path="\/" element={[^}]+}/g,
        'Route path="/" element={<Navigate to="/trade" replace />}'
      );

      // Also replace catch-all route
      appContent = appContent.replace(
        /Route path="\*" element={[^}]+}/g,
        'Route path="*" element={<Navigate to="/trade" replace />}'
      );

      fs.writeFileSync(appTsxPath, appContent);
      log('✅ Updated trade-app routing to make /trade the default route', 'green');
    } else {
      log('✅ /trade route is already set as default in trade-app', 'green');
    }
  } else {
    log('⚠️  Trade-app App.tsx not found, routing may need manual adjustment', 'yellow');
  }

  // DO NOT modify the OSIS app routing - it should redirect to /home
  log('ℹ️  OSIS app routing left unchanged (should redirect to /home)', 'blue');
}

function syncRepositories() {
  log('🔄 Syncing trade-sensei-chat to trade-app repository...', 'blue');
  
  // Create backup of trade-app
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupPath = `${TRADE_APP_PATH}-backup-${timestamp}`;
  log(`📦 Creating backup at ${backupPath}...`, 'blue');
  runCommand(`cp -r "${TRADE_APP_PATH}" "${backupPath}"`);
  
  // Directories to exclude from sync
  const excludeDirs = [
    'node_modules',
    '.git',
    'dist',
    '.next',
    'trading-app',
    'trading-app-backup',
    'whop-intermediary-server',
    'whop-app-template'
  ];
  
  const excludeFiles = [
    '.DS_Store',
    'Thumbs.db'
  ];
  
  // Remove old content (except .git and other important files)
  log('🗑️  Removing old content from trade-app (keeping .git)...', 'blue');
  const items = fs.readdirSync(TRADE_APP_PATH);
  for (const item of items) {
    if (item === '.git' || item === '.gitignore') continue; // Keep git files
    const itemPath = path.join(TRADE_APP_PATH, item);
    runCommand(`rm -rf "${itemPath}"`);
  }
  
  // Copy new content
  log('📋 Copying trade-sensei content to trade-app...', 'blue');
  const sourceItems = fs.readdirSync(TRADE_SENSEI_PATH);
  
  for (const item of sourceItems) {
    if (excludeDirs.includes(item) || excludeFiles.includes(item)) continue;
    if (item.startsWith('.') && item !== '.env.example') continue; // Skip hidden files except .env.example
    
    const sourcePath = path.join(TRADE_SENSEI_PATH, item);
    const destPath = path.join(TRADE_APP_PATH, item);
    
    try {
      if (fs.statSync(sourcePath).isDirectory()) {
        runCommand(`cp -r "${sourcePath}" "${destPath}"`);
      } else {
        runCommand(`cp "${sourcePath}" "${destPath}"`);
      }
    } catch (error) {
      log(`⚠️  Warning: Could not copy ${item}: ${error.message}`, 'yellow');
    }
  }
  
  // Update package.json for trade-app
  const packageJsonPath = path.join(TRADE_APP_PATH, 'package.json');
  if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    packageJson.name = 'trade-app';
    packageJson.description = 'Trade Sensei Trading Application - Synced from trade-sensei-chat';
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    log('✅ Updated trade-app package.json', 'green');
  }
  
  ensureTradeRouteIsDefault();
  
  log('✅ Sync completed successfully!', 'green');
  log(`📦 Backup created at: ${backupPath}`, 'blue');
}

function main() {
  try {
    log('🚀 Starting sync from trade-sensei-chat to trade-app repository...', 'blue');
    log(`FROM: ${TRADE_SENSEI_PATH}`, 'blue');
    log(`TO: ${TRADE_APP_PATH}`, 'blue');
    log('', 'reset');
    
    checkPaths();
    syncRepositories();
    
    log('', 'reset');
    log('🎉 Sync completed successfully!', 'green');
    log('', 'reset');
    log('📋 Next steps:', 'blue');
    log(`1. cd ${TRADE_APP_PATH}`, 'yellow');
    log('2. npm install --legacy-peer-deps', 'yellow');
    log('3. Test: npm run dev', 'yellow');
    log('4. Commit and push changes to trade-app repository', 'yellow');
    log('', 'reset');
    log('💡 The /trade route is configured as the default/home page', 'green');
    
  } catch (error) {
    log('❌ Sync failed:', 'red');
    console.error(error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { main };
