#!/usr/bin/env node

/**
 * Script to check Whop environment variables and configuration
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m',    // Cyan
    success: '\x1b[32m', // Green
    warning: '\x1b[33m', // Yellow
    error: '\x1b[31m',   // Red
    reset: '\x1b[0m'     // Reset
  };
  
  console.log(`${colors[type]}${message}${colors.reset}`);
}

function checkEnvFile() {
  const envPath = path.join(__dirname, '..', '.env');
  
  if (!fs.existsSync(envPath)) {
    log('❌ .env file not found', 'error');
    return null;
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const envVars = {};
  
  envContent.split('\n').forEach(line => {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, ...valueParts] = trimmed.split('=');
      if (key && valueParts.length > 0) {
        envVars[key.trim()] = valueParts.join('=').trim();
      }
    }
  });

  return envVars;
}

function main() {
  log('🔍 Checking Whop environment configuration...', 'info');
  log('', 'info');

  const envVars = checkEnvFile();
  if (!envVars) {
    log('Please create a .env file with your Whop configuration.', 'error');
    process.exit(1);
  }

  // Required Whop variables
  const requiredVars = {
    'VITE_WHOP_APP_ID': 'Whop App ID (public)',
    'WHOP_API_KEY': 'Whop API Key (private)',
    'VITE_WHOP_AGENT_USER_ID': 'Whop Agent User ID (public)',
    'VITE_WHOP_COMPANY_ID': 'Whop Company ID (public)',
    'VITE_SUPABASE_URL': 'Supabase URL (public)',
    'VITE_SUPABASE_ANON_KEY': 'Supabase Anon Key (public)'
  };

  let allPresent = true;
  let hasWhopVars = false;

  log('📋 Environment Variables Check:', 'info');
  log('', 'info');

  for (const [varName, description] of Object.entries(requiredVars)) {
    const value = envVars[varName];
    const isPresent = value && value.trim() !== '';
    
    if (varName.includes('WHOP') && isPresent) {
      hasWhopVars = true;
    }
    
    if (isPresent) {
      log(`✅ ${varName}: ${description}`, 'success');
      // Show partial value for verification (first 8 chars + ...)
      if (value.length > 8) {
        log(`   Value: ${value.substring(0, 8)}...`, 'info');
      } else {
        log(`   Value: ${value}`, 'info');
      }
    } else {
      log(`❌ ${varName}: ${description} - MISSING`, 'error');
      allPresent = false;
    }
    log('', 'info');
  }

  // Check for Whop-specific configuration
  if (!hasWhopVars) {
    log('⚠️ No Whop variables found. This might be a regular (non-Whop) deployment.', 'warning');
  }

  // Summary
  log('📊 Summary:', 'info');
  if (allPresent) {
    log('✅ All required environment variables are present', 'success');
  } else {
    log('❌ Some required environment variables are missing', 'error');
  }

  if (hasWhopVars) {
    log('✅ Whop integration variables found', 'success');
    log('', 'info');
    log('🔧 Next steps for Whop integration:', 'info');
    log('1. Make sure you are logged into Supabase CLI: supabase login', 'info');
    log('2. Deploy the Whop distribution function: npm run deploy:whop-distribution', 'info');
    log('3. Test the integration in your application', 'info');
  } else {
    log('⚠️ No Whop integration variables found', 'warning');
    log('', 'info');
    log('🔧 To set up Whop integration:', 'info');
    log('1. Run: npm run setup:whop', 'info');
    log('2. Follow the prompts to configure your Whop app', 'info');
    log('3. Deploy the functions: npm run deploy:whop-distribution', 'info');
  }

  log('', 'info');
  log('📖 For more information, see WHOP_AGENT_DISTRIBUTION.md', 'info');
}

main();
