#!/usr/bin/env node

/**
 * This script tests the TradingView Poster edge function locally.
 */

import { execSync } from 'child_process';

try {
  // Check if the Supabase CLI is installed
  try {
    execSync('supabase --version', { stdio: 'inherit' });
  } catch (error) {
    throw new Error('Supabase CLI is not installed. Please install it using: npm install -g supabase');
  }
  
  // Start the function locally
  console.log('Starting TradingView Poster edge function locally...');
  console.log('Press Ctrl+C to stop the function.');
  
  // Serve the function locally
  execSync('supabase functions serve tradingview-poster', { stdio: 'inherit' });
} catch (error) {
  console.error('Error testing TradingView Poster edge function:', error);
  process.exit(1);
}
