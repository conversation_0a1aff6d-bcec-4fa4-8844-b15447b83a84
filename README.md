# Trade App

A professional trading platform with real-time market data, advanced charting, and paper trading capabilities.

## 🚀 Features

- **Real-time Trading Interface**: Professional-grade trading platform with live market data
- **Paper Trading System**: Risk-free trading simulation with 100% accuracy
- **Advanced Charting**: TradingView Lightweight Charts with technical indicators
- **Whop Integration**: Seamless integration with Whop for monetization and user management
- **Responsive Design**: Works perfectly on desktop and mobile devices
- **Real-time Market Data**: Integrated with Polygon API for live stock data

## 🎯 Default Route

This app is configured to automatically redirect users to the `/trade` route, providing immediate access to the trading interface.

## 🛠️ Tech Stack

- **Frontend**: React 18, TypeScript, Vite
- **UI Components**: shadcn/ui, Radix UI, Tailwind CSS
- **Charts**: TradingView Lightweight Charts, ECharts
- **Authentication**: Supabase Auth + Whop SDK
- **Database**: Supabase (PostgreSQL)
- **Deployment**: Vercel
- **APIs**: Polygon (market data), Supabase Functions

## 🚀 Live Demo

Visit the live application: [Trade App](https://osis-gh5b669op-osis-team.vercel.app)

## 📦 Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd trade-app
```

2. Install dependencies:
```bash
npm install --legacy-peer-deps
```

3. Set up environment variables:
```bash
cp .env.example .env
# Fill in your environment variables
```

4. Start the development server:
```bash
npm run dev
```

## 🔧 Environment Variables

Create a `.env` file with the following variables:

```env
# Supabase
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Whop Integration
VITE_WHOP_APP_ID=your_whop_app_id
VITE_WHOP_TRADING_APP_ID=your_whop_trading_app_id
WHOP_API_KEY=your_whop_api_key

# Market Data
VITE_POLYGON_API_KEY=your_polygon_api_key

# Other APIs
VITE_GROWI_PUBLIC_ID=your_growi_id (optional)
```

## 🏗️ Build & Deploy

### Build for Production
```bash
npm run build
```

### Deploy to Vercel
```bash
vercel --prod
```

## 📱 Usage

1. **Access Trading Interface**: The app automatically redirects to `/trade`
2. **Paper Trading**: Start with virtual money to practice trading
3. **Real-time Charts**: View live market data and technical indicators
4. **Place Orders**: Execute buy/sell orders in the paper trading environment
5. **Track Performance**: Monitor your trading performance and portfolio

## 🔐 Authentication

The app supports multiple authentication methods:
- **Whop Users**: Automatic authentication via Whop iframe
- **Regular Users**: Email/password authentication via Supabase

## 🎨 UI/UX

- **Dark Theme**: Professional dark theme optimized for trading
- **Responsive Design**: Works on all screen sizes
- **Keyboard Shortcuts**: Quick access to trading functions
- **Real-time Updates**: Live price updates and portfolio changes

## 📊 Trading Features

- **Market Orders**: Execute trades at current market price
- **Limit Orders**: Set specific price targets for trades
- **Stop Orders**: Risk management with stop-loss orders
- **Portfolio Tracking**: Real-time portfolio value and P&L
- **Order History**: Complete history of all trades
- **Position Management**: View and manage open positions

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📄 License

This project is proprietary software. All rights reserved.

## 🆘 Support

For support and questions, please contact the development team.
