<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Fix Onboarding</title>
  <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    button {
      background-color: #3b82f6;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin-top: 20px;
    }
    button:hover {
      background-color: #2563eb;
    }
    .result {
      margin-top: 20px;
      padding: 15px;
      border-radius: 4px;
    }
    .success {
      background-color: #d1fae5;
      border: 1px solid #10b981;
    }
    .error {
      background-color: #fee2e2;
      border: 1px solid #ef4444;
    }
    pre {
      background-color: #f3f4f6;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
    }
  </style>
</head>
<body>
  <h1>Fix Onboarding Issue</h1>
  <p>This page will fix the issue where you're still seeing the onboarding screen despite having paid.</p>
  <p>Click the button below to update your profile and hide the onboarding screen:</p>
  
  <button id="fixButton">Fix Onboarding</button>
  
  <div id="result" class="result" style="display: none;"></div>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const fixButton = document.getElementById('fixButton');
      const resultDiv = document.getElementById('result');
      
      fixButton.addEventListener('click', async function() {
        try {
          fixButton.disabled = true;
          fixButton.textContent = 'Fixing...';
          
          // Get Supabase URL and key from localStorage (set by the main app)
          const supabaseUrl = localStorage.getItem('supabase.url');
          const supabaseKey = localStorage.getItem('supabase.key');
          
          if (!supabaseUrl || !supabaseKey) {
            throw new Error('Supabase configuration not found. Please log in to the main app first.');
          }
          
          // Create Supabase client
          const supabase = supabase.createClient(supabaseUrl, supabaseKey);
          
          // Get current user
          const { data: { user }, error: userError } = await supabase.auth.getUser();
          
          if (userError) throw userError;
          if (!user) throw new Error('No user is currently logged in. Please log in first.');
          
          // Update profile
          const { error: updateError } = await supabase
            .from('profiles')
            .update({
              has_seen_onboarding: true,
              updated_at: new Date().toISOString()
            })
            .eq('id', user.id);
          
          if (updateError) throw updateError;
          
          // Show success message
          resultDiv.className = 'result success';
          resultDiv.innerHTML = `
            <h3>Success!</h3>
            <p>Your profile has been updated successfully. The onboarding screen should no longer appear.</p>
            <p>Please <a href="/">return to the main app</a> and refresh the page.</p>
          `;
          resultDiv.style.display = 'block';
        } catch (error) {
          // Show error message
          resultDiv.className = 'result error';
          resultDiv.innerHTML = `
            <h3>Error</h3>
            <p>An error occurred while trying to fix the onboarding issue:</p>
            <pre>${error.message || JSON.stringify(error)}</pre>
            <p>Please try again or contact support.</p>
          `;
          resultDiv.style.display = 'block';
        } finally {
          fixButton.disabled = false;
          fixButton.textContent = 'Fix Onboarding';
        }
      });
    });
  </script>
</body>
</html>
