/**
 * Service Worker for Trade Sensei Chat
 * Handles caching, preloading, and offline functionality
 */

const CACHE_NAME = 'trade-sensei-v1';
const STATIC_CACHE = 'trade-sensei-static-v1';
const API_CACHE = 'trade-sensei-api-v1';
const IMAGE_CACHE = 'trade-sensei-images-v1';

// Resources to cache immediately
const STATIC_RESOURCES = [
  '/',
  '/index.html',
  '/manifest.json',
  '/favicon.ico',
  '/images/logo.svg',
  '/images/placeholder.svg',
];

// API endpoints to cache
const API_ENDPOINTS = [
  '/api/user',
  '/api/watchlist',
  '/api/portfolio',
];

// Install event - cache static resources
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...');
  
  event.waitUntil(
    Promise.all([
      // Cache static resources
      caches.open(STATIC_CACHE).then((cache) => {
        console.log('Caching static resources');
        return cache.addAll(STATIC_RESOURCES);
      }),
      
      // Skip waiting to activate immediately
      self.skipWaiting()
    ])
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...');
  
  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && 
                cacheName !== API_CACHE && 
                cacheName !== IMAGE_CACHE) {
              console.log('Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      
      // Take control of all clients
      self.clients.claim()
    ])
  );
});

// Fetch event - handle all network requests
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Handle different types of requests
  if (request.method === 'GET') {
    if (isStaticResource(url)) {
      event.respondWith(handleStaticResource(request));
    } else if (isApiRequest(url)) {
      event.respondWith(handleApiRequest(request));
    } else if (isImageRequest(url)) {
      event.respondWith(handleImageRequest(request));
    } else {
      event.respondWith(handleOtherRequest(request));
    }
  }
});

// Handle static resources (HTML, CSS, JS)
async function handleStaticResource(request) {
  try {
    // Try cache first
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Fetch from network and cache
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(STATIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.error('Static resource fetch failed:', error);
    
    // Return cached version if available
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return offline page for HTML requests
    if (request.destination === 'document') {
      return new Response(getOfflineHTML(), {
        headers: { 'Content-Type': 'text/html' }
      });
    }
    
    throw error;
  }
}

// Handle API requests with stale-while-revalidate strategy
async function handleApiRequest(request) {
  try {
    const cache = await caches.open(API_CACHE);
    const cachedResponse = await cache.match(request);
    
    // Start network request
    const networkPromise = fetch(request).then(async (response) => {
      if (response.ok) {
        // Cache successful responses
        cache.put(request, response.clone());
      }
      return response;
    });
    
    // Return cached response immediately if available
    if (cachedResponse) {
      // Update cache in background
      networkPromise.catch(() => {
        console.warn('Background API update failed for:', request.url);
      });
      
      return cachedResponse;
    }
    
    // Wait for network if no cache
    return await networkPromise;
  } catch (error) {
    console.error('API request failed:', error);
    
    // Try to return cached version
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return error response
    return new Response(JSON.stringify({ error: 'Network unavailable' }), {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Handle image requests
async function handleImageRequest(request) {
  try {
    const cache = await caches.open(IMAGE_CACHE);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.error('Image request failed:', error);
    
    // Return placeholder image
    return new Response(getPlaceholderImage(), {
      headers: { 'Content-Type': 'image/svg+xml' }
    });
  }
}

// Handle other requests
async function handleOtherRequest(request) {
  try {
    return await fetch(request);
  } catch (error) {
    console.error('Request failed:', error);
    throw error;
  }
}

// Utility functions
function isStaticResource(url) {
  return url.pathname.match(/\.(js|css|html|woff2?|ttf|eot)$/i) ||
         url.pathname === '/' ||
         url.pathname.startsWith('/assets/');
}

function isApiRequest(url) {
  return url.pathname.startsWith('/api/') ||
         url.pathname.startsWith('/functions/') ||
         url.hostname.includes('supabase');
}

function isImageRequest(url) {
  return url.pathname.match(/\.(png|jpg|jpeg|gif|svg|webp|ico)$/i);
}

function getOfflineHTML() {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Trade Sensei - Offline</title>
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          background: #0A0A0A;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 100vh;
          margin: 0;
          text-align: center;
        }
        .offline-container {
          max-width: 400px;
          padding: 2rem;
        }
        .offline-title {
          font-size: 2rem;
          margin-bottom: 1rem;
        }
        .offline-message {
          margin-bottom: 2rem;
          opacity: 0.8;
        }
        .retry-button {
          background: #3B82F6;
          color: white;
          border: none;
          padding: 0.75rem 1.5rem;
          border-radius: 0.5rem;
          cursor: pointer;
          font-size: 1rem;
        }
        .retry-button:hover {
          background: #2563EB;
        }
      </style>
    </head>
    <body>
      <div class="offline-container">
        <h1 class="offline-title">You're Offline</h1>
        <p class="offline-message">
          Trade Sensei is currently unavailable. Please check your internet connection and try again.
        </p>
        <button class="retry-button" onclick="window.location.reload()">
          Try Again
        </button>
      </div>
    </body>
    </html>
  `;
}

function getPlaceholderImage() {
  return `
    <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
      <rect width="200" height="200" fill="#1F2937"/>
      <text x="100" y="100" text-anchor="middle" dy=".3em" fill="#9CA3AF" font-family="Arial, sans-serif" font-size="14">
        Image Unavailable
      </text>
    </svg>
  `;
}

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    event.waitUntil(handleBackgroundSync());
  }
});

async function handleBackgroundSync() {
  console.log('Performing background sync...');
  
  try {
    // Sync any pending data
    const pendingData = await getStoredPendingData();
    
    for (const data of pendingData) {
      try {
        await syncData(data);
        await removePendingData(data.id);
      } catch (error) {
        console.error('Failed to sync data:', error);
      }
    }
  } catch (error) {
    console.error('Background sync failed:', error);
  }
}

// Placeholder functions for data sync
async function getStoredPendingData() {
  // This would retrieve pending data from IndexedDB
  return [];
}

async function syncData(data) {
  // This would sync data to the server
  console.log('Syncing data:', data);
}

async function removePendingData(id) {
  // This would remove synced data from IndexedDB
  console.log('Removing synced data:', id);
}

// Message handling for communication with main thread
self.addEventListener('message', (event) => {
  const { type, payload } = event.data;
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting();
      break;
      
    case 'CACHE_URLS':
      cacheUrls(payload.urls);
      break;
      
    case 'CLEAR_CACHE':
      clearCache(payload.cacheName);
      break;
      
    default:
      console.log('Unknown message type:', type);
  }
});

async function cacheUrls(urls) {
  try {
    const cache = await caches.open(STATIC_CACHE);
    await cache.addAll(urls);
    console.log('URLs cached successfully');
  } catch (error) {
    console.error('Failed to cache URLs:', error);
  }
}

async function clearCache(cacheName) {
  try {
    await caches.delete(cacheName || CACHE_NAME);
    console.log('Cache cleared successfully');
  } catch (error) {
    console.error('Failed to clear cache:', error);
  }
}
