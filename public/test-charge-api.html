<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Charge API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Charge API</h1>
        <p>This page tests the charge API endpoint to ensure it's working correctly.</p>
        
        <div>
            <button onclick="testChargeAPI()">Test Charge API</button>
            <button onclick="testDirectIntermediaryServer()">Test Direct Intermediary Server</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>
        
        <div id="log" class="log"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        async function testChargeAPI() {
            log('🧪 Testing charge API via whopIntermediaryClient...', 'info');
            
            try {
                // Import the whop intermediary client
                const { whopIntermediaryClient } = await import('/src/lib/whopIntermediaryClient.ts');
                
                log('📡 Creating charge via whop-intermediary-server...', 'info');
                const chargeResponse = await whopIntermediaryClient.createCharge(
                    1000, // $10.00 test charge (in cents)
                    'usd',
                    'Test Payment - Manual Test'
                );

                log('📡 Charge creation response received', 'info');
                log(JSON.stringify(chargeResponse, null, 2), 'info');

                if (!chargeResponse.success) {
                    throw new Error(chargeResponse.error || 'Failed to create charge');
                }

                if (!chargeResponse.data?.inAppPurchase) {
                    throw new Error('No inAppPurchase data in response');
                }

                log('✅ Charge created successfully!', 'success');
                log('InAppPurchase data: ' + JSON.stringify(chargeResponse.data.inAppPurchase, null, 2), 'success');

            } catch (error) {
                log('❌ Charge API test failed:', 'error');
                log(error.message, 'error');
                if (error.stack) {
                    log(error.stack, 'error');
                }
            }
        }

        async function testDirectIntermediaryServer() {
            log('🧪 Testing direct call to intermediary server...', 'info');
            
            try {
                const response = await fetch('http://localhost:3001/api/charge', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        userId: 'user_6PxIxIy5wLpLA',
                        experienceId: 'exp_test'
                    })
                });

                log(`Response status: ${response.status} ${response.statusText}`, 'info');
                
                const responseText = await response.text();
                log('Response body:', 'info');
                log(responseText, 'info');

                if (response.ok) {
                    log('✅ Direct intermediary server test successful!', 'success');
                } else {
                    log('❌ Direct intermediary server test failed', 'error');
                }

            } catch (error) {
                log('❌ Direct intermediary server test failed:', 'error');
                log(error.message, 'error');
            }
        }

        // Auto-run test on page load
        window.addEventListener('load', () => {
            log('🚀 Test page loaded. Click buttons to test the charge API.', 'info');
        });
    </script>
</body>
</html>
