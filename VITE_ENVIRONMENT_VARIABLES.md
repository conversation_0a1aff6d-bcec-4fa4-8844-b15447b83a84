# Vite Environment Variables Guide

## Overview

This guide explains how to use Vite environment variables in the Trade Sensei frontend application. All environment variables are centralized through `src/config/env.ts` for easy management and type safety.

## Important Rules

### 1. **VITE_ Prefix Required**
Only environment variables prefixed with `VITE_` are accessible on the frontend:

```bash
# ✅ Accessible on frontend
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_API_KEY=your-api-key

# ❌ NOT accessible on frontend (server-side only)
SUPABASE_SERVICE_KEY=your-service-key
API_SECRET=your-secret
```

### 2. **Security Considerations**
- `VITE_` variables are **publicly accessible** in the browser
- Never put secrets, private keys, or sensitive data in `VITE_` variables
- Use server-side variables for sensitive data

## Current Environment Variables

### Supabase Configuration
```bash
VITE_SUPABASE_URL=https://pajqstbgncpbpcaffbpm.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Whop Configuration
```bash
VITE_WHOP_INTERMEDIARY_URL=https://whop-intermediary-server.vercel.app
VITE_WHOP_APP_ID=app_VFK6Os0L6NKH0i
VITE_TRADING_WHOP_APP_ID=app_zSMyjWYnc0Et3T
VITE_WHOP_COMPANY_ID=biz_OGyv6Pz0Le35Fa
```

### API Keys (Frontend Accessible)
```bash
VITE_POLYGON_API_KEY=JAWMu4zOUA4W7HrLCOx2GU2Cpml4_Faz
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_51Qq1bADebmd1GpTv...
VITE_GEMINI_API_KEY=AIzaSyBNjBDWuD5bjZxngLmuN9pBM0NFNEVO8jU
VITE_ALPHA_VANTAGE_API_KEY=8WNEWZ28P94N3KZK
```

### PostHog Analytics
```bash
VITE_POSTHOG_KEY=phc_svkkrbxrYi4ckp5G1CmJuVUetTARRYLSFPd4Mbtw8Mz
VITE_POSTHOG_HOST=https://us.i.posthog.com
```

## Usage Examples

### 1. **Using Centralized Configuration (Recommended)**

```typescript
import { CONFIG, API_KEYS, SUPABASE_CONFIG } from '../config/env';

// Supabase client
const supabase = createClient(CONFIG.supabase.url, CONFIG.supabase.anonKey);

// API calls
const polygonData = await fetch(`https://api.polygon.io/v1/open-close/AAPL/2023-01-09?apikey=${CONFIG.apiKeys.polygon}`);

// Whop integration
const whopUrl = CONFIG.whop.intermediaryUrl;

// Environment checks
if (CONFIG.env.isDevelopment) {
  console.log('Development mode');
}
```

### 2. **Direct Access (Not Recommended)**

```typescript
// Direct access (works but not recommended)
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const apiKey = import.meta.env.VITE_POLYGON_API_KEY;
```

### 3. **Component Usage**

```typescript
import React from 'react';
import { CONFIG } from '../config/env';

const MyComponent: React.FC = () => {
  const handleApiCall = async () => {
    const response = await fetch(`https://api.example.com/data`, {
      headers: {
        'Authorization': `Bearer ${CONFIG.apiKeys.gemini}`
      }
    });
  };

  return (
    <div>
      <p>Environment: {CONFIG.env.mode}</p>
      <p>Supabase URL: {CONFIG.supabase.url}</p>
    </div>
  );
};
```

## Environment Validation

The app automatically validates required environment variables on startup:

```typescript
import { validateEnvironment } from './config/env';

// This runs automatically in App.tsx
validateEnvironment(); // Throws error if required variables are missing
```

## Adding New Environment Variables

### 1. **Add to .env file**
```bash
# Add both server and client versions if needed
NEW_API_KEY=your-secret-key
VITE_NEW_API_KEY=your-public-key
```

### 2. **Update src/config/env.ts**
```typescript
export const API_KEYS = {
  // ... existing keys
  newApi: import.meta.env.VITE_NEW_API_KEY,
} as const;
```

### 3. **Use in components**
```typescript
import { CONFIG } from '../config/env';

const apiKey = CONFIG.apiKeys.newApi;
```

## Environment-Specific Configuration

### Development vs Production
```typescript
import { CONFIG } from '../config/env';

const apiUrl = CONFIG.env.isDevelopment 
  ? 'http://localhost:3000/api'
  : 'https://api.tradesensei.com';
```

### Conditional Features
```typescript
// Enable debug features only in development
if (CONFIG.env.isDevelopment) {
  window.debugTools = {
    config: CONFIG,
    // ... other debug tools
  };
}
```

## Best Practices

### ✅ Do
- Use the centralized `CONFIG` object
- Validate environment variables on app startup
- Use TypeScript for type safety
- Group related variables together
- Document new variables in this file

### ❌ Don't
- Put secrets in `VITE_` variables
- Access `import.meta.env` directly in components
- Hardcode environment-specific values
- Forget to add new variables to the centralized config

## Troubleshooting

### Variable Not Accessible
```typescript
// ❌ This won't work
const secret = import.meta.env.SECRET_KEY; // undefined

// ✅ This works
const publicKey = import.meta.env.VITE_PUBLIC_KEY;
```

### Missing Variables
Check the browser console for validation errors:
```
❌ Missing required environment variables: VITE_SUPABASE_URL, VITE_STRIPE_PUBLISHABLE_KEY
```

### TypeScript Errors
Make sure to update the centralized config when adding new variables:
```typescript
// Add to src/config/env.ts
export const NEW_CONFIG = {
  newValue: import.meta.env.VITE_NEW_VALUE,
} as const;
```

## Files Updated

- ✅ `src/config/env.ts` - Centralized configuration
- ✅ `src/integrations/supabase/client.ts` - Uses centralized config
- ✅ `src/services/aiChartDataService.ts` - Uses centralized config
- ✅ `src/lib/whopIntermediaryClient.ts` - Uses centralized config
- ✅ `src/App.tsx` - Environment validation on startup
