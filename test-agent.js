// Simple test to run the agent and see detailed logs
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://pajqstbgncpbpcaffbpm.supabase.co';
const supabaseKey = process.env.SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

async function testAgent() {
  try {
    console.log('Testing agent with ID: 1e5717ce-5ede-4c13-91cf-d282c0984791');
    
    const { data, error } = await supabase.functions.invoke('agent-runner', {
      body: {
        agentId: '1e5717ce-5ede-4c13-91cf-d282c0984791',
        symbol: 'AAPL',
        saveRun: false // Don't save to database for testing
      }
    });

    if (error) {
      console.error('Error:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));
    } else {
      console.log('Success:', data);
      console.log('Result details:', JSON.stringify(data, null, 2));
    }
  } catch (err) {
    console.error('Caught error:', err);
    console.error('Error stack:', err.stack);
  }
}

testAgent();
