<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Whop API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>Whop API Test</h1>
    <p>This page tests the Whop API calls to help debug the production issue.</p>
    
    <div>
        <button onclick="testHealthCheck()">Test Health Check</button>
        <button onclick="testCurrentUser()">Test Current User</button>
        <button onclick="testProxyEndpoint()">Test Proxy Endpoint Directly</button>
        <button onclick="clearOutput()">Clear Output</button>
    </div>
    
    <div id="output" class="output"></div>

    <script>
        function log(message, type = 'info') {
            const output = document.getElementById('output');
            const timestamp = new Date().toISOString();
            const logEntry = `[${timestamp}] ${message}\n`;
            output.textContent += logEntry;
            output.scrollTop = output.scrollHeight;
            
            // Also log to console
            console.log(message);
            
            // Update styling based on type
            if (type === 'error') {
                output.className = 'output error';
            } else if (type === 'success') {
                output.className = 'output success';
            } else {
                output.className = 'output';
            }
        }

        function clearOutput() {
            document.getElementById('output').textContent = '';
            document.getElementById('output').className = 'output';
        }

        async function testHealthCheck() {
            log('🧪 Testing health check...');
            try {
                const response = await fetch('/api/whop-proxy?endpoint=health');
                const responseText = await response.text();
                
                log(`Response status: ${response.status}`);
                log(`Response headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}`);
                log(`Response text: ${responseText}`);
                
                if (response.ok) {
                    log('✅ Health check successful', 'success');
                } else {
                    log('❌ Health check failed', 'error');
                }
            } catch (error) {
                log(`❌ Health check error: ${error.message}`, 'error');
            }
        }

        async function testCurrentUser() {
            log('👤 Testing current user...');
            try {
                const response = await fetch('/api/whop-proxy?endpoint=user/current');
                const responseText = await response.text();
                
                log(`Response status: ${response.status}`);
                log(`Response headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}`);
                log(`Response text: ${responseText}`);
                
                if (response.ok) {
                    log('✅ Current user request successful', 'success');
                } else {
                    log('❌ Current user request failed', 'error');
                }
            } catch (error) {
                log(`❌ Current user error: ${error.message}`, 'error');
            }
        }

        async function testProxyEndpoint() {
            log('🔗 Testing proxy endpoint directly...');
            try {
                const response = await fetch('/api/whop-proxy');
                const responseText = await response.text();
                
                log(`Response status: ${response.status}`);
                log(`Response headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}`);
                log(`Response text: ${responseText}`);
                
                if (response.status === 400) {
                    log('✅ Proxy endpoint exists (expected 400 for missing endpoint param)', 'success');
                } else {
                    log('❌ Unexpected response from proxy endpoint', 'error');
                }
            } catch (error) {
                log(`❌ Proxy endpoint error: ${error.message}`, 'error');
            }
        }

        // Auto-run health check on page load
        window.addEventListener('load', () => {
            log('🚀 Page loaded, running initial tests...');
            setTimeout(testHealthCheck, 1000);
        });
    </script>
</body>
</html>
