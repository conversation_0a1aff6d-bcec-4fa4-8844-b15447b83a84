#!/bin/bash

# Whop Intermediary System Startup Script
echo "🚀 Starting Whop Intermediary System..."

# Function to check if a port is in use
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
        echo "⚠️  Port $1 is already in use"
        return 1
    else
        echo "✅ Port $1 is available"
        return 0
    fi
}

# Check required ports
echo "🔍 Checking required ports..."
check_port 3000 || echo "   (Intermediary server port)"
check_port 3001 || echo "   (Whop proxy port)"
check_port 5173 || echo "   (Main app port)"

echo ""
echo "📋 Starting services..."

# Start intermediary server in background
echo "1️⃣ Starting Whop Intermediary Server (port 3000)..."
cd whop-intermediary-server
npm run dev &
INTERMEDIARY_PID=$!
echo "   PID: $INTERMEDIARY_PID"

# Wait a moment for the server to start
sleep 3

# Go back to main directory
cd ..

# Start main app with Whop proxy
echo "2️⃣ Starting Main App with Whop Proxy (ports 3001 & 5173)..."
npm run start:whop &
MAIN_APP_PID=$!
echo "   PID: $MAIN_APP_PID"

# Wait for services to start
echo ""
echo "⏳ Waiting for services to start..."
sleep 5

# Test the system
echo ""
echo "🧪 Testing system health..."

# Test intermediary server
echo "Testing intermediary server..."
if curl -s http://localhost:3000/api/health > /dev/null; then
    echo "✅ Intermediary server is healthy"
else
    echo "❌ Intermediary server is not responding"
fi

# Test main app proxy
echo "Testing main app proxy..."
if curl -s "http://localhost:5173/api/whop-proxy?endpoint=health" > /dev/null; then
    echo "✅ Main app proxy is working"
else
    echo "❌ Main app proxy is not responding"
fi

echo ""
echo "🎉 System startup complete!"
echo ""
echo "📍 Access URLs:"
echo "   • Main App (direct):     http://localhost:5173"
echo "   • Main App (Whop proxy): http://localhost:3001"
echo "   • Test Page (direct):    http://localhost:5173/whop-test"
echo "   • Test Page (Whop):      http://localhost:3001/whop-test"
echo "   • Intermediary Server:   http://localhost:3000"
echo ""
echo "🛑 To stop the system:"
echo "   kill $INTERMEDIARY_PID $MAIN_APP_PID"
echo "   or press Ctrl+C in each terminal"
echo ""
echo "💡 For Whop user tokens, access through: http://localhost:3001"

# Keep script running to show logs
wait
