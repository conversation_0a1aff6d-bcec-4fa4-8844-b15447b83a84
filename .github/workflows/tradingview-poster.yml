name: <PERSON><PERSON><PERSON><PERSON> Poster

on:
  schedule:
    # Run every 21 minutes during market hours (9am-4pm ET, Monday-Friday)
    # This matches our internal timing exactly to avoid duplicates
    # 9am-4pm ET is approximately 13:00-20:00 UTC
    - cron: '*/21 13-20 * * 1-5'
  workflow_dispatch: # Allow manual triggering

jobs:
  post-to-tradingview:
    runs-on: ubuntu-latest
    steps:
      - name: Trigger TradingView Poster Edge Function
        run: |
          curl -X POST "${{ secrets.SUPABASE_URL }}/functions/v1/tradingview-poster" \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer ${{ secrets.SUPABASE_ANON_KEY }}" \
            -d '{}'
