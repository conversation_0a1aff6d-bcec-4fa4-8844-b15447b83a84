name: Sync Stripe Subscriptions

on:
  schedule:
    # Run every hour
    - cron: '0 * * * *'
  workflow_dispatch:  # Allow manual triggering

jobs:
  sync-subscriptions:
    runs-on: ubuntu-latest
    steps:
      - name: Sync Stripe Subscriptions
        run: |
          curl -X POST https://pajqstbgncpbpcaffbpm.supabase.co/functions/v1/sync-stripe-subscriptions \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}" \
            -d '{}'
