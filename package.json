{"name": "trade-app", "private": true, "version": "1.0.0", "type": "module", "engines": {"node": "20.x"}, "config": {"tradingMode": true}, "scripts": {"dev": "vite", "dev:trading": "node scripts/dev-trading-proxy.cjs", "dev:whop": "whop-proxy --standalone --upstreamPort=8080 --proxyPort=3000", "dev:vercel": "vercel dev --listen 3001", "dev:local": "vite", "start:whop": "concurrently \"npm run dev\" \"npm run dev:whop\"", "start:trading": "concurrently \"npm run dev\" \"npm run dev:trading\"", "trading:on": "npm config set react_shadcn_ts:tradingMode true", "trading:off": "npm config set react_shadcn_ts:tradingMode false", "trading:status": "npm config get react_shadcn_ts:tradingMode", "build": "node scripts/replace-env-vars.js && vite build", "build:dev": "node scripts/replace-env-vars.js && vite build --mode development", "build:analyze": "node scripts/replace-env-vars.js && vite build --mode production && npx vite-bundle-analyzer dist/stats.html", "build:fast": "node scripts/replace-env-vars.js && vite build --mode production --minify esbuild", "lint": "eslint .", "preview": "vite preview", "test": "node scripts/test-runner.js all", "test:ui": "vitest --ui", "test:coverage": "node scripts/test-runner.js --coverage", "test:watch": "vitest watch", "test:unit": "node scripts/test-runner.js unit", "test:integration": "node scripts/test-runner.js integration", "test:performance": "node scripts/test-runner.js performance", "test:blocks": "node scripts/test-runner.js blocks", "test:ui-components": "node scripts/test-runner.js ui", "test:benchmark": "node scripts/test-runner.js --benchmark", "test:validate": "node scripts/test-runner.js --validate", "test:bail": "node scripts/test-runner.js all --bail", "test:verbose": "node scripts/test-runner.js all --verbose", "test:ci": "node scripts/test-runner.js all --bail --coverage --reporter=json", "test:whop": "vitest run src/test/whop-integration.test.ts", "deploy:stripe-checkout": "node scripts/deploy-stripe-checkout-function.js", "deploy:gemini-api": "node scripts/deploy-gemini-api-function.js", "deploy:data-processor": "node scripts/deploy-data-processor-function.js", "deploy:api-processor": "node scripts/deploy-api-processor-function.js", "deploy:agent-runner": "node scripts/deploy-agent-runner-function.js", "deploy:all": "node scripts/deploy-all-functions.js", "deploy:cors-fix": "node scripts/deploy-cors-fix.js", "deploy:api-functions": "node scripts/deploy-api-functions.js", "deploy:whop": "node scripts/deploy-whop-function.js", "deploy:whop-distribution": "node scripts/deploy-whop-distribution-function.js", "update:supabase-env": "node scripts/update-supabase-env.js", "install:agent-builder-deps": "node scripts/install-agent-builder-deps.js", "setup:whop": "node scripts/setup-whop-env.js", "validate:whop": "node scripts/setup-whop-env.js validate", "check:whop-env": "node scripts/check-whop-env.js", "deploy:vercel": "node scripts/deploy-vercel.js", "sync:to-trade-app": "node scripts/sync-to-trade-app.cjs"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@stripe/stripe-js": "^5.10.0", "@supabase/supabase-js": "^2.49.8", "@tailwindcss/line-clamp": "^0.4.4", "@tanstack/react-query": "^5.56.2", "@types/axios": "^0.14.4", "@types/stripe": "^8.0.416", "@whop/api": "^0.0.29", "@whop/iframe": "^0.0.3", "axios": "^1.8.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "cors": "^2.8.5", "date-fns": "^3.6.0", "dom-to-image": "^2.6.0", "dompurify": "^3.2.4", "dotenv": "^16.4.7", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "embla-carousel-react": "^8.3.0", "framer-motion": "^12.15.0", "html2canvas": "^1.4.1", "input-otp": "^1.2.4", "lightweight-charts": "^5.0.7", "lucide-react": "^0.462.0", "marked": "^15.0.7", "next": "^15.2.4", "next-themes": "^0.3.0", "node-fetch": "^3.3.2", "posthog-js": "^1.246.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-icons": "^5.5.0", "react-markdown": "^9.0.3", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "reactflow": "^11.11.4", "recharts": "^2.15.3", "sonner": "^1.5.0", "stripe": "^17.7.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.1", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/cors": "^2.8.17", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/uuid": "^9.0.8", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/coverage-v8": "^1.3.1", "@vitest/ui": "^1.3.1", "@whop-apps/dev-proxy": "^0.0.1-canary.117", "autoprefixer": "^10.4.20", "concurrently": "^9.2.0", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "javascript-obfuscator": "^4.1.1", "jsdom": "^24.0.0", "lovable-tagger": "^1.1.3", "postcss": "^8.4.47", "supabase": "^2.20.12", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1", "vite-plugin-javascript-obfuscator": "^3.1.0", "vitest": "^1.3.1"}, "description": "Trade Sensei Trading Application - Synced from trade-sensei-chat"}