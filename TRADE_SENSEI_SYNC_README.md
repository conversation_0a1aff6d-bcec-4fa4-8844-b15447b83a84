# Trade Sensei Sync Scripts

This document explains how to use the scripts to keep the `trading-app` directory synchronized with the `trade-sensei-chat` repository while maintaining the `/trade` route as the main page.

## Overview

The goal is to completely replace the `trading-app` directory with the current `trade-sensei-chat` directory content, ensuring that:
1. The `/trade` route remains the default/home page
2. The repository stays up to date with changes made in `trade-sensei-chat`
3. Easy synchronization for future updates

## Scripts Available

### 1. `sync-with-trade-sensei.js` (Recommended)

This script completely replaces the `trading-app` directory with the current `trade-sensei-chat` content.

**Usage:**
```bash
npm run sync:trade-sensei
```

**What it does:**
- ✅ Creates backup of existing `trading-app` directory
- ✅ Removes current `trading-app` directory
- ✅ Copies all current trade-sensei content to `trading-app`
- ✅ Ensures `/trade` route is the default route
- ✅ Updates package.json with sync scripts
- ✅ Creates sync script inside `trading-app` for future updates
- ✅ Excludes unnecessary directories (node_modules, .git, etc.)

### 2. `replace-trading-app-with-trade-sensei.js` (Advanced)

This script allows you to replace `trading-app` with content from any source.

**Usage:**
```bash
npm run replace:trading-app [source-path]
```

**What it does:**
- Creates backup and removes `trading-app`
- Sets up `trade-sensei` directory (placeholder or from source)
- Creates sync scripts for external repositories

## Step-by-Step Usage

### Initial Setup

1. **Run the sync script:**
   ```bash
   npm run sync:trade-sensei
   ```

2. **Navigate to trading-app and install dependencies:**
   ```bash
   cd trading-app
   npm install
   ```

3. **Test the application:**
   ```bash
   npm run dev
   ```
   The app should automatically redirect to `/trade` as the home page.

### Future Updates

When you make changes to the main `trade-sensei-chat` repository and want to update the `trading-app`:

**Option 1: From the root directory**
```bash
npm run sync:trade-sensei
cd trading-app
npm install  # Only if new dependencies were added
```

**Option 2: From within trading-app directory**
```bash
cd trading-app
npm run sync:from-trade-sensei
npm install  # Only if new dependencies were added
```

## Directory Structure After Sync

```
trade-sensei-chat/
├── trading-app/                 # Complete copy of trade-sensei-chat
│   ├── src/
│   │   ├── App.tsx             # Routes configured with /trade as default
│   │   └── ...                 # All other source files
│   ├── package.json            # Updated with sync scripts
│   ├── sync-from-trade-sensei.js  # Local sync script
│   └── ...                     # All other project files
├── trading-app-backup/         # Backup of previous trading-app
├── scripts/
│   ├── sync-with-trade-sensei.js
│   └── replace-trading-app-with-trade-sensei.js
└── ...
```

## Key Features

### Automatic Route Configuration

The script ensures that the `/trade` route is the default route by:
- Checking `src/App.tsx` for existing route configuration
- Updating default route (`/`) to redirect to `/trade`
- Updating catch-all route (`*`) to redirect to `/trade`

### Smart File Exclusion

The sync process excludes:
- `node_modules/` (will be reinstalled)
- `.git/` (avoids nested git repositories)
- `dist/` (build artifacts)
- `trading-app/` (prevents recursion)
- `trading-app-backup/` (previous backups)
- Other unnecessary directories

### Backup Safety

- Always creates backup before replacing
- Backup stored in `trading-app-backup/`
- Can restore manually if needed

## Troubleshooting

### If sync fails:
1. Check that you're in the project root directory
2. Ensure `trading-app` directory exists
3. Check file permissions
4. Restore from backup if needed: `mv trading-app-backup trading-app`

### If routing doesn't work:
1. Check `trading-app/src/App.tsx`
2. Ensure routes redirect to `/trade`
3. Manually update if needed

### If dependencies are missing:
1. Run `npm install` in the `trading-app` directory
2. Check for any new dependencies in the main project

## Benefits

1. **Always Up-to-Date**: `trading-app` stays synchronized with latest changes
2. **Safe Updates**: Automatic backups prevent data loss
3. **Consistent Routing**: `/trade` always remains the main page
4. **Easy Deployment**: `trading-app` can be deployed independently
5. **No Manual Work**: Automated process reduces errors

## Deployment

After syncing, the `trading-app` directory is ready for deployment:

```bash
cd trading-app
npm run build
# Deploy using your preferred method (Vercel, Netlify, etc.)
```

The deployed app will automatically redirect users to the `/trade` route as the home page.
