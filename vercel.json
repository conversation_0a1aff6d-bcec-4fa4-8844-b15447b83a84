{"version": 2, "framework": "vite", "installCommand": "npm install --legacy-peer-deps --include=optional", "buildCommand": "npm run build", "outputDirectory": "dist", "build": {"env": {"NODE_VERSION": "20.x", "WHOP_INTERMEDIARY_URL": "https://whop-intermediary-server.vercel.app"}}, "env": {"WHOP_INTERMEDIARY_URL": "https://whop-intermediary-server.vercel.app"}, "routes": [{"src": "/api/whop-proxy", "dest": "/api/whop-proxy"}, {"src": "/api/whop/(.*)", "dest": "/api/whop/$1"}, {"src": "/assets/(.*)", "dest": "/assets/$1"}, {"src": "/(.*\\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot|json|txt|xml|pdf))", "dest": "/$1"}, {"src": "/(.*)", "dest": "/index.html"}]}