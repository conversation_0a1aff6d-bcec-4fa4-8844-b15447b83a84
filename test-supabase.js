// Simple script to test Supabase connection
const { createClient } = require('@supabase/supabase-js');

// Hardcoded values for testing
const supabaseUrl = 'https://pajqstbgncpbpcaffbpm.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBhanFzdGJnbmNwYnBjYWZmYnBtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzk3NzIyODMsImV4cCI6MjA1NTM0ODI4M30.8IyM7AaWMGoceTOMW1LJklWC27uTGY7xh02LgKSZw7U';

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Test function
async function testConnection() {
  console.log('Testing Supabase connection...');
  console.log('URL:', supabaseUrl);
  console.log('Key:', supabaseAnonKey);
  
  try {
    // Try to get the current time from Supabase
    const { data, error } = await supabase.from('profiles').select('count(*)', { count: 'exact', head: true });
    
    if (error) {
      console.error('Error connecting to Supabase:', error);
    } else {
      console.log('Successfully connected to Supabase!');
      console.log('Data:', data);
    }
  } catch (err) {
    console.error('Exception when connecting to Supabase:', err);
  }
}

// Run the test
testConnection();
