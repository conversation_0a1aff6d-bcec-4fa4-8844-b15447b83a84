// Deploy Supabase Edge Functions
const fs = require('fs');
const path = require('path');
const https = require('https');
const { execSync } = require('child_process');

// Configuration
const SUPABASE_PROJECT_ID = 'pajqstbgncpbpcaffbpm';
const FUNCTIONS_TO_DEPLOY = ['stripe-checkout'];

// Helper function to execute shell commands
function runCommand(command) {
  try {
    return execSync(command, { encoding: 'utf8' });
  } catch (error) {
    return null;
  }
}

// Deploy each function
async function deployFunctions() {
  for (const functionName of FUNCTIONS_TO_DEPLOY) {
    try {
      // Navigate to the function directory
      const functionPath = path.join('supabase', 'functions', functionName);
      
      // Check if the function directory exists
      if (!fs.existsSync(functionPath)) {
        continue;
      }
      
      // Deploy using Supabase CLI if available
      const supabaseCLIAvailable = runCommand('which supabase');
      
      if (supabaseCLIAvailable) {
        runCommand(`cd ${functionPath} && supabase functions deploy ${functionName} --project-ref ${SUPABASE_PROJECT_ID}`);
      }
    } catch (error) {
      // Silently continue on error
    }
  }
}

// Run the deployment
deployFunctions();
