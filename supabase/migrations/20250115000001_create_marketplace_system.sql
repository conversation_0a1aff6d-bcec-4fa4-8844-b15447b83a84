-- Create marketplace system for AI agent monetization
-- This migration adds all necessary tables and functions for the marketplace

-- Table to store seller Stripe Connect accounts
CREATE TABLE IF NOT EXISTS public.seller_accounts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
  stripe_account_id TEXT NOT NULL UNIQUE,
  account_status TEXT NOT NULL DEFAULT 'pending', -- pending, active, restricted, inactive
  charges_enabled BOOLEAN DEFAULT FALSE,
  payouts_enabled BOOLEAN DEFAULT FALSE,
  details_submitted BOOLEAN DEFAULT FALSE,
  requirements_due TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Table to store marketplace transactions
CREATE TABLE IF NOT EXISTS public.marketplace_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  buyer_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  seller_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  agent_id UUID REFERENCES public.agents(id) ON DELETE CASCADE NOT NULL,
  stripe_payment_intent_id TEXT NOT NULL UNIQUE,
  stripe_transfer_id TEXT, -- Transfer to seller
  amount_total DECIMAL(10,2) NOT NULL, -- Total amount paid by buyer
  amount_seller DECIMAL(10,2) NOT NULL, -- Amount transferred to seller (after platform fee)
  platform_fee DECIMAL(10,2) NOT NULL, -- Platform fee amount
  platform_fee_percentage DECIMAL(5,2) NOT NULL DEFAULT 15.00, -- Platform fee percentage
  currency TEXT NOT NULL DEFAULT 'usd',
  status TEXT NOT NULL DEFAULT 'pending', -- pending, completed, failed, refunded
  stripe_status TEXT, -- Stripe payment status
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Table to store purchased agents (user's purchased library) - SECURE VERSION
CREATE TABLE IF NOT EXISTS public.purchased_agents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  buyer_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  seller_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  original_agent_id UUID REFERENCES public.agents(id) ON DELETE CASCADE NOT NULL,
  -- SECURITY: No longer create full copies, use licensing system instead
  license_key TEXT NOT NULL UNIQUE, -- Encrypted license for secure execution
  transaction_id UUID REFERENCES public.marketplace_transactions(id) ON DELETE CASCADE NOT NULL,
  purchase_price DECIMAL(10,2) NOT NULL,
  custom_name TEXT, -- Buyer can rename their license
  is_active BOOLEAN DEFAULT TRUE,
  -- SECURITY: Add usage tracking and limits
  usage_count INTEGER DEFAULT 0,
  max_usage_limit INTEGER DEFAULT NULL, -- NULL = unlimited
  license_expires_at TIMESTAMP WITH TIME ZONE DEFAULT NULL, -- NULL = never expires
  -- SECURITY: Add fingerprinting to prevent sharing
  buyer_fingerprint TEXT, -- Unique identifier tied to buyer's account
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  
  -- Ensure unique purchase per buyer per agent
  UNIQUE(buyer_id, original_agent_id)
);

-- Table to store seller earnings and payout tracking
CREATE TABLE IF NOT EXISTS public.seller_earnings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  seller_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  transaction_id UUID REFERENCES public.marketplace_transactions(id) ON DELETE CASCADE NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  currency TEXT NOT NULL DEFAULT 'usd',
  status TEXT NOT NULL DEFAULT 'pending', -- pending, available, paid_out
  stripe_transfer_id TEXT,
  payout_date TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_seller_accounts_user_id ON public.seller_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_seller_accounts_stripe_account_id ON public.seller_accounts(stripe_account_id);
CREATE INDEX IF NOT EXISTS idx_marketplace_transactions_buyer_id ON public.marketplace_transactions(buyer_id);
CREATE INDEX IF NOT EXISTS idx_marketplace_transactions_seller_id ON public.marketplace_transactions(seller_id);
CREATE INDEX IF NOT EXISTS idx_marketplace_transactions_agent_id ON public.marketplace_transactions(agent_id);
CREATE INDEX IF NOT EXISTS idx_marketplace_transactions_status ON public.marketplace_transactions(status);
CREATE INDEX IF NOT EXISTS idx_purchased_agents_buyer_id ON public.purchased_agents(buyer_id);
CREATE INDEX IF NOT EXISTS idx_purchased_agents_seller_id ON public.purchased_agents(seller_id);
CREATE INDEX IF NOT EXISTS idx_purchased_agents_original_agent_id ON public.purchased_agents(original_agent_id);
CREATE INDEX IF NOT EXISTS idx_seller_earnings_seller_id ON public.seller_earnings(seller_id);
CREATE INDEX IF NOT EXISTS idx_seller_earnings_status ON public.seller_earnings(status);

-- Add marketplace-specific columns to existing agents table
ALTER TABLE public.agents 
ADD COLUMN IF NOT EXISTS price DECIMAL(10,2) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS is_for_sale BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS sales_count INTEGER DEFAULT 0;

-- Create indexes for marketplace queries on agents table
CREATE INDEX IF NOT EXISTS idx_agents_is_for_sale ON public.agents(is_for_sale) WHERE is_for_sale = TRUE;
CREATE INDEX IF NOT EXISTS idx_agents_price ON public.agents(price) WHERE price IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_agents_marketplace ON public.agents(is_for_sale, price, is_public) WHERE is_for_sale = TRUE;

-- Enable Row Level Security (RLS) on new tables
ALTER TABLE public.seller_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.marketplace_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.purchased_agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.seller_earnings ENABLE ROW LEVEL SECURITY;

-- RLS Policies for seller_accounts
CREATE POLICY "Users can view their own seller account" ON public.seller_accounts
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own seller account" ON public.seller_accounts
  FOR UPDATE USING (auth.uid() = user_id);

-- RLS Policies for marketplace_transactions
CREATE POLICY "Users can view their own transactions" ON public.marketplace_transactions
  FOR SELECT USING (auth.uid() = buyer_id OR auth.uid() = seller_id);

-- RLS Policies for purchased_agents
CREATE POLICY "Users can view their own purchased agents" ON public.purchased_agents
  FOR SELECT USING (auth.uid() = buyer_id);

CREATE POLICY "Users can update their own purchased agents" ON public.purchased_agents
  FOR UPDATE USING (auth.uid() = buyer_id);

-- RLS Policies for seller_earnings
CREATE POLICY "Sellers can view their own earnings" ON public.seller_earnings
  FOR SELECT USING (auth.uid() = seller_id);

-- Function to calculate seller earnings after platform fee
CREATE OR REPLACE FUNCTION calculate_seller_amount(total_amount DECIMAL, platform_fee_percentage DECIMAL)
RETURNS DECIMAL AS $$
BEGIN
  RETURN total_amount * (1 - platform_fee_percentage / 100);
END;
$$ LANGUAGE plpgsql;

-- Function to update agent sales count
CREATE OR REPLACE FUNCTION increment_agent_sales_count()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.status = 'completed' AND (OLD.status IS NULL OR OLD.status != 'completed') THEN
    UPDATE public.agents 
    SET sales_count = sales_count + 1,
        updated_at = NOW()
    WHERE id = NEW.agent_id;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update sales count when transaction completes
CREATE TRIGGER update_agent_sales_count
  AFTER INSERT OR UPDATE ON public.marketplace_transactions
  FOR EACH ROW
  EXECUTE FUNCTION increment_agent_sales_count();

-- SECURITY: Function to create secure agent license instead of copying
CREATE OR REPLACE FUNCTION create_secure_agent_license(
  p_buyer_id UUID,
  p_original_agent_id UUID,
  p_custom_name TEXT DEFAULT NULL,
  p_max_usage_limit INTEGER DEFAULT NULL,
  p_license_expires_at TIMESTAMP WITH TIME ZONE DEFAULT NULL
)
RETURNS TEXT AS $$
DECLARE
  v_original_agent RECORD;
  v_license_key TEXT;
  v_buyer_fingerprint TEXT;
BEGIN
  -- Get the original agent
  SELECT * INTO v_original_agent
  FROM public.agents
  WHERE id = p_original_agent_id;

  IF NOT FOUND THEN
    RAISE EXCEPTION 'Original agent not found';
  END IF;

  -- Generate secure license key (combination of agent ID, buyer ID, and random salt)
  v_license_key := encode(
    digest(
      p_original_agent_id::text || p_buyer_id::text || gen_random_uuid()::text || extract(epoch from now())::text,
      'sha256'
    ),
    'base64'
  );

  -- Generate buyer fingerprint for anti-sharing protection
  v_buyer_fingerprint := encode(
    digest(
      p_buyer_id::text || extract(epoch from now())::text,
      'sha256'
    ),
    'hex'
  );

  RETURN v_license_key;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON public.seller_accounts TO authenticated;
GRANT ALL ON public.marketplace_transactions TO authenticated;
GRANT ALL ON public.purchased_agents TO authenticated;
GRANT ALL ON public.seller_earnings TO authenticated;
GRANT EXECUTE ON FUNCTION calculate_seller_amount TO authenticated;
GRANT EXECUTE ON FUNCTION create_secure_agent_license TO authenticated;
