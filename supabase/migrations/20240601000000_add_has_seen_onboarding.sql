-- Add has_seen_onboarding column to profiles table if it doesn't exist
ALTER TABLE "public"."profiles" 
ADD COLUMN IF NOT EXISTS "has_seen_onboarding" BOOLEAN DEFAULT FALSE;

-- Add display_name column to profiles table if it doesn't exist
ALTER TABLE "public"."profiles" 
ADD COLUMN IF NOT EXISTS "display_name" TEXT;

-- Comment on the column to document its purpose
COMMENT ON COLUMN "public"."profiles"."has_seen_onboarding" 
IS 'Flag to track if the user has seen the onboarding flow';

-- Update existing profiles to set has_seen_onboarding to true if they have a subscription
UPDATE "public"."profiles"
SET "has_seen_onboarding" = TRUE
WHERE "subscription_type" IS NOT NULL;
