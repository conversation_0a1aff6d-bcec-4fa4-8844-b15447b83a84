-- Paper Trading Competition System
-- This migration creates the comprehensive competition system for paper trading

-- Paper Trading Competitions - Main competition management
CREATE TABLE IF NOT EXISTS public.paper_trading_competitions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    creator_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    starting_balance DECIMAL(15,2) NOT NULL DEFAULT 100000.00,
    max_participants INTEGER,
    entry_fee DECIMAL(10,2) DEFAULT 0.00,
    prize_pool DECIMAL(15,2) DEFAULT 0.00,
    rules JSONB, -- Competition rules and restrictions
    allowed_securities TEXT[], -- Array of allowed symbols, null = all allowed
    position_limits JSONB, -- Position size limits and restrictions
    status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'open', 'active', 'completed', 'cancelled')),
    registration_start TIMESTAMPTZ,
    registration_end TIMESTAMPTZ,
    competition_start TIMESTAMPTZ NOT NULL,
    competition_end TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Ensure competition dates are logical
    CONSTRAINT valid_competition_dates CHECK (
        competition_start < competition_end AND
        (registration_start IS NULL OR registration_start <= registration_end) AND
        (registration_end IS NULL OR registration_end <= competition_start)
    )
);

-- Competition Participants - User participation tracking
CREATE TABLE IF NOT EXISTS public.competition_participants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    competition_id UUID NOT NULL REFERENCES public.paper_trading_competitions(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    account_id UUID NOT NULL REFERENCES public.paper_trading_accounts(id) ON DELETE CASCADE,
    joined_at TIMESTAMPTZ DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    final_rank INTEGER,
    final_return_percent DECIMAL(8,4),
    final_portfolio_value DECIMAL(15,2),
    
    -- Ensure unique participation per competition
    UNIQUE(competition_id, user_id)
);

-- Competition Leaderboards - Real-time rankings (materialized for performance)
CREATE TABLE IF NOT EXISTS public.competition_leaderboards (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    competition_id UUID NOT NULL REFERENCES public.paper_trading_competitions(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    participant_id UUID NOT NULL REFERENCES public.competition_participants(id) ON DELETE CASCADE,
    username TEXT NOT NULL, -- Cached for performance
    current_rank INTEGER NOT NULL,
    portfolio_value DECIMAL(15,2) NOT NULL,
    total_return DECIMAL(15,2) NOT NULL,
    return_percent DECIMAL(8,4) NOT NULL,
    unrealized_pnl DECIMAL(15,2) NOT NULL,
    realized_pnl DECIMAL(15,2) NOT NULL,
    total_trades INTEGER DEFAULT 0,
    last_updated TIMESTAMPTZ DEFAULT NOW(),
    
    -- Ensure unique leaderboard entry per competition
    UNIQUE(competition_id, user_id)
);

-- Competition Portfolio Snapshots - Daily snapshots for historical tracking
CREATE TABLE IF NOT EXISTS public.competition_portfolio_snapshots (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    competition_id UUID NOT NULL REFERENCES public.paper_trading_competitions(id) ON DELETE CASCADE,
    participant_id UUID NOT NULL REFERENCES public.competition_participants(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    snapshot_date DATE NOT NULL,
    portfolio_value DECIMAL(15,2) NOT NULL,
    cash_balance DECIMAL(15,2) NOT NULL,
    total_return DECIMAL(15,2) NOT NULL,
    return_percent DECIMAL(8,4) NOT NULL,
    positions_count INTEGER DEFAULT 0,
    trades_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Ensure unique snapshot per participant per day
    UNIQUE(competition_id, participant_id, snapshot_date)
);

-- Indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_competitions_status ON public.paper_trading_competitions(status);
CREATE INDEX IF NOT EXISTS idx_competitions_creator ON public.paper_trading_competitions(creator_id);
CREATE INDEX IF NOT EXISTS idx_competitions_dates ON public.paper_trading_competitions(competition_start, competition_end);

CREATE INDEX IF NOT EXISTS idx_participants_competition ON public.competition_participants(competition_id);
CREATE INDEX IF NOT EXISTS idx_participants_user ON public.competition_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_participants_active ON public.competition_participants(competition_id, is_active);

CREATE INDEX IF NOT EXISTS idx_leaderboard_competition ON public.competition_leaderboards(competition_id);
CREATE INDEX IF NOT EXISTS idx_leaderboard_rank ON public.competition_leaderboards(competition_id, current_rank);
CREATE INDEX IF NOT EXISTS idx_leaderboard_updated ON public.competition_leaderboards(last_updated);

CREATE INDEX IF NOT EXISTS idx_snapshots_competition ON public.competition_portfolio_snapshots(competition_id);
CREATE INDEX IF NOT EXISTS idx_snapshots_participant ON public.competition_portfolio_snapshots(participant_id);
CREATE INDEX IF NOT EXISTS idx_snapshots_date ON public.competition_portfolio_snapshots(snapshot_date);

-- Enable Row Level Security
ALTER TABLE public.paper_trading_competitions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.competition_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.competition_leaderboards ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.competition_portfolio_snapshots ENABLE ROW LEVEL SECURITY;

-- RLS Policies for Competitions
-- Anyone can view open/active competitions
CREATE POLICY "Anyone can view open competitions" ON public.paper_trading_competitions
    FOR SELECT USING (status IN ('open', 'active', 'completed'));

-- Only creators can modify their competitions
CREATE POLICY "Creators can manage their competitions" ON public.paper_trading_competitions
    FOR ALL USING (auth.uid() = creator_id);

-- RLS Policies for Participants
-- Users can view all participants in competitions they're part of
CREATE POLICY "Users can view participants in their competitions" ON public.competition_participants
    FOR SELECT USING (
        competition_id IN (
            SELECT competition_id FROM public.competition_participants 
            WHERE user_id = auth.uid()
        )
    );

-- Users can only manage their own participation
CREATE POLICY "Users can manage their own participation" ON public.competition_participants
    FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for Leaderboards
-- Anyone can view leaderboards for open/active competitions
CREATE POLICY "Anyone can view competition leaderboards" ON public.competition_leaderboards
    FOR SELECT USING (
        competition_id IN (
            SELECT id FROM public.paper_trading_competitions 
            WHERE status IN ('open', 'active', 'completed')
        )
    );

-- Only system can update leaderboards (via functions)
CREATE POLICY "System can update leaderboards" ON public.competition_leaderboards
    FOR INSERT WITH CHECK (true);

CREATE POLICY "System can modify leaderboards" ON public.competition_leaderboards
    FOR UPDATE USING (true);

-- RLS Policies for Portfolio Snapshots
-- Users can view snapshots for competitions they participate in
CREATE POLICY "Users can view snapshots in their competitions" ON public.competition_portfolio_snapshots
    FOR SELECT USING (
        competition_id IN (
            SELECT competition_id FROM public.competition_participants 
            WHERE user_id = auth.uid()
        )
    );

-- Only system can create snapshots
CREATE POLICY "System can create snapshots" ON public.competition_portfolio_snapshots
    FOR INSERT WITH CHECK (true);

-- Function to automatically update competition status based on dates
CREATE OR REPLACE FUNCTION public.update_competition_status()
RETURNS TRIGGER AS $$
BEGIN
    -- Update status based on current time and competition dates
    IF NEW.status = 'draft' AND NEW.registration_start IS NOT NULL AND NOW() >= NEW.registration_start THEN
        NEW.status = 'open';
    END IF;

    IF NEW.status = 'open' AND NOW() >= NEW.competition_start THEN
        NEW.status = 'active';
    END IF;

    IF NEW.status = 'active' AND NOW() >= NEW.competition_end THEN
        NEW.status = 'completed';
    END IF;

    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update competition status
CREATE OR REPLACE TRIGGER update_competition_status_trigger
    BEFORE UPDATE ON public.paper_trading_competitions
    FOR EACH ROW
    EXECUTE FUNCTION public.update_competition_status();

-- Function to calculate and update leaderboard rankings
CREATE OR REPLACE FUNCTION public.update_competition_leaderboard(p_competition_id UUID)
RETURNS VOID AS $$
DECLARE
    participant_record RECORD;
    rank_counter INTEGER := 1;
BEGIN
    -- Delete existing leaderboard entries for this competition
    DELETE FROM public.competition_leaderboards WHERE competition_id = p_competition_id;

    -- Calculate rankings based on portfolio performance
    FOR participant_record IN
        SELECT
            cp.id as participant_id,
            cp.user_id,
            cp.competition_id,
            COALESCE(u.raw_user_meta_data->>'username', u.email) as username,
            pta.portfolio_value,
            (pta.portfolio_value - ptc.starting_balance) as total_return,
            ((pta.portfolio_value - ptc.starting_balance) / ptc.starting_balance * 100) as return_percent,
            pta.unrealized_pnl,
            pta.realized_pnl,
            COALESCE(trade_counts.total_trades, 0) as total_trades
        FROM public.competition_participants cp
        JOIN public.paper_trading_competitions ptc ON cp.competition_id = ptc.id
        JOIN public.paper_trading_accounts pta ON cp.account_id = pta.id
        JOIN auth.users u ON cp.user_id = u.id
        LEFT JOIN (
            SELECT
                account_id,
                COUNT(*) as total_trades
            FROM public.paper_trading_orders
            WHERE status = 'filled'
            GROUP BY account_id
        ) trade_counts ON pta.id = trade_counts.account_id
        WHERE cp.competition_id = p_competition_id
        AND cp.is_active = true
        ORDER BY pta.portfolio_value DESC
    LOOP
        -- Insert leaderboard entry
        INSERT INTO public.competition_leaderboards (
            competition_id,
            user_id,
            participant_id,
            username,
            current_rank,
            portfolio_value,
            total_return,
            return_percent,
            unrealized_pnl,
            realized_pnl,
            total_trades,
            last_updated
        ) VALUES (
            participant_record.competition_id,
            participant_record.user_id,
            participant_record.participant_id,
            participant_record.username,
            rank_counter,
            participant_record.portfolio_value,
            participant_record.total_return,
            participant_record.return_percent,
            participant_record.unrealized_pnl,
            participant_record.realized_pnl,
            participant_record.total_trades,
            NOW()
        );

        rank_counter := rank_counter + 1;
    END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
