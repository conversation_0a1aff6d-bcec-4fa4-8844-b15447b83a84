-- Add public agent features to existing agents table

-- Add new columns to agents table
ALTER TABLE public.agents 
ADD COLUMN IF NOT EXISTS is_public BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS public_description TEXT,
ADD COLUMN IF NOT EXISTS tags TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS likes_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS usage_count INTEGER DEFAULT 0;

-- Create agent_likes table
CREATE TABLE IF NOT EXISTS public.agent_likes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  agent_id UUID REFERENCES public.agents(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  UNIQUE(agent_id, user_id)
);

-- Add new indexes
CREATE INDEX IF NOT EXISTS agents_is_public_idx ON public.agents(is_public);
CREATE INDEX IF NOT EXISTS agents_likes_count_idx ON public.agents(likes_count);
CREATE INDEX IF NOT EXISTS agents_usage_count_idx ON public.agents(usage_count);
CREATE INDEX IF NOT EXISTS agent_likes_agent_id_idx ON public.agent_likes(agent_id);
CREATE INDEX IF NOT EXISTS agent_likes_user_id_idx ON public.agent_likes(user_id);

-- Update existing RLS policy for agents to allow viewing public agents
DROP POLICY IF EXISTS "Users can view their own agents" ON public.agents;
DROP POLICY IF EXISTS "Users can view their own agents or public agents" ON public.agents;
CREATE POLICY "Users can view their own agents or public agents"
  ON public.agents FOR SELECT
  USING (auth.uid() = user_id OR is_public = true);

-- Add RLS policies for agent_likes
DROP POLICY IF EXISTS "Users can view all agent likes" ON public.agent_likes;
DROP POLICY IF EXISTS "Users can create their own likes" ON public.agent_likes;
DROP POLICY IF EXISTS "Users can delete their own likes" ON public.agent_likes;

CREATE POLICY "Users can view all agent likes"
  ON public.agent_likes FOR SELECT
  USING (true);

CREATE POLICY "Users can create their own likes"
  ON public.agent_likes FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own likes"
  ON public.agent_likes FOR DELETE
  USING (auth.uid() = user_id);

-- Enable RLS on agent_likes table
ALTER TABLE public.agent_likes ENABLE ROW LEVEL SECURITY;

-- Create function to update likes count
CREATE OR REPLACE FUNCTION public.update_agent_likes_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE public.agents 
    SET likes_count = likes_count + 1 
    WHERE id = NEW.agent_id;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE public.agents 
    SET likes_count = likes_count - 1 
    WHERE id = OLD.agent_id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update likes count
CREATE TRIGGER update_agent_likes_count_trigger
  AFTER INSERT OR DELETE ON public.agent_likes
  FOR EACH ROW
  EXECUTE FUNCTION public.update_agent_likes_count();

-- Add comment to the new table
COMMENT ON TABLE public.agent_likes IS 'Stores user likes for public agents';
