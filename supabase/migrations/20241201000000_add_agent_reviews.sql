-- Create agent reviews table
CREATE TABLE IF NOT EXISTS agent_reviews (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    agent_id UUID NOT NULL REFERENCES agents(id) ON DELETE CASCADE,
    reviewer_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    title VARCHAR(200),
    review_text TEXT,
    is_verified_purchase BOOLEAN DEFAULT false,
    backtest_performance JSONB, -- Store backtest results that led to this review
    helpful_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one review per user per agent
    UNIQUE(agent_id, reviewer_id)
);

-- Create agent review helpfulness tracking
CREATE TABLE IF NOT EXISTS agent_review_helpfulness (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    review_id UUID NOT NULL REFERENCES agent_reviews(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    is_helpful BOOLEAN NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one vote per user per review
    UNIQUE(review_id, user_id)
);

-- Create agent backtest results table (for public backtests)
CREATE TABLE IF NOT EXISTS agent_public_backtests (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    agent_id UUID NOT NULL REFERENCES agents(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    backtest_config JSONB NOT NULL, -- Configuration used for backtest
    results JSONB NOT NULL, -- Backtest results
    is_public BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add rating summary columns to agents table
ALTER TABLE agents 
ADD COLUMN IF NOT EXISTS average_rating DECIMAL(3,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_reviews INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS rating_distribution JSONB DEFAULT '{"1": 0, "2": 0, "3": 0, "4": 0, "5": 0}';

-- Create function to update agent rating summary
CREATE OR REPLACE FUNCTION update_agent_rating_summary()
RETURNS TRIGGER AS $$
BEGIN
    -- Update the agent's rating summary
    UPDATE agents 
    SET 
        average_rating = (
            SELECT COALESCE(AVG(rating::DECIMAL), 0)
            FROM agent_reviews 
            WHERE agent_id = COALESCE(NEW.agent_id, OLD.agent_id)
        ),
        total_reviews = (
            SELECT COUNT(*)
            FROM agent_reviews 
            WHERE agent_id = COALESCE(NEW.agent_id, OLD.agent_id)
        ),
        rating_distribution = (
            SELECT COALESCE(
                jsonb_object_agg(
                    rating::TEXT, 
                    count
                ),
                '{"1": 0, "2": 0, "3": 0, "4": 0, "5": 0}'::jsonb
            )
            FROM (
                SELECT 
                    rating,
                    COUNT(*) as count
                FROM agent_reviews 
                WHERE agent_id = COALESCE(NEW.agent_id, OLD.agent_id)
                GROUP BY rating
                
                UNION ALL
                
                -- Ensure all ratings 1-5 are represented
                SELECT generate_series(1,5) as rating, 0 as count
            ) rating_counts
            GROUP BY rating
        )
    WHERE id = COALESCE(NEW.agent_id, OLD.agent_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update rating summary
DROP TRIGGER IF EXISTS update_agent_rating_summary_trigger ON agent_reviews;
CREATE TRIGGER update_agent_rating_summary_trigger
    AFTER INSERT OR UPDATE OR DELETE ON agent_reviews
    FOR EACH ROW
    EXECUTE FUNCTION update_agent_rating_summary();

-- Create function to update review helpfulness count
CREATE OR REPLACE FUNCTION update_review_helpfulness_count()
RETURNS TRIGGER AS $$
BEGIN
    -- Update the review's helpful count
    UPDATE agent_reviews 
    SET helpful_count = (
        SELECT COUNT(*)
        FROM agent_review_helpfulness 
        WHERE review_id = COALESCE(NEW.review_id, OLD.review_id)
        AND is_helpful = true
    )
    WHERE id = COALESCE(NEW.review_id, OLD.review_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update helpfulness count
DROP TRIGGER IF EXISTS update_review_helpfulness_count_trigger ON agent_review_helpfulness;
CREATE TRIGGER update_review_helpfulness_count_trigger
    AFTER INSERT OR UPDATE OR DELETE ON agent_review_helpfulness
    FOR EACH ROW
    EXECUTE FUNCTION update_review_helpfulness_count();

-- Enable RLS
ALTER TABLE agent_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_review_helpfulness ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_public_backtests ENABLE ROW LEVEL SECURITY;

-- RLS Policies for agent_reviews
CREATE POLICY "Anyone can read reviews" ON agent_reviews FOR SELECT USING (true);
CREATE POLICY "Users can create reviews" ON agent_reviews FOR INSERT WITH CHECK (auth.uid() = reviewer_id);
CREATE POLICY "Users can update own reviews" ON agent_reviews FOR UPDATE USING (auth.uid() = reviewer_id);
CREATE POLICY "Users can delete own reviews" ON agent_reviews FOR DELETE USING (auth.uid() = reviewer_id);

-- RLS Policies for agent_review_helpfulness
CREATE POLICY "Anyone can read helpfulness" ON agent_review_helpfulness FOR SELECT USING (true);
CREATE POLICY "Users can vote on helpfulness" ON agent_review_helpfulness FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own votes" ON agent_review_helpfulness FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own votes" ON agent_review_helpfulness FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for agent_public_backtests
CREATE POLICY "Anyone can read public backtests" ON agent_public_backtests FOR SELECT USING (is_public = true);
CREATE POLICY "Users can create backtests" ON agent_public_backtests FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own backtests" ON agent_public_backtests FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own backtests" ON agent_public_backtests FOR DELETE USING (auth.uid() = user_id);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_agent_reviews_agent_id ON agent_reviews(agent_id);
CREATE INDEX IF NOT EXISTS idx_agent_reviews_reviewer_id ON agent_reviews(reviewer_id);
CREATE INDEX IF NOT EXISTS idx_agent_reviews_rating ON agent_reviews(rating);
CREATE INDEX IF NOT EXISTS idx_agent_reviews_created_at ON agent_reviews(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_agent_review_helpfulness_review_id ON agent_review_helpfulness(review_id);
CREATE INDEX IF NOT EXISTS idx_agent_review_helpfulness_user_id ON agent_review_helpfulness(user_id);
CREATE INDEX IF NOT EXISTS idx_agent_public_backtests_agent_id ON agent_public_backtests(agent_id);
CREATE INDEX IF NOT EXISTS idx_agent_public_backtests_user_id ON agent_public_backtests(user_id);
