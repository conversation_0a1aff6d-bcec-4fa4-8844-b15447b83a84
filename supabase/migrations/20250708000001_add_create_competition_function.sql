-- Add function to create competitions via RPC to avoid schema cache issues
-- This function bypasses potential foreign key relationship cache problems

CREATE OR REPLACE FUNCTION public.create_competition(
  p_name TEXT,
  p_description TEXT DEFAULT NULL,
  p_creator_id UUID,
  p_starting_balance DECIMAL(15,2) DEFAULT 100000.00,
  p_max_participants INTEGER DEFAULT NULL,
  p_entry_fee DECIMAL(10,2) DEFAULT 0.00,
  p_prize_pool DECIMAL(15,2) DEFAULT 0.00,
  p_rules JSONB DEFAULT NULL,
  p_allowed_securities TEXT[] DEFAULT NULL,
  p_position_limits JSONB DEFAULT NULL,
  p_registration_start TIMESTAMPTZ DEFAULT NULL,
  p_registration_end TIMESTAMPTZ DEFAULT NULL,
  p_competition_start TIMESTAMPTZ DEFAULT NULL,
  p_competition_end TIMESTAMPTZ DEFAULT NULL,
  p_whop_company_id TEXT DEFAULT NULL,
  p_whop_business_id TEXT DEFAULT NULL,
  p_whop_business_handle TEXT DEFAULT NULL,
  p_competition_scope TEXT DEFAULT 'public',
  p_is_cross_community BOOLEAN DEFAULT FALSE,
  p_allowed_whop_communities TEXT[] DEFAULT '{}',
  p_status TEXT DEFAULT 'open'
)
RETURNS UUID AS $$
DECLARE
  competition_id UUID;
BEGIN
  -- Insert the competition record
  INSERT INTO public.paper_trading_competitions (
    name,
    description,
    creator_id,
    starting_balance,
    max_participants,
    entry_fee,
    prize_pool,
    rules,
    allowed_securities,
    position_limits,
    registration_start,
    registration_end,
    competition_start,
    competition_end,
    whop_company_id,
    whop_business_id,
    whop_business_handle,
    competition_scope,
    is_cross_community,
    allowed_whop_communities,
    status,
    created_at,
    updated_at
  ) VALUES (
    p_name,
    p_description,
    p_creator_id,
    p_starting_balance,
    p_max_participants,
    p_entry_fee,
    p_prize_pool,
    p_rules,
    p_allowed_securities,
    p_position_limits,
    p_registration_start,
    p_registration_end,
    p_competition_start,
    p_competition_end,
    p_whop_company_id,
    p_whop_business_id,
    p_whop_business_handle,
    p_competition_scope,
    p_is_cross_community,
    p_allowed_whop_communities,
    p_status,
    NOW(),
    NOW()
  ) RETURNING id INTO competition_id;
  
  RETURN competition_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.create_competition TO authenticated;

-- Add comment for documentation
COMMENT ON FUNCTION public.create_competition IS 'Creates a new paper trading competition with proper validation and permissions';
