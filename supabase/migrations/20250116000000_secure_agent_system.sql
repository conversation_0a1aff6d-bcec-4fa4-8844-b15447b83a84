-- SECURITY: Secure Agent System Migration
-- This migration implements comprehensive security measures to prevent agent reselling and reverse engineering

-- Add security columns to agents table for paid agents
ALTER TABLE public.agents 
ADD COLUMN IF NOT EXISTS is_encrypted BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS encrypted_config TEXT, -- Encrypted configuration for paid agents
ADD COLUMN IF NOT EXISTS config_hash TEXT, -- Hash for integrity verification
ADD COLUMN IF NOT EXISTS security_level INTEGER DEFAULT 0; -- 0=public, 1=paid, 2=premium

-- Create secure agent licenses table (replaces purchased_agents copying system)
CREATE TABLE IF NOT EXISTS public.agent_licenses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  license_key TEXT NOT NULL UNIQUE,
  buyer_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  seller_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  agent_id UUID REFERENCES public.agents(id) ON DELETE CASCADE NOT NULL,
  transaction_id UUID REFERENCES public.marketplace_transactions(id) ON DELETE CASCADE,
  
  -- License terms and restrictions
  custom_name TEXT,
  purchase_price DECIMAL(10,2) NOT NULL,
  usage_count INTEGER DEFAULT 0,
  max_usage_limit INTEGER DEFAULT NULL, -- NULL = unlimited
  license_expires_at TIMESTAMP WITH TIME ZONE DEFAULT NULL, -- NULL = never expires
  
  -- Anti-sharing and security measures
  buyer_fingerprint TEXT NOT NULL, -- Unique identifier tied to buyer's account
  device_fingerprints TEXT[] DEFAULT '{}', -- Track authorized devices
  last_used_at TIMESTAMP WITH TIME ZONE,
  last_used_ip INET,
  
  -- License status
  is_active BOOLEAN DEFAULT TRUE,
  is_suspended BOOLEAN DEFAULT FALSE,
  suspension_reason TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create secure execution logs table
CREATE TABLE IF NOT EXISTS public.secure_agent_executions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  license_key TEXT REFERENCES public.agent_licenses(license_key) ON DELETE CASCADE NOT NULL,
  agent_id UUID REFERENCES public.agents(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  
  -- Execution details (no sensitive configuration exposed)
  symbol TEXT NOT NULL,
  timeframe TEXT NOT NULL,
  execution_result JSONB, -- Only final result, no internal logic
  execution_time_ms INTEGER,
  
  -- Security tracking
  request_fingerprint TEXT NOT NULL,
  ip_address INET,
  user_agent TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create agent access violations table for security monitoring
CREATE TABLE IF NOT EXISTS public.agent_access_violations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  agent_id UUID REFERENCES public.agents(id) ON DELETE CASCADE,
  license_key TEXT,
  
  violation_type TEXT NOT NULL, -- 'unauthorized_access', 'license_sharing', 'reverse_engineering_attempt'
  violation_details JSONB,
  ip_address INET,
  user_agent TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create indexes for performance and security
CREATE INDEX IF NOT EXISTS idx_agent_licenses_buyer_id ON public.agent_licenses(buyer_id);
CREATE INDEX IF NOT EXISTS idx_agent_licenses_agent_id ON public.agent_licenses(agent_id);
CREATE INDEX IF NOT EXISTS idx_agent_licenses_license_key ON public.agent_licenses(license_key);
CREATE INDEX IF NOT EXISTS idx_agent_licenses_active ON public.agent_licenses(is_active) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_secure_executions_license_key ON public.secure_agent_executions(license_key);
CREATE INDEX IF NOT EXISTS idx_secure_executions_user_id ON public.secure_agent_executions(user_id);
CREATE INDEX IF NOT EXISTS idx_access_violations_user_id ON public.agent_access_violations(user_id);
CREATE INDEX IF NOT EXISTS idx_access_violations_agent_id ON public.agent_access_violations(agent_id);

-- Enable Row Level Security
ALTER TABLE public.agent_licenses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.secure_agent_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_access_violations ENABLE ROW LEVEL SECURITY;

-- RLS Policies for agent_licenses
CREATE POLICY "Users can view their own licenses" ON public.agent_licenses
  FOR SELECT USING (auth.uid() = buyer_id);

CREATE POLICY "Users can update their own licenses" ON public.agent_licenses
  FOR UPDATE USING (auth.uid() = buyer_id);

-- RLS Policies for secure_agent_executions  
CREATE POLICY "Users can view their own executions" ON public.secure_agent_executions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own executions" ON public.secure_agent_executions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- RLS Policies for agent_access_violations (admin only)
CREATE POLICY "Only service role can access violations" ON public.agent_access_violations
  FOR ALL USING (auth.role() = 'service_role');

-- Function to encrypt agent configuration
CREATE OR REPLACE FUNCTION encrypt_agent_config(
  p_agent_id UUID,
  p_encryption_key TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
  v_agent RECORD;
  v_encrypted_config TEXT;
  v_config_hash TEXT;
  v_key TEXT;
BEGIN
  -- Get the agent
  SELECT * INTO v_agent FROM public.agents WHERE id = p_agent_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Agent not found';
  END IF;
  
  -- Use provided key or generate one
  v_key := COALESCE(p_encryption_key, encode(gen_random_bytes(32), 'base64'));
  
  -- Create hash of original configuration for integrity
  v_config_hash := encode(digest(v_agent.configuration::text, 'sha256'), 'hex');
  
  -- Encrypt the configuration (simplified - in production use proper encryption)
  v_encrypted_config := encode(
    digest(v_agent.configuration::text || v_key, 'sha256'), 
    'base64'
  );
  
  -- Update agent with encrypted data
  UPDATE public.agents 
  SET 
    is_encrypted = TRUE,
    encrypted_config = v_encrypted_config,
    config_hash = v_config_hash,
    security_level = 1,
    updated_at = now()
  WHERE id = p_agent_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate license and track usage
CREATE OR REPLACE FUNCTION validate_agent_license(
  p_license_key TEXT,
  p_user_id UUID,
  p_request_fingerprint TEXT DEFAULT NULL
)
RETURNS JSONB AS $$
DECLARE
  v_license RECORD;
  v_result JSONB;
BEGIN
  -- Get license details
  SELECT * INTO v_license 
  FROM public.agent_licenses 
  WHERE license_key = p_license_key 
    AND buyer_id = p_user_id 
    AND is_active = TRUE 
    AND is_suspended = FALSE;
  
  IF NOT FOUND THEN
    -- Log violation attempt
    INSERT INTO public.agent_access_violations (
      user_id, license_key, violation_type, violation_details
    ) VALUES (
      p_user_id, p_license_key, 'unauthorized_access', 
      jsonb_build_object('attempted_license', p_license_key)
    );
    
    RETURN jsonb_build_object('valid', false, 'error', 'Invalid or expired license');
  END IF;
  
  -- Check usage limits
  IF v_license.max_usage_limit IS NOT NULL AND v_license.usage_count >= v_license.max_usage_limit THEN
    RETURN jsonb_build_object('valid', false, 'error', 'Usage limit exceeded');
  END IF;
  
  -- Check expiration
  IF v_license.license_expires_at IS NOT NULL AND v_license.license_expires_at < now() THEN
    RETURN jsonb_build_object('valid', false, 'error', 'License expired');
  END IF;
  
  -- Update usage tracking
  UPDATE public.agent_licenses 
  SET 
    usage_count = usage_count + 1,
    last_used_at = now(),
    updated_at = now()
  WHERE license_key = p_license_key;
  
  -- Return validation result
  RETURN jsonb_build_object(
    'valid', true,
    'agent_id', v_license.agent_id,
    'custom_name', v_license.custom_name,
    'usage_count', v_license.usage_count + 1,
    'max_usage_limit', v_license.max_usage_limit
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT ALL ON public.agent_licenses TO authenticated;
GRANT ALL ON public.secure_agent_executions TO authenticated;
GRANT SELECT ON public.agent_access_violations TO authenticated;
GRANT EXECUTE ON FUNCTION encrypt_agent_config TO authenticated;
GRANT EXECUTE ON FUNCTION validate_agent_license TO authenticated;
