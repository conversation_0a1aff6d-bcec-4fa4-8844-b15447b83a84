-- Migration to convert subscription system to Premium-only plan
-- This removes all access restrictions and converts all users to Premium

-- First, update all existing users to Premium plan
UPDATE profiles 
SET subscription_type = 'premium' 
WHERE subscription_type IS NULL OR subscription_type IN ('basic', 'pro');

-- Update all existing subscriptions to Premium
UPDATE subscriptions 
SET subscription_type = 'premium' 
WHERE subscription_type IS NULL OR subscription_type IN ('basic', 'pro');

-- Drop the old subscription_type enum and recreate with only premium
DROP TYPE IF EXISTS subscription_type CASCADE;
CREATE TYPE subscription_type AS ENUM ('premium');

-- Re-add the subscription_type columns with the new enum
ALTER TABLE profiles 
DROP COLUMN IF EXISTS subscription_type,
ADD COLUMN subscription_type subscription_type DEFAULT 'premium';

ALTER TABLE subscriptions 
DROP COLUMN IF EXISTS subscription_type,
ADD COLUMN subscription_type subscription_type DEFAULT 'premium';

-- Update all users to have Premium access
UPDATE profiles SET subscription_type = 'premium';
UPDATE subscriptions SET subscription_type = 'premium';

-- Add comments for documentation
COMMENT ON COLUMN profiles.subscription_type IS 'User subscription plan type - Premium only';
COMMENT ON COLUMN subscriptions.subscription_type IS 'Subscription plan type - Premium only';
COMMENT ON TYPE subscription_type IS 'Subscription plan types - Premium only system';
