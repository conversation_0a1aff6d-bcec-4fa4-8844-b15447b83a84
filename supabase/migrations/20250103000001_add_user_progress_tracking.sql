-- Add user progress tracking for headquarters functionality
-- This migration adds tables and columns to track user progress and achievements

-- Add progress tracking columns to profiles table
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS headquarters_progress JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS last_activity_date TIMES<PERSON>MP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS total_time_saved_hours INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS achievements_unlocked TEXT[] DEFAULT '{}';

-- Create user_achievements table for detailed achievement tracking
CREATE TABLE IF NOT EXISTS public.user_achievements (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  achievement_id TEXT NOT NULL,
  achievement_title TEXT NOT NULL,
  achievement_description TEXT,
  unlocked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  achievement_data JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_activity_log table for tracking user actions
CREATE TABLE IF NOT EXISTS public.user_activity_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  activity_type TEXT NOT NULL,
  activity_data JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_goals table for tracking progress toward goals
CREATE TABLE IF NOT EXISTS public.user_goals (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  goal_type TEXT NOT NULL, -- 'win_rate', 'chat_messages', 'agents_created', etc.
  goal_target NUMERIC NOT NULL,
  current_progress NUMERIC DEFAULT 0,
  is_completed BOOLEAN DEFAULT FALSE,
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_achievements_user_id ON public.user_achievements(user_id);
CREATE INDEX IF NOT EXISTS idx_user_achievements_achievement_id ON public.user_achievements(achievement_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_log_user_id ON public.user_activity_log(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_log_type ON public.user_activity_log(activity_type);
CREATE INDEX IF NOT EXISTS idx_user_goals_user_id ON public.user_goals(user_id);
CREATE INDEX IF NOT EXISTS idx_user_goals_type ON public.user_goals(goal_type);

-- Enable RLS on new tables
ALTER TABLE public.user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_activity_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_goals ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own achievements"
  ON public.user_achievements FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own achievements"
  ON public.user_achievements FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view their own activity log"
  ON public.user_activity_log FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own activity log"
  ON public.user_activity_log FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view their own goals"
  ON public.user_goals FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own goals"
  ON public.user_goals FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own goals"
  ON public.user_goals FOR UPDATE
  USING (auth.uid() = user_id);

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON public.user_achievements TO authenticated;
GRANT SELECT, INSERT ON public.user_activity_log TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.user_goals TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE public.user_achievements IS 'Tracks user achievements and milestones';
COMMENT ON TABLE public.user_activity_log IS 'Logs user activities for analytics and progress tracking';
COMMENT ON TABLE public.user_goals IS 'Tracks user goals and progress toward completion';
COMMENT ON COLUMN public.profiles.headquarters_progress IS 'JSON object storing headquarters-specific progress data';
COMMENT ON COLUMN public.profiles.last_activity_date IS 'Timestamp of user''s last activity';
COMMENT ON COLUMN public.profiles.total_time_saved_hours IS 'Total hours saved through Osis usage';
COMMENT ON COLUMN public.profiles.achievements_unlocked IS 'Array of achievement IDs that user has unlocked';

-- Create function to update last activity date
CREATE OR REPLACE FUNCTION update_user_last_activity()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE public.profiles 
  SET last_activity_date = NOW()
  WHERE id = NEW.user_id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to automatically update last activity (only if tables exist)
DO $$
BEGIN
  -- Create trigger for messages table if it exists
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'messages') THEN
    DROP TRIGGER IF EXISTS update_last_activity_on_message ON public.messages;
    CREATE TRIGGER update_last_activity_on_message
      AFTER INSERT ON public.messages
      FOR EACH ROW
      EXECUTE FUNCTION update_user_last_activity();
  END IF;

  -- Create trigger for chats table if it exists
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'chats') THEN
    DROP TRIGGER IF EXISTS update_last_activity_on_chat ON public.chats;
    CREATE TRIGGER update_last_activity_on_chat
      AFTER INSERT ON public.chats
      FOR EACH ROW
      EXECUTE FUNCTION update_user_last_activity();
  END IF;

  -- Create trigger for agents table if it exists
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'agents') THEN
    DROP TRIGGER IF EXISTS update_last_activity_on_agent ON public.agents;
    CREATE TRIGGER update_last_activity_on_agent
      AFTER INSERT ON public.agents
      FOR EACH ROW
      EXECUTE FUNCTION update_user_last_activity();
  END IF;
END $$;
