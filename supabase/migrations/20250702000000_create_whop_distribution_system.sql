-- Create Whop Agent Distribution System
-- This migration adds tables and functions for Whop owners to distribute agents to all members

-- Table to store Whop distributed agents
CREATE TABLE IF NOT EXISTS public.whop_distributed_agents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  agent_id UUID REFERENCES public.agents(id) ON DELETE CASCADE NOT NULL,
  distributor_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  whop_company_id TEXT NOT NULL,
  whop_experience_id TEXT,
  distributed_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  
  -- Ensure unique distribution per agent per company
  UNIQUE(agent_id, whop_company_id)
);

-- Table to track individual member distributions
CREATE TABLE IF NOT EXISTS public.whop_member_agents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  whop_distribution_id UUID REFERENCES public.whop_distributed_agents(id) ON DELETE CASCADE NOT NULL,
  member_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  whop_member_id TEXT NOT NULL,
  agent_id UUID REFERENCES public.agents(id) ON DELETE CASCADE NOT NULL,
  custom_name TEXT,
  added_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  
  -- Ensure unique agent per member
  UNIQUE(member_user_id, agent_id)
);

-- Enable RLS on new tables
ALTER TABLE public.whop_distributed_agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.whop_member_agents ENABLE ROW LEVEL SECURITY;

-- RLS Policies for whop_distributed_agents
CREATE POLICY "Users can view distributed agents in their company" ON public.whop_distributed_agents
  FOR SELECT USING (
    -- Allow distributors to see their distributions
    distributor_id = auth.uid() OR
    -- Allow members to see distributions in their company (will be enforced by application logic)
    true
  );

CREATE POLICY "Only authenticated users can create distributions" ON public.whop_distributed_agents
  FOR INSERT WITH CHECK (
    auth.uid() IS NOT NULL AND
    distributor_id = auth.uid()
  );

CREATE POLICY "Only distributors can update their distributions" ON public.whop_distributed_agents
  FOR UPDATE USING (distributor_id = auth.uid());

CREATE POLICY "Only distributors can delete their distributions" ON public.whop_distributed_agents
  FOR DELETE USING (distributor_id = auth.uid());

-- RLS Policies for whop_member_agents
CREATE POLICY "Users can view their distributed agents" ON public.whop_member_agents
  FOR SELECT USING (
    member_user_id = auth.uid() OR
    -- Allow distributors to see their distributions
    EXISTS (
      SELECT 1 FROM public.whop_distributed_agents wda
      WHERE wda.id = whop_distribution_id AND wda.distributor_id = auth.uid()
    )
  );

CREATE POLICY "Only system can create member agent distributions" ON public.whop_member_agents
  FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "Users can update their distributed agent names" ON public.whop_member_agents
  FOR UPDATE USING (member_user_id = auth.uid());

-- Function to distribute agent to all Whop members
CREATE OR REPLACE FUNCTION distribute_agent_to_whop_members(
  p_agent_id UUID,
  p_distributor_id UUID,
  p_whop_company_id TEXT,
  p_member_data JSONB,
  p_whop_experience_id TEXT DEFAULT NULL
)
RETURNS TABLE(
  success BOOLEAN,
  distribution_id UUID,
  members_added INTEGER,
  errors TEXT[]
) AS $$
DECLARE
  v_distribution_id UUID;
  v_member JSONB;
  v_member_user_id UUID;
  v_members_added INTEGER := 0;
  v_errors TEXT[] := '{}';
  v_agent_name TEXT;
BEGIN
  -- Verify agent exists and distributor owns it
  SELECT name INTO v_agent_name
  FROM public.agents
  WHERE id = p_agent_id AND user_id = p_distributor_id;
  
  IF NOT FOUND THEN
    RETURN QUERY SELECT false, NULL::UUID, 0, ARRAY['Agent not found or access denied'];
    RETURN;
  END IF;

  -- Create or update distribution record
  INSERT INTO public.whop_distributed_agents (
    agent_id,
    distributor_id,
    whop_company_id,
    whop_experience_id,
    is_active
  )
  VALUES (
    p_agent_id,
    p_distributor_id,
    p_whop_company_id,
    p_whop_experience_id,
    true
  )
  ON CONFLICT (agent_id, whop_company_id)
  DO UPDATE SET
    is_active = true,
    distributed_at = timezone('utc'::text, now())
  RETURNING id INTO v_distribution_id;

  -- Process each member
  FOR v_member IN SELECT * FROM jsonb_array_elements(p_member_data)
  LOOP
    BEGIN
      -- Get or create Supabase user for Whop member
      SELECT id INTO v_member_user_id
      FROM auth.users
      WHERE raw_user_meta_data->>'whop_user_id' = (v_member->>'id');
      
      IF v_member_user_id IS NOT NULL THEN
        -- Add agent to member's library
        INSERT INTO public.whop_member_agents (
          whop_distribution_id,
          member_user_id,
          whop_member_id,
          agent_id,
          custom_name
        )
        VALUES (
          v_distribution_id,
          v_member_user_id,
          v_member->>'id',
          p_agent_id,
          v_agent_name || ' (Distributed)'
        )
        ON CONFLICT (member_user_id, agent_id)
        DO UPDATE SET
          is_active = true,
          added_at = timezone('utc'::text, now());
        
        v_members_added := v_members_added + 1;
      ELSE
        v_errors := array_append(v_errors, 'User not found for Whop member: ' || (v_member->>'username'));
      END IF;
      
    EXCEPTION WHEN OTHERS THEN
      v_errors := array_append(v_errors, 'Error processing member ' || (v_member->>'username') || ': ' || SQLERRM);
    END;
  END LOOP;

  RETURN QUERY SELECT true, v_distribution_id, v_members_added, v_errors;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is Whop owner for a company
CREATE OR REPLACE FUNCTION is_whop_company_owner(
  p_user_id UUID,
  p_whop_company_id TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
  -- This will be implemented to check via Whop API
  -- For now, return true if user has Whop metadata
  RETURN EXISTS (
    SELECT 1 FROM auth.users
    WHERE id = p_user_id
    AND raw_user_meta_data->>'whop_user_id' IS NOT NULL
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_whop_distributed_agents_company ON public.whop_distributed_agents(whop_company_id);
CREATE INDEX IF NOT EXISTS idx_whop_distributed_agents_distributor ON public.whop_distributed_agents(distributor_id);
CREATE INDEX IF NOT EXISTS idx_whop_member_agents_user ON public.whop_member_agents(member_user_id);
CREATE INDEX IF NOT EXISTS idx_whop_member_agents_agent ON public.whop_member_agents(agent_id);
