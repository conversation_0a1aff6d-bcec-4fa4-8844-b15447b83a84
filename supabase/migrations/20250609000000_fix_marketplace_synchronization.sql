-- Fix Marketplace Synchronization Issues
-- This migration addresses data consistency problems between agents and published_agents tables

-- Step 1: Fix agents that are published but not marked as public
-- These agents are in published_agents table with is_active=true but agents.is_public=false
UPDATE agents 
SET is_public = true, updated_at = NOW()
WHERE id IN (
  SELECT a.id 
  FROM agents a 
  INNER JOIN published_agents pa ON a.id = pa.agent_id 
  WHERE a.is_public = false 
  AND pa.is_active = true
);

-- Step 2: Handle agents that are marked as public but not in published_agents table
-- For now, we'll leave these as-is since they might be intended for paid marketplace
-- But we'll add a comment for manual review
COMMENT ON COLUMN agents.is_public IS 'Whether the agent is publicly visible. Should be true if agent is in published_agents with is_active=true OR if is_for_sale=true';

-- Step 3: Create a function to maintain synchronization
CREATE OR REPLACE FUNCTION sync_agent_marketplace_status()
RETURNS TRIGGER AS $$
BEGIN
  -- When an agent is marked as for sale, ensure it's public
  IF NEW.is_for_sale = true AND NEW.is_public = false THEN
    NEW.is_public = true;
  END IF;
  
  -- When an agent is removed from sale and has no published entry, make it private
  IF NEW.is_for_sale = false AND OLD.is_for_sale = true THEN
    -- Check if agent has active published entry
    IF NOT EXISTS (
      SELECT 1 FROM published_agents 
      WHERE agent_id = NEW.id AND is_active = true
    ) THEN
      NEW.is_public = false;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 4: Create trigger to maintain synchronization
DROP TRIGGER IF EXISTS sync_agent_marketplace_status_trigger ON agents;
CREATE TRIGGER sync_agent_marketplace_status_trigger
  BEFORE UPDATE ON agents
  FOR EACH ROW
  EXECUTE FUNCTION sync_agent_marketplace_status();

-- Step 5: Create a function to sync published_agents changes
CREATE OR REPLACE FUNCTION sync_published_agent_status()
RETURNS TRIGGER AS $$
BEGIN
  -- When a published agent is activated, ensure the base agent is public
  IF TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND NEW.is_active = true AND OLD.is_active = false) THEN
    UPDATE agents 
    SET is_public = true, updated_at = NOW()
    WHERE id = NEW.agent_id AND is_public = false;
  END IF;
  
  -- When a published agent is deactivated, check if agent should remain public
  IF TG_OP = 'UPDATE' AND NEW.is_active = false AND OLD.is_active = true THEN
    -- Only make private if not for sale
    UPDATE agents 
    SET is_public = false, updated_at = NOW()
    WHERE id = NEW.agent_id 
    AND is_for_sale = false 
    AND is_public = true;
  END IF;
  
  -- When a published agent is deleted, check if agent should remain public
  IF TG_OP = 'DELETE' THEN
    UPDATE agents 
    SET is_public = false, updated_at = NOW()
    WHERE id = OLD.agent_id 
    AND is_for_sale = false 
    AND is_public = true;
    RETURN OLD;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 6: Create trigger for published_agents synchronization
DROP TRIGGER IF EXISTS sync_published_agent_status_trigger ON published_agents;
CREATE TRIGGER sync_published_agent_status_trigger
  AFTER INSERT OR UPDATE OR DELETE ON published_agents
  FOR EACH ROW
  EXECUTE FUNCTION sync_published_agent_status();

-- Step 7: Create indexes for better performance on marketplace queries
CREATE INDEX IF NOT EXISTS idx_agents_marketplace_visibility 
ON agents(is_public, is_for_sale, price) 
WHERE is_public = true;

CREATE INDEX IF NOT EXISTS idx_published_agents_active 
ON published_agents(agent_id, is_active) 
WHERE is_active = true;

-- Step 8: Add constraints to prevent invalid states
-- Ensure that agents for sale have a price
ALTER TABLE agents 
ADD CONSTRAINT check_for_sale_has_price 
CHECK (
  (is_for_sale = false) OR 
  (is_for_sale = true AND price IS NOT NULL AND price > 0)
);

-- Step 9: Create a view for unified marketplace queries
CREATE OR REPLACE VIEW marketplace_agents AS
SELECT 
  a.id,
  a.user_id,
  a.name,
  a.description,
  a.configuration,
  a.tags,
  a.likes_count,
  a.usage_count,
  a.created_at,
  a.updated_at,
  a.price,
  a.sales_count,
  'paid' as marketplace_type,
  true as is_paid,
  null as published_id,
  null as download_count,
  null as average_rating,
  null as total_reviews,
  null as is_featured,
  null as category
FROM agents a
WHERE a.is_for_sale = true 
  AND a.is_public = true 
  AND a.price IS NOT NULL 
  AND a.price > 0

UNION ALL

SELECT 
  a.id,
  a.user_id,
  COALESCE(pa.name, a.name) as name,
  COALESCE(pa.description, a.description) as description,
  a.configuration,
  COALESCE(pa.tags, a.tags) as tags,
  a.likes_count,
  a.usage_count,
  a.created_at,
  a.updated_at,
  0 as price,
  a.sales_count,
  'free' as marketplace_type,
  false as is_paid,
  pa.id as published_id,
  pa.download_count,
  pa.average_rating,
  pa.total_reviews,
  pa.is_featured,
  pa.category
FROM agents a
INNER JOIN published_agents pa ON a.id = pa.agent_id
WHERE pa.is_active = true;

-- Step 10: Grant appropriate permissions
GRANT SELECT ON marketplace_agents TO authenticated;

-- Step 11: Add helpful comments
COMMENT ON VIEW marketplace_agents IS 'Unified view of all marketplace agents (both paid and free)';
COMMENT ON FUNCTION sync_agent_marketplace_status() IS 'Maintains synchronization between agent sale status and public visibility';
COMMENT ON FUNCTION sync_published_agent_status() IS 'Maintains synchronization between published_agents and agents.is_public';

-- Step 12: Create migration log table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.migration_log (
  migration_name TEXT PRIMARY KEY,
  completed_at TIMESTAMP WITH TIME ZONE NOT NULL,
  description TEXT
);

-- Log the migration completion
INSERT INTO public.migration_log (migration_name, completed_at, description)
VALUES (
  '20250609000000_fix_marketplace_synchronization',
  NOW(),
  'Fixed marketplace synchronization issues between agents and published_agents tables'
) ON CONFLICT (migration_name) DO UPDATE SET
  completed_at = NOW(),
  description = EXCLUDED.description;
