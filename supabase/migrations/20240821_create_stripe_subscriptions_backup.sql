-- Create a backup table for Stripe subscriptions without foreign key constraints
CREATE TABLE IF NOT EXISTS public.stripe_subscriptions_backup (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  stripe_subscription_id TEXT,
  stripe_customer_id TEXT,
  stripe_price_id TEXT,
  customer_email TEXT,
  customer_name TEXT,
  customer_metadata JSONB,
  status TEXT,
  current_period_end TIMESTAMP WITH TIME ZONE,
  cancel_at_period_end BOOLEAN DEFAULT FALSE,
  cancel_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  subscription_data JSONB
);

-- Add indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_stripe_subscriptions_backup_subscription_id 
ON public.stripe_subscriptions_backup (stripe_subscription_id);

CREATE INDEX IF NOT EXISTS idx_stripe_subscriptions_backup_customer_id 
ON public.stripe_subscriptions_backup (stripe_customer_id);

-- Add RLS policies
ALTER TABLE public.stripe_subscriptions_backup ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Service role can manage stripe_subscriptions_backup"
  ON public.stripe_subscriptions_backup FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

COMMENT ON TABLE public.stripe_subscriptions_backup IS 'Backup table for Stripe subscriptions without foreign key constraints';
