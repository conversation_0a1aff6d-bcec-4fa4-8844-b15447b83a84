-- Ensure all required columns exist in the agents table
-- This migration ensures the agents table has all necessary columns for public agent features

-- Add missing columns to agents table if they don't exist
ALTER TABLE public.agents 
ADD COLUMN IF NOT EXISTS is_public BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS public_description TEXT,
ADD COLUMN IF NOT EXISTS tags TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS likes_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS usage_count INTEGER DEFAULT 0;

-- Create indexes if they don't exist
CREATE INDEX IF NOT EXISTS agents_is_public_idx ON public.agents(is_public);
CREATE INDEX IF NOT EXISTS agents_likes_count_idx ON public.agents(likes_count);
CREATE INDEX IF NOT EXISTS agents_usage_count_idx ON public.agents(usage_count);

-- Update RLS policy to allow viewing public agents
DROP POLICY IF EXISTS "Users can view their own agents" ON public.agents;
DROP POLICY IF EXISTS "Users can view their own agents or public agents" ON public.agents;

CREATE POLICY "Users can view their own agents or public agents"
  ON public.agents FOR SELECT
  USING (auth.uid() = user_id OR is_public = true);

-- Ensure the table has RLS enabled
ALTER TABLE public.agents ENABLE ROW LEVEL SECURITY;

-- Add comment to document the purpose
COMMENT ON COLUMN public.agents.is_public IS 'Whether the agent is publicly visible in the marketplace';
COMMENT ON COLUMN public.agents.public_description IS 'Public description shown in the marketplace';
COMMENT ON COLUMN public.agents.tags IS 'Tags for categorizing and searching agents';
COMMENT ON COLUMN public.agents.likes_count IS 'Number of likes this agent has received';
COMMENT ON COLUMN public.agents.usage_count IS 'Number of times this agent has been used';
