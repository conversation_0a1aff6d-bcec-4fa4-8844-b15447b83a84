-- Fix user_tokens table structure and constraints
-- This migration addresses the "Database error saving new user" issue

-- First, ensure the user_tokens table has the correct structure
-- Add missing foreign key constraint if it doesn't exist
DO $$ 
BEGIN
    -- Check if foreign key constraint exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'user_tokens_user_id_fkey' 
        AND table_name = 'user_tokens'
    ) THEN
        -- Add foreign key constraint
        ALTER TABLE public.user_tokens 
        ADD CONSTRAINT user_tokens_user_id_fkey 
        FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Ensure unique constraint on user_id exists
DO $$ 
BEGIN
    -- Check if unique constraint exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'user_tokens_user_id_key' 
        AND table_name = 'user_tokens'
    ) THEN
        -- Add unique constraint
        ALTER TABLE public.user_tokens 
        ADD CONSTRAINT user_tokens_user_id_key UNIQUE (user_id);
    END IF;
END $$;

-- Ensure all required columns exist with correct types
ALTER TABLE public.user_tokens 
ADD COLUMN IF NOT EXISTS tokens_remaining INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_reset TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
ADD COLUMN IF NOT EXISTS last_reset_date DATE DEFAULT CURRENT_DATE,
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now());

-- Remove any columns that might be causing conflicts
ALTER TABLE public.user_tokens 
DROP COLUMN IF EXISTS tokens_used,
DROP COLUMN IF EXISTS daily_limit,
DROP COLUMN IF EXISTS user_name;

-- Ensure RLS is enabled
ALTER TABLE public.user_tokens ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to recreate them cleanly
DROP POLICY IF EXISTS "Users can view their own token records" ON public.user_tokens;
DROP POLICY IF EXISTS "Users can insert their own token records" ON public.user_tokens;
DROP POLICY IF EXISTS "Users can update their own token records" ON public.user_tokens;
DROP POLICY IF EXISTS "Users can delete their own token records" ON public.user_tokens;
DROP POLICY IF EXISTS "Service role can manage user tokens" ON public.user_tokens;

-- Recreate policies with correct permissions
CREATE POLICY "Users can view their own token records"
    ON public.user_tokens FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own token records"
    ON public.user_tokens FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own token records"
    ON public.user_tokens FOR UPDATE
    USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own token records"
    ON public.user_tokens FOR DELETE
    USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage user tokens"
    ON public.user_tokens FOR ALL
    TO service_role
    USING (true)
    WITH CHECK (true);

-- Update the handle_new_user function to create user_tokens records
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_email text;
BEGIN
    -- Get email from auth.users
    SELECT email INTO v_email FROM auth.users WHERE id = NEW.id;

    -- Create profile first (this is essential for auth)
    BEGIN
        INSERT INTO public.profiles (id, full_name, email)
        VALUES (
            NEW.id,
            COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(v_email, '@', 1)),
            v_email
        );
    EXCEPTION WHEN others THEN
        -- Log error but don't stop the process
        RAISE WARNING 'Error creating profile for user ID %, Error: %', NEW.id, SQLERRM;
    END;

    -- Try to create user limits, but don't let it block signup if it fails
    BEGIN
        INSERT INTO public.user_limits (user_id, messages_used, last_reset)
        VALUES (NEW.id, 0, now())
        ON CONFLICT (user_id) DO UPDATE
        SET messages_used = 0,
            last_reset = now();
    EXCEPTION WHEN others THEN
        -- Just log the error and continue
        RAISE WARNING 'Error creating user_limits for user ID %, Error: %', NEW.id, SQLERRM;
    END;

    -- Try to create user_tokens record, but don't let it block signup if it fails
    BEGIN
        INSERT INTO public.user_tokens (user_id, tokens_remaining, last_reset, last_reset_date)
        VALUES (NEW.id, 0, now(), current_date)
        ON CONFLICT (user_id) DO UPDATE
        SET tokens_remaining = 0,
            last_reset = now(),
            last_reset_date = current_date,
            updated_at = now();
    EXCEPTION WHEN others THEN
        -- Just log the error and continue
        RAISE WARNING 'Error creating user_tokens for user ID %, Error: %', NEW.id, SQLERRM;
    END;

    -- Always return new to ensure the auth signup succeeds
    RETURN NEW;
END;
$$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.handle_new_user() TO service_role;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_user_tokens_user_id ON public.user_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_user_tokens_last_reset_date ON public.user_tokens(last_reset_date);
