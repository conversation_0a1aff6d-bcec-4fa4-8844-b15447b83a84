-- Add Whop-specific permission fields to trading competitions
-- This migration extends the competition system to support Whop community permissions

-- Add Whop-specific columns to paper_trading_competitions table
ALTER TABLE public.paper_trading_competitions 
ADD COLUMN IF NOT EXISTS whop_company_id TEXT,
ADD COLUMN IF NOT EXISTS whop_business_id TEXT,
ADD COLUMN IF NOT EXISTS whop_business_handle TEXT,
ADD COLUMN IF NOT EXISTS is_cross_community BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS allowed_whop_communities TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS competition_scope TEXT DEFAULT 'public' CHECK (competition_scope IN ('public', 'whop_local', 'whop_cross_community'));

-- Add indexes for Whop-related queries
CREATE INDEX IF NOT EXISTS idx_competitions_whop_company ON public.paper_trading_competitions(whop_company_id);
CREATE INDEX IF NOT EXISTS idx_competitions_whop_business ON public.paper_trading_competitions(whop_business_id);
CREATE INDEX IF NOT EXISTS idx_competitions_scope ON public.paper_trading_competitions(competition_scope);

-- Function to check if user is a Whop owner for a specific company
CREATE OR REPLACE FUNCTION public.is_whop_company_owner(
  p_user_id UUID,
  p_whop_company_id TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
  user_whop_id TEXT;
  user_access_level TEXT;
BEGIN
  -- Get user's Whop ID and access level from metadata
  SELECT 
    raw_user_meta_data->>'whop_user_id',
    raw_user_meta_data->>'whop_access_level'
  INTO user_whop_id, user_access_level
  FROM auth.users
  WHERE id = p_user_id;
  
  -- Return true if user has admin access level (indicating ownership)
  RETURN user_whop_id IS NOT NULL AND user_access_level = 'admin';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user belongs to a specific Whop community
CREATE OR REPLACE FUNCTION public.user_belongs_to_whop_community(
  p_user_id UUID,
  p_whop_company_id TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
  user_whop_id TEXT;
BEGIN
  -- Get user's Whop ID from metadata
  SELECT raw_user_meta_data->>'whop_user_id'
  INTO user_whop_id
  FROM auth.users
  WHERE id = p_user_id;
  
  -- For now, return true if user is a Whop user
  -- This will be enhanced with actual Whop API calls to verify membership
  RETURN user_whop_id IS NOT NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if business is the official Osis Whop
CREATE OR REPLACE FUNCTION public.is_official_osis_whop(
  p_whop_business_id TEXT DEFAULT NULL,
  p_whop_business_handle TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN (
    p_whop_business_id = 'biz_OGyv6Pz0Le35Fa' OR 
    p_whop_business_handle = 'tryosis'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate competition creation permissions
CREATE OR REPLACE FUNCTION public.validate_whop_competition_permissions(
  p_creator_id UUID,
  p_whop_company_id TEXT,
  p_whop_business_id TEXT,
  p_competition_scope TEXT,
  p_is_cross_community BOOLEAN DEFAULT FALSE
)
RETURNS TABLE(
  is_valid BOOLEAN,
  error_message TEXT
) AS $$
DECLARE
  is_whop_owner BOOLEAN;
  is_official_osis BOOLEAN;
BEGIN
  -- Check if creator is a Whop owner for the specified company
  SELECT public.is_whop_company_owner(p_creator_id, p_whop_company_id) INTO is_whop_owner;
  
  -- Check if this is the official Osis Whop
  SELECT public.is_official_osis_whop(p_whop_business_id) INTO is_official_osis;
  
  -- Validate permissions based on competition scope
  CASE p_competition_scope
    WHEN 'whop_local' THEN
      -- Local community competitions: Must be Whop owner
      IF NOT is_whop_owner THEN
        RETURN QUERY SELECT FALSE, 'Only Whop community owners can create local community competitions';
        RETURN;
      END IF;
      
    WHEN 'whop_cross_community' THEN
      -- Cross-community competitions: Only official Osis Whop owners
      IF NOT (is_whop_owner AND is_official_osis) THEN
        RETURN QUERY SELECT FALSE, 'Only official Osis Whop owners can create cross-community competitions';
        RETURN;
      END IF;
      
    WHEN 'public' THEN
      -- Public competitions: Anyone can create (existing behavior)
      NULL;
      
    ELSE
      RETURN QUERY SELECT FALSE, 'Invalid competition scope';
      RETURN;
  END CASE;
  
  -- If we reach here, permissions are valid
  RETURN QUERY SELECT TRUE, NULL::TEXT;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update RLS policies to include Whop permission checks
DROP POLICY IF EXISTS "Creators can manage their competitions" ON public.paper_trading_competitions;

CREATE POLICY "Creators can manage their competitions" ON public.paper_trading_competitions
  FOR ALL USING (
    auth.uid() = creator_id OR
    -- Allow Whop owners to manage competitions in their community
    (whop_company_id IS NOT NULL AND public.is_whop_company_owner(auth.uid(), whop_company_id))
  );

-- Policy for viewing competitions based on Whop membership
DROP POLICY IF EXISTS "Anyone can view open competitions" ON public.paper_trading_competitions;

CREATE POLICY "Anyone can view open competitions" ON public.paper_trading_competitions
  FOR SELECT USING (
    status IN ('open', 'active', 'completed') AND (
      -- Public competitions are visible to everyone
      competition_scope = 'public' OR
      -- Whop local competitions are visible to community members
      (competition_scope = 'whop_local' AND public.user_belongs_to_whop_community(auth.uid(), whop_company_id)) OR
      -- Cross-community competitions are visible to all Whop users
      (competition_scope = 'whop_cross_community' AND EXISTS (
        SELECT 1 FROM auth.users 
        WHERE id = auth.uid() AND raw_user_meta_data->>'whop_user_id' IS NOT NULL
      ))
    )
  );

-- Add trigger to validate Whop permissions before competition creation
CREATE OR REPLACE FUNCTION public.validate_competition_whop_permissions()
RETURNS TRIGGER AS $$
DECLARE
  validation_result RECORD;
BEGIN
  -- Only validate for Whop competitions
  IF NEW.competition_scope IN ('whop_local', 'whop_cross_community') THEN
    SELECT * INTO validation_result
    FROM public.validate_whop_competition_permissions(
      NEW.creator_id,
      NEW.whop_company_id,
      NEW.whop_business_id,
      NEW.competition_scope,
      NEW.is_cross_community
    );
    
    IF NOT validation_result.is_valid THEN
      RAISE EXCEPTION 'Permission denied: %', validation_result.error_message;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE TRIGGER validate_whop_competition_permissions_trigger
  BEFORE INSERT OR UPDATE ON public.paper_trading_competitions
  FOR EACH ROW
  EXECUTE FUNCTION public.validate_competition_whop_permissions();

-- Add comments for documentation
COMMENT ON COLUMN public.paper_trading_competitions.whop_company_id IS 'Whop company ID for community-restricted competitions';
COMMENT ON COLUMN public.paper_trading_competitions.whop_business_id IS 'Whop business ID for identifying official Osis Whop';
COMMENT ON COLUMN public.paper_trading_competitions.whop_business_handle IS 'Whop business handle for identifying official Osis Whop';
COMMENT ON COLUMN public.paper_trading_competitions.is_cross_community IS 'Whether competition allows participants from multiple Whop communities';
COMMENT ON COLUMN public.paper_trading_competitions.allowed_whop_communities IS 'Array of allowed Whop community IDs for cross-community competitions';
COMMENT ON COLUMN public.paper_trading_competitions.competition_scope IS 'Scope of competition: public, whop_local, or whop_cross_community';
