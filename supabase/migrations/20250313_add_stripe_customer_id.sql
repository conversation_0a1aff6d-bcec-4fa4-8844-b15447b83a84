-- Add stripe_customer_id column to subscriptions table
ALTER TABLE "public"."subscriptions" 
ADD COLUMN IF NOT EXISTS "stripe_customer_id" TEXT;

-- Add an index on the stripe_customer_id column for faster lookups
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_customer_id 
ON "public"."subscriptions" ("stripe_customer_id");

-- Comment on the column to document its purpose
COMMENT ON COLUMN "public"."subscriptions"."stripe_customer_id" 
IS 'The Stripe customer ID associated with this subscription'; 