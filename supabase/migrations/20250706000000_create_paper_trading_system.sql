-- Paper Trading System Database Schema
-- This creates a comprehensive paper trading system that can handle 100,000+ users

-- Paper Trading Accounts - Virtual trading accounts for users
CREATE TABLE IF NOT EXISTS public.paper_trading_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    account_name TEXT NOT NULL DEFAULT 'Default Account',
    starting_balance DECIMAL(15,2) NOT NULL DEFAULT 100000.00,
    current_balance DECIMAL(15,2) NOT NULL DEFAULT 100000.00,
    available_balance DECIMAL(15,2) NOT NULL DEFAULT 100000.00, -- Available for new trades
    portfolio_value DECIMAL(15,2) NOT NULL DEFAULT 0.00, -- Current value of all positions
    total_value DECIMAL(15,2) NOT NULL DEFAULT 100000.00, -- current_balance + portfolio_value
    unrealized_pnl DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    realized_pnl DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    total_pnl DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Ensure one active account per user (can be extended later for multiple accounts)
    UNIQUE(user_id, account_name)
);

-- Paper Trading Orders - All buy/sell orders
CREATE TABLE IF NOT EXISTS public.paper_trading_orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES public.paper_trading_accounts(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    symbol TEXT NOT NULL,
    order_type TEXT NOT NULL CHECK (order_type IN ('market', 'limit', 'stop', 'stop_limit')),
    side TEXT NOT NULL CHECK (side IN ('buy', 'sell')),
    quantity DECIMAL(15,4) NOT NULL CHECK (quantity > 0),
    price DECIMAL(15,4), -- For limit orders
    stop_price DECIMAL(15,4), -- For stop orders
    time_in_force TEXT NOT NULL DEFAULT 'DAY' CHECK (time_in_force IN ('DAY', 'GTC', 'IOC', 'FOK')),
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'filled', 'partially_filled', 'cancelled', 'rejected', 'expired')),
    filled_quantity DECIMAL(15,4) DEFAULT 0,
    avg_fill_price DECIMAL(15,4),
    total_value DECIMAL(15,2), -- Total value of the order when filled
    commission DECIMAL(10,2) DEFAULT 0.00, -- Simulated commission
    order_source TEXT DEFAULT 'web', -- web, mobile, api
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    filled_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ -- For DAY orders
);

-- Paper Trading Positions - Current holdings
CREATE TABLE IF NOT EXISTS public.paper_trading_positions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES public.paper_trading_accounts(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    symbol TEXT NOT NULL,
    quantity DECIMAL(15,4) NOT NULL CHECK (quantity != 0), -- Can be negative for short positions
    avg_cost DECIMAL(15,4) NOT NULL, -- Average cost basis
    market_price DECIMAL(15,4), -- Current market price (updated regularly)
    market_value DECIMAL(15,2), -- quantity * market_price
    unrealized_pnl DECIMAL(15,2), -- (market_price - avg_cost) * quantity
    unrealized_pnl_percent DECIMAL(8,4), -- unrealized_pnl / (avg_cost * abs(quantity)) * 100
    day_change DECIMAL(15,2) DEFAULT 0.00, -- Today's P&L change
    day_change_percent DECIMAL(8,4) DEFAULT 0.00,
    total_cost DECIMAL(15,2), -- Total amount invested (avg_cost * abs(quantity))
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    last_price_update TIMESTAMPTZ,
    
    -- Unique position per symbol per account
    UNIQUE(account_id, symbol)
);

-- Paper Trading Transactions - Audit trail of all transactions
CREATE TABLE IF NOT EXISTS public.paper_trading_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES public.paper_trading_accounts(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    order_id UUID REFERENCES public.paper_trading_orders(id) ON DELETE SET NULL,
    symbol TEXT NOT NULL,
    transaction_type TEXT NOT NULL CHECK (transaction_type IN ('buy', 'sell', 'dividend', 'split', 'fee', 'deposit', 'withdrawal')),
    quantity DECIMAL(15,4), -- Can be null for fees/deposits
    price DECIMAL(15,4), -- Execution price
    amount DECIMAL(15,2) NOT NULL, -- Total transaction amount (positive for credits, negative for debits)
    commission DECIMAL(10,2) DEFAULT 0.00,
    description TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Paper Trading Performance Metrics - Daily/historical performance tracking
CREATE TABLE IF NOT EXISTS public.paper_trading_performance (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES public.paper_trading_accounts(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    starting_balance DECIMAL(15,2) NOT NULL,
    ending_balance DECIMAL(15,2) NOT NULL,
    portfolio_value DECIMAL(15,2) NOT NULL,
    total_value DECIMAL(15,2) NOT NULL,
    daily_pnl DECIMAL(15,2) NOT NULL,
    daily_pnl_percent DECIMAL(8,4) NOT NULL,
    cumulative_pnl DECIMAL(15,2) NOT NULL,
    cumulative_pnl_percent DECIMAL(8,4) NOT NULL,
    trades_count INTEGER DEFAULT 0,
    winning_trades INTEGER DEFAULT 0,
    losing_trades INTEGER DEFAULT 0,
    win_rate DECIMAL(5,2) DEFAULT 0.00,
    largest_win DECIMAL(15,2) DEFAULT 0.00,
    largest_loss DECIMAL(15,2) DEFAULT 0.00,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Unique performance record per account per date
    UNIQUE(account_id, date)
);

-- Paper Trading Watchlists - User's watchlists for paper trading
CREATE TABLE IF NOT EXISTS public.paper_trading_watchlists (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES public.paper_trading_accounts(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL DEFAULT 'Default Watchlist',
    symbols TEXT[] NOT NULL DEFAULT '{}', -- Array of stock symbols
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for optimal performance with 100,000+ users
CREATE INDEX IF NOT EXISTS idx_paper_accounts_user_id ON public.paper_trading_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_paper_accounts_active ON public.paper_trading_accounts(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_paper_orders_account_id ON public.paper_trading_orders(account_id);
CREATE INDEX IF NOT EXISTS idx_paper_orders_user_id ON public.paper_trading_orders(user_id);
CREATE INDEX IF NOT EXISTS idx_paper_orders_symbol ON public.paper_trading_orders(symbol);
CREATE INDEX IF NOT EXISTS idx_paper_orders_status ON public.paper_trading_orders(status);
CREATE INDEX IF NOT EXISTS idx_paper_orders_created_at ON public.paper_trading_orders(created_at);
CREATE INDEX IF NOT EXISTS idx_paper_orders_pending ON public.paper_trading_orders(status, created_at) WHERE status IN ('pending', 'partially_filled');

CREATE INDEX IF NOT EXISTS idx_paper_positions_account_id ON public.paper_trading_positions(account_id);
CREATE INDEX IF NOT EXISTS idx_paper_positions_user_id ON public.paper_trading_positions(user_id);
CREATE INDEX IF NOT EXISTS idx_paper_positions_symbol ON public.paper_trading_positions(symbol);
CREATE INDEX IF NOT EXISTS idx_paper_positions_updated_at ON public.paper_trading_positions(updated_at);

CREATE INDEX IF NOT EXISTS idx_paper_transactions_account_id ON public.paper_trading_transactions(account_id);
CREATE INDEX IF NOT EXISTS idx_paper_transactions_user_id ON public.paper_trading_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_paper_transactions_symbol ON public.paper_trading_transactions(symbol);
CREATE INDEX IF NOT EXISTS idx_paper_transactions_type ON public.paper_trading_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_paper_transactions_created_at ON public.paper_trading_transactions(created_at);

CREATE INDEX IF NOT EXISTS idx_paper_performance_account_id ON public.paper_trading_performance(account_id);
CREATE INDEX IF NOT EXISTS idx_paper_performance_user_id ON public.paper_trading_performance(user_id);
CREATE INDEX IF NOT EXISTS idx_paper_performance_date ON public.paper_trading_performance(date);

CREATE INDEX IF NOT EXISTS idx_paper_watchlists_account_id ON public.paper_trading_watchlists(account_id);
CREATE INDEX IF NOT EXISTS idx_paper_watchlists_user_id ON public.paper_trading_watchlists(user_id);

-- Enable Row Level Security (RLS) for all tables
ALTER TABLE public.paper_trading_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.paper_trading_orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.paper_trading_positions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.paper_trading_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.paper_trading_performance ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.paper_trading_watchlists ENABLE ROW LEVEL SECURITY;

-- Create RLS policies to ensure users can only access their own data
CREATE POLICY "Users can only access their own paper trading accounts" ON public.paper_trading_accounts
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can only access their own paper trading orders" ON public.paper_trading_orders
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can only access their own paper trading positions" ON public.paper_trading_positions
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can only access their own paper trading transactions" ON public.paper_trading_transactions
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can only access their own paper trading performance" ON public.paper_trading_performance
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can only access their own paper trading watchlists" ON public.paper_trading_watchlists
    FOR ALL USING (auth.uid() = user_id);

-- Function to automatically create a paper trading account for new users
CREATE OR REPLACE FUNCTION public.create_paper_trading_account()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.paper_trading_accounts (user_id, account_name, starting_balance, current_balance, available_balance, total_value)
    VALUES (NEW.id, 'Default Account', 100000.00, 100000.00, 100000.00, 100000.00);

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create paper trading account when user signs up
CREATE OR REPLACE TRIGGER create_paper_trading_account_trigger
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION public.create_paper_trading_account();

-- Function to update account balances when positions change
CREATE OR REPLACE FUNCTION public.update_account_balances()
RETURNS TRIGGER AS $$
DECLARE
    account_record RECORD;
    total_portfolio_value DECIMAL(15,2);
    total_unrealized_pnl DECIMAL(15,2);
BEGIN
    -- Get the account ID from either NEW or OLD record
    IF TG_OP = 'DELETE' THEN
        SELECT * INTO account_record FROM public.paper_trading_accounts WHERE id = OLD.account_id;
    ELSE
        SELECT * INTO account_record FROM public.paper_trading_accounts WHERE id = NEW.account_id;
    END IF;

    -- Calculate total portfolio value and unrealized P&L
    SELECT
        COALESCE(SUM(market_value), 0),
        COALESCE(SUM(unrealized_pnl), 0)
    INTO total_portfolio_value, total_unrealized_pnl
    FROM public.paper_trading_positions
    WHERE account_id = account_record.id;

    -- Update account balances
    UPDATE public.paper_trading_accounts
    SET
        portfolio_value = total_portfolio_value,
        total_value = current_balance + total_portfolio_value,
        unrealized_pnl = total_unrealized_pnl,
        total_pnl = realized_pnl + total_unrealized_pnl,
        updated_at = NOW()
    WHERE id = account_record.id;

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Triggers to update account balances when positions change
CREATE OR REPLACE TRIGGER update_account_balances_on_position_change
    AFTER INSERT OR UPDATE OR DELETE ON public.paper_trading_positions
    FOR EACH ROW
    EXECUTE FUNCTION public.update_account_balances();

-- Function to update position market values when prices change
CREATE OR REPLACE FUNCTION public.update_position_market_values(
    p_symbol TEXT,
    p_market_price DECIMAL(15,4)
)
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER := 0;
BEGIN
    UPDATE public.paper_trading_positions
    SET
        market_price = p_market_price,
        market_value = quantity * p_market_price,
        unrealized_pnl = (p_market_price - avg_cost) * quantity,
        unrealized_pnl_percent = CASE
            WHEN avg_cost > 0 THEN ((p_market_price - avg_cost) / avg_cost) * 100
            ELSE 0
        END,
        last_price_update = NOW(),
        updated_at = NOW()
    WHERE symbol = p_symbol;

    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update account balance (used by order processor)
CREATE OR REPLACE FUNCTION public.update_account_balance(
    p_account_id UUID,
    p_balance_change DECIMAL(15,2),
    p_is_realized BOOLEAN DEFAULT FALSE
)
RETURNS VOID AS $$
BEGIN
    UPDATE public.paper_trading_accounts
    SET
        current_balance = current_balance + p_balance_change,
        available_balance = available_balance + p_balance_change,
        realized_pnl = CASE
            WHEN p_is_realized THEN realized_pnl + p_balance_change
            ELSE realized_pnl
        END,
        updated_at = NOW()
    WHERE id = p_account_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to calculate daily performance metrics
CREATE OR REPLACE FUNCTION public.calculate_daily_performance()
RETURNS INTEGER AS $$
DECLARE
    account_record RECORD;
    performance_date DATE := CURRENT_DATE;
    records_created INTEGER := 0;
BEGIN
    -- Loop through all active accounts
    FOR account_record IN
        SELECT * FROM public.paper_trading_accounts WHERE is_active = true
    LOOP
        -- Insert or update daily performance record
        INSERT INTO public.paper_trading_performance (
            account_id,
            user_id,
            date,
            starting_balance,
            ending_balance,
            portfolio_value,
            total_value,
            daily_pnl,
            daily_pnl_percent,
            cumulative_pnl,
            cumulative_pnl_percent
        ) VALUES (
            account_record.id,
            account_record.user_id,
            performance_date,
            account_record.starting_balance,
            account_record.current_balance,
            account_record.portfolio_value,
            account_record.total_value,
            account_record.total_value - account_record.starting_balance, -- Simplified daily P&L
            ((account_record.total_value - account_record.starting_balance) / account_record.starting_balance) * 100,
            account_record.total_pnl,
            (account_record.total_pnl / account_record.starting_balance) * 100
        )
        ON CONFLICT (account_id, date)
        DO UPDATE SET
            ending_balance = EXCLUDED.ending_balance,
            portfolio_value = EXCLUDED.portfolio_value,
            total_value = EXCLUDED.total_value,
            daily_pnl = EXCLUDED.daily_pnl,
            daily_pnl_percent = EXCLUDED.daily_pnl_percent,
            cumulative_pnl = EXCLUDED.cumulative_pnl,
            cumulative_pnl_percent = EXCLUDED.cumulative_pnl_percent;

        records_created := records_created + 1;
    END LOOP;

    RETURN records_created;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up old data (for maintenance)
CREATE OR REPLACE FUNCTION public.cleanup_paper_trading_data()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
    cutoff_date TIMESTAMPTZ := NOW() - INTERVAL '1 year';
BEGIN
    -- Delete old filled/cancelled orders (keep for 1 year)
    DELETE FROM public.paper_trading_orders
    WHERE status IN ('filled', 'cancelled', 'expired', 'rejected')
    AND created_at < cutoff_date;

    GET DIAGNOSTICS deleted_count = ROW_COUNT;

    -- Delete old transactions (keep for 1 year)
    DELETE FROM public.paper_trading_transactions
    WHERE created_at < cutoff_date;

    -- Delete old performance records (keep for 1 year)
    DELETE FROM public.paper_trading_performance
    WHERE date < (CURRENT_DATE - INTERVAL '1 year');

    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create paper trading accounts for existing users
CREATE OR REPLACE FUNCTION public.create_paper_accounts_for_existing_users()
RETURNS INTEGER AS $$
DECLARE
    user_record RECORD;
    accounts_created INTEGER := 0;
BEGIN
    -- Loop through all users who don't have paper trading accounts
    FOR user_record IN
        SELECT u.id
        FROM auth.users u
        LEFT JOIN public.paper_trading_accounts pta ON u.id = pta.user_id
        WHERE pta.user_id IS NULL
    LOOP
        -- Create paper trading account for this user
        INSERT INTO public.paper_trading_accounts (
            user_id,
            account_name,
            starting_balance,
            current_balance,
            available_balance,
            total_value
        ) VALUES (
            user_record.id,
            'Default Account',
            100000.00,
            100000.00,
            100000.00,
            100000.00
        );

        accounts_created := accounts_created + 1;
    END LOOP;

    RETURN accounts_created;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create accounts for all existing users (run this once)
SELECT public.create_paper_accounts_for_existing_users();
