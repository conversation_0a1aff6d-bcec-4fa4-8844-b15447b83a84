-- Create the subscription_type enum if it doesn't exist
DO $$ BEGIN
    CREATE TYPE subscription_type AS ENUM ('basic', 'pro', 'premium');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Add subscription_type column to profiles table if it doesn't exist
ALTER TABLE profiles
ADD COLUMN IF NOT EXISTS subscription_type subscription_type;

-- Add subscription_type column to subscriptions table if it doesn't exist
ALTER TABLE subscriptions
ADD COLUMN IF NOT EXISTS subscription_type subscription_type;

-- Add comments for documentation
COMMENT ON COLUMN profiles.subscription_type IS 'User subscription plan type';
COMMENT ON COLUMN subscriptions.subscription_type IS 'Subscription plan type';