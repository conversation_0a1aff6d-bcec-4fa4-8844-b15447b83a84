-- Create scan_jobs table for tracking async scan progress
CREATE TABLE IF NOT EXISTS scan_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    agent_id UUID NOT NULL,
    market_index TEXT NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('pending', 'running', 'completed', 'failed')),
    progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    total_stocks INTEGER DEFAULT 0,
    processed_stocks INTEGER DEFAULT 0,
    results_count INTEGER DEFAULT 0,
    started_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create scan_results table for storing individual scan results
CREATE TABLE IF NOT EXISTS scan_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    scan_job_id UUID NOT NULL REFERENCES scan_jobs(id) ON DELETE CASCADE,
    symbol TEXT NOT NULL,
    signal TEXT NOT NULL,
    confidence INTEGER NOT NULL,
    price DECIMAL(10,2),
    change DECIMAL(10,2),
    percent_change DECIMAL(5,2),
    is_enhanced BOOLEAN DEFAULT FALSE,
    risk_management JSONB,
    advanced_analysis JSONB,
    signal_quality JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_scan_jobs_user_id ON scan_jobs(user_id);
CREATE INDEX IF NOT EXISTS idx_scan_jobs_status ON scan_jobs(status);
CREATE INDEX IF NOT EXISTS idx_scan_jobs_created_at ON scan_jobs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_scan_results_scan_job_id ON scan_results(scan_job_id);
CREATE INDEX IF NOT EXISTS idx_scan_results_symbol ON scan_results(symbol);
CREATE INDEX IF NOT EXISTS idx_scan_results_confidence ON scan_results(confidence DESC);

-- Enable RLS (Row Level Security)
ALTER TABLE scan_jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE scan_results ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for scan_jobs
CREATE POLICY "Users can view their own scan jobs" ON scan_jobs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own scan jobs" ON scan_jobs
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own scan jobs" ON scan_jobs
    FOR UPDATE USING (auth.uid() = user_id);

-- Create RLS policies for scan_results
CREATE POLICY "Users can view results from their own scan jobs" ON scan_results
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM scan_jobs 
            WHERE scan_jobs.id = scan_results.scan_job_id 
            AND scan_jobs.user_id = auth.uid()
        )
    );

CREATE POLICY "Service role can insert scan results" ON scan_results
    FOR INSERT WITH CHECK (true);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_scan_jobs_updated_at 
    BEFORE UPDATE ON scan_jobs 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions to service role
GRANT ALL ON scan_jobs TO service_role;
GRANT ALL ON scan_results TO service_role;
