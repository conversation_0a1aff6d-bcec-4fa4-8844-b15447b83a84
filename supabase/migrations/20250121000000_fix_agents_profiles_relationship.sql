-- Fix agents and profiles relationship issue
-- This migration addresses the "Could not find a relationship between 'agents' and 'profiles'" error

-- The issue is that there's no direct foreign key relationship between agents and profiles tables.
-- Both tables reference auth.users, but there's no direct relationship between them.
-- The solution is to use manual joins in queries instead of relying on Supabase's automatic relationship detection.

-- This migration focuses on optimizing the join performance with proper indexes

-- Create indexes to improve join performance if they don't exist
CREATE INDEX IF NOT EXISTS idx_agents_user_id ON public.agents(user_id);
CREATE INDEX IF NOT EXISTS idx_profiles_id ON public.profiles(id);

-- Add comments to document the relationship pattern
COMMENT ON TABLE public.agents IS 'User-created trading agents. Join with profiles via agents.user_id = profiles.id';
COMMENT ON TABLE public.profiles IS 'User profiles. Join with agents via profiles.id = agents.user_id';

-- Add column comments to document the join pattern
COMMENT ON COLUMN public.agents.user_id IS 'References auth.users.id - join with profiles.id for user data';
COMMENT ON COLUMN public.profiles.id IS 'Primary key referencing auth.users.id - join with agents.user_id for agent data';
