-- Admin User System Migration
-- This migration implements comprehensive admin functionality including user roles, agent verification, and content moderation

-- Add admin role to profiles table
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS role TEXT DEFAULT 'user' CHECK (role IN ('user', 'admin')),
ADD COLUMN IF NOT EXISTS is_banned BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS banned_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS banned_reason TEXT,
ADD COLUMN IF NOT EXISTS banned_by UUID REFERENCES auth.users(id);

-- Add verification system to agents table
ALTER TABLE public.agents 
ADD COLUMN IF NOT EXISTS is_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS verified_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS verified_by UUID REFERENCES auth.users(id),
ADD COLUMN IF NOT EXISTS verification_notes TEXT;

-- Create admin action logs table
CREATE TABLE IF NOT EXISTS public.admin_action_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  admin_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  action_type TEXT NOT NULL CHECK (action_type IN ('verify_agent', 'unverify_agent', 'ban_user', 'unban_user', 'delete_agent', 'content_filter')),
  target_type TEXT NOT NULL CHECK (target_type IN ('user', 'agent')),
  target_id UUID NOT NULL,
  details JSONB DEFAULT '{}',
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create user bans table for detailed ban management
CREATE TABLE IF NOT EXISTS public.user_bans (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  banned_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  reason TEXT NOT NULL,
  ban_type TEXT DEFAULT 'marketplace' CHECK (ban_type IN ('marketplace', 'full', 'temporary')),
  expires_at TIMESTAMP WITH TIME ZONE, -- NULL for permanent bans
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create bad words filter table
CREATE TABLE IF NOT EXISTS public.bad_words_filter (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  word TEXT NOT NULL UNIQUE,
  severity TEXT DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high')),
  category TEXT DEFAULT 'general',
  is_active BOOLEAN DEFAULT TRUE,
  added_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_profiles_role ON public.profiles(role);
CREATE INDEX IF NOT EXISTS idx_profiles_is_banned ON public.profiles(is_banned);
CREATE INDEX IF NOT EXISTS idx_agents_is_verified ON public.agents(is_verified);
CREATE INDEX IF NOT EXISTS idx_agents_verified_at ON public.agents(verified_at);
CREATE INDEX IF NOT EXISTS idx_admin_action_logs_admin_id ON public.admin_action_logs(admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_action_logs_action_type ON public.admin_action_logs(action_type);
CREATE INDEX IF NOT EXISTS idx_admin_action_logs_created_at ON public.admin_action_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_user_bans_user_id ON public.user_bans(user_id);
CREATE INDEX IF NOT EXISTS idx_user_bans_is_active ON public.user_bans(is_active);
CREATE INDEX IF NOT EXISTS idx_bad_words_filter_word ON public.bad_words_filter(word);
CREATE INDEX IF NOT EXISTS idx_bad_words_filter_is_active ON public.bad_words_filter(is_active);

-- Set admin privileges for specified users
UPDATE public.profiles 
SET role = 'admin' 
WHERE email IN ('<EMAIL>', '<EMAIL>');

-- Insert default bad words (basic set - can be expanded)
INSERT INTO public.bad_words_filter (word, severity, category) VALUES
('spam', 'medium', 'general'),
('scam', 'high', 'fraud'),
('fake', 'medium', 'misleading'),
('cheat', 'medium', 'misleading'),
('hack', 'medium', 'security'),
('exploit', 'medium', 'security'),
('guaranteed', 'medium', 'misleading'),
('100%', 'medium', 'misleading'),
('risk-free', 'high', 'misleading'),
('get rich quick', 'high', 'misleading')
ON CONFLICT (word) DO NOTHING;

-- RLS Policies for admin tables
ALTER TABLE public.admin_action_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_bans ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bad_words_filter ENABLE ROW LEVEL SECURITY;

-- Admin action logs - only admins can view and create
CREATE POLICY "Admins can view all action logs" ON public.admin_action_logs
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

CREATE POLICY "Admins can create action logs" ON public.admin_action_logs
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

-- User bans - only admins can manage
CREATE POLICY "Admins can view all user bans" ON public.user_bans
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

CREATE POLICY "Admins can manage user bans" ON public.user_bans
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

-- Bad words filter - admins can manage, service role can read for filtering
CREATE POLICY "Admins can manage bad words filter" ON public.bad_words_filter
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

CREATE POLICY "Service role can read bad words filter" ON public.bad_words_filter
  FOR SELECT TO service_role USING (true);

-- Functions for admin operations
CREATE OR REPLACE FUNCTION public.is_admin(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.profiles
    WHERE profiles.id = user_id AND profiles.role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is banned from marketplace
CREATE OR REPLACE FUNCTION public.is_user_banned(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.user_bans
    WHERE user_bans.user_id = user_id
    AND user_bans.is_active = true
    AND (user_bans.expires_at IS NULL OR user_bans.expires_at > NOW())
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log admin actions
CREATE OR REPLACE FUNCTION public.log_admin_action(
  p_action_type TEXT,
  p_target_type TEXT,
  p_target_id UUID,
  p_details JSONB DEFAULT '{}',
  p_notes TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  v_log_id UUID;
BEGIN
  -- Check if user is admin
  IF NOT public.is_admin() THEN
    RAISE EXCEPTION 'Access denied. Admin role required.';
  END IF;

  INSERT INTO public.admin_action_logs (
    admin_id, action_type, target_type, target_id, details, notes
  ) VALUES (
    auth.uid(), p_action_type, p_target_type, p_target_id, p_details, p_notes
  ) RETURNING id INTO v_log_id;

  RETURN v_log_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check content for bad words
CREATE OR REPLACE FUNCTION public.check_content_filter(
  p_content TEXT
)
RETURNS JSONB AS $$
DECLARE
  v_bad_word RECORD;
  v_violations JSONB := '[]'::JSONB;
  v_content_lower TEXT;
BEGIN
  v_content_lower := LOWER(p_content);

  -- Check against bad words filter
  FOR v_bad_word IN
    SELECT word, severity, category
    FROM public.bad_words_filter
    WHERE is_active = true
  LOOP
    IF v_content_lower LIKE '%' || LOWER(v_bad_word.word) || '%' THEN
      v_violations := v_violations || jsonb_build_object(
        'word', v_bad_word.word,
        'severity', v_bad_word.severity,
        'category', v_bad_word.category
      );
    END IF;
  END LOOP;

  RETURN jsonb_build_object(
    'has_violations', jsonb_array_length(v_violations) > 0,
    'violations', v_violations,
    'violation_count', jsonb_array_length(v_violations)
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to verify an agent (admin only)
CREATE OR REPLACE FUNCTION public.verify_agent(
  p_agent_id UUID,
  p_notes TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if user is admin
  IF NOT public.is_admin() THEN
    RAISE EXCEPTION 'Access denied. Admin role required.';
  END IF;

  -- Update agent verification status
  UPDATE public.agents
  SET
    is_verified = true,
    verified_at = NOW(),
    verified_by = auth.uid(),
    verification_notes = p_notes,
    updated_at = NOW()
  WHERE id = p_agent_id;

  -- Log the action
  PERFORM public.log_admin_action(
    'verify_agent',
    'agent',
    p_agent_id,
    jsonb_build_object('notes', p_notes)
  );

  RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to unverify an agent (admin only)
CREATE OR REPLACE FUNCTION public.unverify_agent(
  p_agent_id UUID,
  p_notes TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if user is admin
  IF NOT public.is_admin() THEN
    RAISE EXCEPTION 'Access denied. Admin role required.';
  END IF;

  -- Update agent verification status
  UPDATE public.agents
  SET
    is_verified = false,
    verified_at = NULL,
    verified_by = NULL,
    verification_notes = p_notes,
    updated_at = NOW()
  WHERE id = p_agent_id;

  -- Log the action
  PERFORM public.log_admin_action(
    'unverify_agent',
    'agent',
    p_agent_id,
    jsonb_build_object('notes', p_notes)
  );

  RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to ban a user (admin only)
CREATE OR REPLACE FUNCTION public.ban_user(
  p_user_id UUID,
  p_reason TEXT,
  p_ban_type TEXT DEFAULT 'marketplace',
  p_expires_at TIMESTAMP WITH TIME ZONE DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if user is admin
  IF NOT public.is_admin() THEN
    RAISE EXCEPTION 'Access denied. Admin role required.';
  END IF;

  -- Deactivate any existing bans for this user
  UPDATE public.user_bans
  SET is_active = false, updated_at = NOW()
  WHERE user_id = p_user_id AND is_active = true;

  -- Create new ban record
  INSERT INTO public.user_bans (
    user_id, banned_by, reason, ban_type, expires_at
  ) VALUES (
    p_user_id, auth.uid(), p_reason, p_ban_type, p_expires_at
  );

  -- Update profile ban status
  UPDATE public.profiles
  SET
    is_banned = true,
    banned_at = NOW(),
    banned_reason = p_reason,
    banned_by = auth.uid(),
    updated_at = NOW()
  WHERE id = p_user_id;

  -- Log the action
  PERFORM public.log_admin_action(
    'ban_user',
    'user',
    p_user_id,
    jsonb_build_object(
      'reason', p_reason,
      'ban_type', p_ban_type,
      'expires_at', p_expires_at
    )
  );

  RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to unban a user (admin only)
CREATE OR REPLACE FUNCTION public.unban_user(
  p_user_id UUID,
  p_notes TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if user is admin
  IF NOT public.is_admin() THEN
    RAISE EXCEPTION 'Access denied. Admin role required.';
  END IF;

  -- Deactivate all active bans for this user
  UPDATE public.user_bans
  SET is_active = false, updated_at = NOW()
  WHERE user_id = p_user_id AND is_active = true;

  -- Update profile ban status
  UPDATE public.profiles
  SET
    is_banned = false,
    banned_at = NULL,
    banned_reason = NULL,
    banned_by = NULL,
    updated_at = NOW()
  WHERE id = p_user_id;

  -- Log the action
  PERFORM public.log_admin_action(
    'unban_user',
    'user',
    p_user_id,
    jsonb_build_object('notes', p_notes)
  );

  RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to delete an agent (admin only)
CREATE OR REPLACE FUNCTION public.admin_delete_agent(
  p_agent_id UUID,
  p_reason TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if user is admin
  IF NOT public.is_admin() THEN
    RAISE EXCEPTION 'Access denied. Admin role required.';
  END IF;

  -- Log the action before deletion
  PERFORM public.log_admin_action(
    'delete_agent',
    'agent',
    p_agent_id,
    jsonb_build_object('reason', p_reason)
  );

  -- Delete the agent (cascading deletes will handle related records)
  DELETE FROM public.agents WHERE id = p_agent_id;

  RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.is_admin(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_user_banned(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.check_content_filter(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.log_admin_action(TEXT, TEXT, UUID, JSONB, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.verify_agent(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.unverify_agent(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.ban_user(UUID, TEXT, TEXT, TIMESTAMP WITH TIME ZONE) TO authenticated;
GRANT EXECUTE ON FUNCTION public.unban_user(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.admin_delete_agent(UUID, TEXT) TO authenticated;
