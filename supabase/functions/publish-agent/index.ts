// Publish Agent Edge Function
// This function handles publishing user agents to the marketplace

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS'
};

// Handle OPTIONS requests for CORS
function handleOptions() {
  return new Response(null, {
    status: 204,
    headers: corsHeaders
  });
}

interface PublishAgentRequest {
  agentId: string;
  name: string;
  description?: string;
  category: string;
  tags?: string[];
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return handleOptions();
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('No authorization header provided');
    }

    // Verify user authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    );

    if (authError || !user) {
      throw new Error('Invalid authentication token');
    }

    // Parse request body
    const requestData: PublishAgentRequest = await req.json();
    
    // Validate required fields
    if (!requestData.agentId || !requestData.name || !requestData.category) {
      throw new Error('Missing required fields: agentId, name, and category are required');
    }

    console.log(`Publishing agent ${requestData.agentId} for user ${user.id}`);

    // Verify the agent exists and belongs to the user
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('*')
      .eq('id', requestData.agentId)
      .eq('user_id', user.id)
      .single();

    if (agentError || !agent) {
      throw new Error('Agent not found or you do not have permission to publish it');
    }

    // Verify the category exists
    const { data: category, error: categoryError } = await supabase
      .from('agent_categories')
      .select('name')
      .eq('name', requestData.category)
      .eq('is_active', true)
      .single();

    if (categoryError || !category) {
      throw new Error('Invalid category specified');
    }

    // Check if agent is already published by this user
    const { data: existingPublished, error: existingError } = await supabase
      .from('published_agents')
      .select('id')
      .eq('agent_id', requestData.agentId)
      .eq('publisher_id', user.id)
      .maybeSingle();

    if (existingError && existingError.code !== 'PGRST116') {
      throw new Error('Error checking existing published agent');
    }

    let publishedAgent;

    if (existingPublished) {
      // Update existing published agent and reactivate if needed
      const { data: updatedAgent, error: updateError } = await supabase
        .from('published_agents')
        .update({
          name: requestData.name,
          description: requestData.description || null,
          category: requestData.category,
          tags: requestData.tags || [],
          is_active: true, // Ensure it's active when republishing
          updated_at: new Date().toISOString()
        })
        .eq('id', existingPublished.id)
        .select('*')
        .single();

      if (updateError) {
        throw new Error(`Failed to update published agent: ${updateError.message}`);
      }

      publishedAgent = updatedAgent;
      console.log(`Updated published agent ${existingPublished.id}`);
    } else {
      // Create new published agent
      const { data: newAgent, error: insertError } = await supabase
        .from('published_agents')
        .insert({
          agent_id: requestData.agentId,
          publisher_id: user.id,
          name: requestData.name,
          description: requestData.description || null,
          category: requestData.category,
          tags: requestData.tags || [],
          is_active: true
        })
        .select('*')
        .single();

      if (insertError) {
        throw new Error(`Failed to publish agent: ${insertError.message}`);
      }

      publishedAgent = newAgent;
      console.log(`Created new published agent ${newAgent.id}`);

      // Track public agent creation in gamification system for new publications
      try {
        console.log('🎯 Tracking public agent creation for user:', user.id);

        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('headquarters_progress')
          .eq('id', user.id)
          .single();

        if (profileError) {
          console.error('❌ Error fetching profile:', profileError);
        } else if (profile?.headquarters_progress) {
          const progress = profile.headquarters_progress;
          console.log('📈 Current progress before update:', progress);

          const oldPublicAgentCount = progress.publicAgentsCreated || 0;
          progress.publicAgentsCreated = oldPublicAgentCount + 1;

          console.log(`📊 Public agent count: ${oldPublicAgentCount} → ${progress.publicAgentsCreated}`);

          // Mark first public agent as completed
          if (!progress.hasCreatedFirstPublicAgent) {
            progress.hasCreatedFirstPublicAgent = true;
            console.log('🏁 Setting hasCreatedFirstPublicAgent to true');
          }

          console.log('💾 Saving updated progress:', progress);

          // Save updated progress
          const { error: updateError } = await supabase
            .from('profiles')
            .update({ headquarters_progress: progress })
            .eq('id', user.id);

          if (updateError) {
            console.error('❌ Error updating progress:', updateError);
          } else {
            console.log('✅ Public agent creation tracked in gamification system successfully');
          }
        } else {
          console.log('⚠️ No headquarters_progress found in profile');
        }
      } catch (gamificationError) {
        console.error('❌ Error tracking public agent creation:', gamificationError);
        // Don't fail the agent publication if gamification tracking fails
      }
    }

    // SYNCHRONIZATION FIX: Ensure the base agent is marked as public when published
    console.log('🔄 Synchronizing agent public status...');
    const { error: syncError } = await supabase
      .from('agents')
      .update({
        is_public: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', requestData.agentId)
      .eq('user_id', user.id);

    if (syncError) {
      console.warn('⚠️ Failed to sync agent public status:', syncError);
      // Don't fail the whole operation, but log the warning
    } else {
      console.log('✅ Agent public status synchronized successfully');
    }

    // Return success response
    return new Response(
      JSON.stringify({
        success: true,
        publishedAgent: publishedAgent,
        message: existingPublished ? 'Agent updated successfully' : 'Agent published successfully'
      }),
      {
        status: 200,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    );

  } catch (error) {
    console.error('Error in publish-agent function:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'An unexpected error occurred'
      }),
      {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    );
  }
});
