import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface StockDetailRequest {
  symbol: string;
}

interface StockDetailResponse {
  ticker: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap?: number;
  peRatio?: number;
  week52High?: number;
  week52Low?: number;
  avgVolume?: number;
  sector?: string;
  description?: string;
  employees?: number;
  headquarters?: string;
  website?: string;
  ceo?: string;
  founded?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const POLYGON_API_KEY = Deno.env.get('POLYGON_API_KEY')
    if (!POLYGON_API_KEY) {
      throw new Error('POLYGON_API_KEY environment variable is not set')
    }

    const { symbol }: StockDetailRequest = await req.json()
    
    if (!symbol) {
      return new Response(
        JSON.stringify({ error: 'Symbol is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const upperSymbol = symbol.toUpperCase()
    console.log(`Fetching stock detail for: ${upperSymbol}`)

    // Get current price and basic data
    const priceUrl = `https://api.polygon.io/v2/aggs/ticker/${upperSymbol}/prev?adjusted=true&apiKey=${POLYGON_API_KEY}`
    const priceResponse = await fetch(priceUrl)
    const priceData = await priceResponse.json()

    if (!priceData.results || priceData.results.length === 0) {
      return new Response(
        JSON.stringify({ error: 'Stock data not found' }),
        { 
          status: 404, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const prevData = priceData.results[0]
    
    // Get current/latest price
    const latestUrl = `https://api.polygon.io/v1/last/stocks/${upperSymbol}?apiKey=${POLYGON_API_KEY}`
    let currentPrice = prevData.c // fallback to previous close
    
    try {
      const latestResponse = await fetch(latestUrl)
      const latestData = await latestResponse.json()
      if (latestData.last && latestData.last.price) {
        currentPrice = latestData.last.price
      }
    } catch (error) {
      console.log('Could not fetch latest price, using previous close:', error)
    }

    // Calculate change
    const change = currentPrice - prevData.c
    const changePercent = (change / prevData.c) * 100

    // Get company details
    const detailsUrl = `https://api.polygon.io/v3/reference/tickers/${upperSymbol}?apiKey=${POLYGON_API_KEY}`
    let companyDetails: any = {}
    
    try {
      const detailsResponse = await fetch(detailsUrl)
      const detailsData = await detailsResponse.json()
      if (detailsData.results) {
        companyDetails = detailsData.results
      }
    } catch (error) {
      console.log('Could not fetch company details:', error)
    }

    // Get 52-week high/low and other stats
    const aggregatesUrl = `https://api.polygon.io/v2/aggs/ticker/${upperSymbol}/range/1/day/${getDateOneYearAgo()}/${getCurrentDate()}?adjusted=true&sort=asc&limit=50000&apiKey=${POLYGON_API_KEY}`
    let week52High: number | undefined
    let week52Low: number | undefined
    let avgVolume: number | undefined

    try {
      const aggregatesResponse = await fetch(aggregatesUrl)
      const aggregatesData = await aggregatesResponse.json()
      
      if (aggregatesData.results && aggregatesData.results.length > 0) {
        const results = aggregatesData.results
        week52High = Math.max(...results.map((r: any) => r.h))
        week52Low = Math.min(...results.map((r: any) => r.l))
        
        // Calculate average volume over the period
        const totalVolume = results.reduce((sum: number, r: any) => sum + (r.v || 0), 0)
        avgVolume = totalVolume / results.length
      }
    } catch (error) {
      console.log('Could not fetch 52-week data:', error)
    }

    // Build response
    const response: StockDetailResponse = {
      ticker: upperSymbol,
      name: companyDetails.name || upperSymbol,
      price: currentPrice,
      change: change,
      changePercent: changePercent,
      volume: prevData.v || 0,
      marketCap: companyDetails.market_cap,
      week52High,
      week52Low,
      avgVolume,
      sector: companyDetails.sic_description,
      description: companyDetails.description,
      employees: companyDetails.total_employees,
      headquarters: companyDetails.address ? 
        `${companyDetails.address.city}, ${companyDetails.address.state}` : undefined,
      website: companyDetails.homepage_url,
      ceo: companyDetails.ceo,
      founded: companyDetails.list_date
    }

    return new Response(
      JSON.stringify(response),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error in stock-detail function:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

function getCurrentDate(): string {
  return new Date().toISOString().split('T')[0]
}

function getDateOneYearAgo(): string {
  const date = new Date()
  date.setFullYear(date.getFullYear() - 1)
  return date.toISOString().split('T')[0]
}
