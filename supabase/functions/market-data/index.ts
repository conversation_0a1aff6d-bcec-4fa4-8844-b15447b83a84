import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Allow-Methods': '*'
};

// Initialize Supabase client with admin privileges
const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Get Polygon API key from environment variables
const POLYGON_API_KEY = Deno.env.get('POLYGON_API_KEY') || '';

if (!POLYGON_API_KEY) {
  console.error('POLYGON_API_KEY is not set in environment variables');
}

// Helper function to format polygon symbol
const formatPolygonSymbol = async (symbol: string): Promise<string> => {
  // Check if it's a crypto symbol
  const isCrypto = /^[A-Z]{3,5}$/.test(symbol) &&
                  !symbol.includes('.') &&
                  ['BTC', 'ETH', 'SOL', 'XRP', 'ADA', 'DOGE', 'DOT', 'AVAX', 'MATIC', 'LINK', 'UNI', 'SHIB'].includes(symbol);

  if (isCrypto) {
    return `X:${symbol}USD`;
  }

  return symbol;
};

// Function to get Polygon API parameters for different timeframes
function getPolygonTimeframeParams(timeframe: string) {
  const now = new Date();
  const today = now.toISOString().split('T')[0];
  let from: string;
  let multiplier: number;
  let timespan: string;

  switch (timeframe) {
    case '1D':
      // For 1 day, use minute data
      from = today;
      multiplier = 5;
      timespan = 'minute';
      break;
    case '1W':
      // For 1 week, use hourly data
      const oneWeekAgo = new Date(now);
      oneWeekAgo.setDate(now.getDate() - 7);
      from = oneWeekAgo.toISOString().split('T')[0];
      multiplier = 1;
      timespan = 'hour';
      break;
    case '1M':
      // For 1 month, use daily data
      const oneMonthAgo = new Date(now);
      oneMonthAgo.setMonth(now.getMonth() - 1);
      from = oneMonthAgo.toISOString().split('T')[0];
      multiplier = 1;
      timespan = 'day';
      break;
    default:
      from = today;
      multiplier = 1;
      timespan = 'minute';
  }

  return {
    from,
    to: today,
    multiplier,
    timespan
  };
}

// Fetch market data from Polygon
async function fetchMarketData(symbol: string, timeframe: string) {
  try {
    // Format the symbol for Polygon API
    const formattedSymbol = await formatPolygonSymbol(symbol);

    // Get timeframe parameters
    const { from: startDate, to: endDate, multiplier, timespan } = getPolygonTimeframeParams(timeframe);

    const url = `https://api.polygon.io/v2/aggs/ticker/${formattedSymbol}/range/${multiplier}/${timespan}/${startDate}/${endDate}?adjusted=true&sort=asc&limit=50000&apiKey=${POLYGON_API_KEY}`;

    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (!data.results || !Array.isArray(data.results)) {
      throw new Error('Invalid data format received from API');
    }

    // Transform the data into the required format
    const transformedData = data.results.map((item: any) => ({
      timestamp: item.t,
      open: item.o,
      high: item.h,
      low: item.l,
      close: item.c,
      volume: item.v
    }));

    // Calculate previous close (for 1D timeframe)
    let previousClose = null;
    if (timeframe === '1D' && transformedData.length > 0) {
      // For 1D, try to get the previous day's close
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterdayStr = yesterday.toISOString().split('T')[0];

      const prevDayUrl = `https://api.polygon.io/v2/aggs/ticker/${formattedSymbol}/range/1/day/${yesterdayStr}/${yesterdayStr}?adjusted=true&apiKey=${POLYGON_API_KEY}`;

      try {
        const prevDayResponse = await fetch(prevDayUrl);
        if (prevDayResponse.ok) {
          const prevDayData = await prevDayResponse.json();
          if (prevDayData.results && prevDayData.results.length > 0) {
            previousClose = prevDayData.results[0].c;
          }
        }
      } catch (error) {
        console.error('Error fetching previous close:', error);
      }
    }

    // Get current price (latest close)
    const currentPrice = transformedData.length > 0 ? transformedData[transformedData.length - 1].close : null;

    return {
      symbol: formattedSymbol,
      data: transformedData,
      config: {
        timeframe,
        showVolume: true
      },
      previousClose,
      currentPrice
    };
  } catch (error) {
    console.error(`Error fetching market data for ${symbol}:`, error);
    throw error;
  }
}

// Main serve function
serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }

  try {
    // Parse the request body
    let body;
    try {
      body = await req.json();
    } catch (e) {
      return new Response(JSON.stringify({ error: 'Invalid request body' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    const { symbol, timeframe = '1D' } = body;

    // Validate required parameters
    if (!symbol) {
      return new Response(JSON.stringify({ error: 'Symbol is required' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Get the authenticated user's ID
    const authHeader = req.headers.get('Authorization');

    if (!authHeader) {
      return new Response(JSON.stringify({ error: 'No authorization header' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Get the authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''));

    if (userError || !user) {
      return new Response(JSON.stringify({ error: 'Unauthorized', details: userError?.message }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Fetch market data
    const data = await fetchMarketData(symbol, timeframe);

    // Return the response
    return new Response(JSON.stringify(data), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
