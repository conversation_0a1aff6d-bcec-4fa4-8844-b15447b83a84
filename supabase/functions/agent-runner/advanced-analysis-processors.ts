// Advanced Analysis Block Processors
import { AdvancedAnalysisBlock, BlockType } from './agent-types.ts';

export class AdvancedAnalysisProcessor {
  static async processCorrelationAnalysis(
    block: AdvancedAnalysisBlock, 
    marketData: any, 
    context: any
  ): Promise<any> {
    const { benchmark = 'SPY', lookback_period = 60, correlation_threshold = 0.7 } = block.parameters;
    
    const correlation = await this.calculateCorrelation(marketData, benchmark, lookback_period);
    
    return {
      method: 'Correlation Analysis',
      benchmark,
      correlation_coefficient: correlation,
      lookback_period,
      is_correlated: Math.abs(correlation) >= correlation_threshold,
      interpretation: this.interpretCorrelation(correlation),
      market_beta: correlation * (marketData.volatility / context.benchmarkVolatility || 1)
    };
  }

  static async processMomentumShift(
    block: AdvancedAnalysisBlock, 
    marketData: any, 
    context: any
  ): Promise<any> {
    const { detection_method = 'rate_of_change', lookback_period = 20, sensitivity = 'medium' } = block.parameters;
    
    switch (detection_method) {
      case 'rate_of_change':
        return this.detectRateOfChangeShift(marketData, lookback_period, sensitivity);
      case 'macd_divergence':
        return this.detectMACDDivergence(marketData, block.parameters);
      case 'rsi_momentum':
        return this.detectRSIMomentum(marketData, block.parameters);
      default:
        throw new Error(`Unknown momentum shift method: ${detection_method}`);
    }
  }

  static async processTrendStrength(
    block: AdvancedAnalysisBlock, 
    marketData: any, 
    context: any
  ): Promise<any> {
    const { method = 'adx_based', period = 14, strong_trend_threshold = 25 } = block.parameters;
    
    switch (method) {
      case 'adx_based':
        return this.calculateADXTrendStrength(marketData, period, strong_trend_threshold);
      case 'slope_analysis':
        return this.calculateSlopeTrendStrength(marketData, block.parameters);
      case 'momentum_strength':
        return this.calculateMomentumTrendStrength(marketData, block.parameters);
      default:
        throw new Error(`Unknown trend strength method: ${method}`);
    }
  }

  static async processVolatilityFilter(
    block: AdvancedAnalysisBlock, 
    marketData: any, 
    context: any
  ): Promise<any> {
    const { method = 'atr_percentile', lookback_period = 20, filter_mode = 'exclude_extremes' } = block.parameters;
    
    switch (method) {
      case 'atr_percentile':
        return this.calculateATRPercentile(marketData, lookback_period, filter_mode);
      case 'bollinger_squeeze':
        return this.detectBollingerSqueeze(marketData, block.parameters);
      case 'vix_relative':
        return this.analyzeVIXRelative(marketData, block.parameters);
      default:
        throw new Error(`Unknown volatility filter method: ${method}`);
    }
  }

  static async processFibonacciLevels(
    block: AdvancedAnalysisBlock, 
    marketData: any, 
    context: any
  ): Promise<any> {
    const { calculation_method = 'swing_high_low', lookback_period = 50, key_levels = [0.236, 0.382, 0.5, 0.618, 0.786] } = block.parameters;
    
    const prices = marketData.prices.slice(-lookback_period);
    const high = Math.max(...prices.map(p => p.high));
    const low = Math.min(...prices.map(p => p.low));
    const currentPrice = prices[prices.length - 1].close;
    
    const range = high - low;
    const levels = key_levels.map(level => ({
      level: level,
      price: calculation_method === 'swing_high_low' ? low + (range * level) : high - (range * level),
      distance_from_current: 0,
      significance: this.calculateFibSignificance(level)
    }));

    // Calculate distances
    levels.forEach(level => {
      level.distance_from_current = Math.abs(currentPrice - level.price) / currentPrice * 100;
    });

    return {
      method: 'Fibonacci Levels',
      calculation_method,
      swing_high: high,
      swing_low: low,
      current_price: currentPrice,
      levels: levels.sort((a, b) => a.distance_from_current - b.distance_from_current),
      nearest_level: levels.reduce((nearest, level) => 
        level.distance_from_current < nearest.distance_from_current ? level : nearest
      )
    };
  }

  static async processMarketSentiment(
    block: AdvancedAnalysisBlock, 
    marketData: any, 
    context: any
  ): Promise<any> {
    const { sentiment_source = 'vix_analysis', lookback_period = 10, bullish_threshold = 30 } = block.parameters;
    
    switch (sentiment_source) {
      case 'vix_analysis':
        return this.analyzeVIXSentiment(marketData, lookback_period, bullish_threshold);
      case 'put_call_ratio':
        return this.analyzePutCallRatio(marketData, block.parameters);
      default:
        throw new Error(`Unknown sentiment source: ${sentiment_source}`);
    }
  }

  static async processSectorAnalysis(
    block: AdvancedAnalysisBlock, 
    marketData: any, 
    context: any
  ): Promise<any> {
    const { analysis_type = 'relative_strength', sector_benchmark = 'auto_detect', lookback_period = 30 } = block.parameters;
    
    const sectorETF = sector_benchmark === 'auto_detect' ? 
      this.detectSectorETF(marketData.symbol) : sector_benchmark;
    
    switch (analysis_type) {
      case 'relative_strength':
        return this.calculateRelativeStrength(marketData, sectorETF, lookback_period);
      case 'sector_rotation':
        return this.analyzeSectorRotation(marketData, sectorETF, block.parameters);
      default:
        throw new Error(`Unknown sector analysis type: ${analysis_type}`);
    }
  }

  // Helper methods for advanced analysis calculations
  private static async calculateCorrelation(marketData: any, benchmark: string, period: number): Promise<number> {
    // Simplified correlation calculation
    // In production, this would fetch actual benchmark data and calculate correlation
    return 0.65; // Mock correlation value
  }

  private static interpretCorrelation(correlation: number): string {
    if (correlation > 0.7) return 'Strong positive correlation with market';
    if (correlation > 0.3) return 'Moderate positive correlation with market';
    if (correlation > -0.3) return 'Low correlation with market';
    if (correlation > -0.7) return 'Moderate negative correlation with market';
    return 'Strong negative correlation with market';
  }

  private static async detectRateOfChangeShift(marketData: any, period: number, sensitivity: string): Promise<any> {
    const prices = marketData.prices.slice(-period);
    const currentPrice = prices[prices.length - 1].close;
    const pastPrice = prices[0].close;
    
    const rateOfChange = ((currentPrice - pastPrice) / pastPrice) * 100;
    const threshold = sensitivity === 'high' ? 3 : sensitivity === 'medium' ? 5 : 7;
    
    return {
      method: 'Rate of Change',
      rate_of_change: rateOfChange,
      period,
      momentum_shift_detected: Math.abs(rateOfChange) > threshold,
      direction: rateOfChange > 0 ? 'bullish' : 'bearish',
      strength: Math.abs(rateOfChange) > threshold * 2 ? 'strong' : 'moderate'
    };
  }

  private static async detectMACDDivergence(marketData: any, parameters: any): Promise<any> {
    const { fast_period = 12, slow_period = 26, signal_period = 9 } = parameters;
    
    // Simplified MACD calculation
    const prices = marketData.prices.map(p => p.close);
    const macd = this.calculateMACD(prices, fast_period, slow_period, signal_period);
    
    return {
      method: 'MACD Divergence',
      macd_line: macd.macd,
      signal_line: macd.signal,
      histogram: macd.histogram,
      divergence_detected: macd.divergence,
      momentum_direction: macd.macd > macd.signal ? 'bullish' : 'bearish'
    };
  }

  private static async calculateADXTrendStrength(marketData: any, period: number, threshold: number): Promise<any> {
    const adx = this.calculateADX(marketData.prices, period);
    
    let strength: string;
    if (adx > threshold * 1.6) strength = 'very strong';
    else if (adx > threshold) strength = 'strong';
    else if (adx > threshold * 0.6) strength = 'moderate';
    else strength = 'weak';
    
    return {
      method: 'ADX-Based',
      adx_value: adx,
      trend_strength: strength,
      is_trending: adx > threshold,
      period
    };
  }

  private static async calculateATRPercentile(marketData: any, period: number, filterMode: string): Promise<any> {
    const atrValues = this.calculateATRSeries(marketData.prices, period);
    const currentATR = atrValues[atrValues.length - 1];
    const percentile = this.calculatePercentile(atrValues, currentATR);
    
    let volatilityLevel: string;
    if (percentile > 75) volatilityLevel = 'high';
    else if (percentile > 25) volatilityLevel = 'moderate';
    else volatilityLevel = 'low';
    
    const shouldFilter = this.shouldFilterByVolatility(percentile, filterMode);
    
    return {
      method: 'ATR Percentile',
      current_atr: currentATR,
      atr_percentile: percentile,
      volatility_level: volatilityLevel,
      should_filter: shouldFilter,
      filter_mode: filterMode
    };
  }

  private static async analyzeVIXSentiment(marketData: any, period: number, threshold: number): Promise<any> {
    // Mock VIX analysis - in production would fetch actual VIX data
    const mockVIX = 25; // Current VIX level
    
    let sentiment: string;
    if (mockVIX < 20) sentiment = 'complacent';
    else if (mockVIX < threshold) sentiment = 'bullish';
    else if (mockVIX < 40) sentiment = 'bearish';
    else sentiment = 'extreme_fear';
    
    return {
      method: 'VIX Analysis',
      vix_level: mockVIX,
      sentiment,
      fear_greed_index: Math.max(0, Math.min(100, 100 - mockVIX * 2)),
      contrarian_signal: sentiment === 'extreme_fear' ? 'bullish' : sentiment === 'complacent' ? 'bearish' : 'neutral'
    };
  }

  private static async calculateRelativeStrength(marketData: any, sectorETF: string, period: number): Promise<any> {
    // Mock relative strength calculation
    const relativeStrength = 15; // Percentage outperformance vs sector
    
    return {
      method: 'Relative Strength',
      sector_etf: sectorETF,
      relative_performance: relativeStrength,
      period,
      outperforming: relativeStrength > 0,
      strength_rating: relativeStrength > 10 ? 'strong' : relativeStrength > 0 ? 'moderate' : 'weak'
    };
  }

  // Utility calculation methods
  private static calculateMACD(prices: number[], fast: number, slow: number, signal: number) {
    // Simplified MACD calculation
    const emaFast = this.calculateEMA(prices, fast);
    const emaSlow = this.calculateEMA(prices, slow);
    const macd = emaFast - emaSlow;
    const signalLine = this.calculateEMA([macd], signal);
    
    return {
      macd,
      signal: signalLine,
      histogram: macd - signalLine,
      divergence: false // Simplified
    };
  }

  private static calculateEMA(prices: number[], period: number): number {
    if (prices.length === 0) return 0;
    const multiplier = 2 / (period + 1);
    let ema = prices[0];
    
    for (let i = 1; i < prices.length; i++) {
      ema = (prices[i] * multiplier) + (ema * (1 - multiplier));
    }
    
    return ema;
  }

  private static calculateADX(prices: any[], period: number): number {
    // Simplified ADX calculation
    return 25; // Mock ADX value
  }

  private static calculateATRSeries(prices: any[], period: number): number[] {
    const atrValues = [];
    for (let i = period; i < prices.length; i++) {
      const slice = prices.slice(i - period, i);
      atrValues.push(this.calculateATR(slice));
    }
    return atrValues;
  }

  private static calculateATR(prices: any[]): number {
    if (prices.length < 2) return 0;
    
    let trSum = 0;
    for (let i = 1; i < prices.length; i++) {
      const high = prices[i].high;
      const low = prices[i].low;
      const prevClose = prices[i - 1].close;
      
      const tr = Math.max(
        high - low,
        Math.abs(high - prevClose),
        Math.abs(low - prevClose)
      );
      trSum += tr;
    }
    
    return trSum / (prices.length - 1);
  }

  private static calculatePercentile(values: number[], target: number): number {
    const sorted = [...values].sort((a, b) => a - b);
    const index = sorted.findIndex(v => v >= target);
    return (index / sorted.length) * 100;
  }

  private static shouldFilterByVolatility(percentile: number, mode: string): boolean {
    switch (mode) {
      case 'exclude_extremes':
        return percentile < 10 || percentile > 90;
      case 'low_volatility_only':
        return percentile > 25;
      case 'high_volatility_only':
        return percentile < 75;
      case 'moderate_volatility':
        return percentile < 25 || percentile > 75;
      default:
        return false;
    }
  }

  private static calculateFibSignificance(level: number): string {
    const significance = {
      0.236: 'low',
      0.382: 'moderate',
      0.5: 'high',
      0.618: 'very high',
      0.786: 'moderate'
    };
    return significance[level as keyof typeof significance] || 'low';
  }

  private static detectSectorETF(symbol: string): string {
    // Simplified sector detection based on symbol
    const sectorMap: Record<string, string> = {
      'AAPL': 'XLK', // Technology
      'MSFT': 'XLK',
      'GOOGL': 'XLK',
      'JPM': 'XLF', // Financial
      'BAC': 'XLF',
      'XOM': 'XLE', // Energy
      'CVX': 'XLE'
    };
    
    return sectorMap[symbol] || 'SPY'; // Default to market
  }
}
