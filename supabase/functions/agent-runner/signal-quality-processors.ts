// Signal Quality & Filtering Block Processors
import { SignalQualityBlock, BlockType } from './agent-types.ts';

export class SignalQualityProcessor {
  static async processSignalQualityFilter(
    block: SignalQualityBlock, 
    marketData: any, 
    context: any
  ): Promise<any> {
    const { 
      min_confidence = 70, 
      require_volume_confirmation = true, 
      require_trend_alignment = false,
      quality_score_threshold = 75 
    } = block.parameters;
    
    const qualityMetrics = await this.calculateQualityMetrics(marketData, context);
    
    const passesFilter = 
      qualityMetrics.confidence >= min_confidence &&
      (!require_volume_confirmation || qualityMetrics.volume_confirmed) &&
      (!require_trend_alignment || qualityMetrics.trend_aligned) &&
      qualityMetrics.overall_score >= quality_score_threshold;
    
    return {
      method: 'Signal Quality Filter',
      passes_filter: passesFilter,
      quality_metrics: qualityMetrics,
      filter_criteria: {
        min_confidence,
        require_volume_confirmation,
        require_trend_alignment,
        quality_score_threshold
      },
      reasoning: this.generateQualityReasoning(qualityMetrics, passesFilter)
    };
  }

  static async processConfidenceThreshold(
    block: SignalQualityBlock, 
    marketData: any, 
    context: any
  ): Promise<any> {
    const { 
      minimum_confidence = 60, 
      maximum_confidence = 95, 
      adjustment_factor = 1.0,
      dynamic_threshold = false 
    } = block.parameters;
    
    let adjustedConfidence = context.baseConfidence || 50;
    
    if (dynamic_threshold) {
      const marketConditions = await this.assessMarketConditions(marketData);
      adjustedConfidence = this.adjustConfidenceForMarket(adjustedConfidence, marketConditions);
    }
    
    adjustedConfidence *= adjustment_factor;
    adjustedConfidence = Math.max(minimum_confidence, Math.min(maximum_confidence, adjustedConfidence));
    
    return {
      method: 'Confidence Threshold',
      original_confidence: context.baseConfidence || 50,
      adjusted_confidence: adjustedConfidence,
      adjustment_factor,
      dynamic_adjustment: dynamic_threshold,
      meets_threshold: adjustedConfidence >= minimum_confidence,
      confidence_level: this.categorizeConfidence(adjustedConfidence)
    };
  }

  static async processSignalConfirmationDelay(
    block: SignalQualityBlock, 
    marketData: any, 
    context: any
  ): Promise<any> {
    const { 
      confirmation_periods = 2, 
      require_consecutive = true, 
      decay_factor = 0.9,
      max_delay_periods = 5 
    } = block.parameters;
    
    const confirmationHistory = context.signalHistory || [];
    const currentSignal = context.currentSignal;
    
    const confirmation = this.checkSignalConfirmation(
      currentSignal,
      confirmationHistory,
      confirmation_periods,
      require_consecutive,
      decay_factor
    );
    
    return {
      method: 'Signal Confirmation Delay',
      current_signal: currentSignal,
      confirmation_periods,
      periods_confirmed: confirmation.periodsConfirmed,
      is_confirmed: confirmation.isConfirmed,
      consecutive_required: require_consecutive,
      confidence_decay: confirmation.decayedConfidence,
      delay_periods_used: confirmation.delayPeriods
    };
  }

  static async processMarketHoursFilter(
    block: SignalQualityBlock, 
    marketData: any, 
    context: any
  ): Promise<any> {
    const { 
      market_open = '09:30', 
      market_close = '16:00', 
      timezone = 'EST',
      allow_premarket = false,
      allow_afterhours = false 
    } = block.parameters;
    
    const currentTime = new Date();
    const marketStatus = this.getMarketStatus(currentTime, block.parameters);
    
    const allowedSessions = ['regular'];
    if (allow_premarket) allowedSessions.push('premarket');
    if (allow_afterhours) allowedSessions.push('afterhours');
    
    const passesFilter = allowedSessions.includes(marketStatus.session);
    
    return {
      method: 'Market Hours Filter',
      current_time: currentTime.toISOString(),
      market_session: marketStatus.session,
      market_open: market_open,
      market_close: market_close,
      timezone,
      passes_filter: passesFilter,
      allowed_sessions: allowedSessions,
      time_until_open: marketStatus.timeUntilOpen,
      time_until_close: marketStatus.timeUntilClose
    };
  }

  // Helper methods for signal quality calculations
  private static async calculateQualityMetrics(marketData: any, context: any) {
    const volumeConfirmation = this.checkVolumeConfirmation(marketData);
    const trendAlignment = this.checkTrendAlignment(marketData, context);
    const technicalStrength = this.assessTechnicalStrength(marketData);
    const marketConditions = await this.assessMarketConditions(marketData);
    
    const overallScore = this.calculateOverallQualityScore({
      volumeConfirmation,
      trendAlignment,
      technicalStrength,
      marketConditions
    });
    
    return {
      confidence: context.baseConfidence || 50,
      volume_confirmed: volumeConfirmation.confirmed,
      trend_aligned: trendAlignment.aligned,
      technical_strength: technicalStrength.score,
      market_conditions: marketConditions.score,
      overall_score: overallScore,
      quality_factors: this.identifyQualityFactors({
        volumeConfirmation,
        trendAlignment,
        technicalStrength,
        marketConditions
      })
    };
  }

  private static checkVolumeConfirmation(marketData: any) {
    const recentVolumes = marketData.prices.slice(-10).map(p => p.volume);
    const currentVolume = recentVolumes[recentVolumes.length - 1];
    const avgVolume = recentVolumes.slice(0, -1).reduce((sum, v) => sum + v, 0) / (recentVolumes.length - 1);
    
    const volumeRatio = currentVolume / avgVolume;
    const confirmed = volumeRatio >= 1.2; // 20% above average
    
    return {
      confirmed,
      current_volume: currentVolume,
      average_volume: avgVolume,
      volume_ratio: volumeRatio,
      strength: volumeRatio > 2 ? 'strong' : volumeRatio > 1.5 ? 'moderate' : 'weak'
    };
  }

  private static checkTrendAlignment(marketData: any, context: any) {
    const prices = marketData.prices.map(p => p.close);
    const shortMA = this.calculateSMA(prices.slice(-10), 10);
    const longMA = this.calculateSMA(prices.slice(-20), 20);
    
    const trendDirection = shortMA > longMA ? 'bullish' : 'bearish';
    const signalDirection = context.currentSignal;
    const aligned = trendDirection === signalDirection;
    
    return {
      aligned,
      trend_direction: trendDirection,
      signal_direction: signalDirection,
      short_ma: shortMA,
      long_ma: longMA,
      trend_strength: Math.abs(shortMA - longMA) / longMA * 100
    };
  }

  private static assessTechnicalStrength(marketData: any) {
    const prices = marketData.prices.map(p => p.close);
    const rsi = this.calculateRSI(prices, 14);
    const macd = this.calculateMACDSignal(prices);
    
    let score = 50; // Base score
    
    // RSI contribution
    if (rsi > 30 && rsi < 70) score += 20; // Not overbought/oversold
    else if (rsi > 70 || rsi < 30) score += 10; // Extreme levels can be good for reversals
    
    // MACD contribution
    if (macd.signal === 'bullish') score += 15;
    else if (macd.signal === 'bearish') score -= 15;
    
    return {
      score: Math.max(0, Math.min(100, score)),
      rsi_value: rsi,
      macd_signal: macd.signal,
      technical_indicators: {
        rsi: { value: rsi, interpretation: this.interpretRSI(rsi) },
        macd: macd
      }
    };
  }

  private static async assessMarketConditions(marketData: any) {
    // Mock market conditions assessment
    const volatility = this.calculateVolatility(marketData.prices);
    const trend = this.assessOverallTrend(marketData.prices);
    
    let score = 50;
    
    // Volatility impact
    if (volatility < 0.15) score += 10; // Low volatility is good
    else if (volatility > 0.3) score -= 10; // High volatility is risky
    
    // Trend impact
    if (trend.strength > 0.7) score += 15; // Strong trend is good
    
    return {
      score: Math.max(0, Math.min(100, score)),
      volatility,
      trend: trend.direction,
      trend_strength: trend.strength,
      market_regime: this.identifyMarketRegime(volatility, trend)
    };
  }

  private static calculateOverallQualityScore(metrics: any): number {
    const weights = {
      volume: 0.25,
      trend: 0.25,
      technical: 0.3,
      market: 0.2
    };
    
    let score = 0;
    score += metrics.volumeConfirmation.confirmed ? 25 * weights.volume : 0;
    score += metrics.trendAlignment.aligned ? 25 * weights.trend : 0;
    score += metrics.technicalStrength.score * weights.technical;
    score += metrics.marketConditions.score * weights.market;
    
    return Math.round(score);
  }

  private static identifyQualityFactors(metrics: any): string[] {
    const factors = [];
    
    if (metrics.volumeConfirmation.confirmed) {
      factors.push(`Volume confirmation (${metrics.volumeConfirmation.strength})`);
    }
    
    if (metrics.trendAlignment.aligned) {
      factors.push('Trend alignment');
    }
    
    if (metrics.technicalStrength.score > 70) {
      factors.push('Strong technical setup');
    }
    
    if (metrics.marketConditions.score > 70) {
      factors.push('Favorable market conditions');
    }
    
    return factors;
  }

  private static generateQualityReasoning(metrics: any, passed: boolean): string {
    if (passed) {
      return `Signal passes quality filter with ${metrics.overall_score}% quality score. ${metrics.quality_factors.join(', ')}.`;
    } else {
      const issues = [];
      if (metrics.confidence < 70) issues.push('low confidence');
      if (!metrics.volume_confirmed) issues.push('no volume confirmation');
      if (!metrics.trend_aligned) issues.push('trend misalignment');
      if (metrics.overall_score < 75) issues.push('low quality score');
      
      return `Signal filtered out due to: ${issues.join(', ')}.`;
    }
  }

  private static adjustConfidenceForMarket(baseConfidence: number, marketConditions: any): number {
    let adjustment = 1.0;
    
    // Adjust based on volatility
    if (marketConditions.volatility > 0.3) adjustment *= 0.9; // Reduce confidence in high volatility
    else if (marketConditions.volatility < 0.15) adjustment *= 1.1; // Increase confidence in low volatility
    
    // Adjust based on trend strength
    if (marketConditions.trend_strength > 0.7) adjustment *= 1.1; // Increase confidence in strong trends
    
    return baseConfidence * adjustment;
  }

  private static categorizeConfidence(confidence: number): string {
    if (confidence >= 80) return 'very high';
    if (confidence >= 70) return 'high';
    if (confidence >= 60) return 'moderate';
    if (confidence >= 50) return 'low';
    return 'very low';
  }

  private static checkSignalConfirmation(
    currentSignal: string,
    history: any[],
    periods: number,
    consecutive: boolean,
    decayFactor: number
  ) {
    let confirmedPeriods = 0;
    let consecutiveCount = 0;
    let decayedConfidence = 50;
    
    for (let i = 0; i < Math.min(periods, history.length); i++) {
      const historicalSignal = history[history.length - 1 - i];
      
      if (historicalSignal.signal === currentSignal) {
        confirmedPeriods++;
        consecutiveCount = consecutive ? consecutiveCount + 1 : confirmedPeriods;
        decayedConfidence += (historicalSignal.confidence || 50) * Math.pow(decayFactor, i);
      } else if (consecutive) {
        break; // Break consecutive chain
      }
    }
    
    const isConfirmed = consecutive ? 
      consecutiveCount >= periods : 
      confirmedPeriods >= periods;
    
    return {
      periodsConfirmed: confirmedPeriods,
      isConfirmed,
      decayedConfidence: Math.min(100, decayedConfidence),
      delayPeriods: confirmedPeriods
    };
  }

  private static getMarketStatus(currentTime: Date, parameters: any) {
    const { market_open, market_close, timezone } = parameters;
    
    // Simplified market hours check (assumes EST)
    const hour = currentTime.getHours();
    const minute = currentTime.getMinutes();
    const currentMinutes = hour * 60 + minute;
    
    const [openHour, openMinute] = market_open.split(':').map(Number);
    const [closeHour, closeMinute] = market_close.split(':').map(Number);
    
    const openMinutes = openHour * 60 + openMinute;
    const closeMinutes = closeHour * 60 + closeMinute;
    
    let session: string;
    if (currentMinutes < openMinutes) {
      session = 'premarket';
    } else if (currentMinutes >= openMinutes && currentMinutes < closeMinutes) {
      session = 'regular';
    } else {
      session = 'afterhours';
    }
    
    return {
      session,
      timeUntilOpen: session === 'premarket' ? openMinutes - currentMinutes : 0,
      timeUntilClose: session === 'regular' ? closeMinutes - currentMinutes : 0
    };
  }

  // Utility calculation methods
  private static calculateSMA(prices: number[], period: number): number {
    if (prices.length < period) return 0;
    const sum = prices.slice(-period).reduce((sum, price) => sum + price, 0);
    return sum / period;
  }

  private static calculateRSI(prices: number[], period: number): number {
    if (prices.length < period + 1) return 50;
    
    let gains = 0;
    let losses = 0;
    
    for (let i = 1; i <= period; i++) {
      const change = prices[prices.length - i] - prices[prices.length - i - 1];
      if (change > 0) gains += change;
      else losses -= change;
    }
    
    const avgGain = gains / period;
    const avgLoss = losses / period;
    
    if (avgLoss === 0) return 100;
    
    const rs = avgGain / avgLoss;
    return 100 - (100 / (1 + rs));
  }

  private static calculateMACDSignal(prices: number[]) {
    // Simplified MACD calculation
    const ema12 = this.calculateEMA(prices, 12);
    const ema26 = this.calculateEMA(prices, 26);
    const macdLine = ema12 - ema26;
    
    return {
      macd_line: macdLine,
      signal: macdLine > 0 ? 'bullish' : 'bearish',
      strength: Math.abs(macdLine)
    };
  }

  private static calculateEMA(prices: number[], period: number): number {
    if (prices.length === 0) return 0;
    const multiplier = 2 / (period + 1);
    let ema = prices[0];
    
    for (let i = 1; i < prices.length; i++) {
      ema = (prices[i] * multiplier) + (ema * (1 - multiplier));
    }
    
    return ema;
  }

  private static calculateVolatility(prices: any[]): number {
    if (prices.length < 2) return 0;
    
    const returns = [];
    for (let i = 1; i < prices.length; i++) {
      const ret = Math.log(prices[i].close / prices[i - 1].close);
      returns.push(ret);
    }
    
    const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length;
    
    return Math.sqrt(variance * 252); // Annualized
  }

  private static assessOverallTrend(prices: any[]) {
    const closes = prices.map(p => p.close);
    const firstPrice = closes[0];
    const lastPrice = closes[closes.length - 1];
    
    const direction = lastPrice > firstPrice ? 'bullish' : 'bearish';
    const strength = Math.abs(lastPrice - firstPrice) / firstPrice;
    
    return { direction, strength };
  }

  private static interpretRSI(rsi: number): string {
    if (rsi > 70) return 'overbought';
    if (rsi < 30) return 'oversold';
    if (rsi > 50) return 'bullish momentum';
    return 'bearish momentum';
  }

  private static identifyMarketRegime(volatility: number, trend: any): string {
    if (volatility > 0.3) return 'high_volatility';
    if (trend.strength > 0.7) return 'trending';
    if (volatility < 0.15) return 'low_volatility';
    return 'ranging';
  }
}
