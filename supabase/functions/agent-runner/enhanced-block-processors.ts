// Enhanced Block Processors for New AI Trading Agent Blocks
import { 
  RiskAnalysisBlock, 
  AdvancedAnalysisBlock, 
  SignalQualityBlock,
  EnhancedSignalOutput,
  BlockType 
} from './agent-types.ts';

// Risk Analysis Block Processors
export class RiskAnalysisProcessor {
  static async processRiskAnalyzer(
    block: RiskAnalysisBlock, 
    marketData: any, 
    context: any
  ): Promise<any> {
    const { method, atr_multiplier = 2, atr_period = 14, percentage = 3, risk_tolerance = 'moderate' } = block.parameters;
    
    switch (method) {
      case 'atr_based':
        return this.calculateATRBasedRisk(marketData, atr_multiplier, atr_period, risk_tolerance);
      case 'support_resistance':
        return this.calculateSupportResistanceRisk(marketData, block.parameters);
      case 'percentage_based':
        return this.calculatePercentageBasedRisk(marketData, percentage, risk_tolerance);
      case 'volatility_adjusted':
        return this.calculateVolatilityAdjustedRisk(marketData, block.parameters);
      default:
        throw new Error(`Unknown risk analyzer method: ${method}`);
    }
  }

  static async processTargetAnalyzer(
    block: RiskAnalysisBlock, 
    marketData: any, 
    context: any
  ): Promise<any> {
    const { method, risk_reward_ratio = 2, target_levels = 3, probability_weighting = true } = block.parameters;
    
    switch (method) {
      case 'fibonacci_resistance':
        return this.calculateFibonacciTargets(marketData, block.parameters);
      case 'risk_reward_ratio':
        return this.calculateRiskRewardTargets(marketData, risk_reward_ratio, target_levels);
      case 'resistance_levels':
        return this.calculateResistanceTargets(marketData, block.parameters);
      case 'momentum_projection':
        return this.calculateMomentumTargets(marketData, block.parameters);
      default:
        throw new Error(`Unknown target analyzer method: ${method}`);
    }
  }

  static async processSignalEnhancer(
    block: RiskAnalysisBlock, 
    marketData: any, 
    context: any
  ): Promise<any> {
    const { enhancement_type, include_risk_metrics = true, educational_mode = true } = block.parameters;

    const enhancement = {
      risk_management: include_risk_metrics ? await this.generateRiskMetrics(marketData, context) : null,
      educational: educational_mode ? await this.generateEducationalContent(marketData, context) : null
    };

    return enhancement;
  }

  static async processRiskRewardAnalyzer(
    block: RiskAnalysisBlock, 
    marketData: any, 
    context: any
  ): Promise<any> {
    const { min_risk_reward_ratio = 1.5, max_risk_percentage = 5, quality_threshold = 70 } = block.parameters;
    
    const analysis = {
      risk_reward_ratio: await this.calculateRiskRewardRatio(marketData, context),
      risk_percentage: await this.calculateRiskPercentage(marketData, context),
      quality_score: await this.calculateQualityScore(marketData, context),
      meets_criteria: false
    };

    analysis.meets_criteria = 
      analysis.risk_reward_ratio >= min_risk_reward_ratio &&
      analysis.risk_percentage <= max_risk_percentage &&
      analysis.quality_score >= quality_threshold;

    return analysis;
  }

  // Helper methods for risk analysis calculations
  private static async calculateATRBasedRisk(marketData: any, multiplier: number, period: number, tolerance: string) {
    const atr = this.calculateATR(marketData.prices, period);
    const currentPrice = marketData.prices[marketData.prices.length - 1].close;
    
    const riskAmount = atr * multiplier;
    const stopLossPrice = currentPrice - riskAmount;
    const riskPercentage = (riskAmount / currentPrice) * 100;

    return {
      method: 'ATR-Based',
      stop_loss_price: stopLossPrice,
      risk_amount: riskAmount,
      risk_percentage: riskPercentage,
      atr_value: atr,
      reasoning: `Stop loss calculated using ${period}-period ATR with ${multiplier}x multiplier for ${tolerance} risk tolerance`
    };
  }

  private static async calculateSupportResistanceRisk(marketData: any, parameters: any) {
    const { lookback_period = 50, strength_threshold = 3, buffer_percentage = 0.5 } = parameters;
    const supportLevel = this.findNearestSupport(marketData.prices, lookback_period, strength_threshold);
    const currentPrice = marketData.prices[marketData.prices.length - 1].close;
    
    const bufferAmount = supportLevel * (buffer_percentage / 100);
    const stopLossPrice = supportLevel - bufferAmount;
    const riskPercentage = ((currentPrice - stopLossPrice) / currentPrice) * 100;

    return {
      method: 'Support/Resistance',
      stop_loss_price: stopLossPrice,
      support_level: supportLevel,
      risk_percentage: riskPercentage,
      reasoning: `Stop loss placed below key support level at $${supportLevel.toFixed(2)} with ${buffer_percentage}% buffer`
    };
  }

  private static async calculatePercentageBasedRisk(marketData: any, percentage: number, tolerance: string) {
    const currentPrice = marketData.prices[marketData.prices.length - 1].close;
    const stopLossPrice = currentPrice * (1 - percentage / 100);

    return {
      method: 'Fixed Percentage',
      stop_loss_price: stopLossPrice,
      risk_percentage: percentage,
      reasoning: `Fixed ${percentage}% stop loss for ${tolerance} risk tolerance`
    };
  }

  private static async calculateVolatilityAdjustedRisk(marketData: any, parameters: any) {
    const { base_percentage = 2, volatility_multiplier = 1.5, lookback_period = 20 } = parameters;
    const volatility = this.calculateVolatility(marketData.prices, lookback_period);
    const adjustedPercentage = base_percentage * (1 + volatility * volatility_multiplier);
    
    const currentPrice = marketData.prices[marketData.prices.length - 1].close;
    const stopLossPrice = currentPrice * (1 - adjustedPercentage / 100);

    return {
      method: 'Volatility Adjusted',
      stop_loss_price: stopLossPrice,
      risk_percentage: adjustedPercentage,
      volatility: volatility,
      reasoning: `Risk adjusted for current volatility: ${adjustedPercentage.toFixed(1)}%`
    };
  }

  private static async calculateFibonacciTargets(marketData: any, parameters: any) {
    const { lookback_period = 50, key_levels = [0.236, 0.382, 0.5, 0.618, 0.786] } = parameters;
    const prices = marketData.prices.slice(-lookback_period);
    const high = Math.max(...prices.map(p => p.high));
    const low = Math.min(...prices.map(p => p.low));
    const currentPrice = prices[prices.length - 1].close;
    
    const range = high - low;
    const targets = key_levels.map(level => ({
      price: low + (range * level),
      percentage: ((low + (range * level) - currentPrice) / currentPrice) * 100,
      probability: this.calculateTargetProbability(level),
      level_type: `Fibonacci ${(level * 100).toFixed(1)}%`
    }));

    return {
      method: 'Fibonacci Levels',
      targets: targets.filter(t => t.price > currentPrice),
      swing_high: high,
      swing_low: low
    };
  }

  private static async calculateRiskRewardTargets(marketData: any, ratio: number, levels: number) {
    const currentPrice = marketData.prices[marketData.prices.length - 1].close;
    const riskAmount = currentPrice * 0.03; // Assume 3% risk
    
    const targets = [];
    for (let i = 1; i <= levels; i++) {
      const targetPrice = currentPrice + (riskAmount * ratio * i);
      targets.push({
        price: targetPrice,
        percentage: ((targetPrice - currentPrice) / currentPrice) * 100,
        probability: Math.max(80 - (i * 15), 30), // Decreasing probability
        level_type: `Target ${i} (${ratio}:1 R/R)`
      });
    }

    return {
      method: 'Risk/Reward Ratio',
      targets,
      risk_amount: riskAmount
    };
  }

  // Utility calculation methods
  private static calculateATR(prices: any[], period: number): number {
    if (prices.length < period + 1) return 0;
    
    let trSum = 0;
    for (let i = 1; i < period + 1; i++) {
      const high = prices[prices.length - i].high;
      const low = prices[prices.length - i].low;
      const prevClose = prices[prices.length - i - 1].close;
      
      const tr = Math.max(
        high - low,
        Math.abs(high - prevClose),
        Math.abs(low - prevClose)
      );
      trSum += tr;
    }
    
    return trSum / period;
  }

  private static findNearestSupport(prices: any[], lookback: number, strength: number): number {
    const recentPrices = prices.slice(-lookback);
    const lows = recentPrices.map(p => p.low);
    
    // Simple support detection - find the lowest low in recent period
    return Math.min(...lows);
  }

  private static calculateVolatility(prices: any[], period: number): number {
    if (prices.length < period) return 0;
    
    const returns = [];
    for (let i = 1; i < period; i++) {
      const currentPrice = prices[prices.length - i].close;
      const prevPrice = prices[prices.length - i - 1].close;
      returns.push(Math.log(currentPrice / prevPrice));
    }
    
    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / returns.length;
    
    return Math.sqrt(variance * 252); // Annualized volatility
  }

  private static calculateTargetProbability(fibLevel: number): number {
    // Probability decreases as Fibonacci level increases
    const probabilities = {
      0.236: 85,
      0.382: 75,
      0.5: 65,
      0.618: 55,
      0.786: 45
    };
    return probabilities[fibLevel as keyof typeof probabilities] || 50;
  }

  private static async generateRiskMetrics(marketData: any, context: any) {
    return {
      max_risk_per_trade: 2,
      portfolio_heat: 8,
      correlation_risk: 0.3,
      volatility_risk: 'moderate'
    };
  }



  private static async generateEducationalContent(marketData: any, context: any) {
    return {
      strategy_explanation: 'This signal is based on technical analysis with proper risk management',
      risk_warnings: ['Market conditions can change rapidly', 'Past performance does not guarantee future results'],
      market_context: 'Current market showing moderate volatility'
    };
  }

  private static async calculateRiskRewardRatio(marketData: any, context: any): Promise<number> {
    // Simplified calculation - would be more sophisticated in production
    return 2.5;
  }

  private static async calculateRiskPercentage(marketData: any, context: any): Promise<number> {
    return 3;
  }

  private static async calculateQualityScore(marketData: any, context: any): Promise<number> {
    return 75;
  }
}
