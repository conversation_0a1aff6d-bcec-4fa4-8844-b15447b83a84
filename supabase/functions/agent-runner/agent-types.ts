// Agent Types
// Type definitions for the agent system

// Block types
export enum BlockType {
  WHEN_RUN = 'WHEN_RUN', // Added WHEN_RUN block type
  INDICATOR = 'INDICATOR',
  PRICE = 'PRICE',
  FUNDAMENTAL = 'FUNDAMENT<PERSON>',
  CONDITION = 'CONDITION',
  COMPARISON = 'COMPARISON', // New block type for comparing two values
  TRIGGER = 'TRIGGER',

  OPERATOR = 'OPERATOR',
  BULLISH_CONFIDENCE_BOOST = 'BULLISH_CONFIDENCE_BOOST',
  BEARISH_CONFIDENCE_BOOST = 'BEARISH_CONFIDENCE_BOOST',
  CONFIDENCE_BOOST = 'CONFIDENCE_BOOST', // Unified confidence boost block
  AND = 'AND', // AND logic block
  OR = 'OR',   // OR logic block
  CANDLE_PATTERN = 'CANDLE_PATTERN', // Candle pattern detection block
  STOCK_SENTIMENT = 'STOCK_SENTIMENT', // Stock sentiment analysis block

  // Technical Indicators - Comprehensive Set
  MOMENTUM_INDICATOR = 'MOMENTUM_INDICATOR',
  MOVING_AVERAGE = 'MOVING_AVERAGE',
  TREND_INDICATOR = 'TREND_INDICATOR',
  VOLUME_INDICATOR = 'VOLUME_INDICATOR',
  VOLATILITY_INDICATOR = 'VOLATILITY_INDICATOR',

  // Market Analysis Blocks
  SUPPORT_RESISTANCE = 'SUPPORT_RESISTANCE',
  TREND_LINE_ANALYSIS = 'TREND_LINE_ANALYSIS',
  CHART_PATTERN = 'CHART_PATTERN',
  MARKET_STRUCTURE = 'MARKET_STRUCTURE',
  BREAKOUT_DETECTION = 'BREAKOUT_DETECTION',
  GAP_ANALYSIS = 'GAP_ANALYSIS',

  // Risk Analysis & Signal Enhancement Blocks
  RISK_ANALYZER = 'RISK_ANALYZER',
  TARGET_ANALYZER = 'TARGET_ANALYZER',
  SIGNAL_ENHANCER = 'SIGNAL_ENHANCER',
  RISK_REWARD_ANALYZER = 'RISK_REWARD_ANALYZER',

  // Advanced Analysis Blocks
  CORRELATION_ANALYSIS = 'CORRELATION_ANALYSIS',
  MOMENTUM_SHIFT = 'MOMENTUM_SHIFT',
  TREND_STRENGTH = 'TREND_STRENGTH',
  VOLATILITY_FILTER = 'VOLATILITY_FILTER',
  FIBONACCI_LEVELS = 'FIBONACCI_LEVELS',
  MARKET_SENTIMENT = 'MARKET_SENTIMENT',
  SECTOR_ANALYSIS = 'SECTOR_ANALYSIS',

  // Signal Quality & Filtering Blocks
  SIGNAL_QUALITY_FILTER = 'SIGNAL_QUALITY_FILTER',
  CONFIDENCE_THRESHOLD = 'CONFIDENCE_THRESHOLD',
  SIGNAL_CONFIRMATION_DELAY = 'SIGNAL_CONFIRMATION_DELAY',
  MARKET_HOURS_FILTER = 'MARKET_HOURS_FILTER',

  // Signal Generation Blocks
  PRICE_ACTION_SIGNAL = 'PRICE_ACTION_SIGNAL',
  MULTI_TIMEFRAME_ANALYSIS = 'MULTI_TIMEFRAME_ANALYSIS',
  DIVERGENCE_DETECTION = 'DIVERGENCE_DETECTION',
  VOLUME_CONFIRMATION = 'VOLUME_CONFIRMATION',
  MARKET_REGIME = 'MARKET_REGIME',

  // Logic & Flow Control
  NOT_OPERATOR = 'NOT_OPERATOR',
  TIME_FILTER = 'TIME_FILTER',
  MARKET_CONDITION_FILTER = 'MARKET_CONDITION_FILTER',

  // Debugging
  CONSOLE_LOG = 'CONSOLE_LOG'
}

// Base block interface
export interface Block {
  id: string;
  type: BlockType;
  position: { x: number; y: number };
}

// Technical indicator block
export interface IndicatorBlock extends Block {
  type: BlockType.INDICATOR;
  indicator: string; // 'rsi', 'macd', 'sma', etc.
  parameters: Record<string, any>; // Parameters specific to the indicator
  outputConnections: string[]; // IDs of connected blocks
}

// Price data block
export interface PriceBlock extends Block {
  type: BlockType.PRICE;
  dataPoint: string; // 'open', 'high', 'low', 'close', 'volume', etc.
  timeframe?: string; // Optional timeframe override
  lookback?: number; // How many periods to look back
  outputConnections: string[]; // IDs of connected blocks
}

// Fundamental data block
export interface FundamentalBlock extends Block {
  type: BlockType.FUNDAMENTAL;
  metric: string; // The specific financial metric to retrieve
  statement: 'balance_sheet' | 'income_statement' | 'cash_flow_statement' | 'comprehensive_income'; // Which financial statement
  period?: 'quarterly' | 'annual'; // Time period (defaults to most recent)
  parameters?: Record<string, any>; // Additional parameters
  outputConnections: string[]; // IDs of connected blocks
}

// Condition block (comparison only)
export interface ConditionBlock extends Block {
  type: BlockType.CONDITION;
  operator: string; // '>', '<', '==', '>=', '<=', '!=', 'between'
  inputConnections: string[]; // IDs of input blocks
  compareValue?: number; // Value to compare against
  compareValue2?: number; // Second value for 'between' operator
  trueConnection?: string; // ID of block to execute if condition is true
  falseConnection?: string; // ID of block to execute if condition is false
}

// Comparison block (compare two values)
export interface ComparisonBlock extends Block {
  type: BlockType.COMPARISON;
  operator: string; // '>', '<', '==', '>=', '<=', '!='
  inputConnections: string[]; // IDs of exactly two input blocks [valueA, valueB]
  trueConnection?: string; // ID of block to execute if comparison is true
  falseConnection?: string; // ID of block to execute if comparison is false
}

// Trigger block (final output)
export interface TriggerBlock extends Block {
  type: BlockType.TRIGGER;
  signal: 'bullish' | 'bearish' | 'neutral';
  confidence: number; // 0-100
  inputConnections: string[]; // IDs of input blocks
}



// When Run block (entry point)
export interface WhenRunBlock extends Block {
  type: BlockType.WHEN_RUN;
  outputConnections: string[]; // IDs of connected blocks
}

// Operator block (mathematical operations)
export interface OperatorBlock extends Block {
  type: BlockType.OPERATOR;
  operation: string; // 'add', 'subtract', 'multiply', 'divide', 'average', etc.
  inputConnections: string[]; // IDs of input blocks
  outputConnections: string[]; // IDs of connected blocks
}

// Bullish Confidence Boost block (signal confidence adjustment)
export interface BullishConfidenceBoostBlock extends Block {
  type: BlockType.BULLISH_CONFIDENCE_BOOST;
  percentage: number; // Percentage to boost bullish confidence (e.g., 10 for +10%)
  inputConnections: string[]; // IDs of input blocks
  outputConnections: string[]; // IDs of connected blocks
}

// Bearish Confidence Boost block (signal confidence adjustment)
export interface BearishConfidenceBoostBlock extends Block {
  type: BlockType.BEARISH_CONFIDENCE_BOOST;
  percentage: number; // Percentage to boost bearish confidence (e.g., 10 for +10%)
  inputConnections: string[]; // IDs of input blocks
  outputConnections: string[]; // IDs of connected blocks
}

// Unified Confidence Boost block (signal confidence adjustment)
export interface ConfidenceBoostBlock extends Block {
  type: BlockType.CONFIDENCE_BOOST;
  boostType: 'bullish' | 'bearish' | 'neutral'; // Type of confidence boost
  percentage: number; // Percentage to boost confidence (e.g., 10 for +10%)
  inputConnections: string[]; // IDs of input blocks
  outputConnections: string[]; // IDs of connected blocks
}

// AND logic block (logical AND operation)
export interface AndBlock extends Block {
  type: BlockType.AND;
  inputConnections: string[]; // IDs of input blocks (conditions)
  trueConnection?: string; // ID of block to execute if all inputs are true
  falseConnection?: string; // ID of block to execute if any input is false
}

// OR logic block (logical OR operation)
export interface OrBlock extends Block {
  type: BlockType.OR;
  inputConnections: string[]; // IDs of input blocks (conditions)
  trueConnection?: string; // ID of block to execute if any input is true
  falseConnection?: string; // ID of block to execute if all inputs are false
}

// Candle pattern detection block
export interface CandlePatternBlock extends Block {
  type: BlockType.CANDLE_PATTERN;
  pattern: string; // Pattern type: 'doji', 'hammer', 'shooting_star', 'engulfing_bullish', 'engulfing_bearish', 'any', etc.
  timeframe?: string; // Timeframe: 'day', 'hour', '15minute', '5minute', '1minute'
  inputConnections: string[]; // IDs of input blocks (price data)
  // For specific pattern mode (when pattern is not 'any')
  trueConnection?: string; // ID of block to execute if pattern is detected
  falseConnection?: string; // ID of block to execute if pattern is not detected
  // For any pattern mode (when pattern is 'any')
  bullishConnection?: string; // ID of block to execute for bullish patterns
  bearishConnection?: string; // ID of block to execute for bearish patterns
  neutralConnection?: string; // ID of block to execute for neutral patterns
}

// Stock sentiment analysis block
export interface StockSentimentBlock extends Block {
  type: BlockType.STOCK_SENTIMENT;
  articleLimit?: number; // Number of articles to analyze (default: 10)
  daysBack?: number; // How many days back to look for articles (default: 7)
  inputConnections: string[]; // IDs of input blocks (not required but for consistency)
  // Output connections for different sentiment outcomes
  bullishConnection?: string; // ID of block to execute for bullish sentiment
  bearishConnection?: string; // ID of block to execute for bearish sentiment
  neutralConnection?: string; // ID of block to execute for neutral sentiment
}

// Moving Average block
export interface MovingAverageBlock extends Block {
  type: BlockType.MOVING_AVERAGE;
  averageType: 'sma' | 'ema' | 'wma' | 'vwap';
  period: number;
  timeframe: string;
  source: 'close' | 'open' | 'high' | 'low' | 'hl2' | 'hlc3' | 'ohlc4';
  outputConnections: string[];
}

// Momentum Indicator block
export interface MomentumIndicatorBlock extends Block {
  type: BlockType.MOMENTUM_INDICATOR;
  indicator: 'rsi' | 'stochastic' | 'williams_r' | 'cci';
  period: number;
  timeframe: string;
  smoothK?: number;
  smoothD?: number;
  parameters?: Record<string, any>;
  outputConnections: string[];
}

// Technical Indicator blocks
export interface TechnicalIndicatorBlock extends Block {
  type: BlockType.TREND_INDICATOR | BlockType.VOLUME_INDICATOR | BlockType.VOLATILITY_INDICATOR;
  indicator: string; // Specific indicator type
  parameters: Record<string, any>; // Indicator-specific parameters
  outputConnections: string[]; // IDs of connected blocks
}

// Market Analysis blocks
export interface MarketAnalysisBlock extends Block {
  type: BlockType.SUPPORT_RESISTANCE | BlockType.TREND_LINE_ANALYSIS | BlockType.MARKET_STRUCTURE;
  method: string; // Analysis method
  parameters: Record<string, any>; // Method-specific parameters
  outputConnections?: string[]; // IDs of connected blocks
  bullishConnection?: string; // Connection for bullish output
  bearishConnection?: string; // Connection for bearish output
  neutralConnection?: string; // Connection for neutral output
  atLevelConnection?: string; // Connection for at level output (support/resistance)
  notAtLevelConnection?: string; // Connection for not at level output (support/resistance)
}

// Risk Analysis & Signal Enhancement blocks
export interface RiskAnalysisBlock extends Block {
  type: BlockType.RISK_ANALYZER | BlockType.TARGET_ANALYZER | BlockType.SIGNAL_ENHANCER | BlockType.RISK_REWARD_ANALYZER;
  method: string; // Analysis method
  parameters: Record<string, any>; // Method-specific parameters
  inputConnections: string[]; // IDs of input blocks
  outputConnections: string[]; // IDs of connected blocks
}

// Advanced Analysis blocks
export interface AdvancedAnalysisBlock extends Block {
  type: BlockType.CORRELATION_ANALYSIS | BlockType.MOMENTUM_SHIFT | BlockType.TREND_STRENGTH | BlockType.VOLATILITY_FILTER | BlockType.FIBONACCI_LEVELS | BlockType.MARKET_SENTIMENT | BlockType.SECTOR_ANALYSIS;
  method: string; // Analysis method
  parameters: Record<string, any>; // Method-specific parameters
  inputConnections: string[]; // IDs of input blocks
  outputConnections: string[]; // IDs of connected blocks
}

// Signal Quality & Filtering blocks
export interface SignalQualityBlock extends Block {
  type: BlockType.SIGNAL_QUALITY_FILTER | BlockType.CONFIDENCE_THRESHOLD | BlockType.SIGNAL_CONFIRMATION_DELAY | BlockType.MARKET_HOURS_FILTER;
  method: string; // Filter method
  parameters: Record<string, any>; // Method-specific parameters
  inputConnections: string[]; // IDs of input blocks
  outputConnections: string[]; // IDs of connected blocks
}

// Signal Generation blocks
export interface SignalGenerationBlock extends Block {
  type: BlockType.PRICE_ACTION_SIGNAL | BlockType.MULTI_TIMEFRAME_ANALYSIS | BlockType.DIVERGENCE_DETECTION | BlockType.VOLUME_CONFIRMATION | BlockType.MARKET_REGIME;
  signalType: string; // Type of signal to generate
  parameters: Record<string, any>; // Signal-specific parameters
  outputConnections: string[]; // IDs of connected blocks
}

// Logic & Flow Control blocks
export interface LogicFlowBlock extends Block {
  type: BlockType.NOT_OPERATOR | BlockType.TIME_FILTER | BlockType.MARKET_CONDITION_FILTER;
  parameters: Record<string, any>; // Logic-specific parameters
  inputConnections: string[]; // IDs of input blocks
  outputConnections: string[]; // IDs of connected blocks
}

// Console Log block for debugging
export interface ConsoleLogBlock extends Block {
  type: BlockType.CONSOLE_LOG;
  message: string; // Message to log
  inputConnections: string[]; // IDs of input blocks
  outputConnections: string[]; // IDs of connected blocks
}

// Union type for all block types
export type BlockUnion =
  | WhenRunBlock
  | IndicatorBlock
  | PriceBlock
  | FundamentalBlock
  | ConditionBlock
  | ComparisonBlock
  | TriggerBlock

  | OperatorBlock
  | BullishConfidenceBoostBlock
  | BearishConfidenceBoostBlock
  | ConfidenceBoostBlock
  | AndBlock
  | OrBlock
  | CandlePatternBlock
  | StockSentimentBlock
  | MovingAverageBlock
  | MomentumIndicatorBlock
  | TechnicalIndicatorBlock
  | MarketAnalysisBlock
  | RiskAnalysisBlock
  | AdvancedAnalysisBlock
  | SignalQualityBlock
  | SignalGenerationBlock
  | LogicFlowBlock
  | ConsoleLogBlock;

// Agent configuration
export interface AgentConfig {
  name?: string; // Optional name for the agent
  description?: string;
  blocks: BlockUnion[];
  entryBlockId: string; // ID of the first block to execute
}

// Agent execution result
export interface AgentResult {
  signal: 'bullish' | 'bearish' | 'neutral';
  confidence: number; // 0-100
  reasoning: string;
  metrics: Record<string, any>; // Metrics calculated during execution
  executionPath: string[]; // IDs of blocks executed in order
  executionTime: number; // Time taken to execute in ms
  timestamp: string; // ISO timestamp of execution
}

// Enhanced Signal Output Format
export interface EnhancedSignalOutput extends AgentResult {
  // Risk Management Guidance
  risk_management?: {
    entry_price: number;
    stop_loss: {
      price: number;
      percentage: number;
      method: string;
      reasoning: string;
    };
    take_profit: {
      targets: Array<{
        price: number;
        percentage: number;
        probability: number;
        level_type: string;
      }>;
      method: string;
    };
    risk_reward_ratio: number;
    position_size_suggestion: {
      percentage_of_portfolio: number;
      dollar_amount: number;
      reasoning: string;
    };
  };

  // Advanced Analysis
  advanced_analysis?: {
    trend_strength: {
      score: number; // 0-100
      direction: 'up' | 'down' | 'sideways';
      method: string;
    };
    volatility_assessment: {
      level: 'low' | 'moderate' | 'high';
      percentile: number;
      suitable_for_strategy: boolean;
    };
    support_resistance: {
      nearest_support: number;
      nearest_resistance: number;
      strength_score: number;
    };
    correlation_analysis: {
      market_correlation: number;
      sector_correlation: number;
      relative_strength: number;
    };
    momentum_analysis: {
      momentum_score: number;
      momentum_shift_detected: boolean;
      momentum_direction: 'increasing' | 'decreasing' | 'stable';
    };
  };

  // Signal Quality Metrics
  signal_quality?: {
    overall_score: number; // 0-100
    volume_confirmation: boolean;
    trend_alignment: boolean;
    multiple_timeframe_agreement: boolean;
    quality_factors: string[];
  };

  // Educational Information
  educational?: {
    strategy_explanation: string;
    risk_warnings: string[];
    market_context: string;
    similar_historical_setups: number;
  };
}

// Financial statement data structure
export interface FinancialStatement {
  [metric: string]: {
    label: string;
    order: number;
    unit: string;
    value: number;
  } | number;
}

// Complete financials data structure
export interface FinancialsData {
  balance_sheet?: FinancialStatement;
  income_statement?: FinancialStatement;
  cash_flow_statement?: FinancialStatement;
  comprehensive_income?: FinancialStatement;
}

// Polygon data structure
export interface PolygonData {
  symbol: string; // Stock symbol for this data
  price: {
    current: number;
    open: number;
    high: number;
    low: number;
    close: number;
    volume: number;
    timestamp: number;
  };
  historical: {
    open: number[];
    high: number[];
    low: number[];
    close: number[];
    volume: number[];
    timestamp: number[];
  };
  indicators: Record<string, any>; // Calculated indicators
  fundamentals: Record<string, any>; // Legacy fundamental data
  financials?: FinancialsData; // Complete financial statements data
}
