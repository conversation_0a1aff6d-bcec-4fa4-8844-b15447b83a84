import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Create paper trading accounts for all users who don't have one
async function createPaperAccountsForAllUsers() {
  try {
    console.log('Starting to create paper trading accounts for all users...')
    
    // Get all users from auth.users
    const { data: users, error: usersError } = await supabase.auth.admin.listUsers()
    
    if (usersError) {
      throw new Error(`Failed to fetch users: ${usersError.message}`)
    }
    
    if (!users || users.users.length === 0) {
      return { message: 'No users found', created: 0 }
    }
    
    console.log(`Found ${users.users.length} users`)
    
    let created = 0
    let errors = 0
    
    for (const user of users.users) {
      try {
        // Check if user already has a paper trading account
        const { data: existingAccount } = await supabase
          .from('paper_trading_accounts')
          .select('id')
          .eq('user_id', user.id)
          .single()
        
        if (existingAccount) {
          console.log(`User ${user.id} already has a paper trading account`)
          continue
        }
        
        // Create paper trading account for this user
        const { error: createError } = await supabase
          .from('paper_trading_accounts')
          .insert({
            user_id: user.id,
            account_name: 'Default Account',
            starting_balance: 100000.00,
            current_balance: 100000.00,
            available_balance: 100000.00,
            portfolio_value: 0.00,
            total_value: 100000.00,
            unrealized_pnl: 0.00,
            realized_pnl: 0.00,
            total_pnl: 0.00,
            is_active: true
          })
        
        if (createError) {
          console.error(`Failed to create account for user ${user.id}:`, createError)
          errors++
        } else {
          console.log(`Created paper trading account for user ${user.id}`)
          created++
        }
        
      } catch (error) {
        console.error(`Error processing user ${user.id}:`, error)
        errors++
      }
    }
    
    return {
      message: `Paper trading account creation completed`,
      totalUsers: users.users.length,
      created,
      errors,
      skipped: users.users.length - created - errors
    }
    
  } catch (error) {
    console.error('Error in createPaperAccountsForAllUsers:', error)
    throw error
  }
}

// Main request handler
serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    console.log('Create paper accounts function called')
    
    const result = await createPaperAccountsForAllUsers()
    
    return new Response(
      JSON.stringify({ success: true, data: result }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    )

  } catch (error) {
    console.error('Create paper accounts error:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message || 'Internal server error' 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500 
      }
    )
  }
})
