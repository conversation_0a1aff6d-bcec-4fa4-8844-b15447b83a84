import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Credentials': 'true'
};

// Get the encryption key from environment variables
const ENCRYPTION_KEY = Deno.env.get('ENCRYPTION_KEY') || '';

// Initialize Supabase client with admin privileges
const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
const supabase = createClient(supabaseUrl, supabaseServiceKey)

/**
 * Decrypts data using AES-GCM
 * @param encryptedData - Base64 encoded encrypted data with IV
 * @param password - The password to use for decryption
 * @returns The decrypted data
 */
async function decryptData(encryptedData: string, password: string): Promise<string> {
  try {
    // Convert from base64
    const encryptedBytes = Uint8Array.from(atob(encryptedData), c => c.charCodeAt(0));

    // Extract IV (first 12 bytes)
    const iv = encryptedBytes.slice(0, 12);

    // Extract encrypted data (everything after IV)
    const data = encryptedBytes.slice(12);

    // Derive the key
    const encoder = new TextEncoder();
    const passwordBuffer = encoder.encode(password);

    // Import the password as a key
    const baseKey = await crypto.subtle.importKey(
      'raw',
      passwordBuffer,
      { name: 'PBKDF2' },
      false,
      ['deriveKey']
    );

    // Salt for key derivation
    const salt = new Uint8Array([
      0x63, 0x72, 0x79, 0x70, 0x74, 0x6f, 0x53, 0x61,
      0x6c, 0x74, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36
    ]);

    // Derive a key using PBKDF2
    const key = await crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt,
        iterations: 100000,
        hash: 'SHA-256'
      },
      baseKey,
      { name: 'AES-GCM', length: 256 },
      false,
      ['decrypt']
    );

    // Decrypt the data
    const decryptedBuffer = await crypto.subtle.decrypt(
      {
        name: 'AES-GCM',
        iv
      },
      key,
      data
    );

    // Decode the data
    const decoder = new TextDecoder();
    return decoder.decode(decryptedBuffer);
  } catch (error) {
    console.error('Decryption error:', error);
    throw new Error('Failed to decrypt data');
  }
}

/**
 * Deobfuscates the data by extracting it from the generic object
 * @param data - The obfuscated data
 * @returns The original data
 */
function deobfuscateData(data: any): any {
  return data.client_data;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    })
  }

  try {
    // Verify authentication
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(JSON.stringify({ error: 'Missing authorization header' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // Extract the token
    const token = authHeader.replace('Bearer ', '');

    // Verify the token with Supabase
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // Parse the request body
    let body;
    try {
      body = await req.json()
    } catch (e) {
      return new Response(JSON.stringify({ error: 'Invalid request body' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // Extract and decrypt the data
    const { data: encryptedData, metadata } = body;

    if (!encryptedData) {
      return new Response(JSON.stringify({ error: 'Missing data parameter' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // Decrypt the prompt
    const prompt = await decryptData(encryptedData, ENCRYPTION_KEY);

    // Extract options and config from metadata
    const { options = {}, config = {} } = deobfuscateData(metadata);

    // Get the Gemini API key
    const apiKey = Deno.env.get('GEMINI_API_KEY');

    if (!apiKey) {
      return new Response(JSON.stringify({ error: 'API key not configured' }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // Prepare request body for Gemini API
    const requestBody = {
      contents: [{
        parts: [{
          text: prompt
        }]
      }],
      generationConfig: {
        temperature: 0.7,
        maxOutputTokens: 1000,
        ...(config.generationConfig || {})
      },
      // Add Google Search tool if grounding is enabled
      ...(options.useGrounding ? {
        tools: [{ googleSearch: {} }]
      } : {}),
      ...(config || {})
    };

    const startTime = Date.now();

    try {
      // Call the Gemini API
      const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-goog-api-key': apiKey,
        },
        body: JSON.stringify(requestBody)
      });

      const data = await response.json();
      const endTime = Date.now();
      const latency = (endTime - startTime) / 1000; // Convert to seconds

      if (!response.ok) {
        return new Response(JSON.stringify({
          error: data?.error?.message || 'Failed to get API response',
          status: response.status
        }), {
          status: response.status,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        })
      }

      return new Response(JSON.stringify({
        data,
        latency,
        status: response.status
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    } catch (error) {
      const endTime = Date.now();
      const latency = (endTime - startTime) / 1000;

      return new Response(JSON.stringify({
        error: error.message,
        latency
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }
})
