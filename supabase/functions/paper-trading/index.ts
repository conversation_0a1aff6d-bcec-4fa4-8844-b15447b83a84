import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Polygon API key for real-time price data
const polygonApiKey = Deno.env.get('POLYGON_API_KEY')!

interface PaperTradingOrder {
  symbol: string
  order_type: 'market' | 'limit' | 'stop' | 'stop_limit'
  side: 'buy' | 'sell'
  quantity: number
  price?: number
  stop_price?: number
  time_in_force?: 'DAY' | 'GTC' | 'IOC' | 'FOK'
}

interface MarketPrice {
  symbol: string
  price: number
  timestamp: string
}

// Get current market price from Polygon API
async function getCurrentPrice(symbol: string): Promise<number> {
  if (!polygonApiKey) {
    throw new Error('Polygon API key not configured')
  }

  try {
    const url = `https://api.polygon.io/v2/last/trade/${symbol}?apiKey=${polygonApiKey}`
    const response = await fetch(url)
    const data = await response.json()

    if (data.results && data.results.price) {
      return data.results.price
    }

    // Fallback to previous close if last trade not available
    const closeUrl = `https://api.polygon.io/v2/aggs/ticker/${symbol}/prev?apiKey=${polygonApiKey}`
    const closeResponse = await fetch(closeUrl)
    const closeData = await closeResponse.json()

    if (closeData.results && closeData.results.length > 0) {
      return closeData.results[0].c
    }

    throw new Error(`No price data available for ${symbol}`)
  } catch (error) {
    console.error(`Error fetching price for ${symbol}:`, error)
    throw error
  }
}

// Get user's paper trading account
async function getUserAccount(userId: string) {
  let { data, error } = await supabase
    .from('paper_trading_accounts')
    .select('*')
    .eq('user_id', userId)
    .eq('is_active', true)
    .single()

  if (error && error.code === 'PGRST116') {
    // No account found, create one
    console.log(`Creating paper trading account for user ${userId}`)
    const { data: newAccount, error: createError } = await supabase
      .from('paper_trading_accounts')
      .insert({
        user_id: userId,
        account_name: 'Default Account',
        starting_balance: 100000.00,
        current_balance: 100000.00,
        available_balance: 100000.00,
        total_value: 100000.00
      })
      .select()
      .single()

    if (createError) {
      console.error('Error creating user account:', createError)
      throw new Error('Failed to create user account')
    }

    return newAccount
  }

  if (error) {
    console.error('Error fetching user account:', error)
    throw new Error('Failed to fetch user account')
  }

  return data
}

// Place a paper trading order
async function placeOrder(userId: string, orderData: PaperTradingOrder) {
  const account = await getUserAccount(userId)
  
  // Get current market price
  const currentPrice = await getCurrentPrice(orderData.symbol)
  
  // Calculate order value and validate buying power
  const executionPrice = orderData.order_type === 'market' ? currentPrice : (orderData.price || currentPrice)
  const orderValue = orderData.quantity * executionPrice
  
  if (orderData.side === 'buy' && orderValue > account.available_balance) {
    throw new Error('Insufficient buying power')
  }
  
  // For sell orders, check if user has enough shares
  if (orderData.side === 'sell') {
    const { data: position } = await supabase
      .from('paper_trading_positions')
      .select('quantity')
      .eq('account_id', account.id)
      .eq('symbol', orderData.symbol)
      .single()
    
    if (!position || position.quantity < orderData.quantity) {
      throw new Error('Insufficient shares to sell')
    }
  }
  
  // Create the order
  const { data: order, error: orderError } = await supabase
    .from('paper_trading_orders')
    .insert({
      account_id: account.id,
      user_id: userId,
      symbol: orderData.symbol,
      order_type: orderData.order_type,
      side: orderData.side,
      quantity: orderData.quantity,
      price: orderData.price,
      stop_price: orderData.stop_price,
      time_in_force: orderData.time_in_force || 'DAY',
      status: orderData.order_type === 'market' ? 'filled' : 'pending',
      filled_quantity: orderData.order_type === 'market' ? orderData.quantity : 0,
      avg_fill_price: orderData.order_type === 'market' ? executionPrice : null,
      total_value: orderData.order_type === 'market' ? orderValue : null,
      filled_at: orderData.order_type === 'market' ? new Date().toISOString() : null,
      expires_at: orderData.time_in_force === 'DAY' ? 
        new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() : null
    })
    .select()
    .single()
  
  if (orderError) {
    console.error('Error creating order:', orderError)
    throw new Error('Failed to create order')
  }
  
  // If market order, execute immediately
  if (orderData.order_type === 'market') {
    await executeOrder(order.id, executionPrice)
  }
  
  return order
}

// Execute an order (fill it)
async function executeOrder(orderId: string, fillPrice: number) {
  // Get order details
  const { data: order, error: orderError } = await supabase
    .from('paper_trading_orders')
    .select('*')
    .eq('id', orderId)
    .single()
  
  if (orderError || !order) {
    throw new Error('Order not found')
  }
  
  const orderValue = order.quantity * fillPrice
  const commission = 0 // No commission for paper trading
  
  // Update order status
  await supabase
    .from('paper_trading_orders')
    .update({
      status: 'filled',
      filled_quantity: order.quantity,
      avg_fill_price: fillPrice,
      total_value: orderValue,
      filled_at: new Date().toISOString()
    })
    .eq('id', orderId)
  
  // Update account balance using the database function
  const balanceChange = order.side === 'buy' ? -orderValue : orderValue
  const isRealized = order.side === 'sell'

  await supabase.rpc('update_account_balance', {
    p_account_id: order.account_id,
    p_balance_change: balanceChange,
    p_is_realized: isRealized
  })
  
  // Update or create position
  await updatePosition(order.account_id, order.symbol, order.side, order.quantity, fillPrice)
  
  // Create transaction record
  await supabase
    .from('paper_trading_transactions')
    .insert({
      account_id: order.account_id,
      user_id: order.user_id,
      order_id: orderId,
      symbol: order.symbol,
      transaction_type: order.side,
      quantity: order.quantity,
      price: fillPrice,
      amount: order.side === 'buy' ? -orderValue : orderValue,
      commission: commission,
      description: `${order.side.toUpperCase()} ${order.quantity} shares of ${order.symbol} at $${fillPrice}`
    })
  
  return order
}

// Update position after order execution
async function updatePosition(accountId: string, symbol: string, side: 'buy' | 'sell', quantity: number, price: number) {
  const { data: existingPosition } = await supabase
    .from('paper_trading_positions')
    .select('*')
    .eq('account_id', accountId)
    .eq('symbol', symbol)
    .single()
  
  if (!existingPosition) {
    // Create new position
    if (side === 'buy') {
      await supabase
        .from('paper_trading_positions')
        .insert({
          account_id: accountId,
          user_id: (await supabase.from('paper_trading_accounts').select('user_id').eq('id', accountId).single()).data.user_id,
          symbol: symbol,
          quantity: quantity,
          avg_cost: price,
          market_price: price,
          market_value: quantity * price,
          total_cost: quantity * price,
          unrealized_pnl: 0,
          unrealized_pnl_percent: 0,
          last_price_update: new Date().toISOString()
        })
    }
  } else {
    // Update existing position
    const newQuantity = side === 'buy' ? 
      existingPosition.quantity + quantity : 
      existingPosition.quantity - quantity
    
    if (newQuantity === 0) {
      // Close position
      await supabase
        .from('paper_trading_positions')
        .delete()
        .eq('account_id', accountId)
        .eq('symbol', symbol)
    } else {
      // Update position
      const newAvgCost = side === 'buy' ?
        ((existingPosition.avg_cost * existingPosition.quantity) + (price * quantity)) / newQuantity :
        existingPosition.avg_cost
      
      await supabase
        .from('paper_trading_positions')
        .update({
          quantity: newQuantity,
          avg_cost: newAvgCost,
          market_price: price,
          market_value: newQuantity * price,
          total_cost: Math.abs(newQuantity) * newAvgCost,
          unrealized_pnl: (price - newAvgCost) * newQuantity,
          unrealized_pnl_percent: ((price - newAvgCost) / newAvgCost) * 100,
          last_price_update: new Date().toISOString()
        })
        .eq('account_id', accountId)
        .eq('symbol', symbol)
    }
  }
}

// Get user's portfolio summary
async function getPortfolioSummary(userId: string) {
  const account = await getUserAccount(userId)

  const { data: positions } = await supabase
    .from('paper_trading_positions')
    .select('*')
    .eq('account_id', account.id)

  const { data: orders } = await supabase
    .from('paper_trading_orders')
    .select('*')
    .eq('account_id', account.id)
    .order('created_at', { ascending: false })
    .limit(50)

  return {
    account,
    positions: positions || [],
    recent_orders: orders || []
  }
}

// Update market prices for all positions
async function updateMarketPrices(symbols: string[]) {
  const priceUpdates = []

  for (const symbol of symbols) {
    try {
      const currentPrice = await getCurrentPrice(symbol)

      // Update all positions for this symbol
      const { error } = await supabase.rpc('update_position_market_values', {
        p_symbol: symbol,
        p_market_price: currentPrice
      })

      if (error) {
        console.error(`Error updating prices for ${symbol}:`, error)
      } else {
        priceUpdates.push({ symbol, price: currentPrice })
      }
    } catch (error) {
      console.error(`Failed to get price for ${symbol}:`, error)
    }
  }

  return priceUpdates
}

// Cancel an order
async function cancelOrder(userId: string, orderId: string) {
  const { data: order, error } = await supabase
    .from('paper_trading_orders')
    .update({ status: 'cancelled' })
    .eq('id', orderId)
    .eq('user_id', userId)
    .eq('status', 'pending')
    .select()
    .single()

  if (error) {
    throw new Error('Failed to cancel order or order not found')
  }

  return order
}

// Main request handler
serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    // Get user from JWT token
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('No authorization header')
    }

    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      throw new Error('Invalid token')
    }

    const { action, ...params } = await req.json()

    let result
    switch (action) {
      case 'place_order':
        result = await placeOrder(user.id, params as PaperTradingOrder)
        break

      case 'cancel_order':
        result = await cancelOrder(user.id, params.order_id)
        break

      case 'get_portfolio':
        result = await getPortfolioSummary(user.id)
        break

      case 'update_prices':
        result = await updateMarketPrices(params.symbols || [])
        break

      case 'get_account':
        result = await getUserAccount(user.id)
        break

      default:
        throw new Error(`Unknown action: ${action}`)
    }

    return new Response(
      JSON.stringify({ success: true, data: result }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )

  } catch (error) {
    console.error('Paper trading error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'Internal server error'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400
      }
    )
  }
})
