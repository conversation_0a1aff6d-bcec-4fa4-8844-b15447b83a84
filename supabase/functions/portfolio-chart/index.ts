import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';

// Define CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface PortfolioStock {
  ticker: string;
  allocation: number;
  name?: string;
}

interface ChartRequest {
  action: string;
  stocks: PortfolioStock[];
  startDate: string;
  endDate?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Create a Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? '';
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '';
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Get the Polygon API key from environment variables
    const polygonApiKey = Deno.env.get('POLYGON_API_KEY');
    if (!polygonApiKey) {
      throw new Error('Polygon API key not configured');
    }

    // Parse the request body
    const { action, stocks, startDate, endDate = new Date().toISOString().split('T')[0] } = await req.json() as ChartRequest;

    if (action === 'getChartData') {
      console.log(`Fetching chart data for ${stocks.length} stocks from ${startDate} to ${endDate}`);

      // Validate dates
      const start = new Date(startDate);
      const end = new Date(endDate);

      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        throw new Error('Invalid date format');
      }

      // Ensure we have at least one stock
      if (!stocks || stocks.length === 0) {
        throw new Error('No stocks provided');
      }

      // Fetch historical data for each stock
      const stockData: Record<string, any[]> = {};

      for (const stock of stocks) {
        console.log(`Fetching data for ${stock.ticker} with allocation ${stock.allocation}%`);

        try {
          // Use Polygon API to get historical data
          const url = `https://api.polygon.io/v2/aggs/ticker/${stock.ticker}/range/1/day/${startDate}/${endDate}?apiKey=${polygonApiKey}`;
          const response = await fetch(url);
          const data = await response.json();

          if (data.results && data.results.length > 0) {
            stockData[stock.ticker] = data.results.map((result: any) => ({
              date: new Date(result.t).toISOString().split('T')[0],
              close: result.c,
              open: result.o,
              high: result.h,
              low: result.l,
              volume: result.v
            }));
          } else {
            console.log(`No data found for ${stock.ticker}`);
            stockData[stock.ticker] = [];
          }
        } catch (error) {
          console.error(`Error fetching data for ${stock.ticker}:`, error);
          stockData[stock.ticker] = [];
        }
      }

      // Calculate portfolio value over time
      const portfolioValues: { date: string; value: number }[] = [];
      const allDates = new Set<string>();

      // Collect all available dates
      for (const ticker in stockData) {
        for (const dataPoint of stockData[ticker]) {
          allDates.add(dataPoint.date);
        }
      }

      // For 5-year data, we might have too many points
      // Let's sample the data to reduce the number of points
      // This improves performance and reduces payload size

      // Sort dates chronologically
      let sortedDates = Array.from(allDates).sort();

      // If we have more than 100 data points, sample them to reduce the payload size
      // For a 5-year chart, we might have over 1000 points (trading days)
      if (sortedDates.length > 100) {
        console.log(`Sampling ${sortedDates.length} data points down to ~100 points`);

        // Calculate sampling interval
        const samplingInterval = Math.ceil(sortedDates.length / 100);

        // Sample the dates
        const sampledDates: string[] = [];
        for (let i = 0; i < sortedDates.length; i += samplingInterval) {
          sampledDates.push(sortedDates[i]);
        }

        // Always include the first and last date
        if (sampledDates[0] !== sortedDates[0]) {
          sampledDates.unshift(sortedDates[0]);
        }
        if (sampledDates[sampledDates.length - 1] !== sortedDates[sortedDates.length - 1]) {
          sampledDates.push(sortedDates[sortedDates.length - 1]);
        }

        // Replace the sorted dates with the sampled dates
        sortedDates = sampledDates;
        console.log(`Sampled down to ${sortedDates.length} data points`);
      }

      if (sortedDates.length === 0) {
        // No data available
        return new Response(
          JSON.stringify({
            success: true,
            chartData: [],
            message: 'No historical data available for the selected stocks'
          }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }

      // Initialize with the first date's prices
      const initialValues: Record<string, number> = {};
      let initialTotal = 0;

      // Find the first available price for each stock
      for (const ticker in stockData) {
        const stockPoints = stockData[ticker];
        if (stockPoints.length > 0) {
          // Find the earliest data point
          const earliestPoint = stockPoints.reduce((earliest, current) => {
            return new Date(current.date) < new Date(earliest.date) ? current : earliest;
          }, stockPoints[0]);

          initialValues[ticker] = earliestPoint.close;
          const stock = stocks.find(s => s.ticker === ticker);
          if (stock) {
            initialTotal += earliestPoint.close * (stock.allocation / 100);
          }
        }
      }

      // Calculate portfolio value for each date
      for (const date of sortedDates) {
        let portfolioValue = 0;
        let validDataPoints = 0;

        for (const ticker in stockData) {
          // Find the data point for this date
          const dataPoint = stockData[ticker].find(dp => dp.date === date);

          if (dataPoint) {
            const stock = stocks.find(s => s.ticker === ticker);
            if (stock) {
              // Calculate the value based on allocation
              portfolioValue += dataPoint.close * (stock.allocation / 100);
              validDataPoints++;
            }
          } else if (initialValues[ticker]) {
            // Use the initial value if no data for this date
            const stock = stocks.find(s => s.ticker === ticker);
            if (stock) {
              portfolioValue += initialValues[ticker] * (stock.allocation / 100);
              validDataPoints++;
            }
          }
        }

        // Only add the date if we have data for at least one stock
        if (validDataPoints > 0) {
          portfolioValues.push({
            date,
            value: parseFloat(portfolioValue.toFixed(2))
          });
        }
      }

      // Normalize values to start from 100 (percentage basis)
      const normalizedValues = portfolioValues.map((point, index) => {
        if (index === 0) {
          return { date: point.date, value: 100 };
        } else {
          const percentChange = (point.value / portfolioValues[0].value) * 100;
          return { date: point.date, value: parseFloat(percentChange.toFixed(2)) };
        }
      });

      return new Response(
        JSON.stringify({
          success: true,
          chartData: portfolioValues,
          normalizedData: normalizedValues,
          initialValue: portfolioValues.length > 0 ? portfolioValues[0].value : 0
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    throw new Error(`Unsupported action: ${action}`);
  } catch (error) {
    console.error('Error processing request:', error);

    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
    );
  }
});
