const REDIS_URL = Deno.env.get('UPSTASH_REDIS_URL')!;

export async function getCachedData(key: string) {
  try {
    const response = await fetch(`${REDIS_URL}/get/${key}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) return null;
    
    const data = await response.json();
    return data.result;
  } catch (error) {
    return null;
  }
}

export async function setCachedData(key: string, value: any, expireInSeconds = 3600) {
  try {
    const response = await fetch(`${REDIS_URL}/set/${key}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        value,
        exp: expireInSeconds,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to set cache');
    }

    return true;
  } catch (error) {
    return false;
  }
}
