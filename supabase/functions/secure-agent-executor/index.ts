import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface SecureExecutionRequest {
  license_key: string;
  symbol: string;
  timeframe?: string;
  request_fingerprint?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get user from auth header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('No authorization header')
    }

    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      throw new Error('Invalid authentication token')
    }

    // Parse request body
    const requestData: SecureExecutionRequest = await req.json()
    
    if (!requestData.license_key || !requestData.symbol) {
      throw new Error('Missing required parameters: license_key and symbol')
    }

    console.log(`Secure agent execution request from user ${user.id} for license ${requestData.license_key}`)

    // SECURITY: Validate license and get agent access
    const { data: licenseValidation, error: licenseError } = await supabase
      .rpc('validate_agent_license', {
        p_license_key: requestData.license_key,
        p_user_id: user.id,
        p_request_fingerprint: requestData.request_fingerprint || 'unknown'
      })

    if (licenseError || !licenseValidation?.valid) {
      // Log security violation
      await supabase
        .from('agent_access_violations')
        .insert({
          user_id: user.id,
          license_key: requestData.license_key,
          violation_type: 'unauthorized_access',
          violation_details: {
            error: licenseValidation?.error || 'License validation failed',
            symbol: requestData.symbol,
            timeframe: requestData.timeframe
          },
          ip_address: req.headers.get('x-forwarded-for') || 'unknown',
          user_agent: req.headers.get('user-agent') || 'unknown'
        })

      return new Response(JSON.stringify({
        success: false,
        error: licenseValidation?.error || 'Access denied'
      }), {
        status: 403,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    const agentId = licenseValidation.agent_id

    // SECURITY: Get agent configuration securely (only for execution, not exposure)
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('name, is_encrypted, encrypted_config, configuration, security_level')
      .eq('id', agentId)
      .single()

    if (agentError || !agent) {
      throw new Error('Agent not found or access denied')
    }

    // SECURITY: For encrypted/paid agents, use secure execution without exposing config
    let executionResult
    
    if (agent.is_encrypted && agent.security_level > 0) {
      // SECURE EXECUTION: Execute without exposing internal configuration
      executionResult = await executeSecureAgent(agent, requestData.symbol, requestData.timeframe || 'day')
    } else {
      // Regular execution for free/public agents
      executionResult = await executeRegularAgent(agent.configuration, requestData.symbol, requestData.timeframe || 'day')
    }

    // Log successful execution (without sensitive data)
    await supabase
      .from('secure_agent_executions')
      .insert({
        license_key: requestData.license_key,
        agent_id: agentId,
        user_id: user.id,
        symbol: requestData.symbol,
        timeframe: requestData.timeframe || 'day',
        execution_result: {
          signal: executionResult.signal,
          confidence: executionResult.confidence,
          reasoning: executionResult.reasoning,
          timestamp: executionResult.timestamp
        },
        execution_time_ms: executionResult.executionTime,
        request_fingerprint: requestData.request_fingerprint || 'unknown',
        ip_address: req.headers.get('x-forwarded-for') || 'unknown',
        user_agent: req.headers.get('user-agent') || 'unknown'
      })

    // Return only the execution result (no internal configuration exposed)
    return new Response(JSON.stringify({
      success: true,
      result: {
        signal: executionResult.signal,
        confidence: executionResult.confidence,
        reasoning: executionResult.reasoning,
        timestamp: executionResult.timestamp,
        agent_name: licenseValidation.custom_name || agent.name,
        usage_count: licenseValidation.usage_count,
        max_usage_limit: licenseValidation.max_usage_limit
      }
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })

  } catch (error) {
    console.error('Secure agent execution error:', error)
    
    return new Response(JSON.stringify({
      success: false,
      error: error.message || 'Internal server error'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})

/**
 * SECURITY: Execute agent securely without exposing configuration
 * This function runs the agent logic server-side and only returns results
 */
async function executeSecureAgent(agent: any, symbol: string, timeframe: string) {
  // SECURITY NOTE: This is where we would implement the secure execution
  // For now, we'll call the existing agent-runner but in production this should be
  // a completely separate, obfuscated execution environment
  
  const supabaseUrl = Deno.env.get('SUPABASE_URL')!
  const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY')!
  
  try {
    const response = await fetch(`${supabaseUrl}/functions/v1/agent-runner`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseAnonKey}`
      },
      body: JSON.stringify({
        // SECURITY: Use decrypted config only for execution, never expose it
        agentConfig: agent.configuration, // In production, this would be decrypted server-side
        symbol: symbol,
        timeframe: timeframe,
        secure_mode: true // Flag to indicate secure execution
      })
    })

    if (!response.ok) {
      throw new Error(`Agent execution failed: ${response.statusText}`)
    }

    const result = await response.json()
    return result.result || result

  } catch (error) {
    console.error('Secure agent execution error:', error)
    throw new Error('Agent execution failed')
  }
}

/**
 * Execute regular (non-encrypted) agent
 */
async function executeRegularAgent(configuration: any, symbol: string, timeframe: string) {
  const supabaseUrl = Deno.env.get('SUPABASE_URL')!
  const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY')!
  
  try {
    const response = await fetch(`${supabaseUrl}/functions/v1/agent-runner`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseAnonKey}`
      },
      body: JSON.stringify({
        agentConfig: configuration,
        symbol: symbol,
        timeframe: timeframe
      })
    })

    if (!response.ok) {
      throw new Error(`Agent execution failed: ${response.statusText}`)
    }

    const result = await response.json()
    return result.result || result

  } catch (error) {
    console.error('Regular agent execution error:', error)
    throw new Error('Agent execution failed')
  }
}
