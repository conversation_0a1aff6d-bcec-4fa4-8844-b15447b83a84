import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

// This function is designed to be called by a cron job service
// It triggers the paper trading processor at regular intervals

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
      }
    })
  }

  try {
    console.log('Paper trading cron job triggered at:', new Date().toISOString())
    
    // Get the base URL for the Supabase functions
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const processorUrl = `${supabaseUrl}/functions/v1/paper-trading-processor`
    
    // Call the paper trading processor
    const response = await fetch(processorUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${Deno.env.get('SUPABASE_ANON_KEY')}`
      },
      body: JSON.stringify({ trigger: 'cron' })
    })
    
    const result = await response.json()
    
    if (result.success) {
      console.log('Paper trading processor completed successfully')
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: 'Paper trading cron job completed successfully',
          timestamp: new Date().toISOString()
        }),
        { 
          headers: { 
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          status: 200 
        }
      )
    } else {
      console.error('Paper trading processor failed:', result.error)
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Paper trading processor failed',
          details: result.error,
          timestamp: new Date().toISOString()
        }),
        { 
          headers: { 
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          status: 500 
        }
      )
    }
    
  } catch (error) {
    console.error('Cron job error:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message,
        timestamp: new Date().toISOString()
      }),
      { 
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        },
        status: 500 
      }
    )
  }
})
