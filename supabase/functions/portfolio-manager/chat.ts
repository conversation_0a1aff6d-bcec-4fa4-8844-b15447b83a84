// Chat functionality for portfolio-manager edge function

interface PortfolioStock {
  ticker: string;
  allocation: number;
  name?: string;
  domain?: string;
}

interface Portfolio {
  name: string;
  description: string;
  stocks: PortfolioStock[];
  investmentAmount?: number;
  investmentDate?: string;
  currentValue?: number;
  currentReturn?: number;
}

interface ChatResponse {
  message: string;
  portfolioChanges?: {
    stocks?: PortfolioStock[];
  };
}

export async function handleChatMessage(
  message: string,
  portfolio: Portfolio
): Promise<ChatResponse> {
  try {
    const apiKey = Deno.env.get('GEMINI_API_KEY');
    if (!apiKey) {
      throw new Error('Gemini API key not configured');
    }

    // Create a prompt for <PERSON> to interpret the chat message
    const prompt = `
    You are a professional financial advisor assistant. You're helping a user manage their investment portfolio.

    The user has the following portfolio:
    Name: ${portfolio.name}
    Description: ${portfolio.description}
    Stocks:
    ${portfolio.stocks.map(stock => `- ${stock.ticker} (${stock.name || stock.ticker}): ${stock.allocation}%`).join('\n')}

    The user has sent the following message about their portfolio:
    "${message}"

    Analyze their message and respond appropriately. If they're asking for information, provide it.
    If they're requesting changes to their portfolio (adding/removing stocks, changing allocations, etc.),
    make those changes and explain what you did.

    Format your response as a valid JSON object with the following structure:
    {
      "message": "Your response to the user explaining what you did or answering their question",
      "portfolioChanges": {
        "stocks": [
          {
            "ticker": "AAPL",
            "name": "Apple Inc.",
            "allocation": 20
          },
          ...
        ]
      }
    }

    If no portfolio changes are needed, omit the "portfolioChanges" field.
    Make sure the total allocation always adds up to 100%.
    Only return the JSON object, nothing else.
    `;

    // Call the Gemini API
    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-goog-api-key': apiKey,
      },
      body: JSON.stringify({
        contents: [{
          parts: [{ text: prompt }]
        }],
        generationConfig: {
          temperature: 0.2,
          maxOutputTokens: 1000
        }
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Gemini API error: ${errorData.error?.message || response.statusText}`);
    }

    const data = await response.json();
    const content = data.candidates?.[0]?.content?.parts?.[0]?.text || '';

    // Extract the JSON from the response
    const jsonMatch = content.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('Failed to extract JSON from Gemini response');
    }

    const chatResponse = JSON.parse(jsonMatch[0]) as ChatResponse;

    // Validate the response
    if (!chatResponse.message) {
      throw new Error('Invalid response format: missing message field');
    }

    // If there are portfolio changes, validate that allocations add up to 100%
    if (chatResponse.portfolioChanges?.stocks) {
      const totalAllocation = chatResponse.portfolioChanges.stocks.reduce(
        (sum, stock) => sum + stock.allocation,
        0
      );

      // Allow for small rounding errors
      if (Math.abs(totalAllocation - 100) > 0.5) {
        // Adjust allocations to ensure they add up to 100%
        const adjustmentFactor = 100 / totalAllocation;
        chatResponse.portfolioChanges.stocks = chatResponse.portfolioChanges.stocks.map(stock => ({
          ...stock,
          allocation: Math.round(stock.allocation * adjustmentFactor * 10) / 10 // Round to 1 decimal place
        }));
      }
    }

    return chatResponse;
  } catch (error) {
    console.error('Error processing chat message:', error);
    return {
      message: `I'm sorry, I couldn't process your request. Error: ${error.message}`
    };
  }
}
