import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
}

interface AIAgentRequest {
  description: string;
  userId: string;
  timestamp: string;
  currentAgent?: any; // The current agent being edited (if any)
  conversationHistory?: string[]; // Previous messages in this agent-building session
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    // Parse request body
    const requestData: AIAgentRequest = await req.json()
    console.log('AI Agent Generation Request:', requestData)

    // Validate request
    if (!requestData.description || !requestData.userId) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Missing required fields: description and userId'
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Log the request details
    console.log('Request details:', {
      description: requestData.description,
      hasCurrentAgent: !!requestData.currentAgent,
      conversationLength: requestData.conversationHistory?.length || 0
    })

    // Get Gemini API key
    const geminiApiKey = Deno.env.get('GEMINI_API_KEY')
    if (!geminiApiKey) {
      console.error('GEMINI_API_KEY not found in environment variables')

      // Generate a demo agent based on the user's request
      const demoAgent = generateDemoAgent(requestData.description)

      return new Response(
        JSON.stringify({
          success: true,
          agent: demoAgent,
          reasoning: "Demo agent generated (Gemini API key required for custom AI generation)"
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Generate or edit agent with Gemini AI
    const aiResponse = await generateAgentWithGemini(
      requestData.description,
      geminiApiKey,
      requestData.currentAgent,
      requestData.conversationHistory
    )

    if (!aiResponse.success) {
      return new Response(
        JSON.stringify(aiResponse),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Log successful generation
    console.log('AI agent generated successfully:', aiResponse.agent?.name)

    return new Response(
      JSON.stringify(aiResponse),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('Error in ai-agent-generator:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})

/**
 * Generate a simple agent based on the user's request when API key is not available
 */
function generateDemoAgent(description: string) {
  // Create a minimal agent with just WHEN_RUN and TRIGGER blocks
  return {
    name: "Demo Agent",
    description: `Demo agent for: ${description} (API key required for custom generation)`,
    entryBlockId: "when-run-1",
    blocks: [
      {
        id: "when-run-1",
        type: "WHEN_RUN",
        position: { x: 100, y: 100 },
        outputConnections: ["trigger-1"]
      },
      {
        id: "trigger-1",
        type: "TRIGGER",
        signal: "bullish",
        confidence: 50,
        position: { x: 400, y: 100 },
        inputConnections: ["when-run-1"]
      }
    ]
  }
}

/**
 * Generate agent configuration using Gemini AI
 */
async function generateAgentWithGemini(
  description: string,
  apiKey: string,
  currentAgent?: any,
  conversationHistory?: string[]
) {
  try {
    const prompt = createAgentGenerationPrompt(description, currentAgent, conversationHistory)

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: 0.1,
          topK: 1,
          topP: 1,
          maxOutputTokens: 4096,
        }
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Gemini API error:', errorText)
      return {
        success: false,
        error: 'AI service temporarily unavailable'
      }
    }

    const data = await response.json()

    if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
      console.error('Invalid Gemini response structure:', data)
      return {
        success: false,
        error: 'AI generated invalid response'
      }
    }

    const generatedText = data.candidates[0].content.parts[0].text
    console.log('Gemini generated text:', generatedText)

    // Parse the JSON response from Gemini
    const jsonMatch = generatedText.match(/```json\n([\s\S]*?)\n```/) || generatedText.match(/\`\`\`json\n([\s\S]*?)\n\`\`\`/)
    if (!jsonMatch) {
      console.error('No JSON found in Gemini response')
      console.error('Generated text:', generatedText)
      return {
        success: false,
        error: 'AI could not generate a valid agent configuration'
      }
    }

    let rawJson = jsonMatch[1]
    console.log('Raw JSON from AI:', rawJson)

    // Clean up the JSON - remove comments and fix common issues
    rawJson = rawJson
      .replace(/\/\/.*$/gm, '') // Remove single-line comments
      .replace(/\/\*[\s\S]*?\*\//g, '') // Remove multi-line comments
      .replace(/,(\s*[}\]])/g, '$1') // Remove trailing commas
      .trim()

    let agentConfig
    try {
      agentConfig = JSON.parse(rawJson)
    } catch (parseError) {
      console.error('JSON parse error:', parseError)
      console.error('Cleaned JSON:', rawJson)
      return {
        success: false,
        error: 'AI generated invalid JSON format'
      }
    }

    console.log('Raw JSON from AI:', JSON.stringify(agentConfig, null, 2))

    // Check if AI returned wrong format (with "agent" wrapper)
    if (agentConfig.agent && !agentConfig.name) {
      console.log('AI returned wrapped format, extracting agent config...')
      agentConfig = agentConfig.agent
    }

    // Validate basic structure
    if (!agentConfig.blocks || !Array.isArray(agentConfig.blocks)) {
      console.error('AI returned invalid format - missing blocks array')
      return {
        success: false,
        error: 'AI generated invalid agent format - missing blocks array'
      }
    }

    console.log('Agent generated successfully:', agentConfig.name)
    return {
      success: true,
      agent: agentConfig,
      reasoning: extractReasoning(generatedText)
    }

  } catch (error) {
    console.error('Error generating agent with Gemini:', error)
    return {
      success: false,
      error: 'Failed to generate agent with AI'
    }
  }
}

/**
 * Create a direct prompt for Gemini AI to generate ONLY what the user specifically requests
 * Can either create a new agent or edit an existing one based on conversation context
 */
function createAgentGenerationPrompt(
  description: string,
  currentAgent?: any,
  conversationHistory?: string[]
): string {
  // Define all available blocks that actually exist in our system
  const AVAILABLE_BLOCKS = `
AVAILABLE BLOCK TYPES (ONLY use these - no others exist):

CRITICAL INSTRUCTIONS:
- Create ONLY the blocks the user specifically mentions
- Do NOT add extra blocks or logic the user didn't ask for
- Do NOT use preset templates or patterns
- Build exactly what is requested, nothing more
- EVERY BLOCK MUST BE CONNECTED - no disconnected blocks allowed
- Follow the connection patterns exactly as shown below
- Respond with EXACTLY the JSON format - no extra text or explanations

1. WHEN_RUN - Entry point (required)
   Properties: outputConnections

2. MOMENTUM_INDICATOR - RSI, Stochastic, Williams %R, CCI (ONLY these 4 indicators work)
   Properties: indicator ("rsi", "stochastic", "williams_r", "cci"), period, timeframe, inputConnections, outputConnections

3. FUNDAMENTAL - Financial data and ratios
   Properties: metric, statement ("balance_sheet", "income_statement", "cash_flow_statement", "calculated"), period ("quarterly", "annual"), inputConnections, outputConnections
   Common metrics: "return_on_equity", "return_on_assets", "debt_to_equity", "current_ratio", "price_to_earnings", "revenue", "net_income_loss"

4. MOVING_AVERAGE - SMA, EMA, WMA, VWAP (ONLY these 4 averages work)
   Properties: averageType ("sma", "ema", "wma", "vwap"), period, timeframe, source ("close", "open", "high", "low"), inputConnections, outputConnections

5. PRICE - Price data
   Properties: dataPoint ("open", "high", "low", "close", "volume"), timeframe, inputConnections, outputConnections

6. CONDITION - Comparison logic (Takes ONE input value, compares it to compareValue)
   Properties: operator ("<", ">", "<=", ">=", "==", "!="), compareValue (number to compare against), inputConnections (ONLY ONE input), trueConnection, falseConnection

7. COMPARISON - Compare two values (Takes TWO inputs, compares them directly)
   Properties: operator ("<", ">", "<=", ">=", "==", "!="), inputConnections (EXACTLY TWO inputs [valueA, valueB]), trueConnection, falseConnection

8. TRIGGER - Final signal output
   Properties: signal ("bullish", "bearish", "neutral"), confidence (0-100), inputConnections

9. CANDLE_PATTERN - Candlestick patterns
   Properties: pattern ("any", "doji", "hammer", "shooting_star", "marubozu", "engulfing"), timeframe, inputConnections, bullishConnection, bearishConnection, neutralConnection

10. CHART_PATTERN - Chart patterns
   Properties: pattern ("any", "double_top", "double_bottom", "head_shoulders", "triangle"), timeframe, inputConnections, bullishConnection, bearishConnection, neutralConnection

11. CONFIDENCE_BOOST - Boost signal confidence
   Properties: boostType ("bullish", "bearish", "neutral"), percentage, inputConnections, outputConnections

CRITICAL RULES:
- NEVER use blocks not in this list - they don't exist and will cause errors
- NEVER use "SUPPORT_RESISTANCE", "MACD", "BOLLINGER", "ATR" block types - they don't exist
- NEVER use "SMA" or "EMA" block types - use MOVING_AVERAGE with averageType="sma"/"ema"
- MOMENTUM_INDICATOR only supports: "rsi", "stochastic", "williams_r", "cci" - NO OTHER INDICATORS
- MOVING_AVERAGE only supports: "sma", "ema", "wma", "vwap" - NO OTHER AVERAGES
- FUNDAMENTAL blocks use statement="calculated" for ratios like ROE, ROA, P/E, or specific statements like "balance_sheet", "income_statement"
- FUNDAMENTAL blocks use period="quarterly" or "annual" for time period
- CONDITION blocks use trueConnection/falseConnection, NOT outputConnections
- CONDITION blocks take ONLY ONE input value and compare it to compareValue (a fixed number)
- COMPARISON blocks take EXACTLY TWO inputs and compare them directly
- NEVER connect two data blocks to one condition block - use COMPARISON block instead
- Pattern blocks use bullishConnection/bearishConnection/neutralConnection, NOT outputConnections
- Each block can only have ONE output connection per endpoint
- ONLY create trigger blocks that will actually be used - NO unused neutral triggers
- ALWAYS use exact property names as shown above

AGENT BUILDING RULES OF THUMB:
- DON'T use CONFIDENCE_BOOST blocks with TRIGGER blocks - TRIGGER blocks override confidence boosters completely
- You only need 1 of each TRIGGER block type MAX - multiple blocks can connect to the same trigger
- Instead of multiple triggers of the same type, connect multiple conditions to one trigger
- Use CONFIDENCE_BOOST blocks only when you want to modify confidence before it reaches a trigger
- TRIGGER blocks should be the final output - nothing connects after them
`;

  // Check if we're editing an existing agent or creating a new one
  const isEditing = currentAgent && currentAgent.blocks && currentAgent.blocks.length > 0
  const hasConversationHistory = conversationHistory && conversationHistory.length > 0

  // Build conversation context if available
  let conversationContext = ''
  if (hasConversationHistory) {
    conversationContext = `
CONVERSATION HISTORY (this agent-building session only):
${conversationHistory.map((msg, i) => `${i + 1}. ${msg}`).join('\n')}

`
  }

  // Build current agent context if editing
  let currentAgentContext = ''
  if (isEditing) {
    currentAgentContext = `
CURRENT AGENT BEING EDITED:
Name: ${currentAgent.name || 'Unnamed Agent'}
Description: ${currentAgent.description || 'No description'}
Blocks: ${currentAgent.blocks.length} blocks
Current Structure:
${JSON.stringify(currentAgent, null, 2)}

EDITING INSTRUCTIONS:
- You are EDITING the existing agent above, not creating a new one
- Modify only what the user specifically requests
- Keep existing blocks that the user doesn't mention changing
- Maintain existing connections unless user asks to change them
- If user says "add RSI block", add it to the existing agent
- If user says "remove the condition block", remove only that block
- If user says "change the trigger to bearish", modify only the trigger signal

`
  }

  // Create agent based ONLY on user's specific request - NO PRESET TEMPLATES
  return `${AVAILABLE_BLOCKS}

${conversationContext}${currentAgentContext}

${isEditing ? 'EDIT' : 'CREATE'} an agent based on the user's request.

User Request: "${description}"

${isEditing ? 'EDITING' : 'CREATION'} INSTRUCTIONS:
1. Read the user's request carefully
2. ${isEditing ? 'If editing: Modify only what the user specifically requests, keep everything else the same' : 'If creating: Identify ONLY the specific blocks they mention'}
3. ${isEditing ? 'If editing: Add/remove/modify blocks as requested while preserving existing structure' : 'If creating: Create ONLY those blocks and connect them in a simple chain'}
4. Do NOT add extra logic, conditions, or blocks they didn't ask for
5. ALWAYS connect blocks together - never leave them disconnected
6. ${isEditing ? 'If editing: Maintain existing connections unless user asks to change them' : 'If creating: Always start with WHEN_RUN block and connect to the first block they mention'}
7. Connect each block to the next block in the chain
8. End with TRIGGER blocks and connect the previous block to them

CONNECTION RULES:
- WHEN_RUN connects via outputConnections to the first data block
- Data blocks (RSI, SMA, PRICE, etc.) connect via outputConnections to next block
- CONDITION blocks connect via trueConnection/falseConnection to TRIGGER blocks
- COMPARISON blocks connect via trueConnection/falseConnection to TRIGGER blocks
- Pattern blocks connect via bullishConnection/bearishConnection to TRIGGER blocks
- TRIGGER blocks receive connections via inputConnections from previous blocks
- Multiple blocks can connect to the SAME trigger (use inputConnections array)
- CONFIDENCE_BOOST blocks should connect to other blocks, NOT directly to triggers

DETAILED CONNECTION EXAMPLES:

${isEditing ? `
EDITING EXAMPLES:

If user says "add RSI block" and current agent has WHEN_RUN → PRICE → TRIGGER:
- Add RSI block between WHEN_RUN and PRICE
- Result: WHEN_RUN → RSI → PRICE → TRIGGER

If user says "remove the condition block" and current agent has WHEN_RUN → RSI → CONDITION → TRIGGER:
- Remove condition block and connect RSI directly to TRIGGER
- Result: WHEN_RUN → RSI → TRIGGER

If user says "change trigger to bearish" and current agent has bullish trigger:
- Keep all blocks the same, only change trigger signal from "bullish" to "bearish"

If user says "add SMA comparison" and current agent has WHEN_RUN → PRICE → TRIGGER:
- Add SMA block and COMPARISON block
- Result: WHEN_RUN → [PRICE, SMA] → COMPARISON → TRIGGER

` : ''}

Example 1: "I want only RSI and trigger"
{
  "name": "RSI Agent",
  "description": "Simple RSI with trigger",
  "entryBlockId": "when-run-1",
  "blocks": [
    {
      "id": "when-run-1",
      "type": "WHEN_RUN",
      "position": {"x": 100, "y": 100},
      "outputConnections": ["rsi-1"]
    },
    {
      "id": "rsi-1",
      "type": "MOMENTUM_INDICATOR",
      "indicator": "rsi",
      "period": 14,
      "timeframe": "day",
      "position": {"x": 300, "y": 100},
      "inputConnections": ["when-run-1"],
      "outputConnections": ["trigger-1"]
    },
    {
      "id": "trigger-1",
      "type": "TRIGGER",
      "signal": "bullish",
      "confidence": 75,
      "position": {"x": 500, "y": 100},
      "inputConnections": ["rsi-1"]
    }
  ]
}

Example 2: "Just price and condition"
{
  "name": "Price Condition Agent",
  "description": "Price with condition check",
  "entryBlockId": "when-run-1",
  "blocks": [
    {
      "id": "when-run-1",
      "type": "WHEN_RUN",
      "position": {"x": 100, "y": 100},
      "outputConnections": ["price-1"]
    },
    {
      "id": "price-1",
      "type": "PRICE",
      "dataPoint": "close",
      "timeframe": "day",
      "position": {"x": 300, "y": 100},
      "inputConnections": ["when-run-1"],
      "outputConnections": ["condition-1"]
    },
    {
      "id": "condition-1",
      "type": "CONDITION",
      "operator": ">",
      "compareValue": 100,
      "position": {"x": 500, "y": 100},
      "inputConnections": ["price-1"],
      "trueConnection": "trigger-1"
    },
    {
      "id": "trigger-1",
      "type": "TRIGGER",
      "signal": "bullish",
      "confidence": 75,
      "position": {"x": 700, "y": 100},
      "inputConnections": ["condition-1"]
    }
  ]
}

Example 3: "SMA and price comparison"
{
  "name": "SMA Price Comparison",
  "description": "Compare price to SMA",
  "entryBlockId": "when-run-1",
  "blocks": [
    {
      "id": "when-run-1",
      "type": "WHEN_RUN",
      "position": {"x": 100, "y": 100},
      "outputConnections": ["price-1", "sma-1"]
    },
    {
      "id": "price-1",
      "type": "PRICE",
      "dataPoint": "close",
      "timeframe": "day",
      "position": {"x": 300, "y": 50},
      "inputConnections": ["when-run-1"],
      "outputConnections": ["comparison-1"]
    },
    {
      "id": "sma-1",
      "type": "MOVING_AVERAGE",
      "averageType": "sma",
      "period": 20,
      "timeframe": "day",
      "source": "close",
      "position": {"x": 300, "y": 150},
      "inputConnections": ["when-run-1"],
      "outputConnections": ["comparison-1"]
    },
    {
      "id": "comparison-1",
      "type": "COMPARISON",
      "operator": ">",
      "position": {"x": 500, "y": 100},
      "inputConnections": ["price-1", "sma-1"],
      "trueConnection": "trigger-bullish",
      "falseConnection": "trigger-bearish"
    },
    {
      "id": "trigger-bullish",
      "type": "TRIGGER",
      "signal": "bullish",
      "confidence": 80,
      "position": {"x": 700, "y": 50},
      "inputConnections": ["comparison-1"]
    },
    {
      "id": "trigger-bearish",
      "type": "TRIGGER",
      "signal": "bearish",
      "confidence": 80,
      "position": {"x": 700, "y": 150},
      "inputConnections": ["comparison-1"]
    }
  ]
}

Example 4: "ROE and condition check"
{
  "name": "ROE Fundamental Agent",
  "description": "Check Return on Equity with condition",
  "entryBlockId": "when-run-1",
  "blocks": [
    {
      "id": "when-run-1",
      "type": "WHEN_RUN",
      "position": {"x": 100, "y": 100},
      "outputConnections": ["fundamental-1"]
    },
    {
      "id": "fundamental-1",
      "type": "FUNDAMENTAL",
      "metric": "return_on_equity",
      "statement": "calculated",
      "period": "quarterly",
      "position": {"x": 300, "y": 100},
      "inputConnections": ["when-run-1"],
      "outputConnections": ["condition-1"]
    },
    {
      "id": "condition-1",
      "type": "CONDITION",
      "operator": ">",
      "compareValue": 15,
      "position": {"x": 500, "y": 100},
      "inputConnections": ["fundamental-1"],
      "trueConnection": "trigger-1"
    },
    {
      "id": "trigger-1",
      "type": "TRIGGER",
      "signal": "bullish",
      "confidence": 80,
      "position": {"x": 700, "y": 100},
      "inputConnections": ["condition-1"]
    }
  ]
}

Example 5: "Multiple conditions to one trigger" (GOOD - follows rules of thumb)
{
  "name": "Multi-Condition Agent",
  "description": "Multiple conditions connecting to single trigger",
  "entryBlockId": "when-run-1",
  "blocks": [
    {
      "id": "when-run-1",
      "type": "WHEN_RUN",
      "position": {"x": 100, "y": 100},
      "outputConnections": ["rsi-1", "price-1"]
    },
    {
      "id": "rsi-1",
      "type": "MOMENTUM_INDICATOR",
      "indicator": "rsi",
      "period": 14,
      "timeframe": "day",
      "position": {"x": 300, "y": 50},
      "inputConnections": ["when-run-1"],
      "outputConnections": ["condition-1"]
    },
    {
      "id": "price-1",
      "type": "PRICE",
      "dataPoint": "close",
      "timeframe": "day",
      "position": {"x": 300, "y": 150},
      "inputConnections": ["when-run-1"],
      "outputConnections": ["condition-2"]
    },
    {
      "id": "condition-1",
      "type": "CONDITION",
      "operator": ">",
      "compareValue": 70,
      "position": {"x": 500, "y": 50},
      "inputConnections": ["rsi-1"],
      "trueConnection": "trigger-1"
    },
    {
      "id": "condition-2",
      "type": "CONDITION",
      "operator": ">",
      "compareValue": 100,
      "position": {"x": 500, "y": 150},
      "inputConnections": ["price-1"],
      "trueConnection": "trigger-1"
    },
    {
      "id": "trigger-1",
      "type": "TRIGGER",
      "signal": "bullish",
      "confidence": 85,
      "position": {"x": 700, "y": 100},
      "inputConnections": ["condition-1", "condition-2"]
    }
  ]
}

WHAT NOT TO DO (BAD EXAMPLES):

❌ DON'T: Use confidence boost with trigger (trigger overrides it)
// BAD - confidence boost is wasted
CONFIDENCE_BOOST → TRIGGER

❌ DON'T: Create multiple triggers of same type
// BAD - creates duplicate triggers
CONDITION → TRIGGER (bullish)
CONDITION → TRIGGER (bullish) // Unnecessary duplicate

✅ DO: Connect multiple conditions to one trigger
// GOOD - one trigger handles multiple inputs
CONDITION → TRIGGER (bullish)
CONDITION → ↗

MANDATORY REQUIREMENTS:
1. EVERY block must have "inputConnections" (except WHEN_RUN)
2. EVERY block must have output connections (outputConnections, trueConnection, bullishConnection, etc.)
3. NO disconnected blocks allowed
4. Follow the exact connection patterns from the examples above
5. Position blocks horizontally with x: 100, 300, 500, 700, etc.
6. Always end with TRIGGER blocks that receive connections
7. NEVER use CONFIDENCE_BOOST blocks directly before TRIGGER blocks
8. Use only ONE trigger per signal type (bullish/bearish/neutral)
${isEditing ? '9. EDITING: Preserve existing blocks unless user specifically asks to change/remove them' : ''}

Respond with EXACTLY this JSON format (follow the examples above exactly):

{
  "name": "${isEditing ? `[Keep existing name: "${currentAgent?.name || 'Agent'}" unless user asks to change it]` : '[Simple descriptive name based on user request]'}",
  "description": "${isEditing ? `[Keep existing description unless user asks to change it]` : '[Brief description of what the user specifically asked for]'}",
  "entryBlockId": "when-run-1",
  "blocks": [
    ${isEditing ? `// EDITING: Start with the current agent structure and modify only what user requests
    // Current agent has ${currentAgent?.blocks?.length || 0} blocks
    // Modify/add/remove blocks based on user's specific request
    // Keep existing block IDs and connections unless changing them` : `// CREATING: Copy the exact structure from the examples above
    // Replace block types/properties based on user request
    // Keep all connection patterns identical`}
    // Every block must be connected properly
  ]
}`

}





/**
 * Extract reasoning from the generated text
 */
function extractReasoning(text: string): string {
  const reasoningMatch = text.match(/\*\*Reasoning:\*\*\s*(.*?)(?=```json|$)/s)
  return reasoningMatch ? reasoningMatch[1].trim() : ''
}
