// Agent Context Formatter
// Formats agent execution results for Gemini consumption

export interface AgentExecutionResult {
  agentId: string;
  agentName: string;
  symbol: string;
  result: {
    signal: 'bullish' | 'bearish' | 'neutral';
    confidence: number;
    reasoning: string;
    metrics: Record<string, any>;
    executionPath: string[];
    executionTime: number;
    timestamp: string;
  };
  executionTime: number;
  error?: string;
}

export interface FormattedAgentContext {
  summary: string;
  detailedAnalysis: string;
  agentSignals: Record<string, {
    signal: 'bullish' | 'bearish' | 'neutral';
    confidence: number;
  }>;
  agentInsights: string;
  executionMetrics: {
    totalExecutions: number;
    successfulExecutions: number;
    averageConfidence: number;
    executionTime: number;
  };
}

/**
 * Format agent execution results for Gemini context (single agent)
 */
export function formatAgentContextForGemini(
  agentResults: AgentExecutionResult[],
  _userMessage: string
): FormattedAgentContext {
  if (agentResults.length === 0) {
    return {
      summary: "No agent analysis was performed for this query.",
      detailedAnalysis: "",
      agentSignals: {},
      agentInsights: "",
      executionMetrics: {
        totalExecutions: 0,
        successfulExecutions: 0,
        averageConfidence: 0,
        executionTime: 0
      }
    };
  }

  // Filter successful executions
  const successfulResults = agentResults.filter(r => !r.error);
  const failedResults = agentResults.filter(r => r.error);

  // Group results by symbol
  const resultsBySymbol = groupResultsBySymbol(successfulResults);

  // Calculate signals for each symbol
  const agentSignals = calculateAgentSignals(resultsBySymbol);

  // Generate summary
  const summary = generateSummary(successfulResults, agentSignals, failedResults);

  // Generate detailed analysis
  const detailedAnalysis = generateDetailedAnalysis(resultsBySymbol, agentSignals);

  // Generate agent insights
  const agentInsights = generateIndividualInsights(successfulResults);

  // Calculate execution metrics
  const executionMetrics = calculateExecutionMetrics(agentResults);

  return {
    summary,
    detailedAnalysis,
    agentSignals,
    agentInsights,
    executionMetrics
  };
}

/**
 * Group agent results by symbol
 */
function groupResultsBySymbol(
  results: AgentExecutionResult[]
): Record<string, AgentExecutionResult[]> {
  const grouped: Record<string, AgentExecutionResult[]> = {};

  for (const result of results) {
    if (!grouped[result.symbol]) {
      grouped[result.symbol] = [];
    }
    grouped[result.symbol].push(result);
  }

  return grouped;
}

/**
 * Calculate agent signals for each symbol (single agent)
 */
function calculateAgentSignals(
  resultsBySymbol: Record<string, AgentExecutionResult[]>
): Record<string, {
  signal: 'bullish' | 'bearish' | 'neutral';
  confidence: number;
}> {
  const signals: Record<string, any> = {};

  for (const [symbol, results] of Object.entries(resultsBySymbol)) {
    if (results.length === 0) continue;

    // For single agent, just take the first (and only) result
    const result = results[0];

    signals[symbol] = {
      signal: result.result.signal,
      confidence: result.result.confidence
    };
  }

  return signals;
}

/**
 * Generate summary of agent analysis (single agent)
 */
function generateSummary(
  successfulResults: AgentExecutionResult[],
  agentSignals: Record<string, any>,
  failedResults: AgentExecutionResult[]
): string {
  const symbols = Object.keys(agentSignals);
  const totalExecutions = successfulResults.length + failedResults.length;

  if (symbols.length === 0) {
    return `Executed ${totalExecutions} agent analysis, but no successful analysis was completed.`;
  }

  const summaryParts: string[] = [];

  summaryParts.push(`Agent Analysis Summary (${successfulResults.length}/${totalExecutions} executions successful):`);

  for (const symbol of symbols) {
    const signal = agentSignals[symbol];
    summaryParts.push(
      `${symbol}: ${signal.signal.toUpperCase()} signal (${signal.confidence}% confidence)`
    );
  }

  return summaryParts.join('\n');
}

/**
 * Generate detailed analysis by symbol (single agent)
 */
function generateDetailedAnalysis(
  resultsBySymbol: Record<string, AgentExecutionResult[]>,
  agentSignals: Record<string, any>
): string {
  const analysisParts: string[] = [];

  for (const [symbol, results] of Object.entries(resultsBySymbol)) {
    const signal = agentSignals[symbol];
    const result = results[0]; // Single agent result

    analysisParts.push(`\n**${symbol} Detailed Analysis:**`);
    analysisParts.push(`Signal: ${signal.signal.toUpperCase()} (${signal.confidence}% confidence)`);
    analysisParts.push(`Agent: ${result.agentName}`);
    analysisParts.push(`Reasoning: ${result.result.reasoning}`);

    // Include key metrics if available
    if (Object.keys(result.result.metrics).length > 0) {
      analysisParts.push(`\nKey Metrics:`);
      for (const [key, value] of Object.entries(result.result.metrics)) {
        const formattedValue = typeof value === 'number' ? value.toFixed(2) : value;
        analysisParts.push(`- ${key}: ${formattedValue}`);
      }
    }

    // Include execution path if available
    if (result.result.executionPath && result.result.executionPath.length > 0) {
      analysisParts.push(`\nExecution Path: ${result.result.executionPath.join(' → ')}`);
    }
  }

  return analysisParts.join('\n');
}

/**
 * Generate individual agent insights
 */
function generateIndividualInsights(results: AgentExecutionResult[]): string {
  const insights: string[] = [];

  // Group by agent name to show all symbols analyzed by each agent
  const resultsByAgent: Record<string, AgentExecutionResult[]> = {};

  for (const result of results) {
    if (!resultsByAgent[result.agentName]) {
      resultsByAgent[result.agentName] = [];
    }
    resultsByAgent[result.agentName].push(result);
  }

  insights.push("Individual Agent Insights:");

  for (const [agentName, agentResults] of Object.entries(resultsByAgent)) {
    insights.push(`\n**${agentName}:**`);

    for (const result of agentResults) {
      insights.push(`${result.symbol}: ${result.result.signal.toUpperCase()} (${result.result.confidence}%)`);

      // Include key metrics if available
      if (Object.keys(result.result.metrics).length > 0) {
        const keyMetrics = Object.entries(result.result.metrics)
          .slice(0, 3) // Show top 3 metrics
          .map(([key, value]) => `${key}: ${typeof value === 'number' ? value.toFixed(2) : value}`)
          .join(', ');
        insights.push(`  Metrics: ${keyMetrics}`);
      }
    }
  }

  return insights.join('\n');
}

/**
 * Calculate execution metrics
 */
function calculateExecutionMetrics(results: AgentExecutionResult[]): {
  totalExecutions: number;
  successfulExecutions: number;
  averageConfidence: number;
  executionTime: number;
} {
  const successfulResults = results.filter(r => !r.error);

  const averageConfidence = successfulResults.length > 0
    ? successfulResults.reduce((sum, r) => sum + r.result.confidence, 0) / successfulResults.length
    : 0;

  const totalExecutionTime = results.reduce((sum, r) => sum + r.executionTime, 0);

  return {
    totalExecutions: results.length,
    successfulExecutions: successfulResults.length,
    averageConfidence: Math.round(averageConfidence),
    executionTime: totalExecutionTime
  };
}

/**
 * Create Gemini prompt with agent context (single agent)
 */
export function createGeminiPromptWithAgentContext(
  userMessage: string,
  agentContext: FormattedAgentContext,
  marketData?: any
): string {
  const prompt = `Answer this user's message using the data provided from the selected agent.

**User Query:** ${userMessage}

**Agent Analysis Results:**
${agentContext.summary}

${agentContext.detailedAnalysis}

${agentContext.agentInsights}

**Execution Metrics:**
- Total Executions: ${agentContext.executionMetrics.totalExecutions}
- Successful Executions: ${agentContext.executionMetrics.successfulExecutions}
- Average Confidence: ${agentContext.executionMetrics.averageConfidence}%
- Total Execution Time: ${agentContext.executionMetrics.executionTime}ms

${marketData ? `**Market Data:** ${JSON.stringify(marketData, null, 2)}` : ''}`;

  return prompt;
}
