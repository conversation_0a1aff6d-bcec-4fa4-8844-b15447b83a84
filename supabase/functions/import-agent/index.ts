// Import Agent Edge Function
// This function handles importing published agents to user's library

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS'
};

// Handle OPTIONS requests for CORS
function handleOptions() {
  return new Response(null, {
    status: 204,
    headers: corsHeaders
  });
}

interface ImportAgentRequest {
  publishedAgentId: string;
  customName?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return handleOptions();
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('No authorization header provided');
    }

    // Verify user authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    );

    if (authError || !user) {
      throw new Error('Invalid authentication token');
    }

    // Parse request body
    const requestData: ImportAgentRequest = await req.json();
    
    // Validate required fields
    if (!requestData.publishedAgentId) {
      throw new Error('Missing required field: publishedAgentId');
    }

    console.log(`Importing agent ${requestData.publishedAgentId} for user ${user.id}`);

    // Get the published agent with its original agent data
    const { data: publishedAgent, error: publishedError } = await supabase
      .from('published_agents')
      .select(`
        *,
        agents:agent_id (
          name,
          description,
          configuration
        )
      `)
      .eq('id', requestData.publishedAgentId)
      .eq('is_active', true)
      .single();

    if (publishedError || !publishedAgent) {
      throw new Error('Published agent not found or is not active');
    }

    // Check if user has already imported this agent
    const { data: existingImport, error: existingError } = await supabase
      .from('agent_imports')
      .select('id, imported_agent_id')
      .eq('published_agent_id', requestData.publishedAgentId)
      .eq('imported_by', user.id)
      .maybeSingle();

    if (existingError && existingError.code !== 'PGRST116') {
      throw new Error('Error checking existing import');
    }

    if (existingImport) {
      // Agent already imported, just return the existing import
      const { data: existingAgent, error: existingAgentError } = await supabase
        .from('agents')
        .select('*')
        .eq('id', existingImport.imported_agent_id)
        .single();

      if (existingAgentError) {
        throw new Error('Error fetching existing imported agent');
      }

      return new Response(
        JSON.stringify({
          success: true,
          agent: existingAgent,
          message: 'Agent already in your library',
          alreadyImported: true
        }),
        {
          status: 200,
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          }
        }
      );
    }

    // Create a new agent in user's library based on the published agent
    const agentName = requestData.customName || 
                     `${publishedAgent.name} (Imported)` || 
                     `${publishedAgent.agents?.name} (Imported)` || 
                     'Imported Agent';

    const { data: newAgent, error: createError } = await supabase
      .from('agents')
      .insert({
        user_id: user.id,
        name: agentName,
        description: publishedAgent.description || publishedAgent.agents?.description,
        configuration: publishedAgent.agents?.configuration
      })
      .select('*')
      .single();

    if (createError) {
      throw new Error(`Failed to create imported agent: ${createError.message}`);
    }

    // Record the import relationship
    const { data: importRecord, error: importError } = await supabase
      .from('agent_imports')
      .insert({
        published_agent_id: requestData.publishedAgentId,
        imported_by: user.id,
        imported_agent_id: newAgent.id,
        custom_name: requestData.customName || null
      })
      .select('*')
      .single();

    if (importError) {
      // If import record creation fails, clean up the created agent
      await supabase
        .from('agents')
        .delete()
        .eq('id', newAgent.id);
      
      throw new Error(`Failed to record import: ${importError.message}`);
    }

    console.log(`Successfully imported agent ${newAgent.id} from published agent ${requestData.publishedAgentId}`);

    // Return success response
    return new Response(
      JSON.stringify({
        success: true,
        agent: newAgent,
        importRecord: importRecord,
        message: 'Agent imported successfully to your library'
      }),
      {
        status: 200,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    );

  } catch (error) {
    console.error('Error in import-agent function:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'An unexpected error occurred'
      }),
      {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    );
  }
});
