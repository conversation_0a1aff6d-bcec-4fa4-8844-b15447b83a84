#!/usr/bin/env node

/**
 * Test script to validate the improvements made to the TradingView Poster function
 * This script tests the fixes for incomplete comment generation and data structure issues
 */

import { execSync } from 'child_process';
import fetch from 'node-fetch';

const SUPABASE_URL = process.env.SUPABASE_URL || 'http://localhost:54321';
const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY || 'your-anon-key';

// Test cases for different scenarios
const testCases = [
  {
    name: 'GameStop (GME) - Index 15',
    stockIndex: 15,
    description: 'Test the specific case mentioned in the issue'
  },
  {
    name: 'Apple (AAPL) - Index 0', 
    stockIndex: 0,
    description: 'Test with a stable large-cap stock'
  },
  {
    name: '<PERSON><PERSON> (TSLA) - Index 1',
    stockIndex: 1,
    description: 'Test with a volatile stock'
  },
  {
    name: 'Random Selection',
    stockIndex: null,
    description: 'Test time-based stock selection'
  }
];

async function testTradingViewPoster(testCase) {
  console.log(`\n🧪 Testing: ${testCase.name}`);
  console.log(`📝 Description: ${testCase.description}`);
  console.log('─'.repeat(60));

  try {
    const url = testCase.stockIndex !== null 
      ? `${SUPABASE_URL}/functions/v1/tradingview-poster?stockIndex=${testCase.stockIndex}`
      : `${SUPABASE_URL}/functions/v1/tradingview-poster`;

    const startTime = Date.now();
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
      },
      body: JSON.stringify({})
    });

    const endTime = Date.now();
    const duration = endTime - startTime;

    if (!response.ok) {
      const errorText = await response.text();
      console.log(`❌ HTTP Error: ${response.status}`);
      console.log(`📄 Error Details: ${errorText}`);
      return false;
    }

    const result = await response.json();
    
    // Validate the response structure
    console.log(`✅ Response received in ${duration}ms`);
    console.log(`📊 Stock: ${result.stock?.name} (${result.stock?.symbol})`);
    console.log(`💬 Generated Message: "${result.message}"`);
    
    // Check for common issues
    const issues = [];
    
    if (!result.message || result.message.length < 10) {
      issues.push('Message too short or empty');
    }
    
    if (result.message && (
      result.message.includes('Okay, I\'m ready to') ||
      result.message.includes('Okay, here\'s a') ||
      result.message.includes('Here\'s a trader\'s comment') ||
      result.message.includes('based on your requirements')
    )) {
      issues.push('Contains incomplete response pattern');
    }
    
    if (result.message && result.message.includes('[')) {
      issues.push('Contains placeholder brackets');
    }
    
    if (result.message && result.message.endsWith('...')) {
      issues.push('Ends with ellipsis (incomplete)');
    }
    
    if (issues.length > 0) {
      console.log(`⚠️  Issues detected:`);
      issues.forEach(issue => console.log(`   - ${issue}`));
      return false;
    } else {
      console.log(`✅ Message quality: GOOD`);
      console.log(`📏 Message length: ${result.message.length} characters`);
      
      // Check if it mentions trading strategies
      const hasTradingStrategy = /\b(calls?|puts?|long|short|bullish|bearish)\b/i.test(result.message);
      console.log(`📈 Trading strategy mentioned: ${hasTradingStrategy ? 'YES' : 'NO'}`);
      
      // Check if it mentions Osis.co
      const mentionsOsis = /osis\.co/i.test(result.message);
      console.log(`🔗 Mentions Osis.co: ${mentionsOsis ? 'YES' : 'NO'}`);
      
      return true;
    }
    
  } catch (error) {
    console.log(`❌ Test failed with error: ${error.message}`);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Starting TradingView Poster Improvement Tests');
  console.log('=' .repeat(60));
  
  let passedTests = 0;
  let totalTests = testCases.length;
  
  for (const testCase of testCases) {
    const passed = await testTradingViewPoster(testCase);
    if (passed) {
      passedTests++;
    }
    
    // Wait between tests to avoid rate limiting
    if (testCase !== testCases[testCases.length - 1]) {
      console.log('\n⏳ Waiting 3 seconds before next test...');
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }
  
  console.log('\n' + '='.repeat(60));
  console.log(`📊 Test Results: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! The improvements are working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Please review the issues above.');
  }
  
  return passedTests === totalTests;
}

// Run the tests
runAllTests()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
  });
