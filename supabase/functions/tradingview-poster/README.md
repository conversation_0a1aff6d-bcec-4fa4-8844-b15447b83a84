# TradingView Poster Edge Function

This Supabase Edge Function automatically cycles through 48 different symbols (stocks, crypto, forex, commodities, indices) on a perfect 24-hour schedule, fetches current price data from Polygon API, generates intelligent trading messages using Google Gemini AI with real-time Google Search grounding, and posts them to TradingView.

## How It Works

1. **Symbol Selection**: The function cycles through 48 different symbols with each symbol getting exactly 30 minutes. This creates a perfect 24-hour cycle that repeats daily at the same times:
   - **35 US Stocks**: Major companies like Apple, Tesla, NVIDIA, Microsoft, etc.
   - **5 Cryptocurrencies**: Bitcoin, Ethereum, XRP, Dogecoin, Solana
   - **1 Forex Pair**: EUR/USD
   - **2 Commodities**: Gold, Crude Oil
   - **5 Indices**: S&P 500, Nasdaq 100, Dow Jones, US Dollar Index

2. **Price Data Fetching**: The function fetches current price data from Polygon API (where supported), including the latest price, daily change, and percentage change. This ensures accurate, real-time pricing information is included in the trading commentary.

3. **Real-time Information Gathering**: Using Google Search grounding, Gemini AI gathers current news, recent developments, earnings reports, analyst upgrades/downgrades, and significant events related to the selected symbol.

4. **Message Generation**: Using only Google Gemini AI with Google Search grounding, the function generates natural, human-sounding trading messages that include:
   - Latest news and market developments from Google Search
   - Current market sentiment and context
   - Trading recommendations appropriate for each asset type (calls/puts for stocks, long/short for crypto/forex/commodities)
   - Real-time market information and analysis

5. **Osis.co Mention Strategy**: The function implements a 50% randomization system for mentioning Osis.co:
   - 50% of posts naturally integrate Osis.co as the analysis source
   - 50% of posts present the analysis as the trader's own insights
   - This creates more natural variation and avoids repetitive promotional content

6. **TradingView Posting**: The generated message is posted to TradingView's API with proper symbol formatting for different exchanges and asset types.

## Core Features

- **Perfect 24-Hour Cycle**: 48 symbols × 30 minutes = exactly 24 hours, repeating daily
- **Diverse Asset Coverage**: Stocks, crypto, forex, commodities, and indices
- **Real-time Price Data**: Accurate pricing from Polygon API (where supported)
- **Google Search Grounding**: Real-time news and market information gathering
- **No Fallback Systems**: Pure Gemini AI with Google Search - no backup analysis or fallback messages
- **Intelligent Prompting**: Asset-type-aware prompts that generate appropriate trading language
- **Exchange Compatibility**: Properly formats symbols for different exchanges and asset types
- **Special Symbol Handling**: Handles ETF-to-Index conversions (VOO→SPX, QQQ→NDX)
- **Manual Override**: Supports manual symbol selection via query parameter

## Environment Variables

The function requires the following environment variables:

- `GEMINI_API_KEY`: Your Google Gemini API key (with Google Search grounding enabled)
- `POLYGON_API_KEY`: Your Polygon.io API key for real-time price data

Note: The TradingView cookie is hard-coded in the function for authentication.

## Deployment

To deploy this function, run:

```bash
supabase functions deploy tradingview-poster
```

Or use the deployment script:

```bash
node scripts/deploy-tradingview-poster.js
```

## Scheduling

This function can be triggered manually by making a POST request to the function URL, or it can be scheduled to run automatically using a cron job or a service like GitHub Actions.

## Manual Symbol Selection

You can manually select a specific symbol by adding a `stockIndex` query parameter to the URL:

```
https://your-supabase-url/functions/v1/tradingview-poster?stockIndex=5
```

This will use the symbol at index 5 in the list regardless of the current time.

## 24-Hour Schedule

The function cycles through all 48 symbols every 24 hours:
- **12:00 AM**: Apple (AAPL)
- **12:30 AM**: Tesla (TSLA)
- **1:00 AM**: NVIDIA (NVDA)
- **1:30 AM**: AMD (AMD)
- ... (continues for all 48 symbols)
- **11:30 PM**: Dow Jones (CAPITALCOM-US30)
- **12:00 AM**: Cycle repeats

## Example Output

### Stock with Osis.co Mention (50% probability)
```json
{
  "success": true,
  "stock": {
    "symbol": "AAPL",
    "name": "Apple",
    "exchange": "NASDAQ"
  },
  "message": "Osis.co analysis shows bullish signals on Apple at $185.50 (*****, *****%) - calls looking attractive with the strong iPhone 15 sales data released this morning.",
  "tradingViewResult": {
    "id": "12345678",
    "status": "success"
  }
}
```

### Crypto without Osis.co Mention (50% probability)
```json
{
  "success": true,
  "stock": {
    "symbol": "BTC:USD",
    "name": "Bitcoin",
    "exchange": "CRYPTO",
    "postAs": {
      "symbol": "BTCUSDT",
      "name": "Bitcoin",
      "exchange": "CRYPTO"
    }
  },
  "message": "Bitcoin technical setup looks bullish - going long here with the recent ETF approval news and institutional buying pressure.",
  "tradingViewResult": {
    "id": "12345679",
    "status": "success"
  }
}
```

## Special Cases

The function handles special cases for certain symbols:

- **ETF to Index Conversion**: VOO → SPX, QQQ → NDX for proper TradingView posting
- **Exchange Formatting**: Different exchanges (NASDAQ, NYSE, CRYPTO, FOREX, COMMODITY, INDEX) are properly formatted
- **Symbol Mapping**: International symbols map to correct TradingView URLs (e.g., CAPITALCOM-US100, SPX500USD)
- **Asset-Specific Language**: Trading language adapts to asset type (calls/puts for stocks, long/short for crypto/forex)

## Key Improvements

- **Simplified Architecture**: Removed Aura dependency, uses only Gemini AI with Google Search
- **No Fallback Systems**: Pure AI-generated content with real-time information
- **24/7 Coverage**: Perfect 24-hour cycle covering global markets and asset classes
- **Real-time Intelligence**: Google Search grounding provides latest market information
- **Consistent Timing**: Predictable 30-minute intervals for each symbol
