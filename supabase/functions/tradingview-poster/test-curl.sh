#!/bin/bash

# Replace these with your actual Supabase URL and anon key
# You can find these in your Supabase dashboard under Project Settings > API
SUPABASE_URL="https://your-project-id.supabase.co"
SUPABASE_ANON_KEY="your-anon-key"

# Test the TradingView Poster edge function with automatic stock selection
echo "Testing with automatic stock selection:"
curl -X POST "${SUPABASE_URL}/functions/v1/tradingview-poster" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${SUPABASE_ANON_KEY}" \
  -d '{}'

# Wait a moment before the next request
sleep 2

# Test with manual stock selection (index 5 = Amazon)
echo -e "\n\nTesting with manual stock selection (Amazon):"
curl -X POST "${SUPABASE_URL}/functions/v1/tradingview-poster?stockIndex=5" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${SUPABASE_ANON_KEY}" \
  -d '{}'

# Note: This will trigger the function to:
# 1. Select a stock based on the current time or manual override
# 2. Analyze it with Aura
# 3. Generate a sentiment message with Gemini
# 4. Post the message to TradingView
#
# The response will include the selected stock, generated message, and TradingView response
