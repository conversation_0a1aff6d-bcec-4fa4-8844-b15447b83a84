// TradingView Poster Edge Function
// This function selects a Magnificent 7 stock, analyzes it with Aura,
// generates a sentiment message with <PERSON>, and posts it to TradingView

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Allow-Methods': '*'
};

// API keys only - no Supabase needed since we're not using Aura

// API keys
const GEMINI_API_KEY = Deno.env.get('GEMINI_API_KEY') || '';
const POLYGON_API_KEY = Deno.env.get('POLYGON_API_KEY') || '';

// Polygon API helper functions
async function fetchFromPolygon(url: string): Promise<any> {
  try {
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Polygon API error: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error fetching from Polygon API:`, error);
    throw error;
  }
}

async function fetchCurrentPriceData(symbol: string): Promise<{
  symbol: string;
  price: number;
  change: number;
  percentChange: number;
  timestamp: number;
} | null> {
  try {
    if (!POLYGON_API_KEY) {
      console.warn('Polygon API key not available, skipping price data');
      return null;
    }

    // Skip symbols that are not supported by Polygon API for stocks
    // Crypto symbols (contain ':'), forex pairs, commodities, and some indices are not supported
    const unsupportedSymbols = ['EURUSD', 'XAUUSD', 'USOIL', 'DXY', 'SPX500', 'NAS100', 'US30'];

    if (symbol.includes(':') || unsupportedSymbols.includes(symbol)) {
      console.log(`Skipping price fetch for unsupported symbol: ${symbol}`);
      return null;
    }

    // Get current time to ensure latest data (15 minutes delayed)
    const currentTime = Date.now();

    // Fetch latest trade data
    const tradeUrl = `https://api.polygon.io/v2/last/trade/${symbol}?apiKey=${POLYGON_API_KEY}&timestamp=${currentTime}`;
    const tradeResponse = await fetchFromPolygon(tradeUrl);

    if (!tradeResponse.results) {
      console.warn(`No trade data available for ${symbol}`);
      return null;
    }

    const currentPrice = tradeResponse.results.p || 0;

    // Get previous day's close for change calculation
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = yesterday.toISOString().split('T')[0];

    const prevCloseUrl = `https://api.polygon.io/v1/open-close/${symbol}/${yesterdayStr}?apiKey=${POLYGON_API_KEY}`;

    let previousClose = currentPrice; // fallback
    try {
      const prevCloseResponse = await fetchFromPolygon(prevCloseUrl);
      if (prevCloseResponse.close) {
        previousClose = prevCloseResponse.close;
      }
    } catch (error) {
      console.warn(`Could not fetch previous close for ${symbol}, using current price as fallback`);
    }

    const change = currentPrice - previousClose;
    const percentChange = previousClose > 0 ? (change / previousClose) * 100 : 0;

    return {
      symbol,
      price: currentPrice,
      change,
      percentChange,
      timestamp: tradeResponse.results.t || currentTime
    };
  } catch (error) {
    console.error(`Error fetching current price for ${symbol}:`, error);
    return null;
  }
}

// Define the complete list of all symbols to cycle through 7am-5pm - every 30 minutes
// 20 tickers total for 10-hour trading window (7am-5pm = 10 hours = 20 slots at 30min intervals)
const ALL_SYMBOLS_LIST = [
  // US Stocks (active during trading hours)
  { symbol: "AAPL", name: "Apple", exchange: "NASDAQ" },
  { symbol: "TSLA", name: "Tesla", exchange: "NASDAQ" },
  { symbol: "NVDA", name: "NVIDIA", exchange: "NASDAQ" },
  { symbol: "META", name: "Meta", exchange: "NASDAQ" },
  { symbol: "AMZN", name: "Amazon", exchange: "NASDAQ" },
  { symbol: "MSFT", name: "Microsoft", exchange: "NASDAQ" },
  { symbol: "GOOG", name: "Google", exchange: "NASDAQ" },
  { symbol: "GME", name: "GameStop", exchange: "NYSE" },
  { symbol: "SMCI", name: "Super Micro Computer", exchange: "NASDAQ" },
  { symbol: "PLTR", name: "Palantir", exchange: "NYSE" },
  { symbol: "COIN", name: "Coinbase", exchange: "NASDAQ" },
  { symbol: "MSTR", name: "MicroStrategy", exchange: "NASDAQ" },

  // Crypto (24/7 trading)
  { symbol: "BTC:USD", name: "Bitcoin", exchange: "CRYPTO", postAs: { symbol: "BTCUSDT", name: "Bitcoin", exchange: "CRYPTO" } },
  { symbol: "ETH:USD", name: "Ethereum", exchange: "CRYPTO", postAs: { symbol: "ETHUSD", name: "Ethereum", exchange: "CRYPTO" } },
  { symbol: "XRP:USD", name: "XRP", exchange: "CRYPTO", postAs: { symbol: "XRPUSD", name: "XRP", exchange: "CRYPTO" } },
  { symbol: "SOL:USD", name: "Solana", exchange: "CRYPTO", postAs: { symbol: "SOLUSD", name: "Solana", exchange: "CRYPTO" } },

  // International Indices (different time zones)
  { symbol: "DXY", name: "US Dollar Index", exchange: "INDEX", postAs: { symbol: "CAPITALCOM-DXY", name: "US Dollar Index", exchange: "INDEX" } },
  { symbol: "SPX500", name: "S&P 500", exchange: "INDEX", postAs: { symbol: "SPX500USD", name: "S&P 500", exchange: "INDEX" } },
  { symbol: "NAS100", name: "Nasdaq 100", exchange: "INDEX", postAs: { symbol: "CAPITALCOM-US100", name: "Nasdaq 100", exchange: "INDEX" } },
  { symbol: "US30", name: "Dow Jones", exchange: "INDEX", postAs: { symbol: "CAPITALCOM-US30", name: "Dow Jones", exchange: "INDEX" } }
];

// Function to select a symbol based on time - 7am to 5pm, every 30 minutes
function selectStockByTime(manualOverrideIndex?: number): { symbol: string, name: string, exchange: string, postAs?: { symbol: string, name: string, exchange: string } } {
  // If a manual override index is provided, use it from the unified list
  if (manualOverrideIndex !== undefined && manualOverrideIndex >= 0 && manualOverrideIndex < ALL_SYMBOLS_LIST.length) {
    console.log(`Using manual override index: ${manualOverrideIndex}`);
    return ALL_SYMBOLS_LIST[manualOverrideIndex];
  }

  const now = new Date();
  const currentHour = now.getHours();
  const currentMinute = now.getMinutes();

  // Trading window: 7am (07:00) to 5pm (17:00) = 10 hours
  // 30-minute intervals = 20 slots total (7:00, 7:30, 8:00, 8:30, ..., 16:30, 17:00)
  const startHour = 7;  // 7am
  const endHour = 17;   // 5pm

  // Check if we're within trading hours (include 5pm hour, so <= endHour)
  if (currentHour < startHour || currentHour > endHour) {
    console.log(`Outside trading hours (${currentHour}:${currentMinute}). Trading window: ${startHour}:00 - ${endHour}:00`);
    // Use a rotating symbol based on hour even outside trading hours to avoid always returning AAPL
    const outsideHoursIndex = currentHour % ALL_SYMBOLS_LIST.length;
    console.log(`Using outside-hours symbol index: ${outsideHoursIndex}`);
    return ALL_SYMBOLS_LIST[outsideHoursIndex];
  }

  // Calculate 30-minute slots since 7am
  const minutesSinceStart = ((currentHour - startHour) * 60) + currentMinute;
  const slotIndex = Math.floor(minutesSinceStart / 30); // 30-minute intervals
  const symbolIndex = slotIndex % ALL_SYMBOLS_LIST.length;

  console.log(`WITHIN TRADING HOURS - Current time: ${currentHour}:${currentMinute}`);
  console.log(`Minutes since ${startHour}:00: ${minutesSinceStart}`);
  console.log(`30-min slot index: ${slotIndex}, Symbol index: ${symbolIndex}`);
  console.log(`Total symbols in list: ${ALL_SYMBOLS_LIST.length}`);
  console.log(`Selected symbol: ${ALL_SYMBOLS_LIST[symbolIndex].name} (${ALL_SYMBOLS_LIST[symbolIndex].symbol})`);

  return ALL_SYMBOLS_LIST[symbolIndex];
}

// Function to determine if Osis.co should be mentioned (70% probability)
function shouldMentionOsis(): boolean {
  return Math.random() < 0.7;
}

// Function to generate a sentiment message with Gemini using Google Search grounding
async function generateSentimentMessage(stock: {
  symbol: string,
  name: string,
  exchange: string,
  postAs?: { symbol: string, name: string, exchange: string }
}): Promise<string> {
  // Use postAs values if available (for special cases like VOO/SPX and QQQ/NDX)
  const displaySymbol = stock.postAs?.symbol || stock.symbol;
  const displayName = stock.postAs?.name || stock.name;

  // Fetch current price data from Polygon API
  const priceData = await fetchCurrentPriceData(stock.symbol);
  let priceContext = '';

  if (priceData) {
    const priceStr = priceData.price.toFixed(2);
    const changeStr = priceData.change >= 0 ? `+${priceData.change.toFixed(2)}` : priceData.change.toFixed(2);
    const percentStr = priceData.percentChange >= 0 ? `+${priceData.percentChange.toFixed(2)}%` : `${priceData.percentChange.toFixed(2)}%`;
    priceContext = ` Currently trading at $${priceStr} (${changeStr}, ${percentStr}).`;
    console.log(`Price data for ${stock.symbol}: $${priceStr} (${changeStr}, ${percentStr})`);
  } else {
    console.log(`No price data available for ${stock.symbol}`);
  }

  // Determine if we should mention Osis.co (50% probability)
  const mentionOsis = shouldMentionOsis();
  console.log(`Osis.co mention decision: ${mentionOsis ? 'YES' : 'NO'}`);

  // Determine appropriate trading language based on asset type
  const isStock = stock.exchange === 'NASDAQ' || stock.exchange === 'NYSE';
  const assetType = stock.exchange === 'CRYPTO' ? 'cryptocurrency' :
                   stock.exchange === 'FOREX' ? 'forex pair' :
                   stock.exchange === 'COMMODITY' ? 'commodity' :
                   stock.exchange === 'INDEX' ? 'index' : 'stock';

  // Create comprehensive prompt for Gemini with Google Search grounding
  const prompt = `You are a trader posting directly on TradingView. Write ONLY the trading comment itself.

DO NOT write:
- "Here's a trading comment"
- "Based on the latest information"
- "Okay, here's..."
- "Let me write..."
- Any introduction or explanation

Search for latest news about ${displayName} (${displaySymbol}), a ${assetType}.${priceContext}

Start with: "${displayName} technical setup looks bullish" or "${displayName} technical setup looks bearish" or "${displayName} charts showing bullish momentum"

${mentionOsis ? `In the second or third sentence, naturally work in "Osis.co analysis confirms this setup" or "Osis.co data supports this move" or "According to Osis.co indicators"` : ''}

Add recent news/context and ${isStock ? 'mention calls/puts or going long/short' : 'mention going long/short'}. 2-3 sentences total.

Examples:
"Tesla technical setup looks bullish at $245.50 (+3.2%). Strong delivery numbers supporting the move. Calls looking attractive here."
"Bitcoin charts showing bearish momentum below $95k. Osis.co analysis confirms this breakdown setup. Might short here with regulatory concerns."
"Gold technical setup looks bullish above $2650. According to Osis.co indicators, momentum is building. Going long with inflation fears rising."

Write the trading comment:`;

  // Call Gemini API with Google Search grounding enabled
  const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-goog-api-key': GEMINI_API_KEY,
    },
    body: JSON.stringify({
      contents: [
        {
          parts: [
            { text: prompt }
          ]
        }
      ],
      tools: [{ googleSearch: {} }], // Enable Google Search grounding
      generationConfig: {
        temperature: 0.8, // Higher temperature for more varied responses
        maxOutputTokens: 200, // Shorter responses
        topK: 40,
        topP: 0.95
      }
    })
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error(`Gemini API error: ${response.status} - ${errorText}`);
    throw new Error(`Gemini API error: ${response.status}`);
  }

  const data = await response.json();
  console.log('Gemini API response:', JSON.stringify(data, null, 2));

  // Extract message from response
  let message = data.candidates[0].content.parts[0].text.trim();

  // Check for meta-commentary patterns and reject them
  const metaCommentaryPatterns = [
    /^(Okay|Alright|Sure),?\s*here'?s?\s*(a|an|the)/i,
    /^(Okay|Alright|Sure),?\s*I'll/i,
    /^Let me\s*(create|craft|generate|write)/i,
    /^I'll\s*(create|craft|generate|write)/i,
    /^Here'?s?\s*(a|an|the)\s*(trading\s*)?(comment|analysis)/i,
    /^Based on.*information/i,
    /^According to.*latest/i,
    /^Looking at.*latest/i,
    /^From.*search/i
  ];

  const hasMetaCommentary = metaCommentaryPatterns.some(pattern => pattern.test(message));

  if (hasMetaCommentary) {
    console.error(`Detected meta-commentary in response: "${message}"`);
    // Try to extract the actual trading comment if it exists after the meta-commentary
    const lines = message.split('\n').filter((line: string) => line.trim());
    if (lines.length > 1) {
      // Use the last non-empty line as it's likely the actual trading comment
      message = lines[lines.length - 1].trim();
      console.log(`Extracted trading comment: "${message}"`);
    } else {
      // If we can't extract a clean comment, throw an error to retry
      throw new Error('Generated response contains meta-commentary and cannot be cleaned');
    }
  }

  console.log(`Final generated message for ${stock.symbol}: ${message}`);
  return message;
}

// Function to post a message to TradingView
async function postToTradingView(stock: {
  symbol: string,
  name: string,
  exchange: string,
  postAs?: { symbol: string, name: string, exchange: string }
}, message: string): Promise<any> {
  try {
    // Use postAs values if available (for special cases like VOO/SPX and QQQ/NDX)
    const displaySymbol = stock.postAs?.symbol || stock.symbol;
    const displayExchange = stock.postAs?.exchange || stock.exchange;

    // Format the exchange prefix based on the exchange type
    let exchangePrefix = "";
    switch (displayExchange) {
      case "NASDAQ":
        exchangePrefix = "NASDAQ";
        break;
      case "NYSE":
        exchangePrefix = "NYSE";
        break;
      case "CRYPTO":
        exchangePrefix = "";
        break;
      case "INDEX":
        // For indices, we don't need an exchange prefix
        exchangePrefix = "";
        break;
      case "FOREX":
        // For forex pairs, no exchange prefix needed
        exchangePrefix = "";
        break;
      case "COMMODITY":
        // For commodities, no exchange prefix needed
        exchangePrefix = "";
        break;
      default:
        exchangePrefix = "NASDAQ"; // Default to NASDAQ if unknown
    }

    // Format the symbol with exchange prefix
    // For INDEX, FOREX, COMMODITY, and CRYPTO, use the symbol directly
    // For stocks, use exchange prefix
    const formattedSymbol = (displayExchange === "INDEX" || displayExchange === "FOREX" || displayExchange === "COMMODITY" || displayExchange === "CRYPTO")
      ? displaySymbol
      : `${exchangePrefix}:${displaySymbol}`;

    // Prepare the request body for TradingView
    const requestBody = {
      text: message,
      symbols: {
        [displaySymbol]: formattedSymbol
      }
    };

    // Post to TradingView
    const response = await fetch('https://www.tradingview.com/api/v1/minds/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'https://www.tradingview.com',
        'Referer': `https://www.tradingview.com/symbols/${formattedSymbol.replace(':', '-')}/minds/`,
        'Cookie': 'cookiePrivacyPreferenceBannerProduction=notApplicable; _ga=GA1.1.390075791.1736906871; cookiesSettings={"analytics":true,"advertising":true}; g_state={"i_l":0}; device_t=VmJFWkFnOjA.9Pqhhc5MgAfpt1jn_MhYsL-bMlaLfW79fCT-nN1L550; sessionid=po52oo9yfsq0ggyih7o66qz90lt2fiut; sessionid_sign=v3:fF3A34LqESM0qEJhHX4PoUTKiDg6/XZfOhWt4Z9cZV0=; etg=undefined; cachec=undefined; _sp_ses.cf1a=*; _sp_id.cf1a=891071aa-f7c9-4f18-ae1c-87ee84ba99a2.1736906871.19.1750546212.1748913066.138c0ccd-e41f-4853-ba76-d6ca2c705348.12308f39-e38b-4a75-8da6-8a0602baec0a.8a1687a3-7d2b-4633-8c0b-f63921726c42.1750546211509.1; _ga_YVVRYGL0E0=GS2.1.s1750546211$o74$g0$t1750546212$j59$l0$h0'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`TradingView API error: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error posting to TradingView:", error);
    throw error;
  }
}

// Main function to process the request
async function processRequest(stockIndex?: number): Promise<any> {
  try {
    // 1. Select a symbol based on time of day or manual override
    const stock = selectStockByTime(stockIndex);
    console.log(`Selected symbol: ${stock.name} (${stock.symbol})`);

    // 2. Generate a sentiment message with Gemini using Google Search grounding
    const message = await generateSentimentMessage(stock);
    console.log(`Generated message for ${stock.symbol}: "${message}"`);

    // 3. Post the message to TradingView
    const tradingViewResult = await postToTradingView(stock, message);
    console.log(`Posted to TradingView: ${JSON.stringify(tradingViewResult)}`);

    return {
      success: true,
      stock: stock,
      message: message,
      tradingViewResult: tradingViewResult
    };
  } catch (error) {
    console.error("Error processing request:", error);
    throw error;
  }
}

// Serve the edge function
serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }

  try {
    // Parse query parameters
    const url = new URL(req.url);
    const stockIndexParam = url.searchParams.get('stockIndex');
    let stockIndex: number | undefined = undefined;

    // If stockIndex is provided, parse it
    if (stockIndexParam) {
      stockIndex = parseInt(stockIndexParam, 10);
      if (isNaN(stockIndex) || stockIndex < 0 || stockIndex >= ALL_SYMBOLS_LIST.length) {
        return new Response(JSON.stringify({
          error: `Invalid stockIndex. Must be between 0 and ${ALL_SYMBOLS_LIST.length - 1}.`,
          validSymbols: ALL_SYMBOLS_LIST.map((symbol, index) => `${index}: ${symbol.symbol} (${symbol.name})`)
        }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }
    }

    // Process the request with optional stock index
    const result = await processRequest(stockIndex);

    // Return the result
    return new Response(JSON.stringify(result), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } catch (error) {
    // Handle errors
    console.error("Error in edge function:", error);

    return new Response(JSON.stringify({
      error: error.message || 'Unknown error',
      type: error.constructor.name
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});
