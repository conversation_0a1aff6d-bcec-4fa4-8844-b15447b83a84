#!/usr/bin/env node

/**
 * Test script to verify the pattern detection logic works correctly
 */

// Test the incomplete response patterns
function testIncompletePatterns() {
  console.log('🧪 Testing Incomplete Response Pattern Detection');
  console.log('=' .repeat(50));

  const incompletePatterns = [
    /^(Okay|Alright|Sure),?\s*I'm\s*(ready\s*to|going\s*to)/i,
    /^(Okay|Alright|Sure),?\s*here'?s?\s*(a|an|the)/i, // "Okay, here's a trader's comment..."
    /^Let me\s*(create|craft|generate)/i,
    /^I'll\s*(create|craft|generate)/i,
    /^Here'?s?\s*(a|an|the)\s*(trader'?s?\s*)?(comment|analysis|insight)/i, // "Here's a trader's comment..."
    /based on your requirements:?\s*$/i, // Ends with "based on your requirements:"
    /\[.*\]$/, // Ends with placeholder brackets
    /\.\.\.$/, // Ends with ellipsis
    /\(\s*\)/, // Contains empty parentheses like "Apple ( )"
  ];

  const testCases = [
    {
      message: "Okay, here's a trader's comment on Apple (",
      shouldMatch: true,
      description: "The exact problematic case reported"
    },
    {
      message: "Okay, I'm ready to craft a trading comment for Gamestop (",
      shouldMatch: true,
      description: "Original problematic pattern"
    },
    {
      message: "Here's a trader's comment based on your requirements:",
      shouldMatch: true,
      description: "Meta-commentary pattern"
    },
    {
      message: "Apple technical setup looks bullish - calls might be interesting.",
      shouldMatch: false,
      description: "Good trading comment"
    },
    {
      message: "Osis.co analysis shows bullish signals on Tesla - going long here.",
      shouldMatch: false,
      description: "Good Osis.co comment"
    },
    {
      message: "Let me create a trading insight for you...",
      shouldMatch: true,
      description: "Meta-commentary with 'Let me'"
    },
    {
      message: "Apple ( ) showing bullish momentum",
      shouldMatch: true,
      description: "Empty parentheses pattern"
    },
    {
      message: "Technical analysis shows [recent development] supports bullish view...",
      shouldMatch: true,
      description: "Ends with ellipsis"
    }
  ];

  let passed = 0;
  let total = testCases.length;

  testCases.forEach((testCase, index) => {
    const isIncomplete = incompletePatterns.some(pattern => pattern.test(testCase.message));
    const testPassed = isIncomplete === testCase.shouldMatch;
    
    console.log(`\nTest ${index + 1}: ${testCase.description}`);
    console.log(`Message: "${testCase.message}"`);
    console.log(`Expected: ${testCase.shouldMatch ? 'INCOMPLETE' : 'COMPLETE'}`);
    console.log(`Actual: ${isIncomplete ? 'INCOMPLETE' : 'COMPLETE'}`);
    console.log(`Result: ${testPassed ? '✅ PASS' : '❌ FAIL'}`);
    
    if (testPassed) passed++;
  });

  console.log('\n' + '='.repeat(50));
  console.log(`📊 Pattern Detection Results: ${passed}/${total} tests passed`);
  
  return passed === total;
}

// Test the expected starts validation
function testExpectedStarts() {
  console.log('\n🧪 Testing Expected Starts Validation');
  console.log('=' .repeat(50));

  const testCases = [
    {
      message: "Apple technical setup looks bullish - calls might be interesting.",
      displayName: "Apple",
      mentionOsis: false,
      shouldPass: true,
      description: "Starts with stock name"
    },
    {
      message: "Osis.co analysis shows bullish signals on Tesla - going long.",
      displayName: "Tesla", 
      mentionOsis: true,
      shouldPass: true,
      description: "Starts with Osis.co"
    },
    {
      message: "Technical analysis on NVIDIA pointing bullish.",
      displayName: "NVIDIA",
      mentionOsis: false,
      shouldPass: true,
      description: "Starts with 'Technical'"
    },
    {
      message: "Okay, here's a trader's comment on Apple",
      displayName: "Apple",
      mentionOsis: false,
      shouldPass: false,
      description: "Starts with meta-commentary"
    },
    {
      message: "Here's what I think about Tesla...",
      displayName: "Tesla",
      mentionOsis: false,
      shouldPass: false,
      description: "Meta-commentary start"
    }
  ];

  let passed = 0;
  let total = testCases.length;

  testCases.forEach((testCase, index) => {
    const expectedStarts = testCase.mentionOsis 
      ? [`Osis.co analysis`, `Osis.co`, `The Osis.co`]
      : [`${testCase.displayName}`, `My analysis`, `Technical`, `Looking at`, `Been watching`];
    
    const startsCorrectly = expectedStarts.some(start => 
      testCase.message.toLowerCase().startsWith(start.toLowerCase())
    );
    
    const testPassed = startsCorrectly === testCase.shouldPass;
    
    console.log(`\nTest ${index + 1}: ${testCase.description}`);
    console.log(`Message: "${testCase.message}"`);
    console.log(`Expected starts: ${expectedStarts.join(', ')}`);
    console.log(`Expected: ${testCase.shouldPass ? 'PASS' : 'FAIL'}`);
    console.log(`Actual: ${startsCorrectly ? 'PASS' : 'FAIL'}`);
    console.log(`Result: ${testPassed ? '✅ PASS' : '❌ FAIL'}`);
    
    if (testPassed) passed++;
  });

  console.log('\n' + '='.repeat(50));
  console.log(`📊 Expected Starts Results: ${passed}/${total} tests passed`);
  
  return passed === total;
}

// Run all tests
function runAllTests() {
  console.log('🚀 Running Pattern Detection Tests\n');
  
  const patternTestsPassed = testIncompletePatterns();
  const startsTestsPassed = testExpectedStarts();
  
  const allPassed = patternTestsPassed && startsTestsPassed;
  
  console.log('\n' + '='.repeat(60));
  console.log(`🎯 Overall Results: ${allPassed ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'}`);
  console.log(`Pattern Detection: ${patternTestsPassed ? '✅' : '❌'}`);
  console.log(`Expected Starts: ${startsTestsPassed ? '✅' : '❌'}`);
  
  if (allPassed) {
    console.log('🎉 The improved pattern detection should catch incomplete responses!');
  } else {
    console.log('⚠️  Pattern detection needs further refinement.');
  }
  
  return allPassed;
}

// Run the tests
runAllTests();
