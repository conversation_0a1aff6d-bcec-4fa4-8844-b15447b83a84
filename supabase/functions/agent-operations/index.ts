import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase admin client (bypasses RLS)
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const { operation, agentData, userId, agentId } = await req.json()

    if (!operation || !userId) {
      return new Response(
        JSON.stringify({ error: 'Missing operation or userId' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    console.log(`🔧 Agent operation: ${operation} for user: ${userId}`)

    let result

    switch (operation) {
      case 'create':
        if (!agentData) {
          throw new Error('Missing agentData for create operation')
        }

        // Create new agent
        const { data: newAgent, error: createError } = await supabaseAdmin
          .from('agents')
          .insert({
            ...agentData,
            user_id: userId
          })
          .select()
          .single()

        if (createError) throw createError
        result = { success: true, agent: newAgent }
        break

      case 'update':
        if (!agentData || !agentId) {
          throw new Error('Missing agentData or agentId for update operation')
        }

        // Verify ownership first
        const { data: existingAgent, error: ownershipError } = await supabaseAdmin
          .from('agents')
          .select('user_id')
          .eq('id', agentId)
          .single()

        if (ownershipError) throw ownershipError
        if (existingAgent.user_id !== userId) {
          throw new Error('User does not own this agent')
        }

        // Update agent
        const { data: updatedAgent, error: updateError } = await supabaseAdmin
          .from('agents')
          .update(agentData)
          .eq('id', agentId)
          .eq('user_id', userId)
          .select()
          .single()

        if (updateError) throw updateError
        result = { success: true, agent: updatedAgent }
        break

      case 'get':
        if (!agentId) {
          throw new Error('Missing agentId for get operation')
        }

        // Get agent by ID and verify ownership
        const { data: agent, error: getError } = await supabaseAdmin
          .from('agents')
          .select('*')
          .eq('id', agentId)
          .eq('user_id', userId)
          .single()

        if (getError) {
          if (getError.code === 'PGRST116') {
            // No rows returned - agent not found or user doesn't own it
            result = { success: false, error: 'Agent not found or access denied' }
          } else {
            throw getError
          }
        } else {
          result = { success: true, agent: agent }
        }
        break

      case 'list':
        // Get all agents for the user
        const { data: agents, error: listError } = await supabaseAdmin
          .from('agents')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', { ascending: false })

        if (listError) throw listError
        result = { success: true, agents: agents || [] }
        break

      case 'delete':
        if (!agentId) {
          throw new Error('Missing agentId for delete operation')
        }

        // Verify ownership first
        const { data: agentToDelete, error: deleteOwnershipError } = await supabaseAdmin
          .from('agents')
          .select('user_id')
          .eq('id', agentId)
          .single()

        if (deleteOwnershipError) throw deleteOwnershipError
        if (agentToDelete.user_id !== userId) {
          throw new Error('User does not own this agent')
        }

        // Delete agent
        const { error: deleteError } = await supabaseAdmin
          .from('agents')
          .delete()
          .eq('id', agentId)
          .eq('user_id', userId)

        if (deleteError) throw deleteError
        result = { success: true }
        break

      default:
        throw new Error(`Unknown operation: ${operation}`)
    }

    console.log(`✅ Agent operation ${operation} completed successfully`)

    return new Response(
      JSON.stringify(result),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('❌ Error in agent-operations:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Operation failed',
        details: error.message 
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})
