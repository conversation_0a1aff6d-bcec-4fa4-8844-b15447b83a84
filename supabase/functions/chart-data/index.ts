import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Allow-Methods': '*'
};

// Initialize Supabase client with admin privileges
const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Get Polygon API key from environment variables
const POLYGON_API_KEY = Deno.env.get('POLYGON_API_KEY') || '';

if (!POLYGON_API_KEY) {
  console.error('POLYGON_API_KEY is not set in environment variables');
}

// Helper function to format polygon symbol
const formatPolygonSymbol = async (symbol: string): Promise<string> => {
  // Check if it's a crypto symbol
  const isCrypto = /^[A-Z]{3,5}$/.test(symbol) &&
                  !symbol.includes('.') &&
                  ['BTC', 'ETH', 'SOL', 'XRP', 'ADA', 'DOGE', 'DOT', 'AVAX', 'MATIC', 'LINK', 'UNI', 'SHIB'].includes(symbol);

  if (isCrypto) {
    return `X:${symbol}USD`;
  }

  return symbol;
};

// Function to check if the US stock market is currently open
function isMarketOpen(): boolean {
  // Get current date and time in US Eastern Time (ET)
  const now = new Date();
  const etOptions: Intl.DateTimeFormatOptions = { timeZone: 'America/New_York' };
  const etDate = new Date(now.toLocaleString('en-US', etOptions));

  // Get day of week (0 = Sunday, 6 = Saturday)
  const dayOfWeek = etDate.getDay();

  // Check if it's a weekend
  if (dayOfWeek === 0 || dayOfWeek === 6) {
    return false;
  }

  // Get hours and minutes in ET
  const hours = etDate.getHours();
  const minutes = etDate.getMinutes();
  const currentTimeInMinutes = hours * 60 + minutes;

  // Regular market hours: 9:30 AM to 4:00 PM ET
  const marketOpenTimeInMinutes = 9 * 60 + 30;  // 9:30 AM
  const marketCloseTimeInMinutes = 16 * 60;     // 4:00 PM

  // Check if current time is within market hours
  const isOpen = currentTimeInMinutes >= marketOpenTimeInMinutes &&
                 currentTimeInMinutes < marketCloseTimeInMinutes;

  return isOpen;
}

// Function to get the most recent market day
function getMostRecentMarketDay(): string {
  const now = new Date();
  const etOptions: Intl.DateTimeFormatOptions = { timeZone: 'America/New_York' };
  let etDate = new Date(now.toLocaleString('en-US', etOptions));

  // Get day of week (0 = Sunday, 6 = Saturday)
  const dayOfWeek = etDate.getDay();

  // Adjust date to most recent market day
  if (dayOfWeek === 0) { // Sunday, go back to Friday
    etDate.setDate(etDate.getDate() - 2);
  } else if (dayOfWeek === 6) { // Saturday, go back to Friday
    etDate.setDate(etDate.getDate() - 1);
  } else {
    // For weekdays, if it's before market open, use previous day
    const hours = etDate.getHours();
    const minutes = etDate.getMinutes();
    const currentTimeInMinutes = hours * 60 + minutes;
    const marketOpenTimeInMinutes = 9 * 60 + 30; // 9:30 AM

    if (currentTimeInMinutes < marketOpenTimeInMinutes) {
      // Before market open, use previous day
      etDate.setDate(etDate.getDate() - 1);

      // If that takes us to a weekend, adjust further
      const prevDayOfWeek = etDate.getDay();
      if (prevDayOfWeek === 0) { // Sunday, go back to Friday
        etDate.setDate(etDate.getDate() - 2);
      } else if (prevDayOfWeek === 6) { // Saturday, go back to Friday
        etDate.setDate(etDate.getDate() - 1);
      }
    }
  }

  // Format as YYYY-MM-DD
  const year = etDate.getFullYear();
  const month = (etDate.getMonth() + 1).toString().padStart(2, '0');
  const day = etDate.getDate().toString().padStart(2, '0');

  const formattedDate = `${year}-${month}-${day}`;
  return formattedDate;
}

// Function to get interval parameters based on timeframe
function getIntervalParams(timeframe: string, interval?: string): { startDate: string; endDate: string; multiplier: number; timespan: string } {
  // Get current date in US Eastern time (market timezone)
  const now = new Date();

  // Convert to US Eastern time (ET) string representation
  const options: Intl.DateTimeFormatOptions = {
    timeZone: 'America/New_York',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  };
  const etDateStr = now.toLocaleDateString('en-US', options);

  // Parse the ET date string back to a date object
  const [month, day, year] = etDateStr.split('/');
  const etDateFormatted = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;

  // Create a new Date object for the current ET date
  const etNow = new Date(`${etDateFormatted}T12:00:00`);

  let startDate: Date;
  let multiplier: number;
  let timespan: string;

  // Check if market is open
  const marketOpen = isMarketOpen();

  // For 1D timeframe, if market is closed, use the most recent market day
  let useToday = true;
  if (timeframe === '1D' && !marketOpen) {
    const mostRecentMarketDay = getMostRecentMarketDay();
    if (mostRecentMarketDay !== etDateFormatted) {
      useToday = false;
    }
  }

  // Set the start date based on timeframe
  switch (timeframe) {
    case '1D':
      if (useToday) {
        startDate = new Date(etNow);
        startDate.setHours(0, 0, 0, 0); // Start of today in ET
      } else {
        // Use most recent market day
        const mostRecentMarketDay = getMostRecentMarketDay();
        startDate = new Date(mostRecentMarketDay + 'T00:00:00');
      }
      break;
    case '5D': // Add support for 5-day timeframe
      startDate = new Date(etNow);
      // Go back 5 market days (approximately 7 calendar days to account for weekends)
      startDate.setDate(etNow.getDate() - 7);
      break;
    case '1W':
      startDate = new Date(etNow);
      startDate.setDate(etNow.getDate() - 7);
      break;
    case '1M':
      startDate = new Date(etNow);
      startDate.setMonth(etNow.getMonth() - 1);
      break;
    case '3M':
      startDate = new Date(etNow);
      startDate.setMonth(etNow.getMonth() - 3);
      break;
    case '6M':
      startDate = new Date(etNow);
      startDate.setMonth(etNow.getMonth() - 6);
      break;
    case 'YTD': // Add support for Year-to-Date timeframe
      startDate = new Date(etNow);
      startDate.setMonth(0); // January
      startDate.setDate(1);  // 1st day of the month
      break;
    case '1Y':
      startDate = new Date(etNow);
      startDate.setFullYear(etNow.getFullYear() - 1);
      break;
    case '5Y':
      startDate = new Date(etNow);
      startDate.setFullYear(etNow.getFullYear() - 5);
      break;
    default:
      startDate = new Date(etNow);
      startDate.setHours(0, 0, 0, 0); // Default to start of today in ET
  }

  // Set the multiplier and timespan based on interval or default for timeframe
  if (interval) {
    switch (interval) {
      case '1min':
        multiplier = 1;
        timespan = 'minute';
        break;
      case '5min':
        multiplier = 5;
        timespan = 'minute';
        break;
      case '15min':
        multiplier = 15;
        timespan = 'minute';
        break;
      case '30min':
        multiplier = 30;
        timespan = 'minute';
        break;
      case '1hour':
        multiplier = 1;
        timespan = 'hour';
        break;
      case '4hour':
        multiplier = 4;
        timespan = 'hour';
        break;
      case 'daily':
        multiplier = 1;
        timespan = 'day';
        break;
      case 'weekly':
        multiplier = 1;
        timespan = 'week';
        break;
      default:
        // Default based on timeframe
        if (timeframe === '1D') {
          multiplier = 5;
          timespan = 'minute';
        } else if (timeframe === '5D') {
          multiplier = 30;
          timespan = 'minute';
        } else if (timeframe === '1W') {
          multiplier = 30;
          timespan = 'minute';
        } else if (timeframe === '1M') {
          multiplier = 1;
          timespan = 'hour';
        } else if (timeframe === 'YTD') {
          multiplier = 1;
          timespan = 'day';
        } else {
          multiplier = 1;
          timespan = 'day';
        }
    }
  } else {
    // Default based on timeframe
    if (timeframe === '1D') {
      multiplier = 5;
      timespan = 'minute';
    } else if (timeframe === '5D') {
      multiplier = 30;
      timespan = 'minute';
    } else if (timeframe === '1W') {
      multiplier = 30;
      timespan = 'minute';
    } else if (timeframe === '1M') {
      multiplier = 1;
      timespan = 'hour';
    } else if (timeframe === 'YTD') {
      multiplier = 1;
      timespan = 'day';
    } else {
      multiplier = 1;
      timespan = 'day';
    }
  }

  // Calculate start date in ET format
  let etStartDate: string;
  if (timeframe === '1D') {
    if (useToday) {
      // For 1D when market is open, use today's date
      etStartDate = etDateFormatted;
    } else {
      // For 1D when market is closed, use most recent market day
      etStartDate = getMostRecentMarketDay();
    }
  } else {
    // For other timeframes, calculate the start date based on the ET date
    const etStartObj = new Date(etNow);

    switch (timeframe) {
      case '5D':
        etStartObj.setDate(etStartObj.getDate() - 7); // Approximately 5 trading days
        break;
      case '1W':
        etStartObj.setDate(etStartObj.getDate() - 7);
        break;
      case '1M':
        etStartObj.setMonth(etStartObj.getMonth() - 1);
        break;
      case '3M':
        etStartObj.setMonth(etStartObj.getMonth() - 3);
        break;
      case '6M':
        etStartObj.setMonth(etStartObj.getMonth() - 6);
        break;
      case 'YTD':
        etStartObj.setMonth(0); // January
        etStartObj.setDate(1);  // 1st day of the month
        break;
      case '1Y':
        etStartObj.setFullYear(etStartObj.getFullYear() - 1);
        break;
      case '5Y':
        etStartObj.setFullYear(etStartObj.getFullYear() - 5);
        break;
    }

    // Format the start date
    const startYear = etStartObj.getFullYear();
    const startMonth = String(etStartObj.getMonth() + 1).padStart(2, '0');
    const startDay = String(etStartObj.getDate()).padStart(2, '0');
    etStartDate = `${startYear}-${startMonth}-${startDay}`;
  }

  // Determine the end date based on market status
  let etEndDate = etDateFormatted;
  if (timeframe === '1D' && !useToday) {
    etEndDate = getMostRecentMarketDay();
  }

  return {
    startDate: etStartDate,
    endDate: etEndDate,
    multiplier,
    timespan
  };
}

// Define chart data response interface
interface ChartDataResponse {
  symbol: string;
  data: Array<{
    timestamp: number;
    open: number;
    high: number;
    low: number;
    close: number;
    volume: number;
  }>;
  config: {
    timeframe: string;
    showVolume: boolean;
  };
  previousClose: number | null;
  currentPrice: number | null;
  error?: string;
}

// Function to get fallback chart data when regular data is not available
async function getFallbackChartData(formattedSymbol: string, timeframe: string): Promise<ChartDataResponse> {
  console.log(`Chart-data: Getting fallback data for ${formattedSymbol} in timeframe ${timeframe}`);

  // Get current date in US Eastern time
  const now = new Date();
  const options: Intl.DateTimeFormatOptions = {
    timeZone: 'America/New_York',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  };
  const etDateStr = now.toLocaleDateString('en-US', options);
  const [month, day, year] = etDateStr.split('/');

  // Create a date object for today in ET
  const today = new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}T12:00:00`);

  // Try multiple previous days (up to 10 days back)
  // This helps handle weekends and holidays
  const maxDaysToTry = 10;

  for (let daysBack = 1; daysBack <= maxDaysToTry; daysBack++) {
    // Calculate the date to try
    const tryDate = new Date(today);
    tryDate.setDate(today.getDate() - daysBack);

    // Skip weekends
    const dayOfWeek = tryDate.getDay();
    if (dayOfWeek === 0 || dayOfWeek === 6) { // 0 = Sunday, 6 = Saturday
      console.log(`Chart-data: Skipping weekend day ${tryDate.toISOString().split('T')[0]}`);
      continue;
    }

    // Format the date
    const tryYear = tryDate.getFullYear();
    const tryMonth = String(tryDate.getMonth() + 1).padStart(2, '0');
    const tryDay = String(tryDate.getDate()).padStart(2, '0');
    const dateStr = `${tryYear}-${tryMonth}-${tryDay}`;

    console.log(`Chart-data: Trying to get data for ${formattedSymbol} from ${dateStr} (${daysBack} days back)`);

    // Try to get minute data for this date
    const minuteUrl = `https://api.polygon.io/v2/aggs/ticker/${formattedSymbol}/range/5/minute/${dateStr}/${dateStr}?adjusted=true&sort=asc&limit=50000&apiKey=${POLYGON_API_KEY}`;

    try {
      const minuteResponse = await fetch(minuteUrl);

      if (minuteResponse.ok) {
        const minuteData = await minuteResponse.json();

        if (minuteData.results && minuteData.results.length > 0) {
          console.log(`Chart-data: Found ${minuteData.results.length} minute data points for ${formattedSymbol} from ${dateStr}`);

          // Transform the data
          const transformedData = minuteData.results.map((item: any) => ({
            timestamp: item.t,
            open: item.o,
            high: item.h,
            low: item.l,
            close: item.c,
            volume: item.v
          }));

          // Get the closing price as the previous close
          const previousClose = transformedData[transformedData.length - 1].close;

          return {
            symbol: formattedSymbol,
            data: transformedData,
            config: {
              timeframe,
              showVolume: true
            },
            previousClose,
            currentPrice: previousClose
          };
        }
      }

      // If minute data didn't work, try daily data
      const dailyUrl = `https://api.polygon.io/v2/aggs/ticker/${formattedSymbol}/range/1/day/${dateStr}/${dateStr}?adjusted=true&apiKey=${POLYGON_API_KEY}`;
      const dailyResponse = await fetch(dailyUrl);

      if (dailyResponse.ok) {
        const dailyData = await dailyResponse.json();

        if (dailyData.results && dailyData.results.length > 0) {
          console.log(`Chart-data: Found daily data for ${formattedSymbol} from ${dateStr}`);

          const dailyBar = dailyData.results[0];

          // Create synthetic data points for the day
          const marketOpen = new Date(tryDate);
          marketOpen.setHours(9, 30, 0, 0); // 9:30 AM

          const marketClose = new Date(tryDate);
          marketClose.setHours(16, 0, 0, 0); // 4:00 PM

          // Create synthetic data with just two points - market open and close
          const syntheticData = [
            {
              timestamp: marketOpen.getTime(),
              open: dailyBar.o,
              high: dailyBar.h,
              low: dailyBar.l,
              close: dailyBar.o, // Use open price for the first bar
              volume: Math.floor(dailyBar.v / 2) // Split volume between the two bars
            },
            {
              timestamp: marketClose.getTime(),
              open: dailyBar.o,
              high: dailyBar.h,
              low: dailyBar.l,
              close: dailyBar.c,
              volume: Math.floor(dailyBar.v / 2)
            }
          ];

          return {
            symbol: formattedSymbol,
            data: syntheticData,
            config: {
              timeframe,
              showVolume: true
            },
            previousClose: dailyBar.c, // Use the close as previous close
            currentPrice: dailyBar.c
          };
        }
      }
    } catch (error) {
      console.error(`Chart-data: Error fetching data for ${formattedSymbol} from ${dateStr}:`, error);
      // Continue to the next date
    }
  }

  // If we've tried all days and still have no data, try one more approach:
  // Get the most recent daily bar from the previous close endpoint
  try {
    console.log(`Chart-data: Trying to get previous close data for ${formattedSymbol}`);
    const prevCloseUrl = `https://api.polygon.io/v2/aggs/ticker/${formattedSymbol}/prev?adjusted=true&apiKey=${POLYGON_API_KEY}`;
    const prevCloseResponse = await fetch(prevCloseUrl);

    if (prevCloseResponse.ok) {
      const prevCloseData = await prevCloseResponse.json();

      if (prevCloseData.results && prevCloseData.results.length > 0) {
        console.log(`Chart-data: Found previous close data for ${formattedSymbol}`);

        const prevClose = prevCloseData.results[0];
        const prevCloseDate = new Date(prevClose.t);

        // Create synthetic data points for the day
        const marketOpen = new Date(prevCloseDate);
        marketOpen.setHours(9, 30, 0, 0); // 9:30 AM

        const marketClose = new Date(prevCloseDate);
        marketClose.setHours(16, 0, 0, 0); // 4:00 PM

        // Create synthetic data with just two points - market open and close
        const syntheticData = [
          {
            timestamp: marketOpen.getTime(),
            open: prevClose.o,
            high: prevClose.h,
            low: prevClose.l,
            close: prevClose.o, // Use open price for the first bar
            volume: Math.floor(prevClose.v / 2) // Split volume between the two bars
          },
          {
            timestamp: marketClose.getTime(),
            open: prevClose.o,
            high: prevClose.h,
            low: prevClose.l,
            close: prevClose.c,
            volume: Math.floor(prevClose.v / 2)
          }
        ];

        return {
          symbol: formattedSymbol,
          data: syntheticData,
          config: {
            timeframe,
            showVolume: true
          },
          previousClose: prevClose.c,
          currentPrice: prevClose.c
        };
      }
    }
  } catch (error) {
    console.error(`Chart-data: Error fetching previous close data for ${formattedSymbol}:`, error);
  }

  // If all else fails, return empty data with an error message
  return {
    symbol: formattedSymbol,
    data: [],
    config: {
      timeframe,
      showVolume: true
    },
    previousClose: null,
    currentPrice: null,
    error: `No data available for ${formattedSymbol}. This may be because the market is closed or the symbol is not actively traded.`
  };
}

// Function to fetch chart data from Polygon
async function fetchChartData(params: { symbol: string, timeframe: string, interval?: string }): Promise<ChartDataResponse> {
  const { symbol, timeframe, interval } = params;
  let formattedSymbol = '';

  try {
    // Format the symbol for Polygon API
    formattedSymbol = await formatPolygonSymbol(symbol);

    // Get interval parameters
    const { startDate, endDate, multiplier, timespan } = getIntervalParams(timeframe, interval);

    const url = `https://api.polygon.io/v2/aggs/ticker/${formattedSymbol}/range/${multiplier}/${timespan}/${startDate}/${endDate}?adjusted=true&sort=asc&limit=50000&apiKey=${POLYGON_API_KEY}`;

    const response = await fetch(url);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Chart-data: Polygon API error for ${formattedSymbol}: ${response.status} - ${errorText}`);
      throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
    }

    const data = await response.json();

    if (!data.results || !Array.isArray(data.results)) {
      console.error(`Chart-data: Invalid data format for ${formattedSymbol}:`, JSON.stringify(data));

      // For 1D timeframe, try to get fallback data instead of throwing an error
      if (timeframe === '1D') {
        console.log(`Chart-data: Attempting to get fallback data for ${formattedSymbol} 1D timeframe`);
        return await getFallbackChartData(formattedSymbol, timeframe);
      } else {
        throw new Error(`Invalid data format received from API: ${JSON.stringify(data)}`);
      }
    }

    if (data.results.length === 0) {
      console.log(`Chart-data: No data points found for ${formattedSymbol} in timeframe ${timeframe}`);

      // For 1D timeframe, try to get data from the previous day
      if (timeframe === '1D') {
        console.log(`Chart-data: Trying to get previous day data for ${formattedSymbol}`);

        // Get current date in US Eastern time
        const now = new Date();
        const options: Intl.DateTimeFormatOptions = {
          timeZone: 'America/New_York',
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        };
        const etDateStr = now.toLocaleDateString('en-US', options);
        const [month, day, year] = etDateStr.split('/');

        // Create a date object for yesterday
        const yesterday = new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}T12:00:00`);
        yesterday.setDate(yesterday.getDate() - 1);

        // Format yesterday's date
        const yesterdayYear = yesterday.getFullYear();
        const yesterdayMonth = String(yesterday.getMonth() + 1).padStart(2, '0');
        const yesterdayDay = String(yesterday.getDate()).padStart(2, '0');
        const yesterdayStr = `${yesterdayYear}-${yesterdayMonth}-${yesterdayDay}`;

        // Try to get data for yesterday
        const yesterdayUrl = `https://api.polygon.io/v2/aggs/ticker/${formattedSymbol}/range/5/minute/${yesterdayStr}/${yesterdayStr}?adjusted=true&sort=asc&limit=50000&apiKey=${POLYGON_API_KEY}`;
        console.log(`Chart-data: Fetching previous day data from ${yesterdayStr} for ${formattedSymbol}`);

        try {
          const yesterdayResponse = await fetch(yesterdayUrl);

          if (yesterdayResponse.ok) {
            const yesterdayData = await yesterdayResponse.json();

            if (yesterdayData.results && yesterdayData.results.length > 0) {
              console.log(`Chart-data: Found ${yesterdayData.results.length} data points for ${formattedSymbol} from previous day`);

              // Transform the data
              const transformedData = yesterdayData.results.map((item: any) => ({
                timestamp: item.t,
                open: item.o,
                high: item.h,
                low: item.l,
                close: item.c,
                volume: item.v
              }));

              // Get the closing price as the previous close
              const previousClose = transformedData[transformedData.length - 1].close;

              return {
                symbol: formattedSymbol,
                data: transformedData,
                config: {
                  timeframe,
                  showVolume: true
                },
                previousClose,
                currentPrice: previousClose
              };
            }
          }
        } catch (error) {
          console.error(`Chart-data: Error fetching previous day data for ${formattedSymbol}:`, error);
        }
      }

      // If we get here, we couldn't get data for the current day or the previous day
      // Return empty data with a more informative message
      return {
        symbol: formattedSymbol,
        data: [],
        config: {
          timeframe,
          showVolume: true
        },
        previousClose: null,
        currentPrice: null,
        error: `No data available for ${symbol} in timeframe ${timeframe}. This may be because the market is closed or the symbol is not actively traded.`
      };
    }

    // Transform the data into the required format
    const transformedData = data.results.map((item: any) => ({
      timestamp: item.t,
      open: item.o,
      high: item.h,
      low: item.l,
      close: item.c,
      volume: item.v
    }));

    // Calculate previous close (for 1D timeframe)
    let previousClose = null;
    if (timeframe === '1D' && transformedData.length > 0) {
      // For 1D, try to get the previous day's close using ET timezone
      // Get current date in US Eastern time (market timezone)
      const now = new Date();

      // Convert to US Eastern time (ET) string representation
      const options: Intl.DateTimeFormatOptions = {
        timeZone: 'America/New_York',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      };
      const etDateStr = now.toLocaleDateString('en-US', options);

      // Parse the ET date string back to a date object
      const [month, day, year] = etDateStr.split('/');

      // Create a date object for today in ET
      const etToday = new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}T12:00:00`);

      // Calculate yesterday in ET
      const etYesterday = new Date(etToday);
      etYesterday.setDate(etYesterday.getDate() - 1);

      // Format yesterday's date
      const yesterdayYear = etYesterday.getFullYear();
      const yesterdayMonth = String(etYesterday.getMonth() + 1).padStart(2, '0');
      const yesterdayDay = String(etYesterday.getDate()).padStart(2, '0');
      const yesterdayStr = `${yesterdayYear}-${yesterdayMonth}-${yesterdayDay}`;

      const prevDayUrl = `https://api.polygon.io/v2/aggs/ticker/${formattedSymbol}/range/1/day/${yesterdayStr}/${yesterdayStr}?adjusted=true&apiKey=${POLYGON_API_KEY}`;

      try {
        const prevDayResponse = await fetch(prevDayUrl);
        if (prevDayResponse.ok) {
          const prevDayData = await prevDayResponse.json();
          if (prevDayData.results && prevDayData.results.length > 0) {
            previousClose = prevDayData.results[0].c;
          }
        }
      } catch (error) {
        console.error('Error fetching previous close:', error);
      }
    }

    // Get current price (latest close)
    const currentPrice = transformedData.length > 0 ? transformedData[transformedData.length - 1].close : null;

    return {
      symbol: formattedSymbol,
      data: transformedData,
      config: {
        timeframe,
        showVolume: true
      },
      previousClose,
      currentPrice
    };
  } catch (error: any) {
    console.error(`Chart-data: Error fetching chart data for ${symbol}:`, error);
    // Return empty data instead of throwing
    return {
      symbol: formattedSymbol || symbol,
      data: [],
      config: {
        timeframe,
        showVolume: true
      },
      previousClose: null,
      currentPrice: null,
      error: error.message
    };
  }
}

// Define request type for Deno
interface RequestWithJson extends Request {
  json(): Promise<any>;
}

// Main serve function
serve(async (req: RequestWithJson) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }

  try {
    // Parse the request body
    let body: { symbol?: string; timeframe?: string; interval?: string };
    try {
      body = await req.json();
    } catch (e) {
      return new Response(JSON.stringify({ error: 'Invalid request body' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    const { symbol, timeframe = '1D', interval } = body;

    // Validate required parameters
    if (!symbol) {
      return new Response(JSON.stringify({ error: 'Symbol is required' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Get the authenticated user's ID
    const authHeader = req.headers.get('Authorization');

    if (!authHeader) {
      return new Response(JSON.stringify({ error: 'No authorization header' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Get the authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''));

    if (userError || !user) {
      return new Response(JSON.stringify({ error: 'Unauthorized', details: userError?.message }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Fetch chart data
    const data = await fetchChartData({ symbol, timeframe, interval });

    // Return the response
    return new Response(JSON.stringify(data), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error: any) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
