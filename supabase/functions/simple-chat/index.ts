import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ChatRequest {
  messages: Array<{
    role: string;
    content: string;
  }>;
  selectedAgent?: string;
}

interface AgentExecutionResult {
  agentId: string;
  agentName: string;
  symbol: string;
  result: {
    signal: 'bullish' | 'bearish' | 'neutral';
    confidence: number;
    reasoning: string;
    metrics: Record<string, any>;
    executionPath: string[];
    executionTime: number;
    timestamp: string;
  };
  executionTime: number;
  error?: string;
}

// Extract symbols from user message using Gemini
async function extractSymbols(text: string): Promise<string[]> {
  if (!text) return [];

  try {
    const apiKey = Deno.env.get('GEMINI_API_KEY');
    if (!apiKey) return [];

    const prompt = `Extract stock symbols from this text. Return only valid stock symbols as a JSON array.

Text: "${text}"

Rules:
1. Return ONLY valid stock symbols
2. Maximum 3 symbols
3. No ETFs, futures, or derivatives
4. Format: ["AAPL", "MSFT"]

Response (JSON array only):`;

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        contents: [{ parts: [{ text: prompt }] }],
        generationConfig: { temperature: 0.1, maxOutputTokens: 100 }
      })
    });

    if (!response.ok) return [];

    const data = await response.json();
    const extractedText = data.candidates?.[0]?.content?.parts?.[0]?.text?.trim() || '';

    const jsonMatch = extractedText.match(/\[.*\]/s);
    if (!jsonMatch) return [];

    const symbols = JSON.parse(jsonMatch[0]);
    return Array.isArray(symbols) ? symbols.filter(s => typeof s === 'string') : [];
  } catch (error) {
    console.error('Symbol extraction error:', error);
    return [];
  }
}

// Execute selected agent against symbols
async function executeAgent(agentId: string, symbols: string[]): Promise<AgentExecutionResult[]> {
  const supabase = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  );

  // Get agent configuration
  const { data: agent, error: agentError } = await supabase
    .from('agents')
    .select('*')
    .eq('id', agentId)
    .single();

  if (agentError || !agent) {
    console.error('Agent not found:', agentError);
    return [];
  }

  const results: AgentExecutionResult[] = [];

  for (const symbol of symbols) {
    try {
      const startTime = Date.now();

      // Call agent-runner edge function
      const { data, error } = await supabase.functions.invoke('agent-runner', {
        body: {
          agentId: agent.id,
          symbol: symbol,
          configuration: agent.configuration,
          saveRun: false
        }
      });

      const executionTime = Date.now() - startTime;

      if (error) {
        throw new Error(error.message || 'Agent execution failed');
      }



      results.push({
        agentId: agent.id,
        agentName: agent.name,
        symbol,
        result: data,
        executionTime,
        error: undefined
      });

    } catch (error) {
      results.push({
        agentId: agent.id,
        agentName: agent.name,
        symbol,
        result: {
          signal: 'neutral' as const,
          confidence: 0,
          reasoning: `Execution failed: ${error.message}`,
          metrics: {},
          executionPath: [],
          executionTime: 0,
          timestamp: new Date().toISOString()
        },
        executionTime: 0,
        error: error.message
      });
    }
  }

  return results;
}

// Generate response using Gemini - pass through agent analysis directly
async function generateResponse(userMessage: string, agentResults: AgentExecutionResult[]): Promise<string> {
  const apiKey = Deno.env.get('GEMINI_API_KEY');
  if (!apiKey) throw new Error('Gemini API key not configured');

  let prompt: string;

  if (agentResults.length > 0) {
    // Get successful agent results with actual reasoning content
    const successfulResults = agentResults.filter(r => !r.error && r.result?.reasoning && r.result.reasoning.trim().length > 0);

    if (successfulResults.length > 0) {
      const agentAnalysis = successfulResults
        .map(r => r.result.reasoning.trim())
        .join('\n\n');



      prompt = `You are an experienced financial advisor and trading expert. A user has asked you: "${userMessage}"

You have access to analysis from a sophisticated trading agent that has examined the relevant stocks. Based on this agent's findings, provide a natural, conversational response as if you personally analyzed the stocks and came to these conclusions.

Agent's findings for reference:
${agentAnalysis}

Respond naturally as a knowledgeable financial advisor would, incorporating the agent's insights as your own analysis. Be conversational, professional, and helpful. Don't mention the agent or reference "analysis provided" - speak as if this is your own expert opinion based on your analysis.`;
    } else {
      // Agent executed but no meaningful analysis - fall back to general response
      prompt = `You are a helpful AI assistant. Answer this user's question about financial markets: "${userMessage}"`;
    }
  } else {
    // No agent results - general response
    prompt = `You are a helpful AI assistant. Answer this user's question: "${userMessage}"`;
  }

  const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      contents: [{ parts: [{ text: prompt }] }],
      generationConfig: { temperature: 0.3, maxOutputTokens: 2000 }
    })
  });

  if (!response.ok) {
    throw new Error('Failed to generate response');
  }

  const data = await response.json();
  return data.candidates?.[0]?.content?.parts?.[0]?.text || 'Unable to generate response';
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { messages, selectedAgent }: ChatRequest = await req.json();

    const lastMessage = messages?.[messages.length - 1];
    const userMessage = lastMessage?.content || '';

    // Extract symbols from user message
    const symbols = await extractSymbols(userMessage);

    // Execute agent if selected and symbols found
    let agentResults: AgentExecutionResult[] = [];
    if (selectedAgent && symbols.length > 0) {
      agentResults = await executeAgent(selectedAgent, symbols);
    }

    // Generate response
    const responseText = await generateResponse(userMessage, agentResults);

    const result = {
      response: responseText,
      symbols,
      agentResults,
      hasAgentAnalysis: agentResults.length > 0
    };

    return new Response(
      JSON.stringify(result),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );

  } catch (error) {
    console.error('Simple-chat error:', error);
    return new Response(
      JSON.stringify({
        error: 'Failed to process chat request',
        details: error.message,
        stack: error.stack
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});
