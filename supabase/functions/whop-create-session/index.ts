import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase admin client
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const { userId, whopUserId } = await req.json()

    if (!userId || !whopUserId) {
      return new Response(
        JSON.stringify({ error: 'Missing userId or whopUserId' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    console.log('🔐 Creating session for Whop user:', {
      userId,
      whopUserId
    })

    // Verify the user exists and is a Whop user
    const { data: user, error: userError } = await supabaseAdmin.auth.admin.getUserById(userId)
    
    if (userError || !user) {
      console.error('❌ User not found:', userError)
      return new Response(
        JSON.stringify({ error: 'User not found' }),
        { 
          status: 404, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Verify this is a Whop user
    if (!user.user.user_metadata?.isWhopUser || user.user.user_metadata?.whop_user_id !== whopUserId) {
      console.error('❌ User is not a valid Whop user')
      return new Response(
        JSON.stringify({ error: 'Invalid Whop user' }),
        { 
          status: 403, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Create a session for the user using admin API
    // We'll use the createSession method which is more direct
    try {
      console.log('🔐 Creating session using admin.createSession...')

      // Use the admin API to create a session directly
      const { data: sessionData, error: sessionError } = await supabaseAdmin.auth.admin.createSession({
        userId: userId
      })

      if (sessionError || !sessionData) {
        console.error('❌ Error creating session:', sessionError)
        return new Response(
          JSON.stringify({
            error: 'Failed to create session',
            details: sessionError?.message || 'Unknown error'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )

        // Extract tokens from magic link
        const url = new URL(linkData.properties.action_link)
        const accessToken = url.searchParams.get('access_token')
        const refreshToken = url.searchParams.get('refresh_token')

        if (!accessToken || !refreshToken) {
          console.error('❌ Failed to extract tokens from magic link')
          return new Response(
            JSON.stringify({ error: 'Failed to extract session tokens' }),
            {
              status: 500,
              headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            }
          )
        }

        console.log('✅ Session tokens created via fallback method')
        return new Response(
          JSON.stringify({
            success: true,
            session: {
              access_token: accessToken,
              refresh_token: refreshToken,
              user: user.user
            }
          }),
          {
            status: 200,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      console.log('✅ Session created successfully via admin.createSession')
      return new Response(
        JSON.stringify({
          success: true,
          session: {
            access_token: sessionData.session.access_token,
            refresh_token: sessionData.session.refresh_token,
            user: sessionData.user
          }
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )

    } catch (sessionCreationError) {
      console.error('❌ Error in session creation process:', sessionCreationError)
      return new Response(
        JSON.stringify({
          error: 'Session creation failed',
          details: sessionCreationError.message
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

  } catch (error) {
    console.error('❌ Error in whop-create-session:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error.message 
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})
