import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Allow-Methods': '*'
};

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

interface Competition {
  id?: string;
  name: string;
  description?: string;
  starting_balance: number;
  max_participants?: number;
  entry_fee?: number;
  prize_pool?: number;
  rules?: any;
  allowed_securities?: string[];
  position_limits?: any;
  registration_start?: string;
  registration_end?: string;
  competition_start: string;
  competition_end: string;
}

interface JoinCompetitionRequest {
  competition_id: string;
}

// Create a new competition
async function createCompetition(userId: string, competitionData: Competition) {
  // Create the competition using RPC function to avoid schema cache issues
  const { data, error } = await supabase.rpc('create_competition', {
    p_name: competitionData.name,
    p_creator_id: userId,
    p_description: competitionData.description,
    p_starting_balance: competitionData.starting_balance || 100000.00,
    p_max_participants: competitionData.max_participants,
    p_entry_fee: competitionData.entry_fee || 0.00,
    p_prize_pool: competitionData.prize_pool || 0.00,
    p_rules: competitionData.rules,
    p_allowed_securities: competitionData.allowed_securities,
    p_position_limits: competitionData.position_limits,
    p_registration_start: competitionData.registration_start,
    p_registration_end: competitionData.registration_end,
    p_competition_start: competitionData.competition_start,
    p_competition_end: competitionData.competition_end,
    p_status: 'open'
  });

  if (error) {
    console.error('❌ Error creating competition:', error);
    throw new Error(`Competition creation failed: ${error.message}`);
  }

  if (!data) {
    throw new Error('Competition created but no ID returned');
  }

  // The RPC function returns just the UUID, so we need to fetch the full record
  const competitionId = data;
  const { data: fullCompetition, error: fetchError } = await supabase
    .from('paper_trading_competitions')
    .select('*')
    .eq('id', competitionId)
    .single();

  if (fetchError) {
    console.error('❌ Error fetching created competition:', fetchError);
    throw new Error(`Failed to fetch created competition: ${fetchError.message}`);
  }

  return fullCompetition;
}

// Get all available competitions
async function getCompetitions(userId?: string) {
  // First get the competitions
  const { data: competitions, error } = await supabase
    .from('paper_trading_competitions')
    .select('*')
    .in('status', ['draft', 'open', 'active', 'completed']);

  if (error) throw error;

  // Then get participant counts separately
  const competitionsWithCounts = await Promise.all(
    competitions.map(async (competition) => {
      const { count } = await supabase
        .from('competition_participants')
        .select('*', { count: 'exact', head: true })
        .eq('competition_id', competition.id);

      return {
        ...competition,
        participant_count: count || 0
      };
    })
  );

  return competitionsWithCounts;
}

// Get competition details with participants
async function getCompetitionDetails(competitionId: string) {
  const { data: competition, error: compError } = await supabase
    .from('paper_trading_competitions')
    .select('*')
    .eq('id', competitionId)
    .single();

  if (compError) throw compError;

  const { data: participants, error: partError } = await supabase
    .from('competition_participants')
    .select(`
      *,
      user:user_id(email, raw_user_meta_data)
    `)
    .eq('competition_id', competitionId)
    .eq('is_active', true);

  if (partError) throw partError;

  const { data: leaderboard, error: leaderError } = await supabase
    .from('competition_leaderboards')
    .select('*')
    .eq('competition_id', competitionId)
    .order('current_rank', { ascending: true });

  if (leaderError) throw leaderError;

  return {
    competition,
    participants,
    leaderboard
  };
}

// Join a competition
async function joinCompetition(userId: string, request: JoinCompetitionRequest) {
  // Check if competition exists and is open for registration
  const { data: competition, error: compError } = await supabase
    .from('paper_trading_competitions')
    .select('*')
    .eq('id', request.competition_id)
    .single();

  if (compError) throw compError;
  if (!competition) throw new Error('Competition not found');
  if (competition.status !== 'open') throw new Error('Competition is not open for registration');

  // Check if user already joined
  const { data: existingParticipant } = await supabase
    .from('competition_participants')
    .select('id')
    .eq('competition_id', request.competition_id)
    .eq('user_id', userId)
    .single();

  if (existingParticipant) throw new Error('Already joined this competition');

  // Check participant limit
  if (competition.max_participants) {
    const { count } = await supabase
      .from('competition_participants')
      .select('*', { count: 'exact', head: true })
      .eq('competition_id', request.competition_id)
      .eq('is_active', true);

    if (count && count >= competition.max_participants) {
      throw new Error('Competition is full');
    }
  }

  // Get user's default paper trading account
  const { data: account, error: accountError } = await supabase
    .from('paper_trading_accounts')
    .select('id')
    .eq('user_id', userId)
    .eq('is_active', true)
    .single();

  if (accountError || !account) throw new Error('No active paper trading account found');

  // Create competition-specific account with starting balance
  const { data: competitionAccount, error: createAccountError } = await supabase
    .from('paper_trading_accounts')
    .insert({
      user_id: userId,
      account_name: `Competition: ${competition.name}`,
      starting_balance: competition.starting_balance,
      current_balance: competition.starting_balance,
      available_balance: competition.starting_balance,
      portfolio_value: 0,
      total_value: competition.starting_balance,
      is_active: true
    })
    .select()
    .single();

  if (createAccountError) throw createAccountError;

  // Join the competition
  const { data: participant, error: joinError } = await supabase
    .from('competition_participants')
    .insert({
      competition_id: request.competition_id,
      user_id: userId,
      account_id: competitionAccount.id
    })
    .select()
    .single();

  if (joinError) throw joinError;

  return participant;
}

// Update leaderboard for a competition
async function updateLeaderboard(competitionId: string) {
  const { error } = await supabase.rpc('update_competition_leaderboard', {
    p_competition_id: competitionId
  });

  if (error) throw error;
  return { success: true };
}

// Get user's competitions
async function getUserCompetitions(userId: string) {
  const { data, error } = await supabase
    .from('competition_participants')
    .select(`
      *,
      competition:competition_id(*)
    `)
    .eq('user_id', userId)
    .eq('is_active', true);

  if (error) throw error;
  return data;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get user from JWT token
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('No authorization header')
    }

    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      throw new Error('Invalid token')
    }

    const { action, ...params } = await req.json()

    let result
    switch (action) {
      case 'create_competition':
        result = await createCompetition(user.id, params as Competition)
        break

      case 'get_competitions':
        result = await getCompetitions(user.id)
        break

      case 'get_competition_details':
        result = await getCompetitionDetails(params.competition_id)
        break

      case 'join_competition':
        result = await joinCompetition(user.id, params as JoinCompetitionRequest)
        break

      case 'update_leaderboard':
        result = await updateLeaderboard(params.competition_id)
        break

      case 'get_user_competitions':
        result = await getUserCompetitions(user.id)
        break

      default:
        throw new Error(`Unknown action: ${action}`)
    }

    return new Response(
      JSON.stringify({ success: true, data: result }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )

  } catch (error) {
    console.error('Competition management error:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message || 'Internal server error' 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400
      }
    )
  }
})
