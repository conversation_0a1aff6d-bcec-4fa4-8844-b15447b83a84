// Discover Agents Edge Function
// This function handles agent discovery with search and filtering

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
};

// Handle OPTIONS requests for CORS
function handleOptions() {
  return new Response(null, {
    status: 204,
    headers: corsHeaders
  });
}

interface DiscoverAgentsRequest {
  search?: string;
  category?: string;
  tags?: string[];
  sortBy?: 'newest' | 'popular' | 'rating' | 'downloads';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
  featured?: boolean;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return handleOptions();
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Parse request parameters
    let requestData: DiscoverAgentsRequest = {};
    
    if (req.method === 'GET') {
      const url = new URL(req.url);
      requestData = {
        search: url.searchParams.get('search') || undefined,
        category: url.searchParams.get('category') || undefined,
        tags: url.searchParams.get('tags')?.split(',') || undefined,
        sortBy: (url.searchParams.get('sortBy') as any) || 'newest',
        sortOrder: (url.searchParams.get('sortOrder') as any) || 'desc',
        limit: parseInt(url.searchParams.get('limit') || '20'),
        offset: parseInt(url.searchParams.get('offset') || '0'),
        featured: url.searchParams.get('featured') === 'true'
      };
    } else if (req.method === 'POST') {
      requestData = await req.json();
    }

    console.log('Discovering agents with filters:', requestData);

    // STRICT ISOLATION: Build the query for FREE agents only
    // Must have: is_active = true in published_agents
    // Must NOT have: is_for_sale = true in agents table (to prevent cross-contamination)
    let query = supabase
      .from('published_agents')
      .select(`
        *,
        agents!inner (
          id,
          user_id,
          is_for_sale,
          is_public
        )
      `)
      .eq('is_active', true);

    // STRICT ISOLATION: Exclude agents that are for sale with price > 0
    if (requestData.strict_free_only) {
      console.log('🔒 Enforcing strict free-only isolation');
      // Exclude agents that are both for sale AND have a price > 0
      // This ensures paid agents never appear in free marketplace
      // Note: We'll filter these in post-processing since complex AND conditions are tricky in Supabase
      query = query.eq('agents.is_public', true); // Ensure only public agents
    }

    // Apply filters
    if (requestData.search) {
      query = query.or(`name.ilike.%${requestData.search}%,description.ilike.%${requestData.search}%`);
    }

    if (requestData.category) {
      query = query.eq('category', requestData.category);
    }

    if (requestData.tags && requestData.tags.length > 0) {
      query = query.overlaps('tags', requestData.tags);
    }

    if (requestData.featured) {
      query = query.eq('is_featured', true);
    }

    // Apply sorting
    switch (requestData.sortBy) {
      case 'popular':
        query = query.order('download_count', { ascending: requestData.sortOrder === 'asc' });
        break;
      case 'rating':
        query = query.order('average_rating', { ascending: requestData.sortOrder === 'asc' });
        break;
      case 'downloads':
        query = query.order('download_count', { ascending: requestData.sortOrder === 'asc' });
        break;
      case 'newest':
      default:
        query = query.order('created_at', { ascending: requestData.sortOrder === 'asc' });
        break;
    }

    // Apply pagination
    const limit = Math.min(requestData.limit || 20, 100); // Max 100 items per request
    const offset = requestData.offset || 0;
    query = query.range(offset, offset + limit - 1);

    // Execute query
    const { data: rawAgents, error: agentsError } = await query;

    if (agentsError) {
      throw new Error(`Failed to fetch agents: ${agentsError.message}`);
    }

    // CRITICAL FIX: Apply strict isolation filtering after query
    let agents = rawAgents || [];
    if (requestData.strict_free_only) {
      console.log(`🔒 Pre-filter: ${agents.length} agents`);
      // Remove any agents that are for sale with price > 0
      agents = agents.filter(agent => {
        const agentData = agent.agents;
        const isForSale = agentData?.is_for_sale === true;
        const hasPrice = agentData?.price && agentData.price > 0;
        const shouldExclude = isForSale && hasPrice;

        if (shouldExclude) {
          console.log(`🚫 Excluding paid agent from free marketplace: ${agent.name} (price: $${agentData.price})`);
        }

        return !shouldExclude;
      });
      console.log(`🔒 Post-filter: ${agents.length} agents (strict isolation applied)`);
    }

    // Get total count for pagination
    const { count, error: countError } = await supabase
      .from('published_agents')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true);

    if (countError) {
      console.warn('Failed to get total count:', countError.message);
    }

    // Get categories for filtering
    const { data: categories, error: categoriesError } = await supabase
      .from('agent_categories')
      .select('*')
      .eq('is_active', true)
      .order('sort_order');

    if (categoriesError) {
      console.warn('Failed to fetch categories:', categoriesError.message);
    }

    // Get unique publisher IDs to fetch profile data
    const publisherIds = [...new Set((agents || []).map(agent => agent.publisher_id))];

    // Fetch profile data for all publishers
    const { data: profiles } = await supabase
      .from('profiles')
      .select('id, full_name')
      .in('id', publisherIds);

    // Create a map of publisher profiles for quick lookup
    const profileMap = new Map(profiles?.map(profile => [profile.id, profile]) || []);

    // Format response
    const formattedAgents = agents?.map(agent => ({
      id: agent.id,
      agentId: agent.agent_id,
      name: agent.name,
      description: agent.description,
      category: agent.category,
      tags: agent.tags,
      publisherName: profileMap.get(agent.publisher_id)?.full_name || 'Anonymous',
      publisherId: agent.publisher_id,
      downloadCount: agent.download_count,
      averageRating: parseFloat(agent.average_rating) || 0,
      totalReviews: agent.total_reviews,
      isFeatured: agent.is_featured,
      createdAt: agent.created_at,
      updatedAt: agent.updated_at,
      configuration: null // Configuration not needed for discovery
    })) || [];

    console.log(`Found ${formattedAgents.length} agents`);

    // Return success response
    return new Response(
      JSON.stringify({
        success: true,
        agents: formattedAgents,
        categories: categories || [],
        pagination: {
          total: count || 0,
          limit: limit,
          offset: offset,
          hasMore: (count || 0) > offset + limit
        }
      }),
      {
        status: 200,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    );

  } catch (error) {
    console.error('Error in discover-agents function:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'An unexpected error occurred'
      }),
      {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    );
  }
});
