import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase admin client
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const { whopUserId } = await req.json()

    if (!whopUserId) {
      return new Response(
        JSON.stringify({ error: 'Missing whopUserId' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    console.log('🔍 Looking up Whop user in Supabase:', { whopUserId })

    // Get all users and find the one with matching whop_user_id
    const { data: users, error: listError } = await supabaseAdmin.auth.admin.listUsers()
    
    if (listError) {
      console.error('❌ Error listing users:', listError)
      return new Response(
        JSON.stringify({ error: 'Failed to lookup users' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Find user by whop_user_id in user_metadata
    const whopUser = users.users.find(user => 
      user.user_metadata?.whop_user_id === whopUserId &&
      user.user_metadata?.isWhopUser === true
    )

    if (!whopUser) {
      console.log('❌ Whop user not found in Supabase')
      return new Response(
        JSON.stringify({ 
          success: false,
          error: 'Whop user not found' 
        }),
        { 
          status: 404, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    console.log('✅ Found Whop user in Supabase:', whopUser.id)

    return new Response(
      JSON.stringify({
        success: true,
        user: {
          id: whopUser.id,
          email: whopUser.email,
          user_metadata: whopUser.user_metadata
        }
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('❌ Error in whop-user-lookup:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error.message 
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})
