// Candlestick Pattern Recognition

// Interface for candlestick data
export interface Candle {
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

// Interface for pattern detection result
export interface PatternResult {
  pattern: string;
  position: number;
  significance: 'weak' | 'moderate' | 'strong';
  bullish: boolean;
}

// Helper function to determine if a candle is bullish or bearish
function isBullish(candle: Candle): boolean {
  return candle.close > candle.open;
}

// Helper function to determine candle body size
function bodySize(candle: Candle): number {
  return Math.abs(candle.close - candle.open);
}

// Helper function to determine candle range (high to low)
function candleRange(candle: Candle): number {
  return candle.high - candle.low;
}

// Helper function to determine upper wick size
function upperWick(candle: Candle): number {
  return isBullish(candle) 
    ? candle.high - candle.close 
    : candle.high - candle.open;
}

// Helper function to determine lower wick size
function lowerWick(candle: Candle): number {
  return isBullish(candle) 
    ? candle.open - candle.low 
    : candle.close - candle.low;
}

// Detect Doji pattern
export function detectDoji(candles: Candle[], position: number): PatternResult | null {
  if (position < 0 || position >= candles.length) return null;
  
  const candle = candles[position];
  const bodyToRangeRatio = bodySize(candle) / candleRange(candle);
  
  // Doji has a very small body compared to its range
  if (bodyToRangeRatio < 0.1) {
    // Determine significance based on position in trend
    let significance: 'weak' | 'moderate' | 'strong' = 'moderate';
    let bullish = true;
    
    // Check if it's at the end of a downtrend (bullish) or uptrend (bearish)
    if (position >= 3) {
      const priorTrend = candles[position-1].close < candles[position-3].close;
      bullish = priorTrend; // Bullish if prior trend was down
      
      // More significant if it appears after a strong trend
      if (Math.abs(candles[position-3].close - candles[position-1].close) / candles[position-3].close > 0.03) {
        significance = 'strong';
      }
    }
    
    return {
      pattern: 'doji',
      position,
      significance,
      bullish
    };
  }
  
  return null;
}

// Detect Hammer pattern
export function detectHammer(candles: Candle[], position: number): PatternResult | null {
  if (position < 0 || position >= candles.length) return null;
  
  const candle = candles[position];
  const body = bodySize(candle);
  const range = candleRange(candle);
  const lower = lowerWick(candle);
  
  // Hammer has a small body at the top with a long lower wick
  if (body / range < 0.3 && lower / range > 0.6) {
    // Determine significance based on position in trend
    let significance: 'weak' | 'moderate' | 'strong' = 'moderate';
    let bullish = true;
    
    // More significant if it appears at the end of a downtrend
    if (position >= 3) {
      const isDowntrend = candles[position-1].close < candles[position-3].close;
      
      if (isDowntrend) {
        significance = 'strong';
        bullish = true;
      } else {
        significance = 'weak';
        bullish = false; // Inverted hammer in an uptrend is bearish
      }
    }
    
    return {
      pattern: 'hammer',
      position,
      significance,
      bullish
    };
  }
  
  return null;
}

// Detect Shooting Star pattern
export function detectShootingStar(candles: Candle[], position: number): PatternResult | null {
  if (position < 0 || position >= candles.length) return null;
  
  const candle = candles[position];
  const body = bodySize(candle);
  const range = candleRange(candle);
  const upper = upperWick(candle);
  
  // Shooting star has a small body at the bottom with a long upper wick
  if (body / range < 0.3 && upper / range > 0.6) {
    // Determine significance based on position in trend
    let significance: 'weak' | 'moderate' | 'strong' = 'moderate';
    let bullish = false;
    
    // More significant if it appears at the end of an uptrend
    if (position >= 3) {
      const isUptrend = candles[position-1].close > candles[position-3].close;
      
      if (isUptrend) {
        significance = 'strong';
      } else {
        significance = 'weak';
        bullish = true; // In a downtrend, this could be a bullish signal
      }
    }
    
    return {
      pattern: 'shooting_star',
      position,
      significance,
      bullish
    };
  }
  
  return null;
}

// Detect Engulfing pattern
export function detectEngulfing(candles: Candle[], position: number): PatternResult | null {
  if (position < 1 || position >= candles.length) return null;
  
  const current = candles[position];
  const previous = candles[position - 1];
  
  // Current candle's body must completely engulf previous candle's body
  const currentBullish = isBullish(current);
  const previousBullish = isBullish(previous);
  
  // Bullish engulfing: current is bullish, previous is bearish
  // Bearish engulfing: current is bearish, previous is bullish
  if (currentBullish !== previousBullish) {
    const currentBodyLow = Math.min(current.open, current.close);
    const currentBodyHigh = Math.max(current.open, current.close);
    const previousBodyLow = Math.min(previous.open, previous.close);
    const previousBodyHigh = Math.max(previous.open, previous.close);
    
    if (currentBodyLow <= previousBodyLow && currentBodyHigh >= previousBodyHigh) {
      // Determine significance based on size difference
      const currentBodySize = bodySize(current);
      const previousBodySize = bodySize(previous);
      
      let significance: 'weak' | 'moderate' | 'strong' = 'moderate';
      
      if (currentBodySize > previousBodySize * 2) {
        significance = 'strong';
      } else if (currentBodySize > previousBodySize * 1.5) {
        significance = 'moderate';
      } else {
        significance = 'weak';
      }
      
      return {
        pattern: currentBullish ? 'bullish_engulfing' : 'bearish_engulfing',
        position,
        significance,
        bullish: currentBullish
      };
    }
  }
  
  return null;
}

// Detect Morning Star pattern (3-candle bullish reversal)
export function detectMorningStar(candles: Candle[], position: number): PatternResult | null {
  if (position < 2 || position >= candles.length) return null;
  
  const first = candles[position - 2];  // Bearish candle
  const middle = candles[position - 1]; // Small body candle
  const last = candles[position];       // Bullish candle
  
  // First candle should be bearish with a large body
  if (!isBullish(first) && bodySize(first) / candleRange(first) > 0.6) {
    // Middle candle should have a small body
    if (bodySize(middle) / candleRange(middle) < 0.3) {
      // Last candle should be bullish with a large body
      if (isBullish(last) && bodySize(last) / candleRange(last) > 0.6) {
        // Last candle should close above the midpoint of the first candle
        const firstMidpoint = first.open - (bodySize(first) / 2);
        
        if (last.close > firstMidpoint) {
          return {
            pattern: 'morning_star',
            position,
            significance: 'strong',
            bullish: true
          };
        }
      }
    }
  }
  
  return null;
}

// Detect Evening Star pattern (3-candle bearish reversal)
export function detectEveningStar(candles: Candle[], position: number): PatternResult | null {
  if (position < 2 || position >= candles.length) return null;
  
  const first = candles[position - 2];  // Bullish candle
  const middle = candles[position - 1]; // Small body candle
  const last = candles[position];       // Bearish candle
  
  // First candle should be bullish with a large body
  if (isBullish(first) && bodySize(first) / candleRange(first) > 0.6) {
    // Middle candle should have a small body
    if (bodySize(middle) / candleRange(middle) < 0.3) {
      // Last candle should be bearish with a large body
      if (!isBullish(last) && bodySize(last) / candleRange(last) > 0.6) {
        // Last candle should close below the midpoint of the first candle
        const firstMidpoint = first.open + (bodySize(first) / 2);
        
        if (last.close < firstMidpoint) {
          return {
            pattern: 'evening_star',
            position,
            significance: 'strong',
            bullish: false
          };
        }
      }
    }
  }
  
  return null;
}

// Analyze all candlestick patterns for a given dataset
export function analyzeCandlestickPatterns(candles: Candle[]): PatternResult[] {
  const patterns: PatternResult[] = [];
  
  // Start from index 2 to have enough prior candles for pattern detection
  for (let i = 2; i < candles.length; i++) {
    // Check for each pattern type
    const doji = detectDoji(candles, i);
    if (doji) patterns.push(doji);
    
    const hammer = detectHammer(candles, i);
    if (hammer) patterns.push(hammer);
    
    const shootingStar = detectShootingStar(candles, i);
    if (shootingStar) patterns.push(shootingStar);
    
    const engulfing = detectEngulfing(candles, i);
    if (engulfing) patterns.push(engulfing);
    
    const morningStar = detectMorningStar(candles, i);
    if (morningStar) patterns.push(morningStar);
    
    const eveningStar = detectEveningStar(candles, i);
    if (eveningStar) patterns.push(eveningStar);
  }
  
  return patterns;
}
