// Simple test script to verify crypto signal changes
// This is a mock implementation to test the logic

// Mock data
const cryptoSymbol = "X:BTCUSD";
const regularSymbol = "AAPL";

// Mock function to test crypto detection
function isCrypto(symbol) {
  return /^X:.*USD$/.test(symbol) || 
         /^(BTC|ETH|XRP|LTC|DOGE|SOL|ADA|DOT|AVAX|MATIC|LINK|UNI|SHIB)$/.test(symbol.toUpperCase());
}

// Mock function to test signal generation
function generateSignal(symbol, bullishScore, bearishScore) {
  const isCryptoSymbol = isCrypto(symbol);
  
  // Required difference for regular stocks
  const requiredDifference = 25;
  
  if (bullishScore > bearishScore + requiredDifference) {
    return "LONG";
  } else if (bearishScore > bullishScore + requiredDifference) {
    return "SHORT";
  } else if (isCryptoSymbol) {
    // For cryptocurrencies, always provide a LONG or SHORT signal based on which score is higher
    if (bullishScore >= bearishScore) {
      console.log(`CRYPTO TEST: Forcing LONG signal for crypto ${symbol} (bullish score ${bullishScore} >= bearish score ${bearishScore})`);
      return "LONG";
    } else {
      console.log(`CRYPTO TEST: Forcing SHORT signal for crypto ${symbol} (bearish score ${bearishScore} > bullish score ${bullishScore})`);
      return "SHORT";
    }
  } else {
    // For regular stocks, return NEUTRAL if scores are close
    return "NEUTRAL";
  }
}

// Test cases
console.log("=== Testing Crypto Signal Generation ===");

// Test case 1: Crypto with clear bullish signal
console.log("\nTest case 1: Crypto with clear bullish signal");
const signal1 = generateSignal(cryptoSymbol, 80, 30);
console.log(`Signal for ${cryptoSymbol} with bullish 80, bearish 30: ${signal1}`);
console.assert(signal1 === "LONG", "Should be LONG");

// Test case 2: Crypto with clear bearish signal
console.log("\nTest case 2: Crypto with clear bearish signal");
const signal2 = generateSignal(cryptoSymbol, 30, 80);
console.log(`Signal for ${cryptoSymbol} with bullish 30, bearish 80: ${signal2}`);
console.assert(signal2 === "SHORT", "Should be SHORT");

// Test case 3: Crypto with close scores (slightly bullish)
console.log("\nTest case 3: Crypto with close scores (slightly bullish)");
const signal3 = generateSignal(cryptoSymbol, 51, 49);
console.log(`Signal for ${cryptoSymbol} with bullish 51, bearish 49: ${signal3}`);
console.assert(signal3 === "LONG", "Should be LONG");

// Test case 4: Crypto with close scores (slightly bearish)
console.log("\nTest case 4: Crypto with close scores (slightly bearish)");
const signal4 = generateSignal(cryptoSymbol, 49, 51);
console.log(`Signal for ${cryptoSymbol} with bullish 49, bearish 51: ${signal4}`);
console.assert(signal4 === "SHORT", "Should be SHORT");

// Test case 5: Crypto with equal scores
console.log("\nTest case 5: Crypto with equal scores");
const signal5 = generateSignal(cryptoSymbol, 50, 50);
console.log(`Signal for ${cryptoSymbol} with bullish 50, bearish 50: ${signal5}`);
console.assert(signal5 === "LONG", "Should be LONG");

// Test case 6: Regular stock with clear bullish signal
console.log("\nTest case 6: Regular stock with clear bullish signal");
const signal6 = generateSignal(regularSymbol, 80, 30);
console.log(`Signal for ${regularSymbol} with bullish 80, bearish 30: ${signal6}`);
console.assert(signal6 === "LONG", "Should be LONG");

// Test case 7: Regular stock with clear bearish signal
console.log("\nTest case 7: Regular stock with clear bearish signal");
const signal7 = generateSignal(regularSymbol, 30, 80);
console.log(`Signal for ${regularSymbol} with bullish 30, bearish 80: ${signal7}`);
console.assert(signal7 === "SHORT", "Should be SHORT");

// Test case 8: Regular stock with close scores
console.log("\nTest case 8: Regular stock with close scores");
const signal8 = generateSignal(regularSymbol, 51, 49);
console.log(`Signal for ${regularSymbol} with bullish 51, bearish 49: ${signal8}`);
console.assert(signal8 === "NEUTRAL", "Should be NEUTRAL");

console.log("\n=== Test Summary ===");
console.log("All tests completed. Check for any assertion errors above.");
