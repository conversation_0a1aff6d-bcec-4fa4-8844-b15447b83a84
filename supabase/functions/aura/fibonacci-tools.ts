// Advanced Fibonacci Tools

import { Candle } from './candlestick-patterns.ts';

// Interface for Fibonacci analysis results
export interface FibonacciAnalysis {
  trend: 'uptrend' | 'downtrend' | 'neutral';
  swingHigh: number;
  swingLow: number;
  retracements: Record<string, number>;
  extensions: Record<string, number>;
  clusters: Array<{
    price: number;
    strength: 'weak' | 'moderate' | 'strong';
    levels: string[];
  }>;
}

// Find significant swing points for Fibonacci analysis
function findSignificantSwings(candles: Candle[], lookback: number = 10): { high: number, low: number, highIndex: number, lowIndex: number } {
  let highestHigh = -Infinity;
  let lowestLow = Infinity;
  let highestIndex = 0;
  let lowestIndex = 0;

  // Find highest high and lowest low in the lookback period
  for (let i = candles.length - lookback; i < candles.length; i++) {
    if (i < 0) continue;

    if (candles[i].high > highestHigh) {
      highestHigh = candles[i].high;
      highestIndex = i;
    }

    if (candles[i].low < lowestLow) {
      lowestLow = candles[i].low;
      lowestIndex = i;
    }
  }

  return {
    high: highestHigh,
    low: lowestLow,
    highIndex: highestIndex,
    lowIndex: lowestIndex
  };
}

// Calculate Fibonacci retracement levels
export function calculateFibonacciRetracements(candles: Candle[]): FibonacciAnalysis {
  // Find significant swing points
  const swings = findSignificantSwings(candles);

  // Determine trend direction
  const trend = swings.highIndex > swings.lowIndex ? 'uptrend' : 'downtrend';

  // Calculate the range
  const range = swings.high - swings.low;

  // Calculate retracement levels
  const retracements: Record<string, number> = {
    '0.0': trend === 'uptrend' ? swings.high : swings.low,
    '0.236': trend === 'uptrend' ? swings.high - (range * 0.236) : swings.low + (range * 0.236),
    '0.382': trend === 'uptrend' ? swings.high - (range * 0.382) : swings.low + (range * 0.382),
    '0.5': trend === 'uptrend' ? swings.high - (range * 0.5) : swings.low + (range * 0.5),
    '0.618': trend === 'uptrend' ? swings.high - (range * 0.618) : swings.low + (range * 0.618),
    '0.786': trend === 'uptrend' ? swings.high - (range * 0.786) : swings.low + (range * 0.786),
    '1.0': trend === 'uptrend' ? swings.low : swings.high
  };

  // Calculate extension levels
  const extensions: Record<string, number> = {
    '1.272': trend === 'uptrend' ? swings.high + (range * 0.272) : swings.low - (range * 0.272),
    '1.414': trend === 'uptrend' ? swings.high + (range * 0.414) : swings.low - (range * 0.414),
    '1.618': trend === 'uptrend' ? swings.high + (range * 0.618) : swings.low - (range * 0.618),
    '2.0': trend === 'uptrend' ? swings.high + range : swings.low - range,
    '2.618': trend === 'uptrend' ? swings.high + (range * 1.618) : swings.low - (range * 1.618)
  };

  // Find Fibonacci clusters (where multiple levels are close to each other)
  const clusters = findFibonacciClusters(retracements, extensions);

  return {
    trend: trend as 'uptrend' | 'downtrend' | 'neutral',
    swingHigh: swings.high,
    swingLow: swings.low,
    retracements,
    extensions,
    clusters
  };
}

// Find clusters where multiple Fibonacci levels are close to each other
function findFibonacciClusters(
  retracements: Record<string, number>,
  extensions: Record<string, number>,
  tolerance: number = 0.01
): Array<{ price: number, strength: 'weak' | 'moderate' | 'strong', levels: string[] }> {
  // Combine all levels
  const allLevels: Array<{ name: string, price: number }> = [];

  for (const [name, price] of Object.entries(retracements)) {
    allLevels.push({ name: `retracement_${name}`, price });
  }

  for (const [name, price] of Object.entries(extensions)) {
    allLevels.push({ name: `extension_${name}`, price });
  }

  // Sort by price
  allLevels.sort((a, b) => a.price - b.price);

  // Find clusters
  const clusters: Array<{ price: number, strength: 'weak' | 'moderate' | 'strong', levels: string[] }> = [];
  let currentCluster: { price: number, levels: string[] } | null = null;

  for (let i = 0; i < allLevels.length; i++) {
    const level = allLevels[i];

    if (!currentCluster) {
      currentCluster = {
        price: level.price,
        levels: [level.name]
      };
      continue;
    }

    // Check if this level is within tolerance of the current cluster
    if (Math.abs(level.price - currentCluster.price) / currentCluster.price <= tolerance) {
      // Add to current cluster
      currentCluster.levels.push(level.name);
      // Update cluster price to the average
      currentCluster.price = (currentCluster.price * (currentCluster.levels.length - 1) + level.price) / currentCluster.levels.length;
    } else {
      // Determine strength based on number of levels in the cluster
      let strength: 'weak' | 'moderate' | 'strong' = 'weak';
      if (currentCluster.levels.length >= 3) {
        strength = 'strong';
      } else if (currentCluster.levels.length === 2) {
        strength = 'moderate';
      }

      // Add current cluster to results
      clusters.push({
        price: currentCluster.price,
        strength,
        levels: currentCluster.levels
      });

      // Start a new cluster
      currentCluster = {
        price: level.price,
        levels: [level.name]
      };
    }
  }

  // Add the last cluster if it exists
  if (currentCluster) {
    let strength: 'weak' | 'moderate' | 'strong' = 'weak';
    if (currentCluster.levels.length >= 3) {
      strength = 'strong';
    } else if (currentCluster.levels.length === 2) {
      strength = 'moderate';
    }

    clusters.push({
      price: currentCluster.price,
      strength,
      levels: currentCluster.levels
    });
  }

  return clusters;
}

// Calculate Fibonacci extension projections for breakout targets
export function calculateFibonacciExtensions(
  candles: Candle[],
  projectionIndex?: number
): Record<string, number> {
  // Find significant swing points if indices not provided
  const swings = findSignificantSwings(candles);
  const startIndex = swings.lowIndex;
  const endIndex = swings.highIndex;

  // Use the last candle for projection if not specified
  const actualProjectionIndex = projectionIndex !== undefined ?
    projectionIndex : candles.length - 1;

  if (startIndex < 0 || endIndex >= candles.length || actualProjectionIndex >= candles.length) {
    return {};
  }

  // Get price points
  const startPrice = candles[startIndex].close;
  const endPrice = candles[endIndex].close;
  const projectionPrice = candles[actualProjectionIndex].close;

  // Calculate the range
  const range = Math.abs(endPrice - startPrice);

  // Determine if it's an uptrend or downtrend
  const isUptrend = endPrice > startPrice;

  // Calculate extension levels
  const extensions: Record<string, number> = {
    '0.0': projectionPrice,
    '0.618': isUptrend
      ? projectionPrice + (range * 0.618)
      : projectionPrice - (range * 0.618),
    '1.0': isUptrend
      ? projectionPrice + range
      : projectionPrice - range,
    '1.618': isUptrend
      ? projectionPrice + (range * 1.618)
      : projectionPrice - (range * 1.618),
    '2.0': isUptrend
      ? projectionPrice + (range * 2.0)
      : projectionPrice - (range * 2.0),
    '2.618': isUptrend
      ? projectionPrice + (range * 2.618)
      : projectionPrice - (range * 2.618)
  };

  return extensions;
}
