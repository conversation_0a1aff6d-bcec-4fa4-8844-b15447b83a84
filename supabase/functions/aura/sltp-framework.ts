// Aura Stop Loss & Take Profit Framework
// This module implements the enhanced framework for calculating stop loss and take profit levels

import { Candle } from "./technical-analysis.ts";
import { calculateATR } from "./indicators.ts";

// Interface for structure points
interface StructurePoint {
  price: number;
  type: 'swing_high' | 'swing_low' | 'double_top' | 'double_bottom' | 'consolidation' | 'chart_pattern';
  significance: number; // 0-100 scale
  description: string;
}

// Interface for technical protection points
interface TechnicalPoint {
  price: number;
  type: 'volume_node' | 'atr_distance' | 'gap_area' | 'trendline';
  significance: number; // 0-100 scale
  description: string;
}

// Interface for target levels
interface TargetLevel {
  price: number;
  type: 'historical_level' | 'psychological_level' | 'structure_flip' | 'volume_node' |
        'fibonacci_extension' | 'fibonacci_retracement' | 'swing_extreme' | 'chart_pattern' | 'volatility_extension';
  significance: number; // 0-100 scale
  description: string;
}

// Interface for stop loss and take profit calculation result
export interface SLTPResult {
  stopLoss: number;
  takeProfit: number;
  riskRewardRatio: number;
  stopLossExplanation: string;
  takeProfitExplanation: string;
  stopLossFactors: {
    structurePoints: number;
    technicalPoints: number;
    riskManagement: number;
  };
  takeProfitFactors: {
    keyLevels: number;
    fibonacciProjections: number;
    momentumTargets: number;
  };
}

/**
 * Calculate stop loss and take profit levels based on the Aura framework
 * @param data - Historical price data
 * @param signal - Trading signal ('LONG' or 'SHORT')
 * @param swingHighs - Array of swing high prices
 * @param swingLows - Array of swing low prices
 * @param supportLevels - Array of support levels
 * @param resistanceLevels - Array of resistance levels
 * @param fibonacciLevels - Optional Fibonacci levels
 * @returns Stop loss and take profit calculation result
 */
export function calculateEnhancedSLTP(
  data: Candle[],
  signal: 'LONG' | 'SHORT' | 'NEUTRAL',
  swingHighs: number[],
  swingLows: number[],
  supportLevels: number[],
  resistanceLevels: number[],
  fibonacciLevels?: any
): SLTPResult {
  // Get current price (latest close)
  const currentPrice = data[data.length - 1].close;

  // Calculate ATR for volatility-based measurements
  const atrPeriod = 14;
  const atr = calculateATR(data, atrPeriod);
  const currentATR = atr[atr.length - 1];

  // Initialize arrays for stop loss and take profit candidates
  const stopLossCandidates: { price: number; weight: number; description: string }[] = [];
  const takeProfitCandidates: { price: number; weight: number; description: string }[] = [];

  // Initialize factor weights
  const stopLossFactors = {
    structurePoints: 0,
    technicalPoints: 0,
    riskManagement: 0
  };

  const takeProfitFactors = {
    keyLevels: 0,
    fibonacciProjections: 0,
    momentumTargets: 0
  };

  // Check if the symbol is a cryptocurrency from the data
  const isCrypto = data.length > 0 && /^X:.*USD$/.test(data[0].date.toString());

  // For neutral signals, use default values (this should never happen for crypto with our changes)
  if (signal === 'NEUTRAL') {
    // For cryptocurrencies, we should never have a NEUTRAL signal, but if we do, default to LONG
    const effectiveSignal = isCrypto ? 'LONG' : signal;
    const stopLoss = effectiveSignal === 'LONG' ? currentPrice * 0.95 : currentPrice * 1.05;
    const takeProfit = effectiveSignal === 'LONG' ? currentPrice * 1.05 : currentPrice * 0.95;

    return {
      stopLoss,
      takeProfit,
      riskRewardRatio: 1,
      stopLossExplanation: isCrypto ?
        "Default 5% stop loss for crypto (forced LONG signal)" :
        "Default 5% stop loss for neutral signal",
      takeProfitExplanation: isCrypto ?
        "Default 5% take profit for crypto (forced LONG signal)" :
        "Default 5% take profit for neutral signal",
      stopLossFactors: {
        structurePoints: 0,
        technicalPoints: 0,
        riskManagement: 100
      },
      takeProfitFactors: {
        keyLevels: 0,
        fibonacciProjections: 0,
        momentumTargets: 0
      }
    };
  }

  // 1. Key Structure Points (40%)
  // Find nearest significant swing low/high opposite to trade direction
  if (signal === 'LONG') {
    // For long trades, look for swing lows below current price
    const relevantSwingLows = swingLows.filter(level => level < currentPrice).sort((a, b) => b - a);

    if (relevantSwingLows.length > 0) {
      const nearestSwingLow = relevantSwingLows[0];
      const distance = Math.abs((currentPrice - nearestSwingLow) / currentPrice);

      // Weight decreases as distance increases
      const weight = Math.min(15, 15 * (1 - distance));

      stopLossCandidates.push({
        price: nearestSwingLow,
        weight,
        description: `Nearest swing low at ${nearestSwingLow.toFixed(2)}`
      });

      stopLossFactors.structurePoints += weight;
    }

    // Look for recent double bottoms
    if (relevantSwingLows.length >= 2 &&
        Math.abs(relevantSwingLows[0] - relevantSwingLows[1]) / relevantSwingLows[0] < 0.02) {
      const doubleLow = Math.min(relevantSwingLows[0], relevantSwingLows[1]);

      stopLossCandidates.push({
        price: doubleLow * 0.99, // Slight buffer below double bottom
        weight: 10,
        description: `Double bottom formation at ${doubleLow.toFixed(2)}`
      });

      stopLossFactors.structurePoints += 10;
    }

    // Use support levels for stop loss
    if (supportLevels.length > 0) {
      const relevantSupports = supportLevels.filter(level => level < currentPrice).sort((a, b) => b - a);

      if (relevantSupports.length > 0) {
        const nearestSupport = relevantSupports[0];

        stopLossCandidates.push({
          price: nearestSupport * 0.99, // Slight buffer below support
          weight: 8,
          description: `Support level at ${nearestSupport.toFixed(2)}`
        });

        stopLossFactors.structurePoints += 8;
      }
    }
  } else if (signal === 'SHORT') {
    // For short trades, look for swing highs above current price
    const relevantSwingHighs = swingHighs.filter(level => level > currentPrice).sort((a, b) => a - b);

    if (relevantSwingHighs.length > 0) {
      const nearestSwingHigh = relevantSwingHighs[0];
      const distance = Math.abs((nearestSwingHigh - currentPrice) / currentPrice);

      // Weight decreases as distance increases
      const weight = Math.min(15, 15 * (1 - distance));

      stopLossCandidates.push({
        price: nearestSwingHigh,
        weight,
        description: `Nearest swing high at ${nearestSwingHigh.toFixed(2)}`
      });

      stopLossFactors.structurePoints += weight;
    }

    // Look for recent double tops
    if (relevantSwingHighs.length >= 2 &&
        Math.abs(relevantSwingHighs[0] - relevantSwingHighs[1]) / relevantSwingHighs[0] < 0.02) {
      const doubleHigh = Math.max(relevantSwingHighs[0], relevantSwingHighs[1]);

      stopLossCandidates.push({
        price: doubleHigh * 1.01, // Slight buffer above double top
        weight: 10,
        description: `Double top formation at ${doubleHigh.toFixed(2)}`
      });

      stopLossFactors.structurePoints += 10;
    }

    // Use resistance levels for stop loss
    if (resistanceLevels.length > 0) {
      const relevantResistances = resistanceLevels.filter(level => level > currentPrice).sort((a, b) => a - b);

      if (relevantResistances.length > 0) {
        const nearestResistance = relevantResistances[0];

        stopLossCandidates.push({
          price: nearestResistance * 1.01, // Slight buffer above resistance
          weight: 8,
          description: `Resistance level at ${nearestResistance.toFixed(2)}`
        });

        stopLossFactors.structurePoints += 8;
      }
    }
  }

  // 2. Technical Protection Points (30%)
  // ATR-based stop loss
  const atrStopLoss = signal === 'LONG'
    ? currentPrice - (currentATR * 1.0)
    : currentPrice + (currentATR * 1.0);

  stopLossCandidates.push({
    price: atrStopLoss,
    weight: 8,
    description: `1 ATR (${currentATR.toFixed(2)}) distance from entry`
  });

  stopLossFactors.technicalPoints += 8;

  // Recent gap areas (simplified implementation)
  for (let i = data.length - 20; i < data.length - 1; i++) {
    if (i < 0) continue;

    const gap = data[i + 1].low - data[i].high; // Gap up

    if (signal === 'LONG' && gap > 0 && data[i].high < currentPrice) {
      stopLossCandidates.push({
        price: data[i].high,
        weight: 6,
        description: `Recent gap area at ${data[i].high.toFixed(2)}`
      });

      stopLossFactors.technicalPoints += 6;
      break;
    } else if (signal === 'SHORT' && gap < 0 && data[i].low > currentPrice) {
      const gapDown = data[i + 1].high - data[i].low;

      stopLossCandidates.push({
        price: data[i].low,
        weight: 6,
        description: `Recent gap area at ${data[i].low.toFixed(2)}`
      });

      stopLossFactors.technicalPoints += 6;
      break;
    }
  }

  // 3. Risk Management Parameters (30%)
  // Default risk-reward based stop loss
  const defaultRiskPercent = 0.05; // 5%
  const defaultRiskStopLoss = signal === 'LONG'
    ? currentPrice * (1 - defaultRiskPercent)
    : currentPrice * (1 + defaultRiskPercent);

  stopLossCandidates.push({
    price: defaultRiskStopLoss,
    weight: 20,
    description: `Default risk management stop (${(defaultRiskPercent * 100).toFixed(0)}% from entry)`
  });

  stopLossFactors.riskManagement += 20;

  // Volatility-based maximum risk
  const volatilityRisk = Math.max(0.03, Math.min(0.08, currentATR / currentPrice * 3));
  const volatilityStopLoss = signal === 'LONG'
    ? currentPrice * (1 - volatilityRisk)
    : currentPrice * (1 + volatilityRisk);

  stopLossCandidates.push({
    price: volatilityStopLoss,
    weight: 10,
    description: `Volatility-based stop (${(volatilityRisk * 100).toFixed(1)}% from entry)`
  });

  stopLossFactors.riskManagement += 10;

  // Calculate weighted average for stop loss
  let totalWeight = 0;
  let weightedSum = 0;

  for (const candidate of stopLossCandidates) {
    weightedSum += candidate.price * candidate.weight;
    totalWeight += candidate.weight;
  }

  // Default to 5% if no valid candidates
  const stopLoss = totalWeight > 0
    ? weightedSum / totalWeight
    : (signal === 'LONG' ? currentPrice * 0.95 : currentPrice * 1.05);

  // Find the most influential factor for explanation
  const mostInfluentialSL = stopLossCandidates.reduce((prev, current) =>
    (current.weight > prev.weight) ? current : prev,
    { price: 0, weight: 0, description: '' }
  );

  // Now calculate take profit levels

  // 1. Key Resistance/Support Targets (40%)
  if (signal === 'LONG') {
    // For long trades, look for resistance levels above current price
    const relevantResistances = resistanceLevels.filter(level => level > currentPrice).sort((a, b) => a - b);

    if (relevantResistances.length > 0) {
      const nearestResistance = relevantResistances[0];
      const distance = Math.abs((nearestResistance - currentPrice) / currentPrice);

      // Weight decreases as distance increases, but not as much as for stop loss
      const weight = Math.min(15, 15 * (1 - distance * 0.5));

      takeProfitCandidates.push({
        price: nearestResistance,
        weight,
        description: `Nearest resistance at ${nearestResistance.toFixed(2)}`
      });

      takeProfitFactors.keyLevels += weight;
    }

    // Psychological levels (round numbers)
    const nextRoundNumber = Math.ceil(currentPrice / 10) * 10;
    if (nextRoundNumber > currentPrice) {
      takeProfitCandidates.push({
        price: nextRoundNumber,
        weight: 10,
        description: `Psychological level at ${nextRoundNumber.toFixed(0)}`
      });

      takeProfitFactors.keyLevels += 10;
    }

    // Previous market structure areas
    if (swingHighs.length > 0) {
      const relevantSwingHighs = swingHighs.filter(level => level > currentPrice).sort((a, b) => a - b);

      if (relevantSwingHighs.length > 0) {
        takeProfitCandidates.push({
          price: relevantSwingHighs[0],
          weight: 8,
          description: `Previous swing high at ${relevantSwingHighs[0].toFixed(2)}`
        });

        takeProfitFactors.keyLevels += 8;
      }
    }
  } else if (signal === 'SHORT') {
    // For short trades, look for support levels below current price
    const relevantSupports = supportLevels.filter(level => level < currentPrice).sort((a, b) => b - a);

    if (relevantSupports.length > 0) {
      const nearestSupport = relevantSupports[0];
      const distance = Math.abs((currentPrice - nearestSupport) / currentPrice);

      // Weight decreases as distance increases, but not as much as for stop loss
      const weight = Math.min(15, 15 * (1 - distance * 0.5));

      takeProfitCandidates.push({
        price: nearestSupport,
        weight,
        description: `Nearest support at ${nearestSupport.toFixed(2)}`
      });

      takeProfitFactors.keyLevels += weight;
    }

    // Psychological levels (round numbers)
    const nextRoundNumber = Math.floor(currentPrice / 10) * 10;
    if (nextRoundNumber < currentPrice) {
      takeProfitCandidates.push({
        price: nextRoundNumber,
        weight: 10,
        description: `Psychological level at ${nextRoundNumber.toFixed(0)}`
      });

      takeProfitFactors.keyLevels += 10;
    }

    // Previous market structure areas
    if (swingLows.length > 0) {
      const relevantSwingLows = swingLows.filter(level => level < currentPrice).sort((a, b) => b - a);

      if (relevantSwingLows.length > 0) {
        takeProfitCandidates.push({
          price: relevantSwingLows[0],
          weight: 8,
          description: `Previous swing low at ${relevantSwingLows[0].toFixed(2)}`
        });

        takeProfitFactors.keyLevels += 8;
      }
    }
  }

  // 2. Fibonacci Projection Targets (35%)
  // Simplified Fibonacci extensions
  if (signal === 'LONG') {
    const fibExtension127 = currentPrice * 1.127;
    const fibExtension1618 = currentPrice * 1.1618;

    takeProfitCandidates.push({
      price: fibExtension127,
      weight: 15,
      description: `1.27 Fibonacci extension at ${fibExtension127.toFixed(2)}`
    });

    takeProfitCandidates.push({
      price: fibExtension1618,
      weight: 12,
      description: `1.618 Fibonacci extension at ${fibExtension1618.toFixed(2)}`
    });

    takeProfitFactors.fibonacciProjections += 27;
  } else if (signal === 'SHORT') {
    const fibExtension127 = currentPrice * 0.873; // 1 - 0.127
    const fibExtension1618 = currentPrice * 0.8382; // 1 - 0.1618

    takeProfitCandidates.push({
      price: fibExtension127,
      weight: 15,
      description: `1.27 Fibonacci extension at ${fibExtension127.toFixed(2)}`
    });

    takeProfitCandidates.push({
      price: fibExtension1618,
      weight: 12,
      description: `1.618 Fibonacci extension at ${fibExtension1618.toFixed(2)}`
    });

    takeProfitFactors.fibonacciProjections += 27;
  }

  // 3. Momentum-Based Targets (25%)
  // Previous swing point extremes
  if (signal === 'LONG' && swingHighs.length > 0) {
    const highestSwing = Math.max(...swingHighs);

    if (highestSwing > currentPrice) {
      takeProfitCandidates.push({
        price: highestSwing,
        weight: 10,
        description: `Previous swing high extreme at ${highestSwing.toFixed(2)}`
      });

      takeProfitFactors.momentumTargets += 10;
    }
  } else if (signal === 'SHORT' && swingLows.length > 0) {
    const lowestSwing = Math.min(...swingLows);

    if (lowestSwing < currentPrice) {
      takeProfitCandidates.push({
        price: lowestSwing,
        weight: 10,
        description: `Previous swing low extreme at ${lowestSwing.toFixed(2)}`
      });

      takeProfitFactors.momentumTargets += 10;
    }
  }

  // Volatility-based extensions
  const volatilityTarget = signal === 'LONG'
    ? currentPrice + (currentATR * 2.5)
    : currentPrice - (currentATR * 2.5);

  takeProfitCandidates.push({
    price: volatilityTarget,
    weight: 15,
    description: `2.5 ATR (${currentATR.toFixed(2)}) target from entry`
  });

  takeProfitFactors.momentumTargets += 15;

  // Calculate weighted average for take profit
  totalWeight = 0;
  weightedSum = 0;

  for (const candidate of takeProfitCandidates) {
    weightedSum += candidate.price * candidate.weight;
    totalWeight += candidate.weight;
  }

  // Default to 5% if no valid candidates
  const takeProfit = totalWeight > 0
    ? weightedSum / totalWeight
    : (signal === 'LONG' ? currentPrice * 1.05 : currentPrice * 0.95);

  // Find the most influential factor for explanation
  const mostInfluentialTP = takeProfitCandidates.reduce((prev, current) =>
    (current.weight > prev.weight) ? current : prev,
    { price: 0, weight: 0, description: '' }
  );

  // Calculate risk-reward ratio
  const risk = Math.abs(currentPrice - stopLoss);
  const reward = Math.abs(takeProfit - currentPrice);
  const riskRewardRatio = reward / risk;

  // Ensure minimum risk-reward ratio of 1:1
  let finalTakeProfit = takeProfit;
  if (riskRewardRatio < 1) {
    finalTakeProfit = signal === 'LONG'
      ? currentPrice + risk
      : currentPrice - risk;
  }

  // Ensure maximum risk-reward ratio of 3:1 for realistic targets
  const maxRiskRewardRatio = 3;
  if (riskRewardRatio > maxRiskRewardRatio) {
    finalTakeProfit = signal === 'LONG'
      ? currentPrice + (risk * maxRiskRewardRatio)
      : currentPrice - (risk * maxRiskRewardRatio);
  }

  // Calculate final risk-reward ratio
  const finalRisk = Math.abs(currentPrice - stopLoss);
  const finalReward = Math.abs(finalTakeProfit - currentPrice);
  const finalRiskRewardRatio = finalReward / finalRisk;

  return {
    stopLoss,
    takeProfit: finalTakeProfit,
    riskRewardRatio: finalRiskRewardRatio,
    stopLossExplanation: mostInfluentialSL.description,
    takeProfitExplanation: mostInfluentialTP.description,
    stopLossFactors,
    takeProfitFactors
  };
}

/**
 * Detect if a breakout has occurred
 * @param data - Historical price data
 * @param lookbackPeriod - Number of candles to look back
 * @returns Breakout information or null if no breakout
 */
export function detectBreakout(data: Candle[], lookbackPeriod: number = 20): {
  direction: 'up' | 'down';
  strength: 'weak' | 'moderate' | 'strong';
  candle: Candle;
} | null {
  if (data.length < lookbackPeriod + 1) {
    return null;
  }

  const recentData = data.slice(-lookbackPeriod - 1);
  const lastCandle = recentData[recentData.length - 1];
  const previousCandles = recentData.slice(0, -1);

  // Calculate the highest high and lowest low in the lookback period
  const highestHigh = Math.max(...previousCandles.map(c => c.high));
  const lowestLow = Math.min(...previousCandles.map(c => c.low));

  // Check for breakout
  const upBreakout = lastCandle.close > highestHigh;
  const downBreakout = lastCandle.close < lowestLow;

  if (!upBreakout && !downBreakout) {
    return null;
  }

  // Determine breakout direction
  const direction = upBreakout ? 'up' : 'down';

  // Calculate breakout strength based on candle characteristics
  const bodySize = Math.abs(lastCandle.close - lastCandle.open);
  const totalSize = lastCandle.high - lastCandle.low;
  const bodyToTotalRatio = bodySize / totalSize;

  // Strong breakout: large body, small wicks
  // Moderate breakout: medium body, medium wicks
  // Weak breakout: small body, large wicks
  let strength: 'weak' | 'moderate' | 'strong' = 'moderate';

  if (bodyToTotalRatio > 0.7) {
    strength = 'strong';
  } else if (bodyToTotalRatio < 0.4) {
    strength = 'weak';
  }

  return {
    direction,
    strength,
    candle: lastCandle
  };
}

/**
 * Calculate stop loss and take profit for a breakout trade
 * @param data - Historical price data
 * @param signal - Trading signal ('LONG' or 'SHORT')
 * @param breakoutInfo - Breakout information
 * @returns Stop loss and take profit levels
 */
export function calculateBreakoutSLTP(
  data: Candle[],
  signal: 'LONG' | 'SHORT',
  breakoutInfo: { direction: 'up' | 'down'; strength: 'weak' | 'moderate' | 'strong'; candle: Candle }
): { stopLoss: number; takeProfit: number; riskRewardRatio: number } {
  const { direction, strength, candle } = breakoutInfo;
  const currentPrice = data[data.length - 1].close;

  // Calculate body length of breakout candle
  const bodySize = Math.abs(candle.close - candle.open);

  // For strong momentum breakouts, use the breakout candle for projections
  if (strength === 'strong') {
    // Stop loss at the opposite side of the breakout candle
    const stopLoss = direction === 'up'
      ? Math.min(candle.open, candle.low)
      : Math.max(candle.open, candle.high);

    // Take profit at 2x body projection
    const takeProfit = direction === 'up'
      ? currentPrice + (bodySize * 2)
      : currentPrice - (bodySize * 2);

    const risk = Math.abs(currentPrice - stopLoss);
    const reward = Math.abs(takeProfit - currentPrice);

    return {
      stopLoss,
      takeProfit,
      riskRewardRatio: reward / risk
    };
  }

  // For moderate and weak breakouts, use more conservative levels
  // Stop loss just beyond the breakout level
  const breakoutLevel = direction === 'up' ? candle.low : candle.high;
  const buffer = bodySize * 0.1; // 10% of body size as buffer

  const stopLoss = direction === 'up'
    ? breakoutLevel - buffer
    : breakoutLevel + buffer;

  // Take profit based on strength
  const projectionMultiplier = strength === 'moderate' ? 1.5 : 1.0;
  const takeProfit = direction === 'up'
    ? currentPrice + (bodySize * projectionMultiplier)
    : currentPrice - (bodySize * projectionMultiplier);

  const risk = Math.abs(currentPrice - stopLoss);
  const reward = Math.abs(takeProfit - currentPrice);

  return {
    stopLoss,
    takeProfit,
    riskRewardRatio: reward / risk
  };
}
