// Learning Engine Module for Aura
// This module analyzes trade outcomes and generates improvements

import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { Trade } from './trade-tracker.ts';
import { PerformanceMetrics } from './performance-analyzer.ts';

// Types for learning engine
export interface LearningData {
  id?: string;
  date: string;
  timeframe: string;
  pattern: string;
  success_rate: number;
  failure_rate: number;
  adjustment_factor: number;
  notes: string;
}

export interface TradeReview {
  trade_id: string;
  review_notes: string;
  improvement_suggestions: string;
  adjustment_factors: {
    [key: string]: number;
  };
}

export interface LearningInsight {
  category: string;
  insight: string;
  adjustment_factor: number;
  confidence: number;
}

export class LearningEngine {
  private supabase;
  
  constructor(supabaseUrl: string, supabaseKey: string) {
    this.supabase = createClient(supabaseUrl, supabaseKey);
  }
  
  /**
   * Review a single trade and generate insights
   */
  async reviewTrade(trade: Trade): Promise<TradeReview> {
    // Initialize review
    const review: TradeReview = {
      trade_id: trade.id || '',
      review_notes: '',
      improvement_suggestions: '',
      adjustment_factors: {}
    };
    
    // Skip if the trade doesn't have outcome data
    if (!trade.exit_price || trade.profit_loss === undefined || trade.successful === undefined) {
      review.review_notes = 'Trade has not been closed yet.';
      return review;
    }
    
    // Analyze the trade outcome
    const successful = trade.successful;
    const profitLossPercent = trade.profit_loss_percent || 0;
    
    // Generate review notes
    let reviewNotes = `Trade ${successful ? 'succeeded' : 'failed'} with ${profitLossPercent.toFixed(2)}% ${successful ? 'profit' : 'loss'}. `;
    
    // Analyze why the trade succeeded or failed
    if (successful) {
      reviewNotes += 'Analysis of success factors: ';
      
      // Check if take profit was hit
      if (trade.exit_reason === 'TAKE_PROFIT') {
        reviewNotes += 'Take profit target was reached. ';
        review.adjustment_factors['take_profit_accuracy'] = 0.1;
      }
      
      // Check if the trade aligned with market structure
      if (trade.market_structure) {
        reviewNotes += `Market structure was ${trade.market_structure}. `;
        review.adjustment_factors[`market_structure_${trade.market_structure.toLowerCase()}`] = 0.1;
      }
      
      // Check confidence level effectiveness
      reviewNotes += `Confidence level was ${trade.confidence}. `;
      review.adjustment_factors[`confidence_${trade.confidence.toLowerCase()}`] = 0.05;
      
    } else {
      reviewNotes += 'Analysis of failure factors: ';
      
      // Check if stop loss was hit
      if (trade.exit_reason === 'STOP_LOSS') {
        reviewNotes += 'Stop loss was triggered. ';
        review.adjustment_factors['stop_loss_placement'] = -0.1;
      }
      
      // Check if the trade went against market structure
      if (trade.market_structure) {
        reviewNotes += `Market structure was ${trade.market_structure}. `;
        review.adjustment_factors[`market_structure_${trade.market_structure.toLowerCase()}`] = -0.1;
      }
      
      // Check confidence level effectiveness
      reviewNotes += `Confidence level was ${trade.confidence}. `;
      review.adjustment_factors[`confidence_${trade.confidence.toLowerCase()}`] = -0.05;
    }
    
    // Generate improvement suggestions
    let improvementSuggestions = '';
    
    if (!successful) {
      if (trade.exit_reason === 'STOP_LOSS') {
        improvementSuggestions += 'Consider placing stop loss further from entry to account for market volatility. ';
      }
      
      if (trade.signal === 'LONG' && trade.market_structure === 'downtrend') {
        improvementSuggestions += 'Avoid long positions in downtrends unless there are strong reversal signals. ';
      } else if (trade.signal === 'SHORT' && trade.market_structure === 'uptrend') {
        improvementSuggestions += 'Avoid short positions in uptrends unless there are strong reversal signals. ';
      }
      
      if (trade.confidence === 'LOW') {
        improvementSuggestions += 'Consider avoiding trades with low confidence signals. ';
      }
    } else {
      if (profitLossPercent < 2) {
        improvementSuggestions += 'Consider setting more ambitious take profit targets for better risk-reward ratio. ';
      }
      
      if (trade.exit_reason === 'MANUAL') {
        improvementSuggestions += 'Consider setting automated take profit levels to capture gains systematically. ';
      }
    }
    
    review.review_notes = reviewNotes;
    review.improvement_suggestions = improvementSuggestions;
    
    return review;
  }
  
  /**
   * Review all unreviewed trades and update the database
   */
  async reviewAllPendingTrades(): Promise<{ success: boolean, data?: any, error?: any }> {
    try {
      // Get all unreviewed trades
      const { data: trades, error } = await this.supabase
        .from('aura_trades')
        .select('*')
        .not('exit_price', 'is', null)
        .eq('reviewed', false);
      
      if (error) throw error;
      
      const reviewPromises = [];
      
      for (const trade of trades) {
        // Review the trade
        const review = await this.reviewTrade(trade);
        
        // Update the trade with review data
        const updatePromise = this.supabase
          .from('aura_trades')
          .update({
            reviewed: true,
            review_notes: review.review_notes,
            improvement_suggestions: review.improvement_suggestions
          })
          .eq('id', trade.id);
        
        reviewPromises.push(updatePromise);
        
        // Save learning data
        for (const [pattern, adjustmentFactor] of Object.entries(review.adjustment_factors)) {
          await this.saveLearningData({
            date: new Date().toISOString().split('T')[0],
            timeframe: trade.timeframe,
            pattern,
            success_rate: trade.successful ? 1 : 0,
            failure_rate: trade.successful ? 0 : 1,
            adjustment_factor: adjustmentFactor,
            notes: `From trade ${trade.id} (${trade.symbol})`
          });
        }
      }
      
      // Wait for all updates to complete
      await Promise.all(reviewPromises);
      
      return { success: true, data: { reviewed_count: trades.length } };
    } catch (error) {
      console.error('Error reviewing pending trades:', error);
      return { success: false, error };
    }
  }
  
  /**
   * Save learning data to the database
   */
  private async saveLearningData(data: LearningData): Promise<void> {
    try {
      await this.supabase
        .from('aura_learning')
        .insert(data);
    } catch (error) {
      console.error('Error saving learning data:', error);
    }
  }
  
  /**
   * Generate insights from performance metrics
   */
  async generateInsights(metrics: PerformanceMetrics): Promise<LearningInsight[]> {
    const insights: LearningInsight[] = [];
    
    // Analyze overall win rate
    if (metrics.win_rate < 0.4) {
      insights.push({
        category: 'Overall Performance',
        insight: 'Win rate is below 40%. Consider being more selective with trade entries.',
        adjustment_factor: -0.2,
        confidence: 0.8
      });
    } else if (metrics.win_rate > 0.6) {
      insights.push({
        category: 'Overall Performance',
        insight: 'Win rate is above 60%. Current strategy is working well.',
        adjustment_factor: 0.1,
        confidence: 0.7
      });
    }
    
    // Analyze signal type performance
    if (metrics.long_trades >= 5 && metrics.short_trades >= 5) {
      if (metrics.long_win_rate > metrics.short_win_rate + 0.2) {
        insights.push({
          category: 'Signal Type',
          insight: 'Long trades significantly outperform short trades. Consider focusing more on long opportunities.',
          adjustment_factor: 0.15,
          confidence: 0.75
        });
      } else if (metrics.short_win_rate > metrics.long_win_rate + 0.2) {
        insights.push({
          category: 'Signal Type',
          insight: 'Short trades significantly outperform long trades. Consider focusing more on short opportunities.',
          adjustment_factor: 0.15,
          confidence: 0.75
        });
      }
    }
    
    // Analyze confidence levels
    if (metrics.high_confidence_trades >= 5) {
      if (metrics.high_confidence_win_rate < 0.5) {
        insights.push({
          category: 'Confidence Levels',
          insight: 'High confidence trades have a win rate below 50%. Review criteria for high confidence signals.',
          adjustment_factor: -0.2,
          confidence: 0.8
        });
      } else if (metrics.high_confidence_win_rate > 0.7) {
        insights.push({
          category: 'Confidence Levels',
          insight: 'High confidence trades have a win rate above 70%. Current criteria are effective.',
          adjustment_factor: 0.1,
          confidence: 0.7
        });
      }
    }
    
    // Analyze risk-reward ratio
    if (metrics.avg_risk_reward_ratio < 1) {
      insights.push({
        category: 'Risk Management',
        insight: 'Average risk-reward ratio is below 1:1. Consider setting more ambitious take profit targets or tighter stop losses.',
        adjustment_factor: -0.15,
        confidence: 0.8
      });
    } else if (metrics.avg_risk_reward_ratio > 2) {
      insights.push({
        category: 'Risk Management',
        insight: 'Average risk-reward ratio is above 2:1. Current risk management is effective.',
        adjustment_factor: 0.1,
        confidence: 0.7
      });
    }
    
    return insights;
  }
  
  /**
   * Get learning data for a specific pattern
   */
  async getLearningData(
    pattern: string,
    timeframe: string = 'day'
  ): Promise<{ success: boolean, data?: any, error?: any }> {
    try {
      const { data, error } = await this.supabase
        .from('aura_learning')
        .select('*')
        .eq('pattern', pattern)
        .eq('timeframe', timeframe)
        .order('date', { ascending: false });
      
      if (error) throw error;
      
      return { success: true, data };
    } catch (error) {
      console.error('Error fetching learning data:', error);
      return { success: false, error };
    }
  }
  
  /**
   * Get all learning data
   */
  async getAllLearningData(
    timeframe: string = 'day'
  ): Promise<{ success: boolean, data?: any, error?: any }> {
    try {
      const { data, error } = await this.supabase
        .from('aura_learning')
        .select('*')
        .eq('timeframe', timeframe)
        .order('date', { ascending: false });
      
      if (error) throw error;
      
      return { success: true, data };
    } catch (error) {
      console.error('Error fetching all learning data:', error);
      return { success: false, error };
    }
  }
  
  /**
   * Apply learning adjustments to a trading strategy
   * This method would be called before generating a new trade signal
   */
  async applyLearningAdjustments(
    symbol: string,
    timeframe: string = 'day'
  ): Promise<{ success: boolean, data?: any, error?: any }> {
    try {
      // Get all learning data
      const { data: learningData, error } = await this.getAllLearningData(timeframe);
      
      if (error) throw error;
      
      // Group learning data by pattern
      const patternAdjustments: { [key: string]: number } = {};
      
      for (const data of learningData) {
        if (!patternAdjustments[data.pattern]) {
          patternAdjustments[data.pattern] = 0;
        }
        
        patternAdjustments[data.pattern] += data.adjustment_factor;
      }
      
      // Cap adjustments to prevent extreme values
      for (const pattern in patternAdjustments) {
        patternAdjustments[pattern] = Math.max(-0.5, Math.min(0.5, patternAdjustments[pattern]));
      }
      
      return { success: true, data: patternAdjustments };
    } catch (error) {
      console.error('Error applying learning adjustments:', error);
      return { success: false, error };
    }
  }
}
