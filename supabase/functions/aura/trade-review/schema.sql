-- Schema for trade tracking and review

-- Table to store trade recommendations
CREATE TABLE IF NOT EXISTS aura_trades (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  symbol TEXT NOT NULL,
  signal TEXT NOT NULL CHECK (signal IN ('LONG', 'SHORT', 'NEUTRAL')),
  confidence TEXT NOT NULL CHECK (confidence IN ('LOW', 'MEDIUM', 'HIGH')),
  entry_price NUMERIC NOT NULL,
  stop_loss NUMERIC,
  take_profit NUMERIC,
  risk_reward_ratio NUMERIC,
  timeframe TEXT NOT NULL DEFAULT 'day',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Trade outcome fields
  closed_at TIMESTAMP WITH TIME ZONE,
  exit_price NUMERIC,
  exit_reason TEXT CHECK (exit_reason IN ('TAKE_PROFIT', 'STOP_LOSS', 'MANUAL', 'TIMEOUT', NULL)),
  profit_loss NUMERIC,
  profit_loss_percent NUMERIC,
  successful BOOLEAN,
  
  -- Analysis fields
  market_structure TEXT,
  key_support NUMERIC,
  key_resistance NUMERIC,
  
  -- Review fields
  reviewed BOOLEAN DEFAULT FALSE,
  review_notes TEXT,
  improvement_suggestions TEXT
);

-- Table to store trade performance metrics
CREATE TABLE IF NOT EXISTS aura_performance (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  timeframe TEXT NOT NULL DEFAULT 'day',
  total_trades INTEGER DEFAULT 0,
  successful_trades INTEGER DEFAULT 0,
  failed_trades INTEGER DEFAULT 0,
  win_rate NUMERIC,
  avg_profit_loss NUMERIC,
  avg_profit_loss_percent NUMERIC,
  avg_risk_reward_ratio NUMERIC,
  
  -- Performance by signal type
  long_trades INTEGER DEFAULT 0,
  long_successful INTEGER DEFAULT 0,
  long_win_rate NUMERIC,
  short_trades INTEGER DEFAULT 0,
  short_successful INTEGER DEFAULT 0,
  short_win_rate NUMERIC,
  
  -- Performance by confidence level
  high_confidence_trades INTEGER DEFAULT 0,
  high_confidence_win_rate NUMERIC,
  medium_confidence_trades INTEGER DEFAULT 0,
  medium_confidence_win_rate NUMERIC,
  low_confidence_trades INTEGER DEFAULT 0,
  low_confidence_win_rate NUMERIC
);

-- Table to store learning and improvement data
CREATE TABLE IF NOT EXISTS aura_learning (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  date DATE NOT NULL,
  timeframe TEXT NOT NULL DEFAULT 'day',
  pattern TEXT NOT NULL,
  success_rate NUMERIC,
  failure_rate NUMERIC,
  adjustment_factor NUMERIC,
  notes TEXT
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_aura_trades_user_id ON aura_trades(user_id);
CREATE INDEX IF NOT EXISTS idx_aura_trades_symbol ON aura_trades(symbol);
CREATE INDEX IF NOT EXISTS idx_aura_trades_created_at ON aura_trades(created_at);
CREATE INDEX IF NOT EXISTS idx_aura_trades_signal ON aura_trades(signal);
CREATE INDEX IF NOT EXISTS idx_aura_trades_successful ON aura_trades(successful);

CREATE INDEX IF NOT EXISTS idx_aura_performance_user_id ON aura_performance(user_id);
CREATE INDEX IF NOT EXISTS idx_aura_performance_date ON aura_performance(date);

CREATE INDEX IF NOT EXISTS idx_aura_learning_date ON aura_learning(date);
CREATE INDEX IF NOT EXISTS idx_aura_learning_pattern ON aura_learning(pattern);
