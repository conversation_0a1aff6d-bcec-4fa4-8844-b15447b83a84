// Performance Analysis Module for Aura
// This module analyzes trade performance and generates insights

import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { Trade } from './trade-tracker.ts';

// Types for performance analysis
export interface PerformanceMetrics {
  id?: string;
  user_id?: string;
  date: string;
  timeframe: string;
  total_trades: number;
  successful_trades: number;
  failed_trades: number;
  win_rate: number;
  avg_profit_loss: number;
  avg_profit_loss_percent: number;
  avg_risk_reward_ratio: number;
  
  // Performance by signal type
  long_trades: number;
  long_successful: number;
  long_win_rate: number;
  short_trades: number;
  short_successful: number;
  short_win_rate: number;
  
  // Performance by confidence level
  high_confidence_trades: number;
  high_confidence_win_rate: number;
  medium_confidence_trades: number;
  medium_confidence_win_rate: number;
  low_confidence_trades: number;
  low_confidence_win_rate: number;
}

export interface PatternPerformance {
  pattern: string;
  total_trades: number;
  successful_trades: number;
  failed_trades: number;
  win_rate: number;
  avg_profit_loss: number;
  avg_profit_loss_percent: number;
}

export class PerformanceAnalyzer {
  private supabase;
  
  constructor(supabaseUrl: string, supabaseKey: string) {
    this.supabase = createClient(supabaseUrl, supabaseKey);
  }
  
  /**
   * Analyze performance for a specific date range
   */
  async analyzePerformance(
    startDate: string, 
    endDate: string,
    timeframe: string = 'day',
    userId?: string
  ): Promise<{ success: boolean, data?: PerformanceMetrics, error?: any }> {
    try {
      // Query to get trades in the date range
      let query = this.supabase
        .from('aura_trades')
        .select('*')
        .gte('created_at', startDate)
        .lte('created_at', endDate)
        .eq('timeframe', timeframe)
        .not('exit_price', 'is', null); // Only analyze closed trades
      
      // Add user filter if provided
      if (userId) {
        query = query.eq('user_id', userId);
      }
      
      const { data: trades, error } = await query;
      
      if (error) throw error;
      
      // Calculate performance metrics
      const metrics = this.calculateMetrics(trades, timeframe);
      
      // If userId is provided, save the metrics to the database
      if (userId) {
        await this.savePerformanceMetrics({
          ...metrics,
          user_id: userId,
          date: endDate // Use the end date as the reference date
        });
      }
      
      return { success: true, data: metrics };
    } catch (error) {
      console.error('Error analyzing performance:', error);
      return { success: false, error };
    }
  }
  
  /**
   * Calculate performance metrics from a set of trades
   */
  private calculateMetrics(trades: Trade[], timeframe: string): PerformanceMetrics {
    // Initialize metrics
    const metrics: PerformanceMetrics = {
      date: new Date().toISOString().split('T')[0],
      timeframe,
      total_trades: trades.length,
      successful_trades: 0,
      failed_trades: 0,
      win_rate: 0,
      avg_profit_loss: 0,
      avg_profit_loss_percent: 0,
      avg_risk_reward_ratio: 0,
      
      long_trades: 0,
      long_successful: 0,
      long_win_rate: 0,
      short_trades: 0,
      short_successful: 0,
      short_win_rate: 0,
      
      high_confidence_trades: 0,
      high_confidence_win_rate: 0,
      medium_confidence_trades: 0,
      medium_confidence_win_rate: 0,
      low_confidence_trades: 0,
      low_confidence_win_rate: 0
    };
    
    if (trades.length === 0) {
      return metrics;
    }
    
    // Calculate basic metrics
    let totalProfitLoss = 0;
    let totalProfitLossPercent = 0;
    let totalRiskReward = 0;
    
    // Count by signal type and confidence
    let highConfidenceSuccessful = 0;
    let mediumConfidenceSuccessful = 0;
    let lowConfidenceSuccessful = 0;
    
    for (const trade of trades) {
      // Skip trades without outcome data
      if (trade.profit_loss === undefined || trade.successful === undefined) {
        continue;
      }
      
      // Count successful and failed trades
      if (trade.successful) {
        metrics.successful_trades++;
      } else {
        metrics.failed_trades++;
      }
      
      // Accumulate profit/loss
      totalProfitLoss += trade.profit_loss || 0;
      totalProfitLossPercent += trade.profit_loss_percent || 0;
      totalRiskReward += trade.risk_reward_ratio || 0;
      
      // Count by signal type
      if (trade.signal === 'LONG') {
        metrics.long_trades++;
        if (trade.successful) {
          metrics.long_successful++;
        }
      } else if (trade.signal === 'SHORT') {
        metrics.short_trades++;
        if (trade.successful) {
          metrics.short_successful++;
        }
      }
      
      // Count by confidence level
      if (trade.confidence === 'HIGH') {
        metrics.high_confidence_trades++;
        if (trade.successful) {
          highConfidenceSuccessful++;
        }
      } else if (trade.confidence === 'MEDIUM') {
        metrics.medium_confidence_trades++;
        if (trade.successful) {
          mediumConfidenceSuccessful++;
        }
      } else if (trade.confidence === 'LOW') {
        metrics.low_confidence_trades++;
        if (trade.successful) {
          lowConfidenceSuccessful++;
        }
      }
    }
    
    // Calculate averages and rates
    metrics.win_rate = metrics.successful_trades / metrics.total_trades;
    metrics.avg_profit_loss = totalProfitLoss / metrics.total_trades;
    metrics.avg_profit_loss_percent = totalProfitLossPercent / metrics.total_trades;
    metrics.avg_risk_reward_ratio = totalRiskReward / metrics.total_trades;
    
    // Calculate win rates by signal type
    metrics.long_win_rate = metrics.long_trades > 0 ? metrics.long_successful / metrics.long_trades : 0;
    metrics.short_win_rate = metrics.short_trades > 0 ? metrics.short_successful / metrics.short_trades : 0;
    
    // Calculate win rates by confidence level
    metrics.high_confidence_win_rate = metrics.high_confidence_trades > 0 ? 
      highConfidenceSuccessful / metrics.high_confidence_trades : 0;
    metrics.medium_confidence_win_rate = metrics.medium_confidence_trades > 0 ? 
      mediumConfidenceSuccessful / metrics.medium_confidence_trades : 0;
    metrics.low_confidence_win_rate = metrics.low_confidence_trades > 0 ? 
      lowConfidenceSuccessful / metrics.low_confidence_trades : 0;
    
    return metrics;
  }
  
  /**
   * Save performance metrics to the database
   */
  private async savePerformanceMetrics(metrics: PerformanceMetrics): Promise<void> {
    try {
      await this.supabase
        .from('aura_performance')
        .insert(metrics);
    } catch (error) {
      console.error('Error saving performance metrics:', error);
    }
  }
  
  /**
   * Analyze performance by market structure
   */
  async analyzeByMarketStructure(
    startDate: string, 
    endDate: string,
    timeframe: string = 'day'
  ): Promise<{ success: boolean, data?: any, error?: any }> {
    try {
      const { data: trades, error } = await this.supabase
        .from('aura_trades')
        .select('*')
        .gte('created_at', startDate)
        .lte('created_at', endDate)
        .eq('timeframe', timeframe)
        .not('exit_price', 'is', null)
        .not('market_structure', 'is', null);
      
      if (error) throw error;
      
      // Group trades by market structure
      const structureMap: { [key: string]: Trade[] } = {};
      
      for (const trade of trades) {
        if (!trade.market_structure) continue;
        
        if (!structureMap[trade.market_structure]) {
          structureMap[trade.market_structure] = [];
        }
        
        structureMap[trade.market_structure].push(trade);
      }
      
      // Calculate performance for each market structure
      const results: any[] = [];
      
      for (const [structure, structureTrades] of Object.entries(structureMap)) {
        const totalTrades = structureTrades.length;
        const successfulTrades = structureTrades.filter(t => t.successful).length;
        const winRate = totalTrades > 0 ? successfulTrades / totalTrades : 0;
        
        const avgProfitLoss = structureTrades.reduce((sum, t) => sum + (t.profit_loss || 0), 0) / totalTrades;
        const avgProfitLossPercent = structureTrades.reduce((sum, t) => sum + (t.profit_loss_percent || 0), 0) / totalTrades;
        
        results.push({
          market_structure: structure,
          total_trades: totalTrades,
          successful_trades: successfulTrades,
          win_rate: winRate,
          avg_profit_loss: avgProfitLoss,
          avg_profit_loss_percent: avgProfitLossPercent
        });
      }
      
      return { success: true, data: results };
    } catch (error) {
      console.error('Error analyzing by market structure:', error);
      return { success: false, error };
    }
  }
  
  /**
   * Get historical performance metrics
   */
  async getHistoricalPerformance(
    userId: string,
    timeframe: string = 'day',
    limit: number = 30
  ): Promise<{ success: boolean, data?: any, error?: any }> {
    try {
      const { data, error } = await this.supabase
        .from('aura_performance')
        .select('*')
        .eq('user_id', userId)
        .eq('timeframe', timeframe)
        .order('date', { ascending: false })
        .limit(limit);
      
      if (error) throw error;
      
      return { success: true, data };
    } catch (error) {
      console.error('Error fetching historical performance:', error);
      return { success: false, error };
    }
  }
  
  /**
   * Analyze performance by pattern
   */
  async analyzeByPattern(
    startDate: string, 
    endDate: string,
    timeframe: string = 'day'
  ): Promise<{ success: boolean, data?: PatternPerformance[], error?: any }> {
    try {
      // This would require a more complex implementation with pattern recognition
      // For now, we'll return a placeholder
      return { 
        success: true, 
        data: [
          {
            pattern: "Double Bottom",
            total_trades: 0,
            successful_trades: 0,
            failed_trades: 0,
            win_rate: 0,
            avg_profit_loss: 0,
            avg_profit_loss_percent: 0
          }
        ] 
      };
    } catch (error) {
      console.error('Error analyzing by pattern:', error);
      return { success: false, error };
    }
  }
}
