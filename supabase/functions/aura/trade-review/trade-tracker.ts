// Trade Tracking Module for Aura
// This module handles recording, updating, and retrieving trade data

import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// Types for trade tracking
export interface Trade {
  id?: string;
  user_id: string;
  symbol: string;
  signal: 'LONG' | 'SHORT' | 'NEUTRAL';
  confidence: 'LOW' | 'MEDIUM' | 'HIGH';
  entry_price: number;
  stop_loss?: number;
  take_profit?: number;
  risk_reward_ratio?: number;
  timeframe: string;
  created_at?: string;
  
  // Trade outcome fields
  closed_at?: string;
  exit_price?: number;
  exit_reason?: 'TAKE_PROFIT' | 'STOP_LOSS' | 'MANUAL' | 'TIMEOUT';
  profit_loss?: number;
  profit_loss_percent?: number;
  successful?: boolean;
  
  // Analysis fields
  market_structure?: string;
  key_support?: number;
  key_resistance?: number;
  
  // Review fields
  reviewed?: boolean;
  review_notes?: string;
  improvement_suggestions?: string;
}

export class TradeTracker {
  private supabase;
  
  constructor(supabaseUrl: string, supabaseKey: string) {
    this.supabase = createClient(supabaseUrl, supabaseKey);
  }
  
  /**
   * Record a new trade recommendation
   */
  async recordTrade(trade: Trade): Promise<{ success: boolean, data?: any, error?: any }> {
    try {
      const { data, error } = await this.supabase
        .from('aura_trades')
        .insert(trade)
        .select()
        .single();
      
      if (error) throw error;
      
      return { success: true, data };
    } catch (error) {
      console.error('Error recording trade:', error);
      return { success: false, error };
    }
  }
  
  /**
   * Update a trade with outcome information
   */
  async updateTradeOutcome(
    tradeId: string, 
    exitPrice: number, 
    exitReason: 'TAKE_PROFIT' | 'STOP_LOSS' | 'MANUAL' | 'TIMEOUT'
  ): Promise<{ success: boolean, data?: any, error?: any }> {
    try {
      // First, get the trade to calculate profit/loss
      const { data: trade, error: fetchError } = await this.supabase
        .from('aura_trades')
        .select('*')
        .eq('id', tradeId)
        .single();
      
      if (fetchError) throw fetchError;
      
      // Calculate profit/loss
      const entryPrice = trade.entry_price;
      const signal = trade.signal;
      
      let profitLoss = 0;
      let profitLossPercent = 0;
      let successful = false;
      
      if (signal === 'LONG') {
        profitLoss = exitPrice - entryPrice;
        profitLossPercent = (profitLoss / entryPrice) * 100;
        successful = profitLoss > 0;
      } else if (signal === 'SHORT') {
        profitLoss = entryPrice - exitPrice;
        profitLossPercent = (profitLoss / entryPrice) * 100;
        successful = profitLoss > 0;
      }
      
      // Update the trade
      const { data, error } = await this.supabase
        .from('aura_trades')
        .update({
          closed_at: new Date().toISOString(),
          exit_price: exitPrice,
          exit_reason: exitReason,
          profit_loss: profitLoss,
          profit_loss_percent: profitLossPercent,
          successful
        })
        .eq('id', tradeId)
        .select()
        .single();
      
      if (error) throw error;
      
      return { success: true, data };
    } catch (error) {
      console.error('Error updating trade outcome:', error);
      return { success: false, error };
    }
  }
  
  /**
   * Get all trades for a user
   */
  async getUserTrades(userId: string): Promise<{ success: boolean, data?: any, error?: any }> {
    try {
      const { data, error } = await this.supabase
        .from('aura_trades')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      return { success: true, data };
    } catch (error) {
      console.error('Error fetching user trades:', error);
      return { success: false, error };
    }
  }
  
  /**
   * Get all open trades (trades without an exit price)
   */
  async getOpenTrades(userId: string): Promise<{ success: boolean, data?: any, error?: any }> {
    try {
      const { data, error } = await this.supabase
        .from('aura_trades')
        .select('*')
        .eq('user_id', userId)
        .is('exit_price', null)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      return { success: true, data };
    } catch (error) {
      console.error('Error fetching open trades:', error);
      return { success: false, error };
    }
  }
  
  /**
   * Get all closed trades (trades with an exit price)
   */
  async getClosedTrades(userId: string): Promise<{ success: boolean, data?: any, error?: any }> {
    try {
      const { data, error } = await this.supabase
        .from('aura_trades')
        .select('*')
        .eq('user_id', userId)
        .not('exit_price', 'is', null)
        .order('closed_at', { ascending: false });
      
      if (error) throw error;
      
      return { success: true, data };
    } catch (error) {
      console.error('Error fetching closed trades:', error);
      return { success: false, error };
    }
  }
  
  /**
   * Get trades that need to be reviewed (closed but not reviewed)
   */
  async getTradesToReview(): Promise<{ success: boolean, data?: any, error?: any }> {
    try {
      const { data, error } = await this.supabase
        .from('aura_trades')
        .select('*')
        .not('exit_price', 'is', null)
        .eq('reviewed', false)
        .order('closed_at', { ascending: false });
      
      if (error) throw error;
      
      return { success: true, data };
    } catch (error) {
      console.error('Error fetching trades to review:', error);
      return { success: false, error };
    }
  }
  
  /**
   * Mark a trade as reviewed and add review notes
   */
  async reviewTrade(
    tradeId: string, 
    reviewNotes: string, 
    improvementSuggestions: string
  ): Promise<{ success: boolean, data?: any, error?: any }> {
    try {
      const { data, error } = await this.supabase
        .from('aura_trades')
        .update({
          reviewed: true,
          review_notes: reviewNotes,
          improvement_suggestions: improvementSuggestions
        })
        .eq('id', tradeId)
        .select()
        .single();
      
      if (error) throw error;
      
      return { success: true, data };
    } catch (error) {
      console.error('Error reviewing trade:', error);
      return { success: false, error };
    }
  }
  
  /**
   * Get trades by symbol
   */
  async getTradesBySymbol(symbol: string): Promise<{ success: boolean, data?: any, error?: any }> {
    try {
      const { data, error } = await this.supabase
        .from('aura_trades')
        .select('*')
        .eq('symbol', symbol)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      return { success: true, data };
    } catch (error) {
      console.error('Error fetching trades by symbol:', error);
      return { success: false, error };
    }
  }
  
  /**
   * Get trades by date range
   */
  async getTradesByDateRange(
    startDate: string, 
    endDate: string
  ): Promise<{ success: boolean, data?: any, error?: any }> {
    try {
      const { data, error } = await this.supabase
        .from('aura_trades')
        .select('*')
        .gte('created_at', startDate)
        .lte('created_at', endDate)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      return { success: true, data };
    } catch (error) {
      console.error('Error fetching trades by date range:', error);
      return { success: false, error };
    }
  }
}
