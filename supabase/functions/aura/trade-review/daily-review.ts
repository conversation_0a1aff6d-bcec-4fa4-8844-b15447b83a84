// Daily Review Module for Aura
// This module runs a daily review of trades and updates the learning system

import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { TradeTracker } from './trade-tracker.ts';
import { PerformanceAnalyzer } from './performance-analyzer.ts';
import { LearningEngine, LearningInsight } from './learning-engine.ts';

export class DailyReview {
  private supabase;
  private tradeTracker: TradeTracker;
  private performanceAnalyzer: PerformanceAnalyzer;
  private learningEngine: LearningEngine;
  
  constructor(supabaseUrl: string, supabaseKey: string) {
    this.supabase = createClient(supabaseUrl, supabaseKey);
    this.tradeTracker = new TradeTracker(supabaseUrl, supabaseKey);
    this.performanceAnalyzer = new PerformanceAnalyzer(supabaseUrl, supabaseKey);
    this.learningEngine = new LearningEngine(supabaseUrl, supabaseKey);
  }
  
  /**
   * Run the daily review process
   */
  async runDailyReview(): Promise<{ success: boolean, data?: any, error?: any }> {
    try {
      console.log('Starting daily review process...');
      
      // Step 1: Check for trades that need to be closed automatically
      await this.checkForTradeClosures();
      
      // Step 2: Review all pending trades
      const reviewResult = await this.learningEngine.reviewAllPendingTrades();
      console.log(`Reviewed ${reviewResult.data?.reviewed_count || 0} trades`);
      
      // Step 3: Analyze performance for the last day, week, and month
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      
      const lastWeek = new Date(today);
      lastWeek.setDate(lastWeek.getDate() - 7);
      
      const lastMonth = new Date(today);
      lastMonth.setMonth(lastMonth.getMonth() - 1);
      
      const todayStr = today.toISOString().split('T')[0];
      const yesterdayStr = yesterday.toISOString().split('T')[0];
      const lastWeekStr = lastWeek.toISOString().split('T')[0];
      const lastMonthStr = lastMonth.toISOString().split('T')[0];
      
      // Analyze daily performance
      const dailyPerformance = await this.performanceAnalyzer.analyzePerformance(
        yesterdayStr, 
        todayStr,
        'day'
      );
      
      // Analyze weekly performance
      const weeklyPerformance = await this.performanceAnalyzer.analyzePerformance(
        lastWeekStr, 
        todayStr,
        'day'
      );
      
      // Analyze monthly performance
      const monthlyPerformance = await this.performanceAnalyzer.analyzePerformance(
        lastMonthStr, 
        todayStr,
        'day'
      );
      
      console.log('Performance analysis complete');
      
      // Step 4: Generate insights from performance data
      const dailyInsights = await this.learningEngine.generateInsights(dailyPerformance.data!);
      const weeklyInsights = await this.learningEngine.generateInsights(weeklyPerformance.data!);
      const monthlyInsights = await this.learningEngine.generateInsights(monthlyPerformance.data!);
      
      // Combine insights, prioritizing longer-term insights
      const allInsights = this.combineInsights(dailyInsights, weeklyInsights, monthlyInsights);
      
      console.log(`Generated ${allInsights.length} insights`);
      
      // Step 5: Save insights as learning data
      for (const insight of allInsights) {
        await this.learningEngine.saveLearningData({
          date: todayStr,
          timeframe: 'day',
          pattern: insight.category,
          success_rate: insight.adjustment_factor > 0 ? 1 : 0,
          failure_rate: insight.adjustment_factor < 0 ? 1 : 0,
          adjustment_factor: insight.adjustment_factor,
          notes: insight.insight
        });
      }
      
      console.log('Daily review process completed successfully');
      
      return { 
        success: true, 
        data: {
          reviewed_trades: reviewResult.data?.reviewed_count || 0,
          daily_performance: dailyPerformance.data,
          weekly_performance: weeklyPerformance.data,
          monthly_performance: monthlyPerformance.data,
          insights: allInsights
        } 
      };
    } catch (error) {
      console.error('Error running daily review:', error);
      return { success: false, error };
    }
  }
  
  /**
   * Check for trades that need to be closed automatically
   */
  private async checkForTradeClosures(): Promise<void> {
    try {
      // Get all open trades
      const { data: openTrades } = await this.tradeTracker.getOpenTrades('system');
      
      if (!openTrades || openTrades.length === 0) {
        return;
      }
      
      // For each open trade, check if it needs to be closed
      for (const trade of openTrades) {
        // Skip trades without stop loss or take profit
        if (!trade.stop_loss || !trade.take_profit) {
          continue;
        }
        
        // Get the latest price for the symbol
        // This would require an API call to get the current price
        // For now, we'll use a placeholder
        const currentPrice = 0; // Replace with actual price
        
        // Check if the price hit stop loss or take profit
        if (trade.signal === 'LONG') {
          if (currentPrice <= trade.stop_loss) {
            // Hit stop loss
            await this.tradeTracker.updateTradeOutcome(
              trade.id!,
              trade.stop_loss,
              'STOP_LOSS'
            );
          } else if (currentPrice >= trade.take_profit) {
            // Hit take profit
            await this.tradeTracker.updateTradeOutcome(
              trade.id!,
              trade.take_profit,
              'TAKE_PROFIT'
            );
          }
        } else if (trade.signal === 'SHORT') {
          if (currentPrice >= trade.stop_loss) {
            // Hit stop loss
            await this.tradeTracker.updateTradeOutcome(
              trade.id!,
              trade.stop_loss,
              'STOP_LOSS'
            );
          } else if (currentPrice <= trade.take_profit) {
            // Hit take profit
            await this.tradeTracker.updateTradeOutcome(
              trade.id!,
              trade.take_profit,
              'TAKE_PROFIT'
            );
          }
        }
      }
    } catch (error) {
      console.error('Error checking for trade closures:', error);
    }
  }
  
  /**
   * Combine insights from different timeframes, prioritizing longer-term insights
   */
  private combineInsights(
    dailyInsights: LearningInsight[],
    weeklyInsights: LearningInsight[],
    monthlyInsights: LearningInsight[]
  ): LearningInsight[] {
    const combinedInsights: LearningInsight[] = [];
    const categoryMap: { [key: string]: LearningInsight } = {};
    
    // Add monthly insights first (highest priority)
    for (const insight of monthlyInsights) {
      categoryMap[insight.category] = {
        ...insight,
        adjustment_factor: insight.adjustment_factor * 1.5, // Give more weight to monthly insights
        confidence: insight.confidence * 1.2
      };
    }
    
    // Add weekly insights
    for (const insight of weeklyInsights) {
      if (categoryMap[insight.category]) {
        // Average with existing insight
        const existing = categoryMap[insight.category];
        categoryMap[insight.category] = {
          ...existing,
          insight: existing.insight, // Keep the monthly insight text
          adjustment_factor: (existing.adjustment_factor + insight.adjustment_factor) / 2,
          confidence: (existing.confidence + insight.confidence) / 2
        };
      } else {
        categoryMap[insight.category] = {
          ...insight,
          adjustment_factor: insight.adjustment_factor * 1.2, // Give more weight to weekly insights
          confidence: insight.confidence * 1.1
        };
      }
    }
    
    // Add daily insights
    for (const insight of dailyInsights) {
      if (categoryMap[insight.category]) {
        // Average with existing insight, but with less weight
        const existing = categoryMap[insight.category];
        categoryMap[insight.category] = {
          ...existing,
          insight: existing.insight, // Keep the longer-term insight text
          adjustment_factor: (existing.adjustment_factor * 2 + insight.adjustment_factor) / 3,
          confidence: (existing.confidence * 2 + insight.confidence) / 3
        };
      } else {
        categoryMap[insight.category] = insight;
      }
    }
    
    // Convert map back to array
    for (const category in categoryMap) {
      combinedInsights.push(categoryMap[category]);
    }
    
    return combinedInsights;
  }
}
