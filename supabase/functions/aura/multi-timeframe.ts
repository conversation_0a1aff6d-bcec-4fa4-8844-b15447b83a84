// Multi-Timeframe Analysis

import { Candle } from './candlestick-patterns.ts';

// Interface for multi-timeframe analysis
export interface MultiTimeframeAnalysis {
  timeframes: string[];
  trends: Record<string, string>;
  conflictWarnings: string[];
  dominantTrend: string;
  nestedPatterns: string[];
  confirmation: {
    bullish: boolean;
    bearish: boolean;
    neutral: boolean;
    strength: 'weak' | 'moderate' | 'strong';
  };
}

// Determine trend for a specific timeframe
function determineTrend(candles: Candle[]): string {
  // For monthly data, we need a different approach since we won't have many candles
  if (candles.length < 6) {
    // For very limited data, use a simple approach based on recent price movement
    const lastCandle = candles[candles.length - 1];
    const firstCandle = candles[0];

    if (lastCandle.close > firstCandle.close * 1.05) {
      return 'bullish'; // 5% or more increase
    } else if (lastCandle.close < firstCandle.close * 0.95) {
      return 'bearish'; // 5% or more decrease
    } else {
      return 'neutral';
    }
  }

  // For timeframes with more data but still less than 20 candles
  if (candles.length < 20) {
    // Use a shorter lookback period
    const shortPeriod = Math.max(3, Math.floor(candles.length / 3));
    const mediumPeriod = Math.max(5, Math.floor(candles.length / 2));

    // Calculate short SMA
    let shortSum = 0;
    for (let i = candles.length - shortPeriod; i < candles.length; i++) {
      shortSum += candles[i].close;
    }
    const shortSMA = shortSum / shortPeriod;

    // Calculate medium SMA
    let mediumSum = 0;
    for (let i = candles.length - mediumPeriod; i < candles.length; i++) {
      mediumSum += candles[i].close;
    }
    const mediumSMA = mediumSum / mediumPeriod;

    // Get current price
    const currentPrice = candles[candles.length - 1].close;

    if (currentPrice > shortSMA && shortSMA > mediumSMA) {
      return 'bullish';
    } else if (currentPrice < shortSMA && shortSMA < mediumSMA) {
      return 'bearish';
    } else {
      return 'neutral';
    }
  }

  // For timeframes with enough data, use the original approach
  const shortPeriod = 20;
  const longPeriod = Math.min(50, Math.floor(candles.length * 0.8));

  // Calculate short SMA
  let shortSum = 0;
  for (let i = candles.length - shortPeriod; i < candles.length; i++) {
    shortSum += candles[i].close;
  }
  const shortSMA = shortSum / shortPeriod;

  // Calculate long SMA
  let longSum = 0;
  for (let i = candles.length - longPeriod; i < candles.length; i++) {
    if (i < 0) continue;
    longSum += candles[i].close;
  }
  const longSMA = longSum / longPeriod;

  // Get current price
  const currentPrice = candles[candles.length - 1].close;

  // Determine trend based on price relative to SMAs
  if (currentPrice > shortSMA && shortSMA > longSMA) {
    return 'strong_bullish';
  } else if (currentPrice > shortSMA) {
    return 'bullish';
  } else if (currentPrice < shortSMA && shortSMA < longSMA) {
    return 'strong_bearish';
  } else if (currentPrice < shortSMA) {
    return 'bearish';
  } else {
    return 'neutral';
  }
}

// Check for trend conflicts across timeframes
function checkTrendConflicts(trends: Record<string, string>): string[] {
  const conflicts: string[] = [];
  const timeframes = Object.keys(trends);

  // Check for conflicts between adjacent timeframes
  for (let i = 0; i < timeframes.length - 1; i++) {
    const currentTrend = trends[timeframes[i]];
    const higherTrend = trends[timeframes[i + 1]];

    // Skip undefined trends
    if (currentTrend === 'undefined' || higherTrend === 'undefined') {
      continue;
    }

    // Check if trends are in opposite directions
    if (
      (currentTrend.includes('bullish') && higherTrend.includes('bearish')) ||
      (currentTrend.includes('bearish') && higherTrend.includes('bullish'))
    ) {
      conflicts.push(`Conflict between ${timeframes[i]} (${currentTrend}) and ${timeframes[i + 1]} (${higherTrend})`);
    }
  }

  return conflicts;
}

// Determine the dominant trend across timeframes
function determineDominantTrend(trends: Record<string, string>): string {
  // Higher timeframes have more weight
  const timeframes = Object.keys(trends);
  const highestTimeframe = timeframes[timeframes.length - 1];

  // Start with the highest timeframe's trend
  let dominantTrend = trends[highestTimeframe];

  // If highest timeframe is undefined or neutral, check the next one down
  if ((dominantTrend === 'undefined' || dominantTrend === 'neutral') && timeframes.length > 1) {
    dominantTrend = trends[timeframes[timeframes.length - 2]];

    // If second highest is also undefined or neutral, use the lowest timeframe
    if ((dominantTrend === 'undefined' || dominantTrend === 'neutral') && timeframes.length > 2) {
      dominantTrend = trends[timeframes[0]];
    }
  }

  // If all timeframes are undefined or neutral, default to neutral
  if (dominantTrend === 'undefined') {
    dominantTrend = 'neutral';
  }

  return dominantTrend;
}

// Analyze multiple timeframes
export function analyzeMultipleTimeframes(
  timeframeData: Record<string, Candle[]>
): MultiTimeframeAnalysis {
  const timeframes = Object.keys(timeframeData);
  const trends: Record<string, string> = {};

  // Determine trend for each timeframe
  for (const timeframe of timeframes) {
    trends[timeframe] = determineTrend(timeframeData[timeframe]);
  }

  // Check for conflicts
  const conflicts = checkTrendConflicts(trends);

  // Determine dominant trend
  const dominantTrend = determineDominantTrend(trends);

  // Check for nested patterns
  const nestedPatterns: string[] = findNestedPatterns(timeframeData);

  // Determine confirmation across timeframes
  const confirmation = determineConfirmation(trends);

  return {
    timeframes,
    trends,
    conflictWarnings: conflicts,
    dominantTrend,
    nestedPatterns,
    confirmation
  };
}

// Find nested patterns across timeframes
function findNestedPatterns(timeframeData: Record<string, Candle[]>): string[] {
  const patterns: string[] = [];
  const timeframes = Object.keys(timeframeData);

  // This is a simplified implementation
  // In a full implementation, you would look for specific patterns in each timeframe
  // and then check if they form nested structures

  // For now, we'll just check for basic trend alignment
  if (timeframes.length >= 3) {
    const shortTerm = determineTrend(timeframeData[timeframes[0]]);
    const mediumTerm = determineTrend(timeframeData[timeframes[1]]);
    const longTerm = determineTrend(timeframeData[timeframes[2]]);

    // Check for aligned bullish trends
    if (
      shortTerm.includes('bullish') &&
      mediumTerm.includes('bullish') &&
      longTerm.includes('bullish')
    ) {
      patterns.push('nested_bullish_trend');
    }

    // Check for aligned bearish trends
    if (
      shortTerm.includes('bearish') &&
      mediumTerm.includes('bearish') &&
      longTerm.includes('bearish')
    ) {
      patterns.push('nested_bearish_trend');
    }

    // Check for potential reversal setup
    if (
      shortTerm.includes('bullish') &&
      mediumTerm.includes('neutral') &&
      longTerm.includes('bearish')
    ) {
      patterns.push('potential_bullish_reversal');
    }

    if (
      shortTerm.includes('bearish') &&
      mediumTerm.includes('neutral') &&
      longTerm.includes('bullish')
    ) {
      patterns.push('potential_bearish_reversal');
    }
  }

  return patterns;
}

// Determine confirmation across timeframes
function determineConfirmation(trends: Record<string, string>): {
  bullish: boolean;
  bearish: boolean;
  neutral: boolean;
  strength: 'weak' | 'moderate' | 'strong';
} {
  let bullishCount = 0;
  let bearishCount = 0;
  let neutralCount = 0;
  let undefinedCount = 0;

  // Count trend types
  for (const trend of Object.values(trends)) {
    if (trend === 'undefined') {
      undefinedCount++;
    } else if (trend.includes('bullish')) {
      bullishCount++;
    } else if (trend.includes('bearish')) {
      bearishCount++;
    } else {
      neutralCount++;
    }
  }

  // Determine confirmation - exclude undefined trends from calculation
  const validTimeframes = Object.keys(trends).length - undefinedCount;

  // If all trends are undefined, return neutral with weak strength
  if (validTimeframes === 0) {
    return {
      bullish: false,
      bearish: false,
      neutral: true,
      strength: 'weak'
    };
  }

  const bullishConfirmation = bullishCount / validTimeframes > 0.5;
  const bearishConfirmation = bearishCount / validTimeframes > 0.5;
  const neutralConfirmation = neutralCount / validTimeframes > 0.5;

  // Determine strength
  let strength: 'weak' | 'moderate' | 'strong' = 'weak';

  if (bullishConfirmation && bullishCount === validTimeframes) {
    strength = 'strong';
  } else if (bearishConfirmation && bearishCount === validTimeframes) {
    strength = 'strong';
  } else if (bullishConfirmation && bullishCount >= validTimeframes * 0.75) {
    strength = 'moderate';
  } else if (bearishConfirmation && bearishCount >= validTimeframes * 0.75) {
    strength = 'moderate';
  }

  // If we have too many undefined timeframes, reduce strength
  if (undefinedCount > 0 && undefinedCount >= Object.keys(trends).length / 3) {
    strength = 'weak';
  }

  return {
    bullish: bullishConfirmation,
    bearish: bearishConfirmation,
    neutral: neutralConfirmation,
    strength
  };
}
