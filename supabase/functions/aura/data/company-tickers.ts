// Company Tickers List
// This file extracts stock tickers from the SEC company_tickers.json file

import companyTickersData from '../examples/company_tickers.json' assert { type: "json" };

// Extract tickers from the company_tickers.json file
export const extractCompanyTickers = (): string[] => {
  const tickers: string[] = [];
  
  // The JSON structure has numeric keys as strings (e.g., "0", "1", "2", etc.)
  // Each entry contains a "ticker" field with the stock symbol
  for (const key in companyTickersData) {
    if (Object.prototype.hasOwnProperty.call(companyTickersData, key)) {
      const entry = companyTickersData[key];
      if (entry && entry.ticker) {
        // Some tickers have special characters that might cause issues
        // Filter out tickers with hyphens or other special characters
        const ticker = entry.ticker.trim();
        if (ticker && !ticker.includes('-')) {
          tickers.push(ticker);
        }
      }
    }
  }
  
  return tickers;
};

// Export the extracted tickers
export const COMPANY_TICKERS = extractCompanyTickers();

// Note: This list is extracted from the SEC company_tickers.json file
// It includes a wide range of stocks from various exchanges
