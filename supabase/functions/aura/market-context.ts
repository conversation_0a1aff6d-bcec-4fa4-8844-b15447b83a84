// Market Context & Environment Analysis
// Implements advanced market context detection including range/trend identification,
// volatility regime classification, and volume profile analysis

import { Candle } from './candlestick-patterns.ts';

// Interface for market context analysis
export interface MarketContextAnalysis {
  // Market environment
  environment: 'range' | 'trending' | 'breakout' | 'reversal';
  environmentConfidence: 'low' | 'medium' | 'high';

  // Volatility regime
  volatilityRegime: 'low' | 'normal' | 'high';
  volatilityScore: number; // 0-100 scale

  // Range analysis
  rangeHigh?: number;
  rangeLow?: number;
  rangeStrength?: 'weak' | 'moderate' | 'strong';

  // Trend analysis
  trendDirection?: 'up' | 'down';
  trendStrength?: 'weak' | 'moderate' | 'strong';
  trendDuration?: number; // in candles

  // Breakout analysis
  breakoutDirection?: 'up' | 'down';
  breakoutStrength?: 'weak' | 'moderate' | 'strong';
  breakoutLevel?: number;

  // Volume analysis
  volumeProfile: {
    highVolumeNodes: Array<{price: number, volume: number, significance: 'low' | 'medium' | 'high'}>;
    lowVolumeNodes: Array<{price: number, volume: number}>;
    volumeAtPrice: Array<{price: number, volume: number, relativeVolume: number}>;
    pointOfControl: number; // Price with highest volume
  };
}

/**
 * Analyzes the market context and environment
 * Implements Al Brooks-inspired price action analysis
 */
export function analyzeMarketContext(candles: Candle[]): MarketContextAnalysis {
  // Need at least 50 candles for meaningful analysis
  if (candles.length < 50) {
    throw new Error('Insufficient data for market context analysis');
  }

  // Calculate ATR for volatility measurement
  const atr = calculateATR(candles, 14);

  // Detect if market is in a range or trending
  const {
    environment,
    environmentConfidence,
    rangeHigh,
    rangeLow,
    rangeStrength,
    trendDirection,
    trendStrength,
    trendDuration,
    breakoutDirection,
    breakoutStrength,
    breakoutLevel
  } = detectMarketEnvironment(candles, atr);

  // Classify volatility regime
  const { volatilityRegime, volatilityScore } = classifyVolatilityRegime(candles, atr);

  // Analyze volume profile
  const volumeProfile = analyzeVolumeProfile(candles);

  return {
    environment,
    environmentConfidence,
    volatilityRegime,
    volatilityScore,
    rangeHigh,
    rangeLow,
    rangeStrength,
    trendDirection,
    trendStrength,
    trendDuration,
    breakoutDirection,
    breakoutStrength,
    breakoutLevel,
    volumeProfile
  };
}

/**
 * Calculate Average True Range (ATR)
 */
function calculateATR(candles: Candle[], period: number): number[] {
  const trueRanges: number[] = [];
  const atrs: number[] = [];

  // Calculate true ranges
  for (let i = 0; i < candles.length; i++) {
    if (i === 0) {
      // First candle - true range is simply high - low
      trueRanges.push(candles[i].high - candles[i].low);
    } else {
      // True range is the greatest of:
      // 1. Current high - current low
      // 2. Current high - previous close
      // 3. Previous close - current low
      const tr1 = candles[i].high - candles[i].low;
      const tr2 = Math.abs(candles[i].high - candles[i-1].close);
      const tr3 = Math.abs(candles[i-1].close - candles[i].low);
      trueRanges.push(Math.max(tr1, tr2, tr3));
    }
  }

  // Calculate ATR using simple moving average
  for (let i = 0; i < candles.length; i++) {
    if (i < period - 1) {
      atrs.push(NaN); // Not enough data yet
    } else {
      let sum = 0;
      for (let j = 0; j < period; j++) {
        sum += trueRanges[i - j];
      }
      atrs.push(sum / period);
    }
  }

  return atrs;
}

/**
 * Detect if the market is in a range or trending
 * Implements advanced market structure analysis
 */
function detectMarketEnvironment(candles: Candle[], atr: number[]): {
  environment: 'range' | 'trending' | 'breakout' | 'reversal';
  environmentConfidence: 'low' | 'medium' | 'high';
  rangeHigh?: number;
  rangeLow?: number;
  rangeStrength?: 'weak' | 'moderate' | 'strong';
  trendDirection?: 'up' | 'down';
  trendStrength?: 'weak' | 'moderate' | 'strong';
  trendDuration?: number;
  breakoutDirection?: 'up' | 'down';
  breakoutStrength?: 'weak' | 'moderate' | 'strong';
  breakoutLevel?: number;
} {
  // Focus on recent price action (last 50 candles)
  const recentCandles = candles.slice(-50);
  const lastIndex = recentCandles.length - 1;

  // Find swing highs and lows
  const swingHighs: number[] = [];
  const swingLows: number[] = [];

  for (let i = 2; i < recentCandles.length - 2; i++) {
    // Swing high
    if (
      recentCandles[i].high > recentCandles[i-1].high &&
      recentCandles[i].high > recentCandles[i-2].high &&
      recentCandles[i].high > recentCandles[i+1].high &&
      recentCandles[i].high > recentCandles[i+2].high
    ) {
      swingHighs.push(i);
    }

    // Swing low
    if (
      recentCandles[i].low < recentCandles[i-1].low &&
      recentCandles[i].low < recentCandles[i-2].low &&
      recentCandles[i].low < recentCandles[i+1].low &&
      recentCandles[i].low < recentCandles[i+2].low
    ) {
      swingLows.push(i);
    }
  }

  // Analyze market structure
  const higherHighs = countConsecutiveHigherHighs(recentCandles, swingHighs);
  const higherLows = countConsecutiveHigherLows(recentCandles, swingLows);
  const lowerHighs = countConsecutiveLowerHighs(recentCandles, swingHighs);
  const lowerLows = countConsecutiveLowerLows(recentCandles, swingLows);

  // Determine if we're in a trend
  let isTrending = false;
  let trendDirection: 'up' | 'down' | undefined;
  let trendStrength: 'weak' | 'moderate' | 'strong' | undefined;
  let trendDuration = 0;

  if (higherHighs >= 2 && higherLows >= 2) {
    isTrending = true;
    trendDirection = 'up';
    trendDuration = Math.min(higherHighs, higherLows);
    trendStrength = trendDuration >= 4 ? 'strong' : trendDuration >= 3 ? 'moderate' : 'weak';
  } else if (lowerHighs >= 2 && lowerLows >= 2) {
    isTrending = true;
    trendDirection = 'down';
    trendDuration = Math.min(lowerHighs, lowerLows);
    trendStrength = trendDuration >= 4 ? 'strong' : trendDuration >= 3 ? 'moderate' : 'weak';
  }

  // Check for range-bound market
  let isRange = false;
  let rangeHigh: number | undefined;
  let rangeLow: number | undefined;
  let rangeStrength: 'weak' | 'moderate' | 'strong' | undefined;

  if (!isTrending && swingHighs.length >= 2 && swingLows.length >= 2) {
    // Calculate the average of the swing highs and lows
    const recentSwingHighs = swingHighs.slice(-3).map(idx => recentCandles[idx].high);
    const recentSwingLows = swingLows.slice(-3).map(idx => recentCandles[idx].low);

    const avgHigh = recentSwingHighs.reduce((sum, val) => sum + val, 0) / recentSwingHighs.length;
    const avgLow = recentSwingLows.reduce((sum, val) => sum + val, 0) / recentSwingLows.length;

    // Check if swing highs and lows are relatively consistent
    const highDeviation = Math.max(...recentSwingHighs.map(h => Math.abs(h - avgHigh) / avgHigh));
    const lowDeviation = Math.max(...recentSwingLows.map(l => Math.abs(l - avgLow) / avgLow));

    if (highDeviation < 0.03 && lowDeviation < 0.03) {
      isRange = true;
      rangeHigh = avgHigh;
      rangeLow = avgLow;

      // Determine range strength based on number of touches and consistency
      const touchCount = countRangeTouches(recentCandles, avgHigh, avgLow);
      rangeStrength = touchCount >= 6 ? 'strong' : touchCount >= 4 ? 'moderate' : 'weak';
    }
  }

  // Check for breakout
  let isBreakout = false;
  let breakoutDirection: 'up' | 'down' | undefined;
  let breakoutStrength: 'weak' | 'moderate' | 'strong' | undefined;
  let breakoutLevel: number | undefined;

  if (isRange && rangeHigh && rangeLow) {
    const lastClose = recentCandles[lastIndex].close;
    const lastVolume = recentCandles[lastIndex].volume;
    const avgVolume = calculateAverageVolume(recentCandles, 10);

    // Breakout above range
    if (lastClose > rangeHigh * 1.01) {
      isBreakout = true;
      breakoutDirection = 'up';
      breakoutLevel = rangeHigh;

      // Determine breakout strength based on volume and momentum
      const volumeStrength = lastVolume / avgVolume;
      const momentumStrength = (lastClose - rangeHigh) / rangeHigh;

      if (volumeStrength > 2 && momentumStrength > 0.03) {
        breakoutStrength = 'strong';
      } else if (volumeStrength > 1.5 && momentumStrength > 0.02) {
        breakoutStrength = 'moderate';
      } else {
        breakoutStrength = 'weak';
      }
    }
    // Breakout below range
    else if (lastClose < rangeLow * 0.99) {
      isBreakout = true;
      breakoutDirection = 'down';
      breakoutLevel = rangeLow;

      // Determine breakout strength based on volume and momentum
      const volumeStrength = lastVolume / avgVolume;
      const momentumStrength = (rangeLow - lastClose) / rangeLow;

      if (volumeStrength > 2 && momentumStrength > 0.03) {
        breakoutStrength = 'strong';
      } else if (volumeStrength > 1.5 && momentumStrength > 0.02) {
        breakoutStrength = 'moderate';
      } else {
        breakoutStrength = 'weak';
      }
    }
  }

  // Check for trend reversal
  let isReversal = false;

  if (isTrending && trendDirection) {
    const lastClose = recentCandles[lastIndex].close;
    const lastHigh = recentCandles[lastIndex].high;
    const lastLow = recentCandles[lastIndex].low;

    if (trendDirection === 'up') {
      // Potential reversal in uptrend
      if (lowerHighs >= 1 && lowerLows >= 1) {
        isReversal = true;
      }
    } else {
      // Potential reversal in downtrend
      if (higherHighs >= 1 && higherLows >= 1) {
        isReversal = true;
      }
    }
  }

  // Determine overall environment
  let environment: 'range' | 'trending' | 'breakout' | 'reversal';
  let environmentConfidence: 'low' | 'medium' | 'high';

  if (isBreakout) {
    environment = 'breakout';
    environmentConfidence = breakoutStrength === 'strong' ? 'high' :
                           breakoutStrength === 'moderate' ? 'medium' : 'low';
  } else if (isReversal) {
    environment = 'reversal';
    environmentConfidence = 'medium'; // Reversals need confirmation
  } else if (isTrending) {
    environment = 'trending';
    environmentConfidence = trendStrength === 'strong' ? 'high' :
                           trendStrength === 'moderate' ? 'medium' : 'low';
  } else if (isRange) {
    environment = 'range';
    environmentConfidence = rangeStrength === 'strong' ? 'high' :
                           rangeStrength === 'moderate' ? 'medium' : 'low';
  } else {
    // Default to range with low confidence if we can't clearly identify
    environment = 'range';
    environmentConfidence = 'low';
  }

  return {
    environment,
    environmentConfidence,
    rangeHigh,
    rangeLow,
    rangeStrength,
    trendDirection,
    trendStrength,
    trendDuration,
    breakoutDirection,
    breakoutStrength,
    breakoutLevel
  };
}

/**
 * Count consecutive higher highs in swing points
 */
function countConsecutiveHigherHighs(candles: Candle[], swingHighs: number[]): number {
  if (swingHighs.length < 2) return 0;

  let count = 0;
  for (let i = 1; i < swingHighs.length; i++) {
    if (candles[swingHighs[i]].high > candles[swingHighs[i-1]].high) {
      count++;
    } else {
      count = 0; // Reset on failure
    }
  }

  return count;
}

/**
 * Count consecutive higher lows in swing points
 */
function countConsecutiveHigherLows(candles: Candle[], swingLows: number[]): number {
  if (swingLows.length < 2) return 0;

  let count = 0;
  for (let i = 1; i < swingLows.length; i++) {
    if (candles[swingLows[i]].low > candles[swingLows[i-1]].low) {
      count++;
    } else {
      count = 0; // Reset on failure
    }
  }

  return count;
}

/**
 * Count consecutive lower highs in swing points
 */
function countConsecutiveLowerHighs(candles: Candle[], swingHighs: number[]): number {
  if (swingHighs.length < 2) return 0;

  let count = 0;
  for (let i = 1; i < swingHighs.length; i++) {
    if (candles[swingHighs[i]].high < candles[swingHighs[i-1]].high) {
      count++;
    } else {
      count = 0; // Reset on failure
    }
  }

  return count;
}

/**
 * Count consecutive lower lows in swing points
 */
function countConsecutiveLowerLows(candles: Candle[], swingLows: number[]): number {
  if (swingLows.length < 2) return 0;

  let count = 0;
  for (let i = 1; i < swingLows.length; i++) {
    if (candles[swingLows[i]].low < candles[swingLows[i-1]].low) {
      count++;
    } else {
      count = 0; // Reset on failure
    }
  }

  return count;
}

/**
 * Count how many times price has touched the range boundaries
 */
function countRangeTouches(candles: Candle[], rangeHigh: number, rangeLow: number): number {
  let touchCount = 0;
  const touchThreshold = (rangeHigh - rangeLow) * 0.01; // 1% of range

  for (const candle of candles) {
    if (Math.abs(candle.high - rangeHigh) < touchThreshold ||
        Math.abs(candle.low - rangeLow) < touchThreshold) {
      touchCount++;
    }
  }

  return touchCount;
}

/**
 * Calculate average volume over a period
 */
function calculateAverageVolume(candles: Candle[], period: number): number {
  const recentCandles = candles.slice(-period);
  return recentCandles.reduce((sum, candle) => sum + candle.volume, 0) / recentCandles.length;
}

/**
 * Classify the current volatility regime
 */
export function classifyVolatilityRegime(candles: Candle[], atr: number[]): {
  volatilityRegime: 'low' | 'normal' | 'high';
  volatilityScore: number;
} {
  // Need valid ATR values
  const validAtr = atr.filter(val => !isNaN(val));
  if (validAtr.length === 0) {
    return { volatilityRegime: 'normal', volatilityScore: 50 };
  }

  // Get current ATR and historical ATR
  const currentAtr = validAtr[validAtr.length - 1];

  // Calculate percentiles for historical ATR
  const sortedAtr = [...validAtr].sort((a, b) => a - b);
  const lowThreshold = sortedAtr[Math.floor(sortedAtr.length * 0.25)];
  const highThreshold = sortedAtr[Math.floor(sortedAtr.length * 0.75)];

  // Determine volatility regime
  let volatilityRegime: 'low' | 'normal' | 'high';

  if (currentAtr < lowThreshold) {
    volatilityRegime = 'low';
  } else if (currentAtr > highThreshold) {
    volatilityRegime = 'high';
  } else {
    volatilityRegime = 'normal';
  }

  // Calculate volatility score (0-100)
  const minAtr = sortedAtr[0];
  const maxAtr = sortedAtr[sortedAtr.length - 1];
  const volatilityScore = Math.round(((currentAtr - minAtr) / (maxAtr - minAtr)) * 100);

  return { volatilityRegime, volatilityScore };
}

/**
 * Analyze volume profile to identify high-volume price areas
 */
function analyzeVolumeProfile(candles: Candle[]): {
  highVolumeNodes: Array<{price: number, volume: number, significance: 'low' | 'medium' | 'high'}>;
  lowVolumeNodes: Array<{price: number, volume: number}>;
  volumeAtPrice: Array<{price: number, volume: number, relativeVolume: number}>;
  pointOfControl: number;
} {
  // Find price range
  let minPrice = Infinity;
  let maxPrice = -Infinity;

  for (const candle of candles) {
    minPrice = Math.min(minPrice, candle.low);
    maxPrice = Math.max(maxPrice, candle.high);
  }

  // Create price levels (divide range into 20 levels)
  const priceRange = maxPrice - minPrice;
  const levelSize = priceRange / 20;
  const volumeByLevel: {[key: number]: number} = {};

  // Initialize levels
  for (let i = 0; i < 20; i++) {
    const levelPrice = minPrice + (i * levelSize) + (levelSize / 2);
    volumeByLevel[levelPrice] = 0;
  }

  // Distribute volume across price levels
  for (const candle of candles) {
    const candleRange = candle.high - candle.low;
    if (candleRange === 0) continue;

    const volumePerPriceUnit = candle.volume / candleRange;

    for (let i = 0; i < 20; i++) {
      const levelLow = minPrice + (i * levelSize);
      const levelHigh = levelLow + levelSize;
      const levelPrice = levelLow + (levelSize / 2);

      // Check if candle overlaps with this level
      if (candle.low <= levelHigh && candle.high >= levelLow) {
        // Calculate overlap
        const overlapLow = Math.max(candle.low, levelLow);
        const overlapHigh = Math.min(candle.high, levelHigh);
        const overlap = overlapHigh - overlapLow;

        // Add proportional volume
        volumeByLevel[levelPrice] += volumePerPriceUnit * overlap;
      }
    }
  }

  // Convert to array and sort by volume
  const volumeAtPrice = Object.entries(volumeByLevel).map(([price, volume]) => ({
    price: parseFloat(price),
    volume,
    relativeVolume: 0 // Will be calculated next
  }));

  // Calculate relative volume
  const maxVolume = Math.max(...volumeAtPrice.map(level => level.volume));
  volumeAtPrice.forEach(level => {
    level.relativeVolume = level.volume / maxVolume;
  });

  // Sort by volume (descending)
  volumeAtPrice.sort((a, b) => b.volume - a.volume);

  // Identify high-volume nodes (top 30% of levels)
  const highVolumeThreshold = maxVolume * 0.7;
  const mediumVolumeThreshold = maxVolume * 0.4;

  const highVolumeNodes = volumeAtPrice
    .filter(level => level.volume > mediumVolumeThreshold)
    .map(level => ({
      price: level.price,
      volume: level.volume,
      significance: level.volume > highVolumeThreshold ? 'high' : 'medium' as 'low' | 'medium' | 'high'
    }));

  // Identify low-volume nodes (bottom 30% of levels)
  const lowVolumeThreshold = maxVolume * 0.3;
  const lowVolumeNodes = volumeAtPrice
    .filter(level => level.volume < lowVolumeThreshold)
    .map(level => ({
      price: level.price,
      volume: level.volume
    }));

  // Point of control (price level with highest volume)
  const pointOfControl = volumeAtPrice[0].price;

  // Sort volumeAtPrice by price for return
  volumeAtPrice.sort((a, b) => a.price - b.price);

  return {
    highVolumeNodes,
    lowVolumeNodes,
    volumeAtPrice,
    pointOfControl
  };
}
