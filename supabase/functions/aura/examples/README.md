# Aura API Examples

This directory contains example API requests for the Aura API. Use these examples as templates for your own requests.

## Files

- `all-api-requests.md`: Comprehensive documentation of all API endpoints with examples in Markdown format
- `all-api-requests.json`: JSON file containing all example requests for easy copy-pasting
- `public-backtest-example.json`: Example request for the public backtest endpoint
- `public-backtest-example-updated.json`: Updated example with a more realistic date range
- `authenticated-backtest-example.json`: Example request for the authenticated backtest endpoint

## How to Use

1. Choose the endpoint you want to use from `all-api-requests.md`
2. Copy the corresponding JSON example from `all-api-requests.json`
3. Modify the parameters as needed
4. Send the request to the Aura API endpoint

## Testing with cURL

You can test the API using cURL:

```bash
# Public endpoint example
curl -X POST -H "Content-Type: application/json" -d @public-backtest-example.json http://localhost:54321/functions/v1/aura

# Authenticated endpoint example (requires proper authentication)
curl -X POST -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN" -d @authenticated-backtest-example.json http://localhost:54321/functions/v1/aura
```

## Notes

- For authenticated endpoints, you need to include an Authorization header with a valid token
- The `data` field in authenticated requests should contain encrypted data
- All dates should be in YYYY-MM-DD format
- The backtesting tool requires at least 20 candles of data to perform meaningful analysis
