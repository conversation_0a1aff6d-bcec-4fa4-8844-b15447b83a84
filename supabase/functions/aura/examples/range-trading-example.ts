// Example of using the enhanced market structure analysis for range trading

import { Candle } from '../candlestick-patterns.ts';
import { analyzeMarketStructure, evaluateRangeTrade } from '../market-structure.ts';

// Sample function to demonstrate how to use the enhanced market structure analysis
export function demonstrateRangeTrading(candles: Candle[]): void {
  // Step 1: Analyze market structure
  const marketStructure = analyzeMarketStructure(candles);
  
  // Get current price
  const currentPrice = candles[candles.length - 1].close;
  
  console.log('Market Structure Analysis:');
  console.log(`- Structure: ${marketStructure.structure} (Confidence: ${marketStructure.confidence})`);
  console.log(`- Trend Strength: ${marketStructure.trendStrength}/100`);
  console.log(`- Price Location: ${marketStructure.priceLocation}`);
  console.log(`- Range Deviation: ${(marketStructure.rangeDeviation * 100).toFixed(2)}%`);
  
  // Step 2: Evaluate range trading opportunities
  const rangeTrade = evaluateRangeTrade(marketStructure, currentPrice);
  
  console.log('\nRange Trading Evaluation:');
  console.log(`- Action: ${rangeTrade.action}`);
  console.log(`- Confidence: ${rangeTrade.confidence}`);
  console.log(`- Reason: ${rangeTrade.reason}`);
  
  // Step 3: Provide additional context based on market structure
  if (marketStructure.structure === 'range') {
    console.log('\nRange Trading Strategy:');
    console.log('- Look to SHORT near resistance levels');
    console.log('- Look to LONG near support levels');
    console.log('- Use tighter stop losses in range-bound markets');
    
    // Display key levels
    console.log('\nKey Levels:');
    if (marketStructure.keyLevels.resistance.length > 0) {
      console.log(`- Resistance: ${marketStructure.keyLevels.resistance[0].toFixed(2)}`);
    }
    if (marketStructure.keyLevels.support.length > 0) {
      console.log(`- Support: ${marketStructure.keyLevels.support[0].toFixed(2)}`);
    }
    
    // Special case for when we're very close to support or resistance
    const nearestSupport = marketStructure.keyLevels.support.length > 0 ? marketStructure.keyLevels.support[0] : currentPrice * 0.95;
    const nearestResistance = marketStructure.keyLevels.resistance.length > 0 ? marketStructure.keyLevels.resistance[0] : currentPrice * 1.05;
    
    const distanceToSupport = (currentPrice - nearestSupport) / currentPrice;
    const distanceToResistance = (nearestResistance - currentPrice) / currentPrice;
    
    if (distanceToSupport <= 0.02) {
      console.log('\n⚠️ ALERT: Price is very close to support!');
      console.log('Consider LONG position with tight stop loss below support.');
    } else if (distanceToResistance <= 0.02) {
      console.log('\n⚠️ ALERT: Price is very close to resistance!');
      console.log('Consider SHORT position with tight stop loss above resistance.');
    }
  } else {
    console.log('\nNot in a range-bound market. Consider trend-following strategies instead.');
  }
}

// Example of how to use the ratio-based approach for range trading
export function ratioBasedRangeTrading(candles: Candle[], bullishScore: number, bearishScore: number): string {
  // Get current price
  const currentPrice = candles[candles.length - 1].close;
  
  // Analyze market structure
  const marketStructure = analyzeMarketStructure(candles);
  
  // Get nearest support and resistance
  const nearestSupport = marketStructure.keyLevels.support.length > 0 ? marketStructure.keyLevels.support[0] : currentPrice * 0.95;
  const nearestResistance = marketStructure.keyLevels.resistance.length > 0 ? marketStructure.keyLevels.resistance[0] : currentPrice * 1.05;
  
  // Calculate distances to support and resistance
  const distanceToSupport = (currentPrice - nearestSupport) / currentPrice;
  const distanceToResistance = (nearestResistance - currentPrice) / currentPrice;
  
  // Check if we're near support or resistance (within 5%)
  const nearSupport = distanceToSupport <= 0.05;
  const nearResistance = distanceToResistance <= 0.05;
  
  // Calculate the ratio of bearish to bullish score
  const bearishToBullishRatio = bullishScore > 0 ? bearishScore / bullishScore : 999;
  
  // Calculate the ratio of bullish to bearish score
  const bullishToBearishRatio = bearishScore > 0 ? bullishScore / bearishScore : 999;
  
  // Check if the bearish to bullish ratio is below 0.1 (very bullish)
  const isVeryBullish = bearishToBullishRatio < 0.1;
  
  // Check if the bullish to bearish ratio is below 0.1 (very bearish)
  const isVeryBearish = bullishToBearishRatio < 0.1;
  
  let decision = '';
  
  if (marketStructure.structure === 'range') {
    if (nearResistance) {
      if (isVeryBullish) {
        // If bearish/bullish ratio < 0.1, go LONG at resistance
        decision = `LONG at resistance (${nearestResistance.toFixed(2)}) due to very bullish ratio (${bearishToBullishRatio.toFixed(2)})`;
      } else {
        // Otherwise, SHORT at resistance
        decision = `SHORT at resistance (${nearestResistance.toFixed(2)}) with bearish/bullish ratio of ${bearishToBullishRatio.toFixed(2)}`;
      }
    } else if (nearSupport) {
      if (isVeryBearish) {
        // If bullish/bearish ratio < 0.1, go SHORT at support
        decision = `SHORT at support (${nearestSupport.toFixed(2)}) due to very bearish ratio (${bullishToBearishRatio.toFixed(2)})`;
      } else {
        // Otherwise, LONG at support
        decision = `LONG at support (${nearestSupport.toFixed(2)}) with bullish/bearish ratio of ${bullishToBearishRatio.toFixed(2)}`;
      }
    } else {
      decision = 'Not near support or resistance - no clear range trading opportunity';
    }
  } else {
    decision = `Not in a range-bound market (${marketStructure.structure}) - consider trend-following strategies`;
  }
  
  return decision;
}
