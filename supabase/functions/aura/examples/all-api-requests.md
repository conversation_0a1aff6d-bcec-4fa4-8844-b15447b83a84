# Aura API Request Examples

This document contains examples of all API requests you can send to the Aura API. Use these examples as templates for your own requests.

## Table of Contents

1. [Public Endpoints](#public-endpoints)
   - [Public Test](#public-test)
   - [Public Backtest](#public-backtest)
   - [S&P 500 Scanner](#sp-500-scanner)
   - [Russell 2000 Scanner](#russell-2000-scanner)
   - [NASDAQ Scanner](#nasdaq-scanner)
   - [NASDAQ 100 Scanner](#nasdaq-100-scanner)
   - [All Stocks Scanner](#all-stocks-scanner)

2. [Authenticated Endpoints](#authenticated-endpoints)
   - [Analyze Stocks](#analyze-stocks)
   - [Test Endpoint](#test-endpoint)
   - [Backtest](#backtest)
   - [S&P 500 Scanner (Authenticated)](#sp-500-scanner-authenticated)
   - [Russell 2000 Scanner (Authenticated)](#russell-2000-scanner-authenticated)
   - [NASDAQ Scanner (Authenticated)](#nasdaq-scanner-authenticated)
   - [NASDAQ 100 Scanner (Authenticated)](#nasdaq-100-scanner-authenticated)
   - [All Stocks Scanner (Authenticated)](#all-stocks-scanner-authenticated)
   - [Record Trade](#record-trade)

## Public Endpoints

These endpoints don't require authentication and can be accessed directly.

### Public Test

Basic endpoint to analyze a stock or list of stocks.

```json
{
  "action": "public_test",
  "tickers": ["HD", "XOM"],
  "start_date": "2024-05-14",
  "end_date": "2025-05-14",
  "timeframe": "day"
}
```

### Public Backtest

Run a backtest on a stock to see how Aura would have analyzed it at different points in history.

```json
{
  "action": "public_backtest",
  "ticker": "AAPL",
  "start_date": "2024-05-14",
  "end_date": "2025-05-14",
  "timeframe": "day",
  "interval": "week",
  "include_details": false
}
```

### SP 500 Scanner

Scan the S&P 500 for high-confidence trade opportunities.

```json
{
  "action": "scan_sp500",
  "start_date": "2022-05-14",
  "end_date": "2025-05-19",
  "timeframe": "day",
  "min_confidence": 0,
  "max_results": 100
}
```

### Russell 2000 Scanner

Scan the Russell 2000 for high-confidence trade opportunities.

```json
{
  "action": "scan_russell2000",
  "start_date": "2023-01-01",
  "end_date": "2023-12-31",
  "timeframe": "day",
  "min_confidence": 90,
  "max_results": 10,
  "min_price": 5,
  "max_price": 100,
  "market_structure": "any"
}
```

### NASDAQ Scanner

Scan the NASDAQ for high-confidence trade opportunities.

```json
{
  "action": "scan_nasdaq",
  "start_date": "2023-01-01",
  "end_date": "2023-12-31",
  "timeframe": "day",
  "min_confidence": 90,
  "max_results": 10,
  "min_price": 10,
  "max_price": 500,
  "market_structure": "any"
}
```

### NASDAQ 100 Scanner

Scan the NASDAQ 100 for high-confidence trade opportunities.

```json
{
  "action": "scan_nasdaq100",
  "start_date": "2023-01-01",
  "end_date": "2023-12-31",
  "timeframe": "day",
  "min_confidence": 90,
  "max_results": 10,
  "min_price": 10,
  "max_price": 1000,
  "market_structure": "any"
}
```

### All Stocks Scanner

Scan all available stocks for high-confidence trade opportunities.

```json
{
  "action": "scan_all_stocks",
  "start_date": "2023-01-01",
  "end_date": "2023-12-31",
  "timeframe": "day",
  "min_confidence": 90,
  "max_results": 10,
  "min_price": 5,
  "max_price": 500,
  "market_structure": "any"
}
```

## Authenticated Endpoints

These endpoints require authentication. The `data` field should contain encrypted data.

### Analyze Stocks

Detailed analysis of a stock or list of stocks.

```json
{
  "data": "ENCRYPTED_DATA_HERE",
  "action": "analyze",
  "tickers": ["AAPL", "MSFT", "GOOGL"],
  "start_date": "2023-01-01",
  "end_date": "2023-12-31",
  "timeframe": "day"
}
```

### Test Endpoint

Test endpoint that bypasses encryption for easier testing.

```json
{
  "data": "ENCRYPTED_DATA_HERE",
  "action": "test",
  "tickers": ["AAPL", "MSFT", "GOOGL"],
  "start_date": "2023-01-01",
  "end_date": "2023-12-31",
  "timeframe": "day"
}
```

### Backtest

Authenticated version of the backtest endpoint.

```json
{
  "data": "ENCRYPTED_DATA_HERE",
  "action": "backtest",
  "ticker": "AAPL",
  "start_date": "2022-01-01",
  "end_date": "2022-12-31",
  "timeframe": "day",
  "interval": "week",
  "include_details": false
}
```

### SP 500 Scanner (Authenticated)

Authenticated version of the S&P 500 scanner.

```json
{
  "data": "ENCRYPTED_DATA_HERE",
  "action": "scan_sp500",
  "start_date": "2023-01-01",
  "end_date": "2023-12-31",
  "timeframe": "day",
  "min_confidence": 90,
  "max_results": 10,
  "min_price": 10,
  "max_price": 500,
  "market_structure": "any"
}
```

### Russell 2000 Scanner (Authenticated)

Authenticated version of the Russell 2000 scanner.

```json
{
  "data": "ENCRYPTED_DATA_HERE",
  "action": "scan_russell2000",
  "start_date": "2023-01-01",
  "end_date": "2023-12-31",
  "timeframe": "day",
  "min_confidence": 90,
  "max_results": 10,
  "min_price": 5,
  "max_price": 100,
  "market_structure": "any"
}
```

### NASDAQ Scanner (Authenticated)

Authenticated version of the NASDAQ scanner.

```json
{
  "data": "ENCRYPTED_DATA_HERE",
  "action": "scan_nasdaq",
  "start_date": "2023-01-01",
  "end_date": "2023-12-31",
  "timeframe": "day",
  "min_confidence": 90,
  "max_results": 10,
  "min_price": 10,
  "max_price": 500,
  "market_structure": "any"
}
```

### NASDAQ 100 Scanner (Authenticated)

Authenticated version of the NASDAQ 100 scanner.

```json
{
  "data": "ENCRYPTED_DATA_HERE",
  "action": "scan_nasdaq100",
  "start_date": "2023-01-01",
  "end_date": "2023-12-31",
  "timeframe": "day",
  "min_confidence": 90,
  "max_results": 10,
  "min_price": 10,
  "max_price": 1000,
  "market_structure": "any"
}
```

### All Stocks Scanner (Authenticated)

Authenticated version of the All Stocks scanner.

```json
{
  "data": "ENCRYPTED_DATA_HERE",
  "action": "scan_all_stocks",
  "start_date": "2023-01-01",
  "end_date": "2023-12-31",
  "timeframe": "day",
  "min_confidence": 90,
  "max_results": 10,
  "min_price": 5,
  "max_price": 500,
  "market_structure": "any"
}
```

### Record Trade

Record a trade in the system.

```json
{
  "data": "ENCRYPTED_DATA_HERE",
  "action": "record_trade",
  "symbol": "AAPL",
  "entry_price": 150.25,
  "entry_date": "2023-05-15",
  "position_size": 100,
  "direction": "LONG",
  "stop_loss": 145.50,
  "take_profit": 160.00,
  "exit_price": null,
  "exit_date": null,
  "status": "OPEN",
  "notes": "Bullish pattern with strong support"
}
```

## Parameter Details

### Common Parameters

- `action`: The action to perform (required)
- `tickers` or `ticker`: The stock symbol(s) to analyze
- `start_date`: Start date in YYYY-MM-DD format (defaults to 1 year ago)
- `end_date`: End date in YYYY-MM-DD format (defaults to current date)
- `timeframe`: Timeframe for analysis (day, hour, 15minute)

### Scanner Parameters

- `min_confidence`: Minimum confidence score (0-100)
- `max_results`: Maximum number of results to return
- `min_price`: Minimum stock price
- `max_price`: Maximum stock price
- `market_structure`: Filter by market structure (any, uptrend, downtrend, breakout)

### Backtest Parameters

- `interval`: How often to sample (day, week, month)
- `include_details`: Whether to include detailed analysis

## Market Structure Types

The `market_structure` parameter can be one of:
- `any`: No filter
- `uptrend`: Higher highs and higher lows
- `downtrend`: Lower highs and lower lows
- `breakout`: Price moves outside support/resistance with increased volume

## Timeframes

The `timeframe` parameter can be one of:
- `day`: Daily candles
- `hour`: Hourly candles
- `15minute`: 15-minute candles

## Notes

- All dates should be in YYYY-MM-DD format
- Price filters are in USD
- Confidence scores range from 0 to 100
- For authenticated endpoints, the request data should be encrypted
