// Test script for company tickers integration

import { COMPANY_TICKERS } from "../data/company-tickers.ts";
import { ALL_STOCKS } from "../data/all-stocks.ts";
import { SP500_STOCKS } from "../data/sp500-stocks.ts";
import { RUSSELL2000_STOCKS } from "../data/russell2000-stocks.ts";
import { NASDAQ_STOCKS } from "../data/nasdaq-stocks.ts";
import { NASDAQ100_STOCKS } from "../data/nasdaq100-stocks.ts";

// Log the number of tickers in each list
console.log(`Company Tickers: ${COMPANY_TICKERS.length}`);
console.log(`SP500 Stocks: ${SP500_STOCKS.length}`);
console.log(`Russell 2000 Stocks: ${RUSSELL2000_STOCKS.length}`);
console.log(`NASDAQ Stocks: ${NASDAQ_STOCKS.length}`);
console.log(`NASDAQ 100 Stocks: ${NASDAQ100_STOCKS.length}`);
console.log(`All Stocks (combined): ${ALL_STOCKS.length}`);

// Check if some well-known tickers are included
const testTickers = ["AAPL", "MSFT", "GOOGL", "AMZN", "META", "TSLA", "NVDA", "BRK-B", "JPM", "JNJ"];
for (const ticker of testTickers) {
  console.log(`${ticker} in Company Tickers: ${COMPANY_TICKERS.includes(ticker)}`);
  console.log(`${ticker} in All Stocks: ${ALL_STOCKS.includes(ticker)}`);
}

// Print the first 20 tickers from the company tickers list
console.log("\nFirst 20 tickers from Company Tickers:");
console.log(COMPANY_TICKERS.slice(0, 20));

// Print the first 20 tickers from the all stocks list
console.log("\nFirst 20 tickers from All Stocks:");
console.log(ALL_STOCKS.slice(0, 20));

// Check for any duplicates in the ALL_STOCKS list
const duplicateCheck = new Set<string>();
const duplicates: string[] = [];

for (const ticker of ALL_STOCKS) {
  if (duplicateCheck.has(ticker)) {
    duplicates.push(ticker);
  } else {
    duplicateCheck.add(ticker);
  }
}

console.log(`\nNumber of duplicates found: ${duplicates.length}`);
if (duplicates.length > 0) {
  console.log("First 10 duplicates:");
  console.log(duplicates.slice(0, 10));
}

// Check if the ALL_STOCKS list is properly deduplicated
console.log(`\nALL_STOCKS length: ${ALL_STOCKS.length}`);
console.log(`Unique tickers in ALL_STOCKS: ${duplicateCheck.size}`);
console.log(`ALL_STOCKS is ${ALL_STOCKS.length === duplicateCheck.size ? "properly deduplicated" : "not properly deduplicated"}`);
