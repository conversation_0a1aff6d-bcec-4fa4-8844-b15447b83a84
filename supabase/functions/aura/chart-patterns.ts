// Chart Pattern Recognition

import { Candle } from './candlestick-patterns.ts';

// Interface for chart pattern detection result
export interface ChartPattern {
  pattern: string;
  startIndex: number;
  endIndex: number;
  significance: 'weak' | 'moderate' | 'strong';
  bullish: boolean;
  targetPrice?: number;
  supportLevel?: number;
  resistanceLevel?: number;
}

// Helper function to find local maxima (swing highs)
export function findSwingHighs(candles: Candle[], lookback: number = 5): number[] {
  const swingHighs: number[] = [];

  for (let i = lookback; i < candles.length - lookback; i++) {
    let isHigh = true;

    for (let j = i - lookback; j <= i + lookback; j++) {
      if (j === i) continue;

      if (candles[j].high >= candles[i].high) {
        isHigh = false;
        break;
      }
    }

    if (isHigh) {
      swingHighs.push(i);
    }
  }

  return swingHighs;
}

// Helper function to find local minima (swing lows)
export function findSwingLows(candles: Candle[], lookback: number = 5): number[] {
  const swingLows: number[] = [];

  for (let i = lookback; i < candles.length - lookback; i++) {
    let isLow = true;

    for (let j = i - lookback; j <= i + lookback; j++) {
      if (j === i) continue;

      if (candles[j].low <= candles[i].low) {
        isLow = false;
        break;
      }
    }

    if (isLow) {
      swingLows.push(i);
    }
  }

  return swingLows;
}

// Detect Head and Shoulders pattern
export function detectHeadAndShoulders(candles: Candle[]): ChartPattern[] {
  const patterns: ChartPattern[] = [];
  const swingHighs = findSwingHighs(candles);

  // Need at least 5 swing highs to form a head and shoulders
  if (swingHighs.length < 5) return patterns;

  // Look for head and shoulders pattern
  for (let i = 0; i < swingHighs.length - 4; i++) {
    const leftShoulder = swingHighs[i];
    const head = swingHighs[i + 2];
    const rightShoulder = swingHighs[i + 4];

    // Check if the head is higher than both shoulders
    if (
      candles[head].high > candles[leftShoulder].high &&
      candles[head].high > candles[rightShoulder].high
    ) {
      // Check if shoulders are roughly at the same level (within 5%)
      const shoulderDiff = Math.abs(candles[leftShoulder].high - candles[rightShoulder].high);
      const shoulderAvg = (candles[leftShoulder].high + candles[rightShoulder].high) / 2;

      if (shoulderDiff / shoulderAvg < 0.05) {
        // Find the neckline (connecting the lows between shoulders and head)
        const leftTrough = findLowestBetween(candles, leftShoulder, head);
        const rightTrough = findLowestBetween(candles, head, rightShoulder);

        // Check if troughs are roughly at the same level
        const troughDiff = Math.abs(candles[leftTrough].low - candles[rightTrough].low);
        const troughAvg = (candles[leftTrough].low + candles[rightTrough].low) / 2;

        if (troughDiff / troughAvg < 0.05) {
          // Calculate pattern height for target
          const patternHeight = candles[head].high - troughAvg;
          const targetPrice = troughAvg - patternHeight; // Bearish target

          patterns.push({
            pattern: 'head_and_shoulders',
            startIndex: leftShoulder,
            endIndex: rightShoulder,
            significance: 'strong',
            bullish: false,
            targetPrice
          });
        }
      }
    }
  }

  return patterns;
}

// Helper function to find lowest point between two indices
export function findLowestBetween(candles: Candle[], start: number, end: number): number {
  let lowestIndex = start;

  for (let i = start + 1; i < end; i++) {
    if (candles[i].low < candles[lowestIndex].low) {
      lowestIndex = i;
    }
  }

  return lowestIndex;
}

// Helper function to find highest point between two indices
export function findHighestBetween(candles: Candle[], start: number, end: number): number {
  let highestIndex = start;

  for (let i = start + 1; i < end; i++) {
    if (candles[i].high > candles[highestIndex].high) {
      highestIndex = i;
    }
  }

  return highestIndex;
}

// Detect Double Top pattern
export function detectDoubleTop(candles: Candle[]): ChartPattern[] {
  const patterns: ChartPattern[] = [];
  const swingHighs = findSwingHighs(candles);

  // Need at least 2 swing highs to form a double top
  if (swingHighs.length < 2) return patterns;

  for (let i = 0; i < swingHighs.length - 1; i++) {
    const firstTop = swingHighs[i];
    const secondTop = swingHighs[i + 1];

    // Check if tops are at similar levels (within 2%)
    const topDiff = Math.abs(candles[firstTop].high - candles[secondTop].high);
    const topAvg = (candles[firstTop].high + candles[secondTop].high) / 2;

    if (topDiff / topAvg < 0.02) {
      // Check if there's a significant trough between the tops
      const trough = findLowestBetween(candles, firstTop, secondTop);

      // Trough should be at least 3% below the tops
      if ((topAvg - candles[trough].low) / topAvg > 0.03) {
        // Calculate pattern height for target
        const patternHeight = topAvg - candles[trough].low;
        const targetPrice = candles[trough].low - patternHeight; // Bearish target

        patterns.push({
          pattern: 'double_top',
          startIndex: firstTop,
          endIndex: secondTop,
          significance: 'strong',
          bullish: false,
          targetPrice
        });
      }
    }
  }

  return patterns;
}

// Detect Double Bottom pattern
export function detectDoubleBottom(candles: Candle[]): ChartPattern[] {
  const patterns: ChartPattern[] = [];
  const swingLows = findSwingLows(candles);

  // Need at least 2 swing lows to form a double bottom
  if (swingLows.length < 2) return patterns;

  for (let i = 0; i < swingLows.length - 1; i++) {
    const firstBottom = swingLows[i];
    const secondBottom = swingLows[i + 1];

    // Check if bottoms are at similar levels (within 2%)
    const bottomDiff = Math.abs(candles[firstBottom].low - candles[secondBottom].low);
    const bottomAvg = (candles[firstBottom].low + candles[secondBottom].low) / 2;

    if (bottomDiff / bottomAvg < 0.02) {
      // Check if there's a significant peak between the bottoms
      const peak = findHighestBetween(candles, firstBottom, secondBottom);

      // Peak should be at least 3% above the bottoms
      if ((candles[peak].high - bottomAvg) / bottomAvg > 0.03) {
        // Calculate pattern height for target
        const patternHeight = candles[peak].high - bottomAvg;
        const targetPrice = candles[peak].high + patternHeight; // Bullish target

        patterns.push({
          pattern: 'double_bottom',
          startIndex: firstBottom,
          endIndex: secondBottom,
          significance: 'strong',
          bullish: true,
          targetPrice
        });
      }
    }
  }

  return patterns;
}

// Detect Flag pattern
export function detectFlag(candles: Candle[]): ChartPattern[] {
  const patterns: ChartPattern[] = [];

  // Need at least 15 candles to detect a flag
  if (candles.length < 15) return patterns;

  // Look for a strong move (pole) followed by a consolidation (flag)
  for (let i = 5; i < candles.length - 10; i++) {
    // Check for a strong bullish move (pole)
    let isBullishPole = true;
    for (let j = i - 5; j < i; j++) {
      if (candles[j].close <= candles[j].open) {
        isBullishPole = false;
        break;
      }
    }

    // Check for a bearish consolidation (flag)
    if (isBullishPole) {
      let isFlag = true;
      const highestPoint = candles[i - 1].high;
      const lowestPoint = findLowestInRange(candles, i, i + 5);

      // Flag should be a consolidation (not more than 50% of the pole)
      const poleHeight = candles[i - 1].close - candles[i - 5].open;
      const flagHeight = highestPoint - lowestPoint;

      if (flagHeight / poleHeight > 0.5) {
        isFlag = false;
      }

      if (isFlag) {
        // Calculate target (pole height added to breakout point)
        const breakoutPoint = highestPoint;
        const targetPrice = breakoutPoint + poleHeight;

        patterns.push({
          pattern: 'bullish_flag',
          startIndex: i - 5,
          endIndex: i + 5,
          significance: 'moderate',
          bullish: true,
          targetPrice
        });
      }
    }

    // Check for a strong bearish move (pole)
    let isBearishPole = true;
    for (let j = i - 5; j < i; j++) {
      if (candles[j].close >= candles[j].open) {
        isBearishPole = false;
        break;
      }
    }

    // Check for a bullish consolidation (flag)
    if (isBearishPole) {
      let isFlag = true;
      const lowestPoint = candles[i - 1].low;
      const highestPoint = findHighestInRange(candles, i, i + 5);

      // Flag should be a consolidation (not more than 50% of the pole)
      const poleHeight = candles[i - 5].open - candles[i - 1].close;
      const flagHeight = highestPoint - lowestPoint;

      if (flagHeight / poleHeight > 0.5) {
        isFlag = false;
      }

      if (isFlag) {
        // Calculate target (pole height subtracted from breakout point)
        const breakoutPoint = lowestPoint;
        const targetPrice = breakoutPoint - poleHeight;

        patterns.push({
          pattern: 'bearish_flag',
          startIndex: i - 5,
          endIndex: i + 5,
          significance: 'moderate',
          bullish: false,
          targetPrice
        });
      }
    }
  }

  return patterns;
}

// Helper function to find lowest point in a range
function findLowestInRange(candles: Candle[], start: number, end: number): number {
  let lowest = candles[start].low;

  for (let i = start + 1; i <= end && i < candles.length; i++) {
    if (candles[i].low < lowest) {
      lowest = candles[i].low;
    }
  }

  return lowest;
}

// Helper function to find highest point in a range
function findHighestInRange(candles: Candle[], start: number, end: number): number {
  let highest = candles[start].high;

  for (let i = start + 1; i <= end && i < candles.length; i++) {
    if (candles[i].high > highest) {
      highest = candles[i].high;
    }
  }

  return highest;
}

// Import enhanced chart pattern recognition
import { enhancedChartPatternAnalysis } from './enhanced-chart-patterns.ts';

// Analyze all chart patterns for a given dataset
export function analyzeChartPatterns(candles: Candle[]): ChartPattern[] {
  // Detect basic chart patterns
  const headAndShoulders = detectHeadAndShoulders(candles);
  const doubleTops = detectDoubleTop(candles);
  const doubleBottoms = detectDoubleBottom(candles);
  const flags = detectFlag(candles);

  // Get enhanced chart patterns
  const enhancedPatterns = enhancedChartPatternAnalysis(candles);

  // Combine all patterns
  return [
    ...headAndShoulders,
    ...doubleTops,
    ...doubleBottoms,
    ...flags,
    ...enhancedPatterns
  ];
}
