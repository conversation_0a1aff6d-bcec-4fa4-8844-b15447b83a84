// Options Analysis Module
// This module provides functions for analyzing options contracts and recommending the best ones

import { fetchFromPolygon } from "./polygon-api.ts";

// Interface for options contract details
export interface OptionsContract {
  ticker: string;
  contractType: 'call' | 'put';
  strikePrice: number;
  expirationDate: string;
  breakEvenPrice: number;
  lastPrice: number;
  impliedVolatility?: number;
  openInterest?: number;
  delta?: number;
  gamma?: number;
  theta?: number;
  vega?: number;
  underlyingPrice: number;
  changeToBreakEven: number;
  potentialReturn?: number;
  riskRewardRatio?: number;
}

// Interface for options recommendation
export interface OptionsRecommendation {
  bestCall?: OptionsContract;
  bestPut?: OptionsContract;
  recommendation: string;
  reasoning: string;
}

/**
 * Fetch options chain data from Polygon API
 * @param symbol - The underlying asset symbol
 * @param apiKey - Polygon API key
 * @param limit - Maximum number of contracts to return (default: 100)
 * @returns Array of options contracts
 */
export async function fetchOptionsChain(
  symbol: string,
  apiKey: string,
  limit: number = 100
): Promise<OptionsContract[]> {
  try {
    // Construct the API URL
    const url = `https://api.polygon.io/v3/snapshot/options/${symbol}?limit=${limit}&apiKey=${apiKey}`;

    // Fetch data from Polygon API
    const response = await fetchFromPolygon(url);

    if (!response.results || response.results.length === 0) {
      console.log(`No options data available for ${symbol}`);
      return [];
    }

    // Transform the response into our OptionsContract format
    const contracts: OptionsContract[] = response.results.map((result: any) => {
      return {
        ticker: result.details.ticker,
        contractType: result.details.contract_type,
        strikePrice: result.details.strike_price,
        expirationDate: result.details.expiration_date,
        breakEvenPrice: result.break_even_price || 0,
        lastPrice: result.last_trade?.price || 0,
        impliedVolatility: result.implied_volatility,
        openInterest: result.open_interest,
        delta: result.greeks?.delta,
        gamma: result.greeks?.gamma,
        theta: result.greeks?.theta,
        vega: result.greeks?.vega,
        underlyingPrice: result.underlying_asset?.price || 0,
        changeToBreakEven: result.underlying_asset?.change_to_break_even || 0,
        potentialReturn: calculatePotentialReturn(
          result.details.contract_type,
          result.last_trade?.price || 0,
          result.details.strike_price,
          result.underlying_asset?.price || 0
        ),
        riskRewardRatio: calculateRiskRewardRatio(
          result.details.contract_type,
          result.last_trade?.price || 0,
          result.details.strike_price,
          result.underlying_asset?.price || 0
        )
      };
    });

    return contracts;
  } catch (error) {
    console.error(`Error fetching options chain for ${symbol}:`, error);
    return [];
  }
}

/**
 * Calculate potential return for an options contract
 * @param contractType - 'call' or 'put'
 * @param lastPrice - Last price of the contract
 * @param strikePrice - Strike price of the contract
 * @param underlyingPrice - Current price of the underlying asset
 * @returns Potential return percentage
 */
function calculatePotentialReturn(
  contractType: string,
  lastPrice: number,
  strikePrice: number,
  underlyingPrice: number
): number {
  if (lastPrice === 0) return 0;

  if (contractType === 'call') {
    // For calls, potential profit is (underlying price * 1.1 - strike price - premium)
    const potentialProfit = (underlyingPrice * 1.1 - strikePrice - lastPrice);
    return (potentialProfit / lastPrice) * 100;
  } else {
    // For puts, potential profit is (strike price - underlying price * 0.9 - premium)
    const potentialProfit = (strikePrice - underlyingPrice * 0.9 - lastPrice);
    return (potentialProfit / lastPrice) * 100;
  }
}

/**
 * Calculate risk-reward ratio for an options contract
 * @param contractType - 'call' or 'put'
 * @param lastPrice - Last price of the contract
 * @param strikePrice - Strike price of the contract
 * @param underlyingPrice - Current price of the underlying asset
 * @returns Risk-reward ratio
 */
function calculateRiskRewardRatio(
  contractType: string,
  lastPrice: number,
  strikePrice: number,
  underlyingPrice: number
): number {
  if (lastPrice === 0) return 0;

  if (contractType === 'call') {
    // For calls, potential profit is (underlying price * 1.1 - strike price - premium)
    const potentialProfit = (underlyingPrice * 1.1 - strikePrice - lastPrice);
    // Risk is the premium paid
    const risk = lastPrice;
    return potentialProfit / risk;
  } else {
    // For puts, potential profit is (strike price - underlying price * 0.9 - premium)
    const potentialProfit = (strikePrice - underlyingPrice * 0.9 - lastPrice);
    // Risk is the premium paid
    const risk = lastPrice;
    return potentialProfit / risk;
  }
}

/**
 * Recommend the best options contracts based on technical analysis
 * @param symbol - The underlying asset symbol
 * @param currentPrice - Current price of the underlying asset
 * @param signal - Technical analysis signal ('LONG', 'SHORT', 'NEUTRAL')
 * @param apiKey - Polygon API key
 * @returns Options recommendation
 */
export async function recommendOptionsContracts(
  symbol: string,
  currentPrice: number,
  signal: string,
  apiKey: string
): Promise<OptionsRecommendation> {
  try {
    // Fetch options chain data
    const contracts = await fetchOptionsChain(symbol, apiKey);

    if (contracts.length === 0) {
      return {
        recommendation: "No options contracts available",
        reasoning: `Could not find any options contracts for ${symbol}.`
      };
    }

    // Filter contracts by expiration date (30-60 days out)
    const today = new Date();
    const thirtyDaysLater = new Date(today);
    thirtyDaysLater.setDate(today.getDate() + 30);
    const sixtyDaysLater = new Date(today);
    sixtyDaysLater.setDate(today.getDate() + 60);

    const thirtyDaysLaterStr = thirtyDaysLater.toISOString().split('T')[0];
    const sixtyDaysLaterStr = sixtyDaysLater.toISOString().split('T')[0];

    const filteredContracts = contracts.filter(contract => {
      return contract.expirationDate >= thirtyDaysLaterStr &&
             contract.expirationDate <= sixtyDaysLaterStr &&
             contract.lastPrice > 0 &&
             contract.openInterest > 10; // Ensure some liquidity
    });

    if (filteredContracts.length === 0) {
      return {
        recommendation: "No suitable options contracts found",
        reasoning: `Could not find any suitable options contracts for ${symbol} with expiration between 30-60 days and sufficient liquidity.`
      };
    }

    // Separate calls and puts
    const calls = filteredContracts.filter(contract => contract.contractType === 'call');
    const puts = filteredContracts.filter(contract => contract.contractType === 'put');

    // Find the best call and put based on the signal
    let bestCall: OptionsContract | undefined;
    let bestPut: OptionsContract | undefined;
    let recommendation = "";
    let reasoning = "";

    // Check if the symbol is a cryptocurrency
    const isCrypto = /^X:.*USD$/.test(symbol) ||
                    /^(BTC|ETH|XRP|LTC|DOGE|SOL|ADA|DOT|AVAX|MATIC|LINK|UNI|SHIB)$/.test(symbol.toUpperCase());

    // For cryptocurrencies, provide a special message since options aren't typically available
    if (isCrypto) {
      if (signal === 'LONG') {
        recommendation = `Go LONG on ${symbol}`;
        reasoning = `Based on the bullish signal for ${symbol}, we recommend going long. Note that traditional options are not typically available for cryptocurrencies. Consider spot trading or crypto derivatives platforms.`;
      } else if (signal === 'SHORT') {
        recommendation = `Go SHORT on ${symbol}`;
        reasoning = `Based on the bearish signal for ${symbol}, we recommend going short. Note that traditional options are not typically available for cryptocurrencies. Consider spot trading or crypto derivatives platforms.`;
      } else {
        // This should never happen with our changes, but just in case
        recommendation = `Go LONG on ${symbol}`;
        reasoning = `Based on the analysis for ${symbol}, we recommend going long by default. Note that traditional options are not typically available for cryptocurrencies. Consider spot trading or crypto derivatives platforms.`;
      }
    } else if (signal === 'LONG') {
      // For LONG signal, find calls with strike price near the current price
      const nearMoneyCallsWithHighDelta = calls.filter(call =>
        call.strikePrice >= currentPrice * 0.95 &&
        call.strikePrice <= currentPrice * 1.05 &&
        call.delta >= 0.4 && call.delta <= 0.6
      );

      if (nearMoneyCallsWithHighDelta.length > 0) {
        // Sort by highest risk-reward ratio
        nearMoneyCallsWithHighDelta.sort((a, b) => (b.riskRewardRatio || 0) - (a.riskRewardRatio || 0));
        bestCall = nearMoneyCallsWithHighDelta[0];

        recommendation = `Buy ${bestCall.ticker} call option`;
        reasoning = `Based on the bullish signal for ${symbol}, we recommend buying a call option with a strike price of $${bestCall.strikePrice.toFixed(2)} expiring on ${bestCall.expirationDate}. This option has a delta of ${bestCall.delta?.toFixed(2) || 'N/A'}, implying a good balance of risk and reward. The risk-reward ratio is ${bestCall.riskRewardRatio?.toFixed(2) || 'N/A'}, and the potential return is ${bestCall.potentialReturn?.toFixed(2) || 'N/A'}%.`;
      } else {
        recommendation = "No suitable call options found";
        reasoning = `Could not find suitable call options for the bullish signal on ${symbol}. Consider looking at different expiration dates or buying the stock directly.`;
      }
    } else if (signal === 'SHORT') {
      // For SHORT signal, find puts with strike price near the current price
      const nearMoneyPutsWithHighDelta = puts.filter(put =>
        put.strikePrice >= currentPrice * 0.95 &&
        put.strikePrice <= currentPrice * 1.05 &&
        put.delta <= -0.4 && put.delta >= -0.6
      );

      if (nearMoneyPutsWithHighDelta.length > 0) {
        // Sort by highest risk-reward ratio
        nearMoneyPutsWithHighDelta.sort((a, b) => (b.riskRewardRatio || 0) - (a.riskRewardRatio || 0));
        bestPut = nearMoneyPutsWithHighDelta[0];

        recommendation = `Buy ${bestPut.ticker} put option`;
        reasoning = `Based on the bearish signal for ${symbol}, we recommend buying a put option with a strike price of $${bestPut.strikePrice.toFixed(2)} expiring on ${bestPut.expirationDate}. This option has a delta of ${bestPut.delta?.toFixed(2) || 'N/A'}, implying a good balance of risk and reward. The risk-reward ratio is ${bestPut.riskRewardRatio?.toFixed(2) || 'N/A'}, and the potential return is ${bestPut.potentialReturn?.toFixed(2) || 'N/A'}%.`;
      } else {
        recommendation = "No suitable put options found";
        reasoning = `Could not find suitable put options for the bearish signal on ${symbol}. Consider looking at different expiration dates or shorting the stock directly.`;
      }
    } else {
      // For NEUTRAL signal, no specific recommendation
      recommendation = "No options recommendation for neutral signal";
      reasoning = `The technical analysis indicates a neutral outlook for ${symbol}. Options trading is not recommended at this time.`;
    }

    return {
      bestCall,
      bestPut,
      recommendation,
      reasoning
    };
  } catch (error) {
    console.error(`Error recommending options for ${symbol}:`, error);
    return {
      recommendation: "Error analyzing options",
      reasoning: `An error occurred while analyzing options for ${symbol}: ${error.message}`
    };
  }
}
