// Polygon API Helper Functions
// This module provides helper functions for interacting with the Polygon API

/**
 * Fetch data from Polygon API
 * @param url - The full URL to fetch from
 * @returns The JSON response from the API
 */
export async function fetchFromPolygon(url: string): Promise<any> {
  try {
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`Polygon API error: ${response.status} ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error(`Error fetching from Polygon API:`, error);
    throw error;
  }
}

/**
 * Fetch historical data from Polygon API
 * @param symbol - The ticker symbol
 * @param startDate - Start date in YYYY-MM-DD format
 * @param endDate - End date in YYYY-MM-DD format
 * @param apiKey - Polygon API key
 * @param timeframe - Timeframe for the data (e.g., 'day', 'hour', '15minute')
 * @returns The historical data response
 */
export async function fetchHistoricalData(
  symbol: string,
  startDate: string,
  endDate: string,
  apiKey: string,
  timeframe: string = 'day'
): Promise<any> {
  try {
    // Determine the appropriate endpoint based on the timeframe
    let endpoint = '';
    let multiplier = '1';
    let timespan = '';
    
    if (timeframe === 'day') {
      endpoint = 'aggs/ticker';
      multiplier = '1';
      timespan = 'day';
    } else if (timeframe === 'hour') {
      endpoint = 'aggs/ticker';
      multiplier = '1';
      timespan = 'hour';
    } else if (timeframe === '15minute') {
      endpoint = 'aggs/ticker';
      multiplier = '15';
      timespan = 'minute';
    } else {
      throw new Error(`Unsupported timeframe: ${timeframe}`);
    }
    
    // Construct the API URL
    const url = `https://api.polygon.io/v2/${endpoint}/${symbol}/range/${multiplier}/${timespan}/${startDate}/${endDate}?apiKey=${apiKey}`;
    
    // Fetch data from Polygon API
    const response = await fetchFromPolygon(url);
    
    // Transform the response into a more usable format
    const transformedData = {
      symbol,
      data: (response.results || []).map((bar: any) => ({
        date: new Date(bar.t).toISOString(),
        open: bar.o,
        high: bar.h,
        low: bar.l,
        close: bar.c,
        volume: bar.v
      }))
    };
    
    return transformedData;
  } catch (error) {
    console.error(`Error fetching historical data for ${symbol}:`, error);
    return { symbol, data: [] };
  }
}
