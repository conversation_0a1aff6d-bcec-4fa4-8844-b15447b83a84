// Bollinger Bands Analysis

import { Candle } from './candlestick-patterns.ts';

// Interface for Bollinger Band analysis
export interface BollingerBandAnalysis {
  middle: number[];
  upper: number[];
  lower: number[];
  bandwidth: number[];
  squeeze: {
    isSqueezing: boolean;
    squeezeIntensity: 'low' | 'medium' | 'high';
    duration: number;
  }[];
  expansion: {
    isExpanding: boolean;
    expansionIntensity: 'low' | 'medium' | 'high';
    duration: number;
  }[];
}

// Calculate Bollinger Bands with squeeze detection
export function calculateBollingerBandsWithSqueeze(
  data: Candle[], 
  period: number = 20, 
  stdDev: number = 2
): BollingerBandAnalysis {
  // Calculate SMA
  const sma: number[] = [];
  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      sma.push(NaN);
      continue;
    }
    
    let sum = 0;
    for (let j = 0; j < period; j++) {
      sum += data[i - j].close;
    }
    
    sma.push(sum / period);
  }
  
  // Calculate standard deviation and bands
  const upperBand: number[] = [];
  const lowerBand: number[] = [];
  const bandwidth: number[] = [];
  
  for (let i = 0; i < data.length; i++) {
    if (isNaN(sma[i])) {
      upperBand.push(NaN);
      lowerBand.push(NaN);
      bandwidth.push(NaN);
      continue;
    }
    
    // Calculate standard deviation
    let sum = 0;
    for (let j = 0; j < period; j++) {
      if (i - j < 0) break;
      sum += Math.pow(data[i - j].close - sma[i], 2);
    }
    
    const standardDeviation = Math.sqrt(sum / period);
    
    const upper = sma[i] + (stdDev * standardDeviation);
    const lower = sma[i] - (stdDev * standardDeviation);
    
    upperBand.push(upper);
    lowerBand.push(lower);
    
    // Calculate bandwidth (upper - lower) / middle
    const bw = (upper - lower) / sma[i];
    bandwidth.push(bw);
  }
  
  // Detect squeezes and expansions
  const squeeze: {
    isSqueezing: boolean;
    squeezeIntensity: 'low' | 'medium' | 'high';
    duration: number;
  }[] = [];
  
  const expansion: {
    isExpanding: boolean;
    expansionIntensity: 'low' | 'medium' | 'high';
    duration: number;
  }[] = [];
  
  // Calculate the average bandwidth over the entire period
  let validBandwidths = bandwidth.filter(bw => !isNaN(bw));
  const avgBandwidth = validBandwidths.reduce((sum, bw) => sum + bw, 0) / validBandwidths.length;
  
  // Calculate standard deviation of bandwidth
  const bandwidthStdDev = Math.sqrt(
    validBandwidths.reduce((sum, bw) => sum + Math.pow(bw - avgBandwidth, 2), 0) / validBandwidths.length
  );
  
  // Thresholds for squeeze and expansion
  const squeezeLowThreshold = avgBandwidth - (bandwidthStdDev * 0.5);
  const squeezeMediumThreshold = avgBandwidth - bandwidthStdDev;
  const squeezeHighThreshold = avgBandwidth - (bandwidthStdDev * 1.5);
  
  const expansionLowThreshold = avgBandwidth + (bandwidthStdDev * 0.5);
  const expansionMediumThreshold = avgBandwidth + bandwidthStdDev;
  const expansionHighThreshold = avgBandwidth + (bandwidthStdDev * 1.5);
  
  // Track squeeze and expansion durations
  let currentSqueezeDuration = 0;
  let currentExpansionDuration = 0;
  
  for (let i = 0; i < data.length; i++) {
    if (isNaN(bandwidth[i])) {
      squeeze.push({
        isSqueezing: false,
        squeezeIntensity: 'low',
        duration: 0
      });
      
      expansion.push({
        isExpanding: false,
        expansionIntensity: 'low',
        duration: 0
      });
      
      continue;
    }
    
    // Check for squeeze
    let isSqueezing = bandwidth[i] < squeezeLowThreshold;
    let squeezeIntensity: 'low' | 'medium' | 'high' = 'low';
    
    if (bandwidth[i] < squeezeHighThreshold) {
      squeezeIntensity = 'high';
    } else if (bandwidth[i] < squeezeMediumThreshold) {
      squeezeIntensity = 'medium';
    }
    
    if (isSqueezing) {
      currentSqueezeDuration++;
      currentExpansionDuration = 0;
    } else {
      currentSqueezeDuration = 0;
    }
    
    squeeze.push({
      isSqueezing,
      squeezeIntensity,
      duration: currentSqueezeDuration
    });
    
    // Check for expansion
    let isExpanding = bandwidth[i] > expansionLowThreshold;
    let expansionIntensity: 'low' | 'medium' | 'high' = 'low';
    
    if (bandwidth[i] > expansionHighThreshold) {
      expansionIntensity = 'high';
    } else if (bandwidth[i] > expansionMediumThreshold) {
      expansionIntensity = 'medium';
    }
    
    if (isExpanding) {
      currentExpansionDuration++;
      currentSqueezeDuration = 0;
    } else {
      currentExpansionDuration = 0;
    }
    
    expansion.push({
      isExpanding,
      expansionIntensity,
      duration: currentExpansionDuration
    });
  }
  
  return {
    middle: sma,
    upper: upperBand,
    lower: lowerBand,
    bandwidth,
    squeeze,
    expansion
  };
}

// Detect Bollinger Band squeeze
export function detectBollingerSqueeze(data: Candle[]): {
  isSqueezing: boolean;
  intensity: 'low' | 'medium' | 'high';
  duration: number;
  nearBreakout: boolean;
  potentialDirection: 'up' | 'down' | 'unknown';
} {
  // Calculate Bollinger Bands with squeeze detection
  const bbAnalysis = calculateBollingerBandsWithSqueeze(data);
  
  // Get the latest values
  const lastIndex = data.length - 1;
  const currentSqueeze = bbAnalysis.squeeze[lastIndex];
  
  // Check if we're near a breakout
  let nearBreakout = false;
  let potentialDirection: 'up' | 'down' | 'unknown' = 'unknown';
  
  if (currentSqueeze.isSqueezing && currentSqueeze.duration >= 5) {
    // Check the last few candles for momentum
    const recentCandles = data.slice(-5);
    let upMomentum = 0;
    let downMomentum = 0;
    
    for (const candle of recentCandles) {
      if (candle.close > candle.open) {
        upMomentum += (candle.close - candle.open) / candle.open;
      } else {
        downMomentum += (candle.open - candle.close) / candle.open;
      }
    }
    
    // If we have clear momentum in one direction, we might be near a breakout
    if (upMomentum > downMomentum * 1.5) {
      nearBreakout = true;
      potentialDirection = 'up';
    } else if (downMomentum > upMomentum * 1.5) {
      nearBreakout = true;
      potentialDirection = 'down';
    }
  }
  
  return {
    isSqueezing: currentSqueeze.isSqueezing,
    intensity: currentSqueeze.squeezeIntensity,
    duration: currentSqueeze.duration,
    nearBreakout,
    potentialDirection
  };
}
