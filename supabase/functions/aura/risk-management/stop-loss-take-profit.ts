// Aura Stop Loss & Take Profit Framework
// Implements a weighted approach to determine optimal SL and TP levels

import { Candle } from '../candlestick-patterns.ts';
import { calculateATR } from '../market-structure.ts';

// Interface for stop loss and take profit levels with explanations
export interface StopLossTakeProfit {
  stopLoss: number;
  takeProfit: number;
  stopLossExplanation: string;
  takeProfitExplanation: string;
  riskRewardRatio: number;
  stopLossFactors: {
    keyStructurePoints: {
      level: number | null;
      weight: number;
      explanation: string;
    };
    technicalProtectionPoints: {
      level: number | null;
      weight: number;
      explanation: string;
    };
    riskManagementParameters: {
      level: number | null;
      weight: number;
      explanation: string;
    };
  };
  takeProfitFactors: {
    keyResistanceSupportTargets: {
      level: number | null;
      weight: number;
      explanation: string;
    };
    fibonacciProjectionTargets: {
      level: number | null;
      weight: number;
      explanation: string;
    };
    momentumBasedTargets: {
      level: number | null;
      weight: number;
      explanation: string;
    };
  };
}

// Function to calculate optimal stop loss and take profit levels
export function calculateStopLossTakeProfit(
  candles: Candle[],
  signal: 'LONG' | 'SHORT',
  swingHighs: number[],
  swingLows: number[],
  supportLevels: number[],
  resistanceLevels: number[],
  volumeProfile?: { price: number; volume: number }[]
): StopLossTakeProfit {
  const currentPrice = candles[candles.length - 1].close;
  const atr = calculateATR(candles);
  const currentATR = atr[atr.length - 1];

  // Initialize result object with weights from Aura Importance Checklist
  const result: StopLossTakeProfit = {
    stopLoss: 0,
    takeProfit: 0,
    stopLossExplanation: '',
    takeProfitExplanation: '',
    riskRewardRatio: 0,
    stopLossFactors: {
      keyStructurePoints: {
        level: null,
        weight: 0.4, // 40% - Market Structure
        explanation: ''
      },
      technicalProtectionPoints: {
        level: null,
        weight: 0.3, // 30% - Technical Protection
        explanation: ''
      },
      riskManagementParameters: {
        level: null,
        weight: 0.3, // 30% - Risk Management
        explanation: ''
      }
    },
    takeProfitFactors: {
      keyResistanceSupportTargets: {
        level: null,
        weight: 0.4, // 40% - Key Targets
        explanation: ''
      },
      fibonacciProjectionTargets: {
        level: null,
        weight: 0.35, // 35% - Fibonacci
        explanation: ''
      },
      momentumBasedTargets: {
        level: null,
        weight: 0.25, // 25% - Momentum
        explanation: ''
      }
    }
  };

  // ===== STOP LOSS CALCULATION =====

  // 1. Key Structure Points (40%)
  let keyStructureLevel: number | null = null;
  let keyStructureExplanation = '';

  if (signal === 'LONG') {
    // For long positions, find the nearest significant swing low
    const recentSwingLows = [...swingLows].sort((a, b) => b - a); // Sort by recency

    for (const lowIndex of recentSwingLows) {
      const lowPrice = candles[lowIndex].low;
      if (lowPrice < currentPrice) {
        // Add a buffer of 1-2% below the swing low for extra safety
        keyStructureLevel = lowPrice * 0.98; // 2% below swing low
        keyStructureExplanation = `Nearest significant swing low at ${lowPrice.toFixed(2)} with 2% buffer`;
        break;
      }
    }

    // Check for double bottom formations
    if (recentSwingLows.length >= 2) {
      const low1 = candles[recentSwingLows[0]].low;
      const low2 = candles[recentSwingLows[1]].low;

      if (Math.abs(low1 - low2) / low1 < 0.02) { // Within 2% of each other
        const doubleBottomLevel = Math.min(low1, low2) * 0.97; // 3% below the double bottom for extra safety

        if (keyStructureLevel === null || doubleBottomLevel > keyStructureLevel) {
          keyStructureLevel = doubleBottomLevel;
          keyStructureExplanation = `Double bottom formation at ${low1.toFixed(2)} and ${low2.toFixed(2)} with 3% buffer`;
        }
      }
    }
  } else { // SHORT
    // For short positions, find the nearest significant swing high
    const recentSwingHighs = [...swingHighs].sort((a, b) => b - a); // Sort by recency

    for (const highIndex of recentSwingHighs) {
      const highPrice = candles[highIndex].high;
      if (highPrice > currentPrice) {
        // Add a buffer of 1-2% above the swing high for extra safety
        keyStructureLevel = highPrice * 1.02; // 2% above swing high
        keyStructureExplanation = `Nearest significant swing high at ${highPrice.toFixed(2)} with 2% buffer`;
        break;
      }
    }

    // Check for double top formations
    if (recentSwingHighs.length >= 2) {
      const high1 = candles[recentSwingHighs[0]].high;
      const high2 = candles[recentSwingHighs[1]].high;

      if (Math.abs(high1 - high2) / high1 < 0.02) { // Within 2% of each other
        const doubleTopLevel = Math.max(high1, high2) * 1.03; // 3% above the double top for extra safety

        if (keyStructureLevel === null || doubleTopLevel < keyStructureLevel) {
          keyStructureLevel = doubleTopLevel;
          keyStructureExplanation = `Double top formation at ${high1.toFixed(2)} and ${high2.toFixed(2)} with 3% buffer`;
        }
      }
    }
  }

  result.stopLossFactors.keyStructurePoints.level = keyStructureLevel;
  result.stopLossFactors.keyStructurePoints.explanation = keyStructureExplanation;

  // 2. Technical Protection Points (30%)
  let technicalProtectionLevel: number | null = null;
  let technicalProtectionExplanation = '';

  // ATR-based stop loss with extra buffer
  if (signal === 'LONG') {
    technicalProtectionLevel = currentPrice - (currentATR * 1.5); // 1.5 ATR for more space
    technicalProtectionExplanation = `1.5 ATR (${(currentATR * 1.5).toFixed(2)}) below entry price`;
  } else {
    technicalProtectionLevel = currentPrice + (currentATR * 1.5); // 1.5 ATR for more space
    technicalProtectionExplanation = `1.5 ATR (${(currentATR * 1.5).toFixed(2)}) above entry price`;
  }

  // Check for recent gaps
  const recentCandles = candles.slice(-20); // Look at last 20 candles

  for (let i = 1; i < recentCandles.length; i++) {
    const currentCandle = recentCandles[i];
    const previousCandle = recentCandles[i-1];

    if (signal === 'LONG') {
      // Look for gap up (potential support)
      if (currentCandle.low > previousCandle.high) {
        const gapLevel = previousCandle.high;

        if (gapLevel < currentPrice && (technicalProtectionLevel === null || gapLevel > technicalProtectionLevel)) {
          technicalProtectionLevel = gapLevel;
          technicalProtectionExplanation = `Recent gap up support at ${gapLevel.toFixed(2)}`;
          break;
        }
      }
    } else { // SHORT
      // Look for gap down (potential resistance)
      if (currentCandle.high < previousCandle.low) {
        const gapLevel = previousCandle.low;

        if (gapLevel > currentPrice && (technicalProtectionLevel === null || gapLevel < technicalProtectionLevel)) {
          technicalProtectionLevel = gapLevel;
          technicalProtectionExplanation = `Recent gap down resistance at ${gapLevel.toFixed(2)}`;
          break;
        }
      }
    }
  }

  result.stopLossFactors.technicalProtectionPoints.level = technicalProtectionLevel;
  result.stopLossFactors.technicalProtectionPoints.explanation = technicalProtectionExplanation;

  // 3. Risk Management Parameters (30%)
  let riskManagementLevel: number | null = null;
  let riskManagementExplanation = '';

  // Maximum risk based on volatility
  const maxRiskPercentage = Math.min(2.5, Math.max(1.0, currentATR / currentPrice * 100));

  if (signal === 'LONG') {
    riskManagementLevel = currentPrice * (1 - maxRiskPercentage / 100);
    riskManagementExplanation = `Maximum risk of ${maxRiskPercentage.toFixed(1)}% based on volatility`;
  } else {
    riskManagementLevel = currentPrice * (1 + maxRiskPercentage / 100);
    riskManagementExplanation = `Maximum risk of ${maxRiskPercentage.toFixed(1)}% based on volatility`;
  }

  result.stopLossFactors.riskManagementParameters.level = riskManagementLevel;
  result.stopLossFactors.riskManagementParameters.explanation = riskManagementExplanation;

  // ===== TAKE PROFIT CALCULATION =====

  // 1. Key Resistance/Support Targets (40%)
  let keyResistanceSupportLevel: number | null = null;
  let keyResistanceSupportExplanation = '';

  if (signal === 'LONG') {
    // For long positions, find the nearest resistance level
    if (resistanceLevels.length > 0) {
      keyResistanceSupportLevel = resistanceLevels[0];
      keyResistanceSupportExplanation = `Nearest resistance level at ${resistanceLevels[0].toFixed(2)}`;
    }

    // Check for psychological levels (round numbers)
    const nextRoundNumber = Math.ceil(currentPrice / 10) * 10;
    if (nextRoundNumber > currentPrice && (keyResistanceSupportLevel === null || nextRoundNumber < keyResistanceSupportLevel)) {
      keyResistanceSupportLevel = nextRoundNumber;
      keyResistanceSupportExplanation = `Psychological round number at ${nextRoundNumber.toFixed(2)}`;
    }
  } else { // SHORT
    // For short positions, find the nearest support level
    if (supportLevels.length > 0) {
      keyResistanceSupportLevel = supportLevels[0];
      keyResistanceSupportExplanation = `Nearest support level at ${supportLevels[0].toFixed(2)}`;
    }

    // Check for psychological levels (round numbers)
    const nextRoundNumber = Math.floor(currentPrice / 10) * 10;
    if (nextRoundNumber < currentPrice && (keyResistanceSupportLevel === null || nextRoundNumber > keyResistanceSupportLevel)) {
      keyResistanceSupportLevel = nextRoundNumber;
      keyResistanceSupportExplanation = `Psychological round number at ${nextRoundNumber.toFixed(2)}`;
    }
  }

  result.takeProfitFactors.keyResistanceSupportTargets.level = keyResistanceSupportLevel;
  result.takeProfitFactors.keyResistanceSupportTargets.explanation = keyResistanceSupportExplanation;

  // 2. Fibonacci Projection Targets (35%)
  let fibonacciLevel: number | null = null;
  let fibonacciExplanation = '';

  // Find the most recent swing high and low for Fibonacci projections
  if (swingHighs.length > 0 && swingLows.length > 0) {
    const recentSwingHigh = candles[swingHighs[swingHighs.length - 1]].high;
    const recentSwingLow = candles[swingLows[swingLows.length - 1]].low;
    const swingRange = recentSwingHigh - recentSwingLow;

    if (signal === 'LONG') {
      // For long positions, project up from the recent swing low
      const fib05 = recentSwingLow + swingRange * 0.5;
      const fib618 = recentSwingLow + swingRange * 0.618;
      const fib100 = recentSwingLow + swingRange * 1.0;
      const fib127 = recentSwingLow + swingRange * 1.27;

      // Prioritize the 0.5 Fibonacci level if it's above current price
      if (fib05 > currentPrice) {
        fibonacciLevel = fib05;
        fibonacciExplanation = `Fibonacci 0.5 retracement at ${fib05.toFixed(2)}`;
      } else if (fib618 > currentPrice) {
        fibonacciLevel = fib618;
        fibonacciExplanation = `Fibonacci 0.618 retracement at ${fib618.toFixed(2)}`;
      } else if (fib100 > currentPrice) {
        fibonacciLevel = fib100;
        fibonacciExplanation = `Fibonacci 1.0 extension at ${fib100.toFixed(2)}`;
      } else if (fib127 > currentPrice) {
        fibonacciLevel = fib127;
        fibonacciExplanation = `Fibonacci 1.27 extension at ${fib127.toFixed(2)}`;
      }

      // Check if the target is beyond the historical high
      const historicalHigh = Math.max(...candles.map(c => c.high));
      if (fibonacciLevel !== null && fibonacciLevel > historicalHigh) {
        // Cap at historical high
        fibonacciLevel = historicalHigh * 0.99; // Slightly below historical high
        fibonacciExplanation = `Capped at historical high ${historicalHigh.toFixed(2)}`;
      }
    } else { // SHORT
      // For short positions, project down from the recent swing high
      const fib05 = recentSwingHigh - swingRange * 0.5;
      const fib618 = recentSwingHigh - swingRange * 0.618;
      const fib100 = recentSwingHigh - swingRange * 1.0;
      const fib127 = recentSwingHigh - swingRange * 1.27;

      // Prioritize the 0.5 Fibonacci level if it's below current price
      if (fib05 < currentPrice) {
        fibonacciLevel = fib05;
        fibonacciExplanation = `Fibonacci 0.5 retracement at ${fib05.toFixed(2)}`;
      } else if (fib618 < currentPrice) {
        fibonacciLevel = fib618;
        fibonacciExplanation = `Fibonacci 0.618 retracement at ${fib618.toFixed(2)}`;
      } else if (fib100 < currentPrice) {
        fibonacciLevel = fib100;
        fibonacciExplanation = `Fibonacci 1.0 extension at ${fib100.toFixed(2)}`;
      } else if (fib127 < currentPrice) {
        fibonacciLevel = fib127;
        fibonacciExplanation = `Fibonacci 1.27 extension at ${fib127.toFixed(2)}`;
      }

      // Check if the target is beyond the historical low
      const historicalLow = Math.min(...candles.map(c => c.low));
      if (fibonacciLevel !== null && fibonacciLevel < historicalLow) {
        // Cap at historical low
        fibonacciLevel = historicalLow * 1.01; // Slightly above historical low
        fibonacciExplanation = `Capped at historical low ${historicalLow.toFixed(2)}`;
      }
    }
  }

  result.takeProfitFactors.fibonacciProjectionTargets.level = fibonacciLevel;
  result.takeProfitFactors.fibonacciProjectionTargets.explanation = fibonacciExplanation;

  // 3. Momentum-Based Targets (25%)
  let momentumLevel: number | null = null;
  let momentumExplanation = '';

  // Volatility-based extensions (2-3 ATR from entry)
  if (signal === 'LONG') {
    momentumLevel = currentPrice + (2.5 * currentATR);
    momentumExplanation = `2.5 ATR (${(2.5 * currentATR).toFixed(2)}) above entry price`;
  } else {
    momentumLevel = currentPrice - (2.5 * currentATR);
    momentumExplanation = `2.5 ATR (${(2.5 * currentATR).toFixed(2)}) below entry price`;
  }

  result.takeProfitFactors.momentumBasedTargets.level = momentumLevel;
  result.takeProfitFactors.momentumBasedTargets.explanation = momentumExplanation;

  // ===== FINAL CALCULATION =====

  // Calculate weighted stop loss
  let stopLossSum = 0;
  let stopLossWeightSum = 0;

  if (result.stopLossFactors.keyStructurePoints.level !== null) {
    stopLossSum += result.stopLossFactors.keyStructurePoints.level * result.stopLossFactors.keyStructurePoints.weight;
    stopLossWeightSum += result.stopLossFactors.keyStructurePoints.weight;
  }

  if (result.stopLossFactors.technicalProtectionPoints.level !== null) {
    stopLossSum += result.stopLossFactors.technicalProtectionPoints.level * result.stopLossFactors.technicalProtectionPoints.weight;
    stopLossWeightSum += result.stopLossFactors.technicalProtectionPoints.weight;
  }

  if (result.stopLossFactors.riskManagementParameters.level !== null) {
    stopLossSum += result.stopLossFactors.riskManagementParameters.level * result.stopLossFactors.riskManagementParameters.weight;
    stopLossWeightSum += result.stopLossFactors.riskManagementParameters.weight;
  }

  // Calculate weighted take profit
  let takeProfitSum = 0;
  let takeProfitWeightSum = 0;

  if (result.takeProfitFactors.keyResistanceSupportTargets.level !== null) {
    takeProfitSum += result.takeProfitFactors.keyResistanceSupportTargets.level * result.takeProfitFactors.keyResistanceSupportTargets.weight;
    takeProfitWeightSum += result.takeProfitFactors.keyResistanceSupportTargets.weight;
  }

  if (result.takeProfitFactors.fibonacciProjectionTargets.level !== null) {
    takeProfitSum += result.takeProfitFactors.fibonacciProjectionTargets.level * result.takeProfitFactors.fibonacciProjectionTargets.weight;
    takeProfitWeightSum += result.takeProfitFactors.fibonacciProjectionTargets.weight;
  }

  if (result.takeProfitFactors.momentumBasedTargets.level !== null) {
    takeProfitSum += result.takeProfitFactors.momentumBasedTargets.level * result.takeProfitFactors.momentumBasedTargets.weight;
    takeProfitWeightSum += result.takeProfitFactors.momentumBasedTargets.weight;
  }

  // Calculate final stop loss and take profit levels
  if (stopLossWeightSum > 0) {
    result.stopLoss = stopLossSum / stopLossWeightSum;
  } else {
    // Fallback to ATR-based stop loss
    result.stopLoss = signal === 'LONG' ? currentPrice - currentATR : currentPrice + currentATR;
  }

  if (takeProfitWeightSum > 0) {
    result.takeProfit = takeProfitSum / takeProfitWeightSum;
  } else {
    // Fallback to ATR-based take profit
    result.takeProfit = signal === 'LONG' ? currentPrice + (2 * currentATR) : currentPrice - (2 * currentATR);
  }

  // Ensure risk-reward ratio is at least 1:1
  const riskAmount = Math.abs(currentPrice - result.stopLoss);
  const rewardAmount = Math.abs(result.takeProfit - currentPrice);
  result.riskRewardRatio = rewardAmount / riskAmount;

  if (result.riskRewardRatio < 1.0) {
    // Adjust take profit to ensure at least 1:1 risk-reward
    if (signal === 'LONG') {
      result.takeProfit = currentPrice + riskAmount;
    } else {
      result.takeProfit = currentPrice - riskAmount;
    }
    result.riskRewardRatio = 1.0;
  }

  // Cap risk-reward at 2.5:1 if it's too high
  if (result.riskRewardRatio > 2.5) {
    // Adjust take profit to cap risk-reward at 2.5:1
    if (signal === 'LONG') {
      result.takeProfit = currentPrice + (riskAmount * 2.5);
    } else {
      result.takeProfit = currentPrice - (riskAmount * 2.5);
    }
    result.riskRewardRatio = 2.5;
  }

  // Generate explanations
  result.stopLossExplanation = `Weighted stop loss based on: ${result.stopLossFactors.keyStructurePoints.explanation}, ${result.stopLossFactors.technicalProtectionPoints.explanation}, and ${result.stopLossFactors.riskManagementParameters.explanation}`;

  result.takeProfitExplanation = `Weighted take profit based on: ${result.takeProfitFactors.keyResistanceSupportTargets.explanation}, ${result.takeProfitFactors.fibonacciProjectionTargets.explanation}, and ${result.takeProfitFactors.momentumBasedTargets.explanation}`;

  return result;
}
