// Market Scanner for Aura
// This module scans the market for high-confidence trade opportunities

import { fetchHistoricalData, generateTechnicalAnalysis } from "./technical-analysis.ts";
import { SP500_STOCKS } from "./data/sp500-stocks.ts";
import { RUSSELL2000_STOCKS } from "./data/russell2000-stocks.ts";
import { NASDAQ_STOCKS } from "./data/nasdaq-stocks.ts";
import { NASDAQ100_STOCKS } from "./data/nasdaq100-stocks.ts";
import { ALL_STOCKS } from "./data/all-stocks.ts";
import { recommendOptionsContracts, OptionsRecommendation } from './options-analysis.ts';

// Interface for scanner results
export interface ScannerResult {
  symbol: string;
  signal: 'LONG' | 'SHORT' | 'NEUTRAL';
  confidence: 'LOW' | 'MEDIUM' | 'HIGH' | 'VERY_HIGH';
  confidenceScore: number; // 0-100 scale
  currentPrice: number;
  stopLoss: number;
  takeProfit: number;
  riskRewardRatio: number;
  potentialReturn: number;
  potentialReturnPercent: number;
  marketStructure?: string;
  keyLevels?: {
    support: number;
    resistance: number;
  };
  bullishScore?: number;
  bearishScore?: number;
  trend?: string;
  analysis: string;
}



// Interface for simplified scanner results
export interface SimplifiedScannerResult {
  symbol: string;
  signal: 'LONG' | 'SHORT' | 'NEUTRAL';
  price: number;
  stopLoss: number;
  takeProfit: number;
  support: number;
  resistance: number;
  confidence: 'LOW' | 'MEDIUM' | 'HIGH' | 'VERY_HIGH';
  confidenceScore: number;
  bullishScore: number;
  bearishScore: number;
  marketEnvironment: string;
  trend: string;
  optionsRecommendation?: {
    recommendation: string;
    reasoning: string;
  };
}

/**
 * Scan the S&P 500 for high-confidence trade opportunities
 * @param apiKey - Polygon API key
 * @param startDate - Start date for historical data
 * @param endDate - End date for historical data
 * @param timeframe - Timeframe for analysis (e.g., 'day', '15minute')
 * @param minConfidenceScore - Minimum confidence score (0-100) to include in results
 * @param maxResults - Maximum number of results to return
 * @param minPrice - Minimum stock price to include in results
 * @param maxPrice - Maximum stock price to include in results
 * @param marketStructureType - Type of market structure to filter for ('gap', 'range', 'breakout', 'trending', or 'any')
 * @returns Array of scanner results
 */
export async function scanSP500(
  apiKey: string,
  startDate: string,
  endDate: string,
  timeframe: string = 'day',
  minConfidenceScore: number = 90,
  maxResults: number = 10,
  minPrice: number = 0,
  maxPrice: number = Infinity,
  marketStructureType: string = 'any'
): Promise<ScannerResult[]> {
  // Initialize results array
  const results: ScannerResult[] = [];

  // Log the start of the scan
  console.log(`Starting S&P 500 scan for high-confidence trades (${timeframe} timeframe)...`);
  console.log(`Using date range: ${startDate} to ${endDate}. Data will be filtered to this range.`);

  // Process stocks in batches to avoid rate limits
  const batchSize = 10;
  const batches = Math.ceil(SP500_STOCKS.length / batchSize);

  for (let i = 0; i < batches; i++) {
    const batchStart = i * batchSize;
    const batchEnd = Math.min((i + 1) * batchSize, SP500_STOCKS.length);
    const batch = SP500_STOCKS.slice(batchStart, batchEnd);

    console.log(`Processing batch ${i + 1}/${batches} (${batch.join(', ')})...`);

    // Process each stock in the batch concurrently
    const batchPromises = batch.map(symbol => processStock(symbol, apiKey, startDate, endDate, timeframe, minConfidenceScore, minPrice, maxPrice, marketStructureType));
    const batchResults = await Promise.all(batchPromises);

    // Add valid results to the results array
    for (const result of batchResults) {
      if (result) {
        results.push(result);
      }
    }

    // Sort results by confidence score (descending)
    results.sort((a, b) => b.confidenceScore - a.confidenceScore);

    // If we have enough results, we can stop early
    if (results.length >= maxResults) {
      break;
    }

    // Add a small delay between batches to avoid rate limits
    if (i < batches - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  // Return the top results
  return results.slice(0, maxResults);
}

/**
 * Scan the Russell 2000 for high-confidence trade opportunities
 * @param apiKey - Polygon API key
 * @param startDate - Start date for historical data
 * @param endDate - End date for historical data
 * @param timeframe - Timeframe for analysis (e.g., 'day', '15minute')
 * @param minConfidenceScore - Minimum confidence score (0-100) to include in results
 * @param maxResults - Maximum number of results to return
 * @param minPrice - Minimum stock price to include in results
 * @param maxPrice - Maximum stock price to include in results
 * @param marketStructureType - Type of market structure to filter for ('gap', 'range', 'breakout', 'trending', or 'any')
 * @returns Array of scanner results
 */
export async function scanRussell2000(
  apiKey: string,
  startDate: string,
  endDate: string,
  timeframe: string = 'day',
  minConfidenceScore: number = 90,
  maxResults: number = 10,
  minPrice: number = 0,
  maxPrice: number = Infinity,
  marketStructureType: string = 'any'
): Promise<ScannerResult[]> {
  // Initialize results array
  const results: ScannerResult[] = [];

  // Log the start of the scan
  console.log(`Starting Russell 2000 scan for high-confidence trades (${timeframe} timeframe)...`);
  console.log(`Using date range: ${startDate} to ${endDate}. Data will be filtered to this range.`);

  // Process stocks in batches to avoid rate limits
  const batchSize = 10;
  const batches = Math.ceil(RUSSELL2000_STOCKS.length / batchSize);

  for (let i = 0; i < batches; i++) {
    const batchStart = i * batchSize;
    const batchEnd = Math.min((i + 1) * batchSize, RUSSELL2000_STOCKS.length);
    const batch = RUSSELL2000_STOCKS.slice(batchStart, batchEnd);

    console.log(`Processing batch ${i + 1}/${batches} (${batch.join(', ')})...`);

    // Process each stock in the batch concurrently
    const batchPromises = batch.map(symbol => processStock(symbol, apiKey, startDate, endDate, timeframe, minConfidenceScore, minPrice, maxPrice, marketStructureType));
    const batchResults = await Promise.all(batchPromises);

    // Add valid results to the results array
    for (const result of batchResults) {
      if (result) {
        results.push(result);
      }
    }

    // Sort results by confidence score (descending)
    results.sort((a, b) => b.confidenceScore - a.confidenceScore);

    // If we have enough results, we can stop early
    if (results.length >= maxResults) {
      break;
    }

    // Add a small delay between batches to avoid rate limits
    if (i < batches - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  // Return the top results
  return results.slice(0, maxResults);
}

/**
 * Scan the NASDAQ for high-confidence trade opportunities
 * @param apiKey - Polygon API key
 * @param startDate - Start date for historical data
 * @param endDate - End date for historical data
 * @param timeframe - Timeframe for analysis (e.g., 'day', '15minute')
 * @param minConfidenceScore - Minimum confidence score (0-100) to include in results
 * @param maxResults - Maximum number of results to return
 * @param minPrice - Minimum stock price to include in results
 * @param maxPrice - Maximum stock price to include in results
 * @param marketStructureType - Type of market structure to filter for ('gap', 'range', 'breakout', 'trending', or 'any')
 * @returns Array of scanner results
 */
export async function scanNASDAQ(
  apiKey: string,
  startDate: string,
  endDate: string,
  timeframe: string = 'day',
  minConfidenceScore: number = 90,
  maxResults: number = 10,
  minPrice: number = 0,
  maxPrice: number = Infinity,
  marketStructureType: string = 'any'
): Promise<ScannerResult[]> {
  // Initialize results array
  const results: ScannerResult[] = [];

  // Log the start of the scan
  console.log(`Starting NASDAQ scan for high-confidence trades (${timeframe} timeframe)...`);
  console.log(`Using date range: ${startDate} to ${endDate}. Data will be filtered to this range.`);

  // Process stocks in batches to avoid rate limits
  const batchSize = 10;
  const batches = Math.ceil(NASDAQ_STOCKS.length / batchSize);

  for (let i = 0; i < batches; i++) {
    const batchStart = i * batchSize;
    const batchEnd = Math.min((i + 1) * batchSize, NASDAQ_STOCKS.length);
    const batch = NASDAQ_STOCKS.slice(batchStart, batchEnd);

    console.log(`Processing batch ${i + 1}/${batches} (${batch.join(', ')})...`);

    // Process each stock in the batch concurrently
    const batchPromises = batch.map(symbol => processStock(symbol, apiKey, startDate, endDate, timeframe, minConfidenceScore, minPrice, maxPrice, marketStructureType));
    const batchResults = await Promise.all(batchPromises);

    // Add valid results to the results array
    for (const result of batchResults) {
      if (result) {
        results.push(result);
      }
    }

    // Sort results by confidence score (descending)
    results.sort((a, b) => b.confidenceScore - a.confidenceScore);

    // If we have enough results, we can stop early
    if (results.length >= maxResults) {
      break;
    }

    // Add a small delay between batches to avoid rate limits
    if (i < batches - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  // Return the top results
  return results.slice(0, maxResults);
}

/**
 * Scan the NASDAQ 100 for high-confidence trade opportunities
 * @param apiKey - Polygon API key
 * @param startDate - Start date for historical data
 * @param endDate - End date for historical data
 * @param timeframe - Timeframe for analysis (e.g., 'day', '15minute')
 * @param minConfidenceScore - Minimum confidence score (0-100) to include in results
 * @param maxResults - Maximum number of results to return
 * @param minPrice - Minimum stock price to include in results
 * @param maxPrice - Maximum stock price to include in results
 * @param marketStructureType - Type of market structure to filter for ('gap', 'range', 'breakout', 'trending', or 'any')
 * @returns Array of scanner results
 */
export async function scanNASDAQ100(
  apiKey: string,
  startDate: string,
  endDate: string,
  timeframe: string = 'day',
  minConfidenceScore: number = 90,
  maxResults: number = 10,
  minPrice: number = 0,
  maxPrice: number = Infinity,
  marketStructureType: string = 'any'
): Promise<ScannerResult[]> {
  // Initialize results array
  const results: ScannerResult[] = [];

  // Log the start of the scan
  console.log(`Starting NASDAQ 100 scan for high-confidence trades (${timeframe} timeframe)...`);
  console.log(`Using date range: ${startDate} to ${endDate}. Data will be filtered to this range.`);

  // Process stocks in batches to avoid rate limits
  const batchSize = 10;
  const batches = Math.ceil(NASDAQ100_STOCKS.length / batchSize);

  for (let i = 0; i < batches; i++) {
    const batchStart = i * batchSize;
    const batchEnd = Math.min((i + 1) * batchSize, NASDAQ100_STOCKS.length);
    const batch = NASDAQ100_STOCKS.slice(batchStart, batchEnd);

    console.log(`Processing batch ${i + 1}/${batches} (${batch.join(', ')})...`);

    // Process each stock in the batch concurrently
    const batchPromises = batch.map(symbol => processStock(symbol, apiKey, startDate, endDate, timeframe, minConfidenceScore, minPrice, maxPrice, marketStructureType));
    const batchResults = await Promise.all(batchPromises);

    // Add valid results to the results array
    for (const result of batchResults) {
      if (result) {
        results.push(result);
      }
    }

    // Sort results by confidence score (descending)
    results.sort((a, b) => b.confidenceScore - a.confidenceScore);

    // If we have enough results, we can stop early
    if (results.length >= maxResults) {
      break;
    }

    // Add a small delay between batches to avoid rate limits
    if (i < batches - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  // Return the top results
  return results.slice(0, maxResults);
}

/**
 * Scan all stocks across major indices for high-confidence trade opportunities
 * @param apiKey - Polygon API key
 * @param startDate - Start date for historical data
 * @param endDate - End date for historical data
 * @param timeframe - Timeframe for analysis (e.g., 'day', '15minute')
 * @param minConfidenceScore - Minimum confidence score (0-100) to include in results
 * @param maxResults - Maximum number of results to return
 * @param minPrice - Minimum stock price to include in results
 * @param maxPrice - Maximum stock price to include in results
 * @param marketStructureType - Type of market structure to filter for ('gap', 'range', 'breakout', 'trending', or 'any')
 * @returns Array of scanner results
 */
/**
 * Scan a specific list of stocks for high-confidence trading opportunities
 * @param stockList - Array of stock symbols to scan
 * @param apiKey - Polygon API key
 * @param startDate - Start date for historical data
 * @param endDate - End date for historical data
 * @param timeframe - Timeframe for analysis
 * @param minConfidenceScore - Minimum confidence score to include in results
 * @param maxResults - Maximum number of results to return
 * @param minPrice - Minimum stock price to include in results
 * @param maxPrice - Maximum stock price to include in results
 * @param marketStructureType - Type of market structure to filter for ('gap', 'range', 'breakout', 'trending', or 'any')
 * @returns Array of scanner results
 */
export async function scanStockList(
  stockList: string[],
  apiKey: string,
  startDate: string,
  endDate: string,
  timeframe: string = 'day',
  minConfidenceScore: number = 90,
  maxResults: number = 10,
  minPrice: number = 0,
  maxPrice: number = Infinity,
  marketStructureType: string = 'any'
): Promise<ScannerResult[]> {
  // Initialize results array
  const results: ScannerResult[] = [];

  // Log the start of the scan
  console.log(`Starting scan of ${stockList.length} stocks for high-confidence trades (${timeframe} timeframe)...`);
  console.log(`Using date range: ${startDate} to ${endDate}. Data will be filtered to this range.`);

  // Process stocks in batches to avoid rate limits
  const batchSize = 20;
  const batches = Math.ceil(stockList.length / batchSize);

  console.log(`Will process ${batches} batches of up to ${batchSize} stocks each`);

  for (let i = 0; i < batches; i++) {
    const batchStart = i * batchSize;
    const batchEnd = Math.min((i + 1) * batchSize, stockList.length);
    const batch = stockList.slice(batchStart, batchEnd);

    console.log(`Processing batch ${i + 1}/${batches} (${batch.length} stocks)...`);

    // Process each stock in the batch concurrently
    const batchPromises = batch.map(symbol => processStock(symbol, apiKey, startDate, endDate, timeframe, minConfidenceScore, minPrice, maxPrice, marketStructureType));
    const batchResults = await Promise.all(batchPromises);

    // Add valid results to the results array
    for (const result of batchResults) {
      if (result) {
        results.push(result);
        console.log(`Found valid result for ${result.symbol} (${results.length}/${maxResults})`);
      }
    }

    // Sort results by confidence score (descending)
    results.sort((a, b) => b.confidenceScore - a.confidenceScore);

    // If we have enough results, we can stop early
    if (results.length >= maxResults) {
      console.log(`Reached target of ${maxResults} results. Stopping scan.`);
      break;
    }

    // Add a small delay between batches to avoid rate limits
    if (i < batches - 1) {
      const delayMs = 1500; // Increased delay to be more conservative with API rate limits
      console.log(`Waiting ${delayMs}ms before next batch...`);
      await new Promise(resolve => setTimeout(resolve, delayMs));
    }
  }

  // Return the top results
  return results.slice(0, maxResults);
}

/**
 * Scan all stocks for high-confidence trading opportunities
 * @param apiKey - Polygon API key
 * @param startDate - Start date for historical data
 * @param endDate - End date for historical data
 * @param timeframe - Timeframe for analysis
 * @param minConfidenceScore - Minimum confidence score to include in results
 * @param maxResults - Maximum number of results to return
 * @param minPrice - Minimum stock price to include in results
 * @param maxPrice - Maximum stock price to include in results
 * @param marketStructureType - Type of market structure to filter for ('gap', 'range', 'breakout', 'trending', or 'any')
 * @returns Array of scanner results
 */
export async function scanAllStocks(
  apiKey: string,
  startDate: string,
  endDate: string,
  timeframe: string = 'day',
  minConfidenceScore: number = 90,
  maxResults: number = 10,
  minPrice: number = 0,
  maxPrice: number = Infinity,
  marketStructureType: string = 'any'
): Promise<ScannerResult[]> {
  // Simply call scanStockList with ALL_STOCKS
  return scanStockList(
    ALL_STOCKS,
    apiKey,
    startDate,
    endDate,
    timeframe,
    minConfidenceScore,
    maxResults,
    minPrice,
    maxPrice,
    marketStructureType
  );
}

/**
 * Process a single stock and return a result if it meets the criteria
 * @param symbol - Stock symbol
 * @param apiKey - Polygon API key
 * @param startDate - Start date for historical data
 * @param endDate - End date for historical data
 * @param timeframe - Timeframe for analysis
 * @param minConfidenceScore - Minimum confidence score to include
 * @param minPrice - Minimum stock price to include
 * @param maxPrice - Maximum stock price to include
 * @param marketStructureType - Type of market structure to filter for ('gap', 'range', 'breakout', 'trending', or 'any')
 * @returns Scanner result or null if it doesn't meet criteria
 */
async function processStock(
  symbol: string,
  apiKey: string,
  startDate: string,
  endDate: string,
  timeframe: string,
  minConfidenceScore: number,
  minPrice: number = 0,
  maxPrice: number = Infinity,
  marketStructureType: string = 'any'
): Promise<ScannerResult | null> {
  try {
    // Fetch historical data
    const historicalData = await fetchHistoricalData(symbol, startDate, endDate, apiKey, timeframe);

    if (!historicalData.data || historicalData.data.length === 0) {
      console.log(`No historical data available for ${symbol}`);
      return null;
    }

    // Filter the data to ensure it's within the specified date range
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);

    // Set time to beginning/end of day to ensure proper comparison
    startDateObj.setHours(0, 0, 0, 0);
    endDateObj.setHours(23, 59, 59, 999);

    // Check if we're analyzing historical data or current data
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const isFutureEndDate = endDateObj > today;

    if (isFutureEndDate) {
      console.log(`End date ${endDate} is in the future. Will be capped at current date.`);
      endDateObj.setTime(today.getTime());
      endDateObj.setHours(23, 59, 59, 999);
    }

    console.log(`Filtering data for ${symbol} to date range: ${startDateObj.toISOString().split('T')[0]} to ${endDateObj.toISOString().split('T')[0]}`);

    const filteredData = historicalData.data.filter(candle => {
      const candleDate = new Date(candle.date);
      return candleDate >= startDateObj && candleDate <= endDateObj;
    });

    // Update the historical data with the filtered data
    historicalData.data = filteredData;

    if (filteredData.length === 0) {
      console.log(`No historical data available within the specified date range for ${symbol}`);
      return null;
    }

    // Log information about the date range
    if (filteredData.length > 0) {
      const firstDate = new Date(filteredData[0].date);
      const lastDate = new Date(filteredData[filteredData.length - 1].date);
      console.log(`Filtered data for ${symbol} contains ${filteredData.length} candles from ${firstDate.toISOString().split('T')[0]} to ${lastDate.toISOString().split('T')[0]}`);
    }

    // Generate technical analysis
    const analysis = await generateTechnicalAnalysis(symbol, historicalData.data, timeframe, apiKey);

    // Check if the symbol is a cryptocurrency
    const isCrypto = /^X:.*USD$/.test(symbol) ||
                    /^(BTC|ETH|XRP|LTC|DOGE|SOL|ADA|DOT|AVAX|MATIC|LINK|UNI|SHIB)$/.test(symbol.toUpperCase());

    // Skip if no signal or neutral signal (but cryptocurrencies should never have NEUTRAL signals now)
    if (!analysis.signal || (analysis.signal === 'NEUTRAL' && !isCrypto)) {
      return null;
    }

    // Double-check that cryptocurrencies never have NEUTRAL signals
    if (isCrypto && analysis.signal === 'NEUTRAL') {
      console.log(`WARNING: Crypto ${symbol} still has NEUTRAL signal. Forcing to LONG based on default.`);
      analysis.signal = 'LONG';
      analysis.confidence = 'LOW';
    }

    // Skip if price is outside the specified range
    const currentPrice = analysis.currentPrice;
    if (currentPrice < minPrice || currentPrice > maxPrice) {
      console.log(`Skipping ${symbol} - price ${currentPrice} is outside range [${minPrice}, ${maxPrice}]`);
      return null;
    }

    // Skip if market structure doesn't match the requested type
    if (marketStructureType !== 'any') {
      // Get market structure from analysis
      const structure = analysis.marketStructure?.structure;
      const marketContext = analysis.marketContext?.environment;
      const gapPresent = analysis.gapAnalysis?.hasGap;

      // Map the requested market structure type to the actual structure in the analysis
      let matchesStructure = false;

      switch (marketStructureType.toLowerCase()) {
        case 'range':
          // Range detection:
          // 1. Explicit range structure or environment
          // 2. ADX < 20 (weak trend)
          // 3. Price oscillating between support and resistance
          matchesStructure =
            structure === 'range' ||
            marketContext === 'range' ||
            (analysis.indicators?.adx?.value < 20) ||
            (analysis.marketStructure?.priceLocation === 'mid_range');
          break;

        case 'trending':
          // Trending detection:
          // 1. Explicit uptrend or downtrend structure
          // 2. Trending market context
          // 3. ADX > 20-25 (strong trend)
          // 4. Higher highs and higher lows (uptrend) or lower highs and lower lows (downtrend)
          const isUptrend =
            structure === 'uptrend' ||
            (analysis.marketStructure?.higherHighs && analysis.marketStructure?.higherLows);

          const isDowntrend =
            structure === 'downtrend' ||
            (analysis.marketStructure?.lowerHighs && analysis.marketStructure?.lowerLows);

          const hasStrongADX = analysis.indicators?.adx?.value > 20;

          matchesStructure =
            isUptrend ||
            isDowntrend ||
            marketContext === 'trending' ||
            hasStrongADX;
          break;

        case 'breakout':
          // Breakout detection:
          // 1. Explicit breakout environment
          // 2. Recent price action breaking above resistance or below support
          // 3. Increased volume on breakout
          // 4. Bollinger band expansion after squeeze
          const breakingResistance = analysis.supportResistance?.breakingResistance;
          const breakingSupport = analysis.supportResistance?.breakingSupport;
          const volumeIncrease = analysis.volumeProfile?.increasedVolume;
          const bbExpansion = analysis.indicators?.bollingerBands?.expanding;

          matchesStructure =
            marketContext === 'breakout' ||
            breakingResistance ||
            breakingSupport ||
            (bbExpansion && volumeIncrease);
          break;

        case 'gap':
          // Gap detection:
          // Check if there's a gap in recent price action
          matchesStructure = !!gapPresent;
          break;

        default:
          // If we don't recognize the structure type, default to any
          matchesStructure = true;
      }

      // Log detailed information about the market structure matching
      if (matchesStructure) {
        console.log(`${symbol} matches ${marketStructureType} market structure:`);

        if (marketStructureType === 'range') {
          console.log(`  - Structure: ${structure}`);
          console.log(`  - Market context: ${marketContext}`);
          console.log(`  - ADX value: ${analysis.indicators?.adx?.value}`);
          console.log(`  - Price location: ${analysis.marketStructure?.priceLocation}`);

          // Ensure marketStructure is set to 'range' for consistent reporting
          if (!analysis.marketStructure) {
            analysis.marketStructure = { structure: 'range' };
          } else {
            analysis.marketStructure.structure = 'range';
          }
        }
        else if (marketStructureType === 'trending') {
          console.log(`  - Structure: ${structure}`);
          console.log(`  - Higher highs: ${analysis.marketStructure?.higherHighs}`);
          console.log(`  - Higher lows: ${analysis.marketStructure?.higherLows}`);
          console.log(`  - Lower highs: ${analysis.marketStructure?.lowerHighs}`);
          console.log(`  - Lower lows: ${analysis.marketStructure?.lowerLows}`);
          console.log(`  - ADX value: ${analysis.indicators?.adx?.value}`);

          // Determine if it's an uptrend or downtrend
          let trendType = 'uptrend';
          if (analysis.marketStructure?.lowerHighs && analysis.marketStructure?.lowerLows) {
            trendType = 'downtrend';
          }

          // Ensure marketStructure is set to the appropriate trend for consistent reporting
          if (!analysis.marketStructure) {
            analysis.marketStructure = { structure: trendType };
          } else {
            analysis.marketStructure.structure = trendType;
          }
        }
        else if (marketStructureType === 'breakout') {
          console.log(`  - Market context: ${marketContext}`);
          console.log(`  - Breaking resistance: ${analysis.supportResistance?.breakingResistance}`);
          console.log(`  - Breaking support: ${analysis.supportResistance?.breakingSupport}`);
          console.log(`  - Volume increase: ${analysis.volumeProfile?.increasedVolume}`);
          console.log(`  - BB expansion: ${analysis.indicators?.bollingerBands?.expanding}`);

          // Ensure marketStructure is set to 'breakout' for consistent reporting
          if (!analysis.marketStructure) {
            analysis.marketStructure = { structure: 'breakout' };
          } else {
            analysis.marketStructure.structure = 'breakout';
          }
        }
        else if (marketStructureType === 'gap') {
          console.log(`  - Gap present: ${gapPresent}`);
          if (analysis.gapAnalysis) {
            console.log(`  - Gap details: ${JSON.stringify(analysis.gapAnalysis)}`);
          }

          // Ensure marketStructure is set to 'gap' for consistent reporting
          if (!analysis.marketStructure) {
            analysis.marketStructure = { structure: 'gap' };
          } else {
            analysis.marketStructure.structure = 'gap';
          }
        }
      } else {
        console.log(`Skipping ${symbol} - market structure ${structure || marketContext || 'unknown'} doesn't match requested type ${marketStructureType}`);
        return null;
      }
    }

    // Calculate confidence score (0-100)
    let confidenceScore = 0;

    if (analysis.confidence === 'HIGH') {
      confidenceScore = 80;
    } else if (analysis.confidence === 'MEDIUM') {
      confidenceScore = 60;
    } else if (analysis.confidence === 'LOW') {
      confidenceScore = 40;
    }

    // Boost confidence score based on additional factors

    // 1. Multi-timeframe alignment
    if (analysis.multiTimeframe && analysis.multiTimeframe.alignment === 'strong') {
      confidenceScore += 10;
    }

    // 2. Market structure alignment
    if (analysis.marketStructure) {
      if ((analysis.signal === 'LONG' && analysis.marketStructure.structure === 'uptrend') ||
          (analysis.signal === 'SHORT' && analysis.marketStructure.structure === 'downtrend')) {
        confidenceScore += 5;
      }

      if (analysis.marketStructure.confidence === 'high') {
        confidenceScore += 5;
      }
    }

    // 3. Technical indicator alignment
    if (analysis.technicalScore > 70) {
      confidenceScore += 5;
    }

    // 4. Risk-reward ratio
    if (analysis.riskManagement && analysis.riskManagement.riskRewardRatio > 2) {
      confidenceScore += 5;
    }

    // Cap confidence score at 100
    confidenceScore = Math.min(100, confidenceScore);

    // Skip if confidence score is below minimum
    if (confidenceScore < minConfidenceScore) {
      return null;
    }

    // Determine confidence level based on score
    let confidenceLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'VERY_HIGH' = 'LOW';
    if (confidenceScore >= 90) {
      confidenceLevel = 'VERY_HIGH';
    } else if (confidenceScore >= 70) {
      confidenceLevel = 'HIGH';
    } else if (confidenceScore >= 50) {
      confidenceLevel = 'MEDIUM';
    }

    // Calculate potential return
    const takeProfit = analysis.riskManagement.takeProfit;
    const potentialReturn = analysis.signal === 'LONG'
      ? takeProfit - currentPrice
      : currentPrice - takeProfit;
    const potentialReturnPercent = (potentialReturn / currentPrice) * 100;

    // Extract bullish and bearish scores from the analysis
    const bullishScore = analysis.bullishCount || 0;
    const bearishScore = analysis.bearishCount || 0;

    // Extract trend information
    const trend = analysis.trend || 'unknown';

    // Create result object
    const result: ScannerResult = {
      symbol,
      signal: analysis.signal as 'LONG' | 'SHORT' | 'NEUTRAL',
      confidence: confidenceLevel,
      confidenceScore,
      currentPrice,
      stopLoss: analysis.riskManagement.stopLoss,
      takeProfit,
      riskRewardRatio: analysis.riskManagement.riskRewardRatio,
      potentialReturn,
      potentialReturnPercent,
      marketStructure: analysis.marketStructure?.structure,
      keyLevels: {
        // Calculate fallback support and resistance if none are available
        support: analysis.summary?.keyLevels?.support ||
                analysis.riskManagement?.supportLevel ||
                analysis.supportResistance?.nearestSupport ||
                analysis.findings?.supportResistance?.nearestSupport ||
                analysis.keyLevels?.support?.[0] ||
                (currentPrice * 0.95), // Fallback to 5% below current price
        resistance: analysis.summary?.keyLevels?.resistance ||
                   analysis.riskManagement?.resistanceLevel ||
                   analysis.supportResistance?.nearestResistance ||
                   analysis.findings?.supportResistance?.nearestResistance ||
                   analysis.keyLevels?.resistance?.[0] ||
                   (currentPrice * 1.05) // Fallback to 5% above current price
      },
      bullishScore,
      bearishScore,
      trend,
      analysis: analysis.summary || ''
    };

    console.log(`Found high-confidence trade for ${symbol}: ${result.signal} (${result.confidenceScore}%)`);

    // Log support and resistance values for debugging
    console.log(`${symbol} support/resistance values:`);
    console.log(`  - Final Support: ${result.keyLevels.support}`);
    console.log(`  - Final Resistance: ${result.keyLevels.resistance}`);
    console.log(`  - From summary.keyLevels.support: ${analysis.summary?.keyLevels?.support}`);
    console.log(`  - From summary.keyLevels.resistance: ${analysis.summary?.keyLevels?.resistance}`);
    console.log(`  - From riskManagement.supportLevel: ${analysis.riskManagement?.supportLevel}`);
    console.log(`  - From riskManagement.resistanceLevel: ${analysis.riskManagement?.resistanceLevel}`);
    console.log(`  - From supportResistance.nearestSupport: ${analysis.supportResistance?.nearestSupport}`);
    console.log(`  - From supportResistance.nearestResistance: ${analysis.supportResistance?.nearestResistance}`);
    console.log(`  - From keyLevels.support array: ${analysis.keyLevels?.support?.[0]}`);
    console.log(`  - From keyLevels.resistance array: ${analysis.keyLevels?.resistance?.[0]}`);
    console.log(`  - Fallback support (5% below price): ${currentPrice * 0.95}`);
    console.log(`  - Fallback resistance (5% above price): ${currentPrice * 1.05}`);

    // Log the structure of the analysis object to understand what's available
    console.log(`Analysis object structure for ${symbol}:`);
    console.log(`  - Has supportResistance: ${!!analysis.supportResistance}`);
    console.log(`  - Has keyLevels: ${!!analysis.keyLevels}`);
    console.log(`  - Has summary: ${!!analysis.summary}`);
    console.log(`  - Has summary.keyLevels: ${!!analysis.summary?.keyLevels}`);
    console.log(`  - Has findings: ${!!analysis.findings}`);
    console.log(`  - Has findings.supportResistance: ${!!analysis.findings?.supportResistance}`);

    // If we have a summary object, log its structure
    if (analysis.summary) {
      console.log(`Summary object keys: ${Object.keys(analysis.summary).join(', ')}`);
    }

    // If we have a findings object, log its structure
    if (analysis.findings) {
      console.log(`Findings object keys: ${Object.keys(analysis.findings).join(', ')}`);
      if (analysis.findings.supportResistance) {
        console.log(`Findings.supportResistance keys: ${Object.keys(analysis.findings.supportResistance).join(', ')}`);
      }
    }

    return result;
  } catch (error) {
    console.error(`Error processing ${symbol}:`, error);
    return null;
  }
}

/**
 * Convert a regular scanner result to a simplified format
 * @param result - The original scanner result
 * @param apiKey - Optional Polygon API key for options recommendation
 * @returns A simplified scanner result
 */
export async function simplifyResult(result: ScannerResult, apiKey?: string): Promise<SimplifiedScannerResult> {
  const simplified: SimplifiedScannerResult = {
    symbol: result.symbol,
    signal: result.signal,
    price: result.currentPrice,
    stopLoss: result.stopLoss,
    takeProfit: result.takeProfit,
    // Ensure we have valid support and resistance values
    support: result.keyLevels?.support || (result.currentPrice * 0.95),
    resistance: result.keyLevels?.resistance || (result.currentPrice * 1.05),
    confidence: result.confidence,
    confidenceScore: result.confidenceScore,
    bullishScore: result.bullishScore || 0,
    bearishScore: result.bearishScore || 0,
    marketEnvironment: result.marketStructure || 'unknown',
    trend: result.trend || 'unknown'
  };

  // If API key is provided, get options recommendation
  if (apiKey) {
    try {
      const optionsRec = await recommendOptionsContracts(
        result.symbol,
        result.currentPrice,
        result.signal,
        apiKey
      );

      simplified.optionsRecommendation = {
        recommendation: optionsRec.recommendation,
        reasoning: optionsRec.reasoning
      };
    } catch (error) {
      console.error(`Error getting options recommendation for ${result.symbol}:`, error);
      // Don't fail if options recommendation fails
    }
  }

  // Log support and resistance values for debugging
  console.log(`Simplified ${result.symbol} support/resistance values:`);
  console.log(`  - Final Support: ${simplified.support}`);
  console.log(`  - Final Resistance: ${simplified.resistance}`);
  console.log(`  - From result.keyLevels.support: ${result.keyLevels?.support}`);
  console.log(`  - From result.keyLevels.resistance: ${result.keyLevels?.resistance}`);
  console.log(`  - Fallback support (5% below price): ${result.currentPrice * 0.95}`);
  console.log(`  - Fallback resistance (5% above price): ${result.currentPrice * 1.05}`);

  // Force support and resistance to be non-zero values
  if (simplified.support === 0) {
    simplified.support = result.currentPrice * 0.95;
    console.log(`  - Support was 0, forced to fallback: ${simplified.support}`);
  }

  if (simplified.resistance === 0) {
    simplified.resistance = result.currentPrice * 1.05;
    console.log(`  - Resistance was 0, forced to fallback: ${simplified.resistance}`);
  }

  return simplified;
}

/**
 * Convert an array of scanner results to simplified format
 * @param results - The original scanner results
 * @param apiKey - Optional Polygon API key for options recommendation
 * @returns An array of simplified scanner results
 */
export async function simplifyResults(results: ScannerResult[], apiKey?: string): Promise<SimplifiedScannerResult[]> {
  const simplifiedPromises = results.map(result => simplifyResult(result, apiKey));
  return Promise.all(simplifiedPromises);
}
