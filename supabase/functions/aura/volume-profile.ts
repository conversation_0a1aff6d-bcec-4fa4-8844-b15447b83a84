// Volume Profile Analysis

import { Candle } from './candlestick-patterns.ts';

// Interface for volume profile
export interface VolumeProfile {
  priceRange: {
    min: number;
    max: number;
  };
  levels: {
    price: number;
    volume: number;
    relativeVolume: number; // 0-1 scale
  }[];
  valueArea: {
    high: number;
    low: number;
    median: number;
  };
  pointOfControl: number;
}

// Calculate volume profile for a given price range
export function calculateVolumeProfile(data: Candle[], numLevels: number = 20): VolumeProfile {
  if (data.length === 0) {
    throw new Error('No data provided for volume profile calculation');
  }
  
  // Find the price range
  let minPrice = Infinity;
  let maxPrice = -Infinity;
  
  for (const candle of data) {
    minPrice = Math.min(minPrice, candle.low);
    maxPrice = Math.max(maxPrice, candle.high);
  }
  
  // Add a small buffer to the range
  const buffer = (maxPrice - minPrice) * 0.05;
  minPrice -= buffer;
  maxPrice += buffer;
  
  // Calculate the price increment for each level
  const priceIncrement = (maxPrice - minPrice) / numLevels;
  
  // Initialize levels
  const levels: {
    price: number;
    volume: number;
    relativeVolume: number;
  }[] = [];
  
  for (let i = 0; i < numLevels; i++) {
    levels.push({
      price: minPrice + (i * priceIncrement) + (priceIncrement / 2), // Use the middle of the level
      volume: 0,
      relativeVolume: 0
    });
  }
  
  // Distribute volume across levels
  for (const candle of data) {
    // Calculate the range of the candle
    const candleRange = candle.high - candle.low;
    
    // Skip candles with zero range to avoid division by zero
    if (candleRange === 0) continue;
    
    // Calculate the volume per price unit
    const volumePerPriceUnit = candle.volume / candleRange;
    
    // Distribute the volume across the levels the candle spans
    for (let i = 0; i < numLevels; i++) {
      const levelLow = minPrice + (i * priceIncrement);
      const levelHigh = levelLow + priceIncrement;
      
      // Check if the candle overlaps with this level
      if (candle.low <= levelHigh && candle.high >= levelLow) {
        // Calculate the overlap
        const overlapLow = Math.max(candle.low, levelLow);
        const overlapHigh = Math.min(candle.high, levelHigh);
        const overlap = overlapHigh - overlapLow;
        
        // Add the proportional volume to this level
        levels[i].volume += volumePerPriceUnit * overlap;
      }
    }
  }
  
  // Find the maximum volume
  const maxVolume = Math.max(...levels.map(level => level.volume));
  
  // Calculate relative volume
  for (let i = 0; i < numLevels; i++) {
    levels[i].relativeVolume = levels[i].volume / maxVolume;
  }
  
  // Find the point of control (price level with the highest volume)
  const pointOfControl = levels.reduce(
    (max, level) => level.volume > max.volume ? level : max,
    { price: 0, volume: -1, relativeVolume: 0 }
  ).price;
  
  // Calculate the value area (70% of total volume)
  const totalVolume = levels.reduce((sum, level) => sum + level.volume, 0);
  const valueAreaVolume = totalVolume * 0.7;
  
  // Sort levels by volume in descending order
  const sortedLevels = [...levels].sort((a, b) => b.volume - a.volume);
  
  let cumulativeVolume = 0;
  const valueAreaLevels: typeof levels = [];
  
  // Add levels to the value area until we reach 70% of total volume
  for (const level of sortedLevels) {
    valueAreaLevels.push(level);
    cumulativeVolume += level.volume;
    
    if (cumulativeVolume >= valueAreaVolume) {
      break;
    }
  }
  
  // Find the highest and lowest prices in the value area
  const valueAreaHigh = Math.max(...valueAreaLevels.map(level => level.price + (priceIncrement / 2)));
  const valueAreaLow = Math.min(...valueAreaLevels.map(level => level.price - (priceIncrement / 2)));
  
  return {
    priceRange: {
      min: minPrice,
      max: maxPrice
    },
    levels,
    valueArea: {
      high: valueAreaHigh,
      low: valueAreaLow,
      median: (valueAreaHigh + valueAreaLow) / 2
    },
    pointOfControl
  };
}

// Identify value areas in the volume profile
export function identifyValueAreas(data: Candle[]): {
  volumeProfile: VolumeProfile;
  currentPriceLocation: 'above_value_area' | 'in_value_area' | 'below_value_area';
  distanceFromPOC: number;
  distanceFromPOCPercent: number;
} {
  // Calculate the volume profile
  const volumeProfile = calculateVolumeProfile(data);
  
  // Get the current price (close of the last candle)
  const currentPrice = data[data.length - 1].close;
  
  // Determine where the current price is relative to the value area
  let currentPriceLocation: 'above_value_area' | 'in_value_area' | 'below_value_area';
  
  if (currentPrice > volumeProfile.valueArea.high) {
    currentPriceLocation = 'above_value_area';
  } else if (currentPrice < volumeProfile.valueArea.low) {
    currentPriceLocation = 'below_value_area';
  } else {
    currentPriceLocation = 'in_value_area';
  }
  
  // Calculate distance from point of control
  const distanceFromPOC = Math.abs(currentPrice - volumeProfile.pointOfControl);
  const distanceFromPOCPercent = (distanceFromPOC / volumeProfile.pointOfControl) * 100;
  
  return {
    volumeProfile,
    currentPriceLocation,
    distanceFromPOC,
    distanceFromPOCPercent
  };
}
