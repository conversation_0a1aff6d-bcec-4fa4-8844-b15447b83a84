// Risk Management Module
// This module provides functions for calculating stop loss, take profit, and position sizing

import { Candle } from "./technical-analysis.ts";
import { calculateATR } from "./indicators.ts";
import { calculateEnhancedSLTP, detectBreakout, calculateBreakoutSLTP, SLTPResult } from "./sltp-framework.ts";

/**
 * Calculate stop loss and take profit levels using the enhanced Aura framework
 * @param data - Historical price data
 * @param signal - Trading signal ('LONG' or 'SHORT')
 * @param swingHighs - Array of swing high prices
 * @param swingLows - Array of swing low prices
 * @param supportLevels - Array of support levels
 * @param resistanceLevels - Array of resistance levels
 * @returns Stop loss and take profit levels with explanations
 */
export function calculateStopLossTakeProfit(
  data: Candle[],
  signal: 'LONG' | 'SHORT' | 'NEUTRAL',
  swingHighs: number[],
  swingLows: number[],
  supportLevels: number[],
  resistanceLevels: number[]
): SLTPResult {
  // Check if we have a breakout scenario
  const breakoutInfo = detectBreakout(data);
  
  // If we have a strong breakout and the signal matches the breakout direction
  if (breakoutInfo && 
      ((breakoutInfo.direction === 'up' && signal === 'LONG') || 
       (breakoutInfo.direction === 'down' && signal === 'SHORT'))) {
    
    console.log(`Detected ${breakoutInfo.strength} ${breakoutInfo.direction} breakout, using breakout SLTP calculation`);
    
    // Use the breakout-specific calculation
    const breakoutSLTP = calculateBreakoutSLTP(data, signal, breakoutInfo);
    
    // Create a full result object
    return {
      stopLoss: breakoutSLTP.stopLoss,
      takeProfit: breakoutSLTP.takeProfit,
      riskRewardRatio: breakoutSLTP.riskRewardRatio,
      stopLossExplanation: `Stop loss based on ${breakoutInfo.strength} ${breakoutInfo.direction} breakout pattern`,
      takeProfitExplanation: `Take profit based on ${breakoutInfo.strength} ${breakoutInfo.direction} breakout projection`,
      stopLossFactors: {
        structurePoints: 40,
        technicalPoints: 30,
        riskManagement: 30
      },
      takeProfitFactors: {
        keyLevels: 40,
        fibonacciProjections: 35,
        momentumTargets: 25
      }
    };
  }
  
  // Use the enhanced framework for normal scenarios
  return calculateEnhancedSLTP(
    data,
    signal,
    swingHighs,
    swingLows,
    supportLevels,
    resistanceLevels
  );
}

/**
 * Calculate suggested position size based on risk parameters
 * @param accountSize - Account size in currency units
 * @param riskPercent - Risk percentage (1-100)
 * @param entryPrice - Entry price
 * @param stopLoss - Stop loss price
 * @returns Suggested position size
 */
export function calculatePositionSize(
  accountSize: number,
  riskPercent: number,
  entryPrice: number,
  stopLoss: number
): number {
  // Convert risk percent to decimal
  const riskDecimal = riskPercent / 100;
  
  // Calculate risk amount in currency units
  const riskAmount = accountSize * riskDecimal;
  
  // Calculate risk per share/contract
  const riskPerUnit = Math.abs(entryPrice - stopLoss);
  
  // Calculate position size
  const positionSize = riskAmount / riskPerUnit;
  
  return positionSize;
}
