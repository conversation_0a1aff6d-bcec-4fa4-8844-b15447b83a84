// Enhanced Chart Pattern Recognition

import { Candle } from './candlestick-patterns.ts';
import { ChartPattern, findSwingHighs, findSwingLows, findLowestBetween, findHighestBetween } from './chart-patterns.ts';

// Detect Triangle patterns (ascending, descending, symmetrical)
export function detectTriangle(candles: Candle[]): ChartPattern[] {
  const patterns: ChartPattern[] = [];
  
  // Need at least 10 candles to detect a triangle
  if (candles.length < 10) return patterns;
  
  // Find swing highs and lows
  const swingHighs = findSwingHighs(candles);
  const swingLows = findSwingLows(candles);
  
  // Need at least 3 swing points to form a triangle
  if (swingHighs.length < 3 || swingLows.length < 3) return patterns;
  
  // Check for ascending triangle (flat top, rising bottom)
  const ascendingTriangle = detectAscendingTriangle(candles, swingHighs, swingLows);
  if (ascendingTriangle) patterns.push(ascendingTriangle);
  
  // Check for descending triangle (flat bottom, falling top)
  const descendingTriangle = detectDescendingTriangle(candles, swingHighs, swingLows);
  if (descendingTriangle) patterns.push(descendingTriangle);
  
  // Check for symmetrical triangle (converging trendlines)
  const symmetricalTriangle = detectSymmetricalTriangle(candles, swingHighs, swingLows);
  if (symmetricalTriangle) patterns.push(symmetricalTriangle);
  
  return patterns;
}

// Detect Ascending Triangle (flat top, rising bottom)
function detectAscendingTriangle(candles: Candle[], swingHighs: number[], swingLows: number[]): ChartPattern | null {
  // Need at least 3 swing points
  if (swingHighs.length < 2 || swingLows.length < 3) return null;
  
  // Get recent swing highs and lows
  const recentHighs = swingHighs.slice(-3);
  const recentLows = swingLows.slice(-3);
  
  // Check if swing highs are at similar levels (flat top)
  const highValues = recentHighs.map(idx => candles[idx].high);
  const avgHigh = highValues.reduce((sum, val) => sum + val, 0) / highValues.length;
  const highDeviation = Math.max(...highValues.map(h => Math.abs(h - avgHigh) / avgHigh));
  
  // Check if swing lows are rising (ascending bottom)
  const lowValues = recentLows.map(idx => candles[idx].low);
  const isAscending = lowValues[lowValues.length - 1] > lowValues[0];
  
  // Validate pattern
  if (highDeviation < 0.02 && isAscending) {
    // Calculate pattern boundaries
    const startIndex = Math.min(...recentHighs, ...recentLows);
    const endIndex = Math.max(...recentHighs, ...recentLows);
    
    // Calculate breakout target (height of pattern added to resistance)
    const patternHeight = avgHigh - lowValues[0];
    const targetPrice = avgHigh + patternHeight;
    
    return {
      pattern: 'ascending_triangle',
      startIndex,
      endIndex,
      significance: 'strong',
      bullish: true,
      targetPrice
    };
  }
  
  return null;
}

// Detect Descending Triangle (flat bottom, falling top)
function detectDescendingTriangle(candles: Candle[], swingHighs: number[], swingLows: number[]): ChartPattern | null {
  // Need at least 3 swing points
  if (swingHighs.length < 3 || swingLows.length < 2) return null;
  
  // Get recent swing highs and lows
  const recentHighs = swingHighs.slice(-3);
  const recentLows = swingLows.slice(-3);
  
  // Check if swing lows are at similar levels (flat bottom)
  const lowValues = recentLows.map(idx => candles[idx].low);
  const avgLow = lowValues.reduce((sum, val) => sum + val, 0) / lowValues.length;
  const lowDeviation = Math.max(...lowValues.map(l => Math.abs(l - avgLow) / avgLow));
  
  // Check if swing highs are falling (descending top)
  const highValues = recentHighs.map(idx => candles[idx].high);
  const isDescending = highValues[highValues.length - 1] < highValues[0];
  
  // Validate pattern
  if (lowDeviation < 0.02 && isDescending) {
    // Calculate pattern boundaries
    const startIndex = Math.min(...recentHighs, ...recentLows);
    const endIndex = Math.max(...recentHighs, ...recentLows);
    
    // Calculate breakout target (height of pattern subtracted from support)
    const patternHeight = highValues[0] - avgLow;
    const targetPrice = avgLow - patternHeight;
    
    return {
      pattern: 'descending_triangle',
      startIndex,
      endIndex,
      significance: 'strong',
      bullish: false,
      targetPrice
    };
  }
  
  return null;
}

// Detect Symmetrical Triangle (converging trendlines)
function detectSymmetricalTriangle(candles: Candle[], swingHighs: number[], swingLows: number[]): ChartPattern | null {
  // Need at least 3 swing points for each
  if (swingHighs.length < 3 || swingLows.length < 3) return null;
  
  // Get recent swing highs and lows
  const recentHighs = swingHighs.slice(-3);
  const recentLows = swingLows.slice(-3);
  
  // Check if swing highs are descending
  const highValues = recentHighs.map(idx => candles[idx].high);
  const isHighsDescending = highValues[highValues.length - 1] < highValues[0];
  
  // Check if swing lows are ascending
  const lowValues = recentLows.map(idx => candles[idx].low);
  const isLowsAscending = lowValues[lowValues.length - 1] > lowValues[0];
  
  // Validate pattern - both lines should be converging
  if (isHighsDescending && isLowsAscending) {
    // Calculate pattern boundaries
    const startIndex = Math.min(...recentHighs, ...recentLows);
    const endIndex = Math.max(...recentHighs, ...recentLows);
    
    // Calculate pattern height at the start
    const initialHeight = highValues[0] - lowValues[0];
    
    // Determine if breakout is likely bullish or bearish based on volume and momentum
    let isBullish = true;
    
    // Check recent price action and volume for clues
    const recentCandles = candles.slice(-5);
    let bullishVolume = 0;
    let bearishVolume = 0;
    
    for (const candle of recentCandles) {
      if (candle.close > candle.open) {
        bullishVolume += candle.volume;
      } else {
        bearishVolume += candle.volume;
      }
    }
    
    isBullish = bullishVolume > bearishVolume;
    
    // Calculate breakout target based on pattern height
    const targetPrice = isBullish 
      ? highValues[0] + initialHeight 
      : lowValues[0] - initialHeight;
    
    return {
      pattern: 'symmetrical_triangle',
      startIndex,
      endIndex,
      significance: 'moderate',
      bullish: isBullish,
      targetPrice
    };
  }
  
  return null;
}

// Detect Pennant pattern (small symmetrical triangle after a strong move)
export function detectPennant(candles: Candle[]): ChartPattern[] {
  const patterns: ChartPattern[] = [];
  
  // Need at least 15 candles to detect a pennant
  if (candles.length < 15) return patterns;
  
  // Look for a strong move (pole) followed by a consolidation (pennant)
  for (let i = 5; i < candles.length - 10; i++) {
    // Check for a strong bullish move (pole)
    let isBullishPole = true;
    let poleHeight = 0;
    
    for (let j = i - 5; j < i; j++) {
      if (candles[j].close <= candles[j].open) {
        isBullishPole = false;
        break;
      }
      poleHeight += (candles[j].close - candles[j].open);
    }
    
    // Check for a bearish pole
    let isBearishPole = !isBullishPole;
    if (isBearishPole) {
      poleHeight = 0;
      for (let j = i - 5; j < i; j++) {
        if (candles[j].close >= candles[j].open) {
          isBearishPole = false;
          break;
        }
        poleHeight += (candles[j].open - candles[j].close);
      }
    }
    
    // If we have a pole, check for a pennant (small symmetrical triangle)
    if (isBullishPole || isBearishPole) {
      // Find swing points in the consolidation area
      const consolidationCandles = candles.slice(i, i + 10);
      const swingHighs = findSwingHighs(consolidationCandles, 2);
      const swingLows = findSwingLows(consolidationCandles, 2);
      
      // Need at least 2 swing points for each
      if (swingHighs.length >= 2 && swingLows.length >= 2) {
        // Check if swing highs are descending
        const highValues = swingHighs.map(idx => consolidationCandles[idx].high);
        const isHighsDescending = highValues[highValues.length - 1] < highValues[0];
        
        // Check if swing lows are ascending
        const lowValues = swingLows.map(idx => consolidationCandles[idx].low);
        const isLowsAscending = lowValues[lowValues.length - 1] > lowValues[0];
        
        // Validate pattern - both lines should be converging
        if (isHighsDescending && isLowsAscending) {
          // Calculate pattern boundaries
          const startIndex = i - 5;
          const endIndex = i + 10;
          
          // Calculate breakout target based on pole height
          const targetPrice = isBullishPole 
            ? candles[i-1].high + poleHeight 
            : candles[i-1].low - poleHeight;
          
          patterns.push({
            pattern: isBullishPole ? 'bullish_pennant' : 'bearish_pennant',
            startIndex,
            endIndex,
            significance: 'strong',
            bullish: isBullishPole,
            targetPrice
          });
        }
      }
    }
  }
  
  return patterns;
}

// Interface for trendline
export interface Trendline {
  type: 'support' | 'resistance';
  startIndex: number;
  endIndex: number;
  startPrice: number;
  endPrice: number;
  touchPoints: number;
  strength: 'weak' | 'moderate' | 'strong';
}

// Interface for channel
export interface Channel {
  startIndex: number;
  endIndex: number;
  upperStartPrice: number;
  upperEndPrice: number;
  lowerStartPrice: number;
  lowerEndPrice: number;
  touchPoints: number;
  strength: 'weak' | 'moderate' | 'strong';
}

// Identify trendlines and channels
export function identifyTrendlines(candles: Candle[]): {
  trendlines: Trendline[];
  channels: Channel[];
} {
  // Find swing points
  const swingHighs = findSwingHighs(candles);
  const swingLows = findSwingLows(candles);
  
  // Identify potential trendlines
  const supportLines = identifySupportTrendlines(candles, swingLows);
  const resistanceLines = identifyResistanceTrendlines(candles, swingHighs);
  
  // Identify channels (parallel support and resistance lines)
  const channels = identifyChannels(candles, supportLines, resistanceLines);
  
  return {
    trendlines: [...supportLines, ...resistanceLines],
    channels
  };
}

// Identify support trendlines connecting swing lows
function identifySupportTrendlines(candles: Candle[], swingLows: number[]): Trendline[] {
  const trendlines: Trendline[] = [];
  
  // Need at least 3 swing lows to form a trendline
  if (swingLows.length < 3) return trendlines;
  
  // Try to connect different swing lows to form trendlines
  for (let i = 0; i < swingLows.length - 2; i++) {
    const point1 = { index: swingLows[i], price: candles[swingLows[i]].low };
    
    for (let j = i + 1; j < swingLows.length - 1; j++) {
      const point2 = { index: swingLows[j], price: candles[swingLows[j]].low };
      
      // Calculate slope and intercept of the line
      const slope = (point2.price - point1.price) / (point2.index - point1.index);
      
      // Only consider upward sloping or slightly downward sloping lines for support
      if (slope < -0.01) continue;
      
      const intercept = point1.price - (slope * point1.index);
      
      // Count how many other swing lows are near this line
      let touchPoints = 2; // Start with the two points defining the line
      
      for (let k = 0; k < swingLows.length; k++) {
        if (k === i || k === j) continue; // Skip the points defining the line
        
        const testPoint = { index: swingLows[k], price: candles[swingLows[k]].low };
        const expectedPrice = (slope * testPoint.index) + intercept;
        const deviation = Math.abs(testPoint.price - expectedPrice) / testPoint.price;
        
        // If the point is close to the line, count it as a touch point
        if (deviation < 0.01) {
          touchPoints++;
        }
      }
      
      // Only consider lines with at least 3 touch points
      if (touchPoints >= 3) {
        // Calculate the end price (at the last candle)
        const endPrice = (slope * (candles.length - 1)) + intercept;
        
        // Determine strength based on touch points and duration
        let strength: 'weak' | 'moderate' | 'strong' = 'weak';
        if (touchPoints >= 5) {
          strength = 'strong';
        } else if (touchPoints >= 4) {
          strength = 'moderate';
        }
        
        trendlines.push({
          type: 'support',
          startIndex: point1.index,
          endIndex: candles.length - 1,
          startPrice: point1.price,
          endPrice,
          touchPoints,
          strength
        });
      }
    }
  }
  
  return trendlines;
}

// Identify resistance trendlines connecting swing highs
function identifyResistanceTrendlines(candles: Candle[], swingHighs: number[]): Trendline[] {
  const trendlines: Trendline[] = [];
  
  // Need at least 3 swing highs to form a trendline
  if (swingHighs.length < 3) return trendlines;
  
  // Try to connect different swing highs to form trendlines
  for (let i = 0; i < swingHighs.length - 2; i++) {
    const point1 = { index: swingHighs[i], price: candles[swingHighs[i]].high };
    
    for (let j = i + 1; j < swingHighs.length - 1; j++) {
      const point2 = { index: swingHighs[j], price: candles[swingHighs[j]].high };
      
      // Calculate slope and intercept of the line
      const slope = (point2.price - point1.price) / (point2.index - point1.index);
      
      // Only consider downward sloping or slightly upward sloping lines for resistance
      if (slope > 0.01) continue;
      
      const intercept = point1.price - (slope * point1.index);
      
      // Count how many other swing highs are near this line
      let touchPoints = 2; // Start with the two points defining the line
      
      for (let k = 0; k < swingHighs.length; k++) {
        if (k === i || k === j) continue; // Skip the points defining the line
        
        const testPoint = { index: swingHighs[k], price: candles[swingHighs[k]].high };
        const expectedPrice = (slope * testPoint.index) + intercept;
        const deviation = Math.abs(testPoint.price - expectedPrice) / testPoint.price;
        
        // If the point is close to the line, count it as a touch point
        if (deviation < 0.01) {
          touchPoints++;
        }
      }
      
      // Only consider lines with at least 3 touch points
      if (touchPoints >= 3) {
        // Calculate the end price (at the last candle)
        const endPrice = (slope * (candles.length - 1)) + intercept;
        
        // Determine strength based on touch points and duration
        let strength: 'weak' | 'moderate' | 'strong' = 'weak';
        if (touchPoints >= 5) {
          strength = 'strong';
        } else if (touchPoints >= 4) {
          strength = 'moderate';
        }
        
        trendlines.push({
          type: 'resistance',
          startIndex: point1.index,
          endIndex: candles.length - 1,
          startPrice: point1.price,
          endPrice,
          touchPoints,
          strength
        });
      }
    }
  }
  
  return trendlines;
}

// Identify channels (parallel support and resistance lines)
function identifyChannels(candles: Candle[], supportLines: Trendline[], resistanceLines: Trendline[]): Channel[] {
  const channels: Channel[] = [];
  
  // Check each support line against each resistance line
  for (const support of supportLines) {
    for (const resistance of resistanceLines) {
      // Calculate slopes
      const supportSlope = (support.endPrice - support.startPrice) / (support.endIndex - support.startIndex);
      const resistanceSlope = (resistance.endPrice - resistance.startPrice) / (resistance.endIndex - resistance.startIndex);
      
      // Check if slopes are similar (parallel lines)
      const slopeDifference = Math.abs(supportSlope - resistanceSlope);
      if (slopeDifference < 0.005) {
        // Calculate the common range of indices
        const startIndex = Math.max(support.startIndex, resistance.startIndex);
        const endIndex = Math.min(support.endIndex, resistance.endIndex);
        
        // Only consider channels with significant overlap
        if (endIndex - startIndex >= 10) {
          // Calculate prices at the common range
          const supportStartPrice = support.startPrice + supportSlope * (startIndex - support.startIndex);
          const supportEndPrice = supportStartPrice + supportSlope * (endIndex - startIndex);
          
          const resistanceStartPrice = resistance.startPrice + resistanceSlope * (startIndex - resistance.startIndex);
          const resistanceEndPrice = resistanceStartPrice + resistanceSlope * (endIndex - startIndex);
          
          // Calculate channel height
          const channelHeight = resistanceStartPrice - supportStartPrice;
          
          // Only consider channels with reasonable height
          if (channelHeight > 0 && channelHeight / supportStartPrice > 0.02) {
            // Combine touch points from both lines
            const touchPoints = support.touchPoints + resistance.touchPoints;
            
            // Determine strength based on touch points
            let strength: 'weak' | 'moderate' | 'strong' = 'weak';
            if (touchPoints >= 10) {
              strength = 'strong';
            } else if (touchPoints >= 7) {
              strength = 'moderate';
            }
            
            channels.push({
              startIndex,
              endIndex,
              upperStartPrice: resistanceStartPrice,
              upperEndPrice: resistanceEndPrice,
              lowerStartPrice: supportStartPrice,
              lowerEndPrice: supportEndPrice,
              touchPoints,
              strength
            });
          }
        }
      }
    }
  }
  
  return channels;
}

// Convert trendlines to chart patterns
export function trendlinesToPatterns(trendlines: Trendline[]): ChartPattern[] {
  return trendlines.map(trendline => {
    return {
      pattern: `${trendline.type}_trendline`,
      startIndex: trendline.startIndex,
      endIndex: trendline.endIndex,
      significance: trendline.strength,
      bullish: trendline.type === 'support',
      targetPrice: trendline.type === 'support' ? trendline.endPrice : undefined,
      supportLevel: trendline.type === 'support' ? trendline.endPrice : undefined,
      resistanceLevel: trendline.type === 'resistance' ? trendline.endPrice : undefined
    };
  });
}

// Convert channels to chart patterns
export function channelsToPatterns(channels: Channel[]): ChartPattern[] {
  return channels.map(channel => {
    // Determine if the channel is bullish or bearish based on slope
    const isUptrend = channel.upperEndPrice > channel.upperStartPrice;
    
    return {
      pattern: isUptrend ? 'ascending_channel' : 'descending_channel',
      startIndex: channel.startIndex,
      endIndex: channel.endIndex,
      significance: channel.strength,
      bullish: isUptrend,
      supportLevel: channel.lowerEndPrice,
      resistanceLevel: channel.upperEndPrice
    };
  });
}

// Enhanced chart pattern analysis
export function enhancedChartPatternAnalysis(candles: Candle[]): ChartPattern[] {
  // Detect triangle patterns
  const triangles = detectTriangle(candles);
  
  // Detect pennant patterns
  const pennants = detectPennant(candles);
  
  // Identify trendlines and channels
  const { trendlines, channels } = identifyTrendlines(candles);
  
  // Convert trendlines and channels to chart patterns
  const trendlinePatterns = trendlinesToPatterns(trendlines);
  const channelPatterns = channelsToPatterns(channels);
  
  return [
    ...triangles,
    ...pennants,
    ...trendlinePatterns,
    ...channelPatterns
  ];
}
