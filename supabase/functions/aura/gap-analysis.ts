// Gap Analysis

import { Candle } from './candlestick-patterns.ts';

// Interface for gap detection
export interface GapInfo {
  position: number;
  date: string;
  gapType: 'up' | 'down';
  gapSize: number;
  gapSizePercent: number;
  filled: boolean;
  fillDate?: string;
  fillDuration?: number;
  fillProbability: number;
}

// Detect gaps in price data
export function detectGaps(data: Candle[]): GapInfo[] {
  if (data.length < 2) return [];
  
  const gaps: GapInfo[] = [];
  
  // Loop through the data to find gaps
  for (let i = 1; i < data.length; i++) {
    const current = data[i];
    const previous = data[i - 1];
    
    // Check for gap up (today's low > yesterday's high)
    if (current.low > previous.high) {
      const gapSize = current.low - previous.high;
      const gapSizePercent = (gapSize / previous.high) * 100;
      
      // Only consider significant gaps (> 0.5%)
      if (gapSizePercent > 0.5) {
        // Check if the gap has been filled
        let filled = false;
        let fillDate = undefined;
        let fillDuration = undefined;
        
        for (let j = i + 1; j < data.length; j++) {
          if (data[j].low <= previous.high) {
            filled = true;
            fillDate = data[j].date;
            fillDuration = j - i;
            break;
          }
        }
        
        // Calculate fill probability based on historical data and gap size
        const fillProbability = calculateFillProbability('up', gapSizePercent, filled);
        
        gaps.push({
          position: i,
          date: current.date,
          gapType: 'up',
          gapSize,
          gapSizePercent,
          filled,
          fillDate,
          fillDuration,
          fillProbability
        });
      }
    }
    
    // Check for gap down (today's high < yesterday's low)
    if (current.high < previous.low) {
      const gapSize = previous.low - current.high;
      const gapSizePercent = (gapSize / previous.low) * 100;
      
      // Only consider significant gaps (> 0.5%)
      if (gapSizePercent > 0.5) {
        // Check if the gap has been filled
        let filled = false;
        let fillDate = undefined;
        let fillDuration = undefined;
        
        for (let j = i + 1; j < data.length; j++) {
          if (data[j].high >= previous.low) {
            filled = true;
            fillDate = data[j].date;
            fillDuration = j - i;
            break;
          }
        }
        
        // Calculate fill probability based on historical data and gap size
        const fillProbability = calculateFillProbability('down', gapSizePercent, filled);
        
        gaps.push({
          position: i,
          date: current.date,
          gapType: 'down',
          gapSize,
          gapSizePercent,
          filled,
          fillDate,
          fillDuration,
          fillProbability
        });
      }
    }
  }
  
  return gaps;
}

// Calculate the probability of a gap being filled based on historical data and gap size
function calculateFillProbability(gapType: 'up' | 'down', gapSizePercent: number, alreadyFilled: boolean): number {
  if (alreadyFilled) return 100;
  
  // These probabilities are based on statistical analysis of market behavior
  // Smaller gaps are more likely to be filled than larger gaps
  
  if (gapType === 'up') {
    // Gap up (bullish)
    if (gapSizePercent > 5) {
      return 65; // Large gaps up have lower fill probability
    } else if (gapSizePercent > 2) {
      return 75;
    } else {
      return 85; // Small gaps up have high fill probability
    }
  } else {
    // Gap down (bearish)
    if (gapSizePercent > 5) {
      return 70; // Large gaps down have moderate fill probability
    } else if (gapSizePercent > 2) {
      return 80;
    } else {
      return 90; // Small gaps down have very high fill probability
    }
  }
}

// Analyze recent gaps and their significance
export function analyzeRecentGaps(data: Candle[]): {
  hasRecentGap: boolean;
  recentGaps: GapInfo[];
  gapSummary: string;
} {
  const allGaps = detectGaps(data);
  
  // Consider only gaps in the last 20 candles
  const recentGaps = allGaps.filter(gap => gap.position >= data.length - 20);
  
  // Generate a summary of the gaps
  let gapSummary = '';
  
  if (recentGaps.length === 0) {
    gapSummary = 'No significant gaps detected in recent price action.';
  } else {
    const unfilled = recentGaps.filter(gap => !gap.filled);
    
    if (unfilled.length > 0) {
      const mostRecent = unfilled[unfilled.length - 1];
      gapSummary = `${unfilled.length} unfilled gap(s) detected. Most recent is a ${mostRecent.gapSizePercent.toFixed(2)}% gap ${mostRecent.gapType} on ${mostRecent.date} with ${mostRecent.fillProbability.toFixed(0)}% fill probability.`;
    } else {
      gapSummary = `${recentGaps.length} recent gap(s) detected, all have been filled.`;
    }
  }
  
  return {
    hasRecentGap: recentGaps.length > 0,
    recentGaps,
    gapSummary
  };
}
