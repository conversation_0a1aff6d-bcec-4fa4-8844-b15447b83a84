// Technical Indicators Module
// This module provides functions for calculating various technical indicators

import { Candle } from "./technical-analysis.ts";

/**
 * Calculate Simple Moving Average (SMA)
 * @param data - Array of price values
 * @param period - Period for the moving average
 * @returns Array of SMA values
 */
export function calculateSMA(data: number[], period: number): number[] {
  const result: number[] = [];
  
  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      result.push(NaN); // Not enough data for the first (period-1) elements
      continue;
    }
    
    let sum = 0;
    for (let j = 0; j < period; j++) {
      sum += data[i - j];
    }
    
    result.push(sum / period);
  }
  
  return result;
}

/**
 * Calculate Exponential Moving Average (EMA)
 * @param data - Array of price values
 * @param period - Period for the moving average
 * @returns Array of EMA values
 */
export function calculateEMA(data: number[], period: number): number[] {
  const result: number[] = [];
  const multiplier = 2 / (period + 1);
  
  // Start with SMA for the first value
  let ema = data.slice(0, period).reduce((sum, price) => sum + price, 0) / period;
  
  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      result.push(NaN); // Not enough data for the first (period-1) elements
      continue;
    }
    
    if (i === period - 1) {
      result.push(ema);
      continue;
    }
    
    // EMA = (Close - Previous EMA) * multiplier + Previous EMA
    ema = (data[i] - ema) * multiplier + ema;
    result.push(ema);
  }
  
  return result;
}

/**
 * Calculate Relative Strength Index (RSI)
 * @param data - Array of price values
 * @param period - Period for RSI calculation
 * @returns Array of RSI values
 */
export function calculateRSI(data: number[], period: number): number[] {
  const result: number[] = [];
  const gains: number[] = [];
  const losses: number[] = [];
  
  // Calculate price changes
  for (let i = 1; i < data.length; i++) {
    const change = data[i] - data[i - 1];
    gains.push(change > 0 ? change : 0);
    losses.push(change < 0 ? -change : 0);
  }
  
  // Calculate average gains and losses
  const avgGains: number[] = [];
  const avgLosses: number[] = [];
  
  // First average gain and loss
  let firstAvgGain = gains.slice(0, period).reduce((sum, gain) => sum + gain, 0) / period;
  let firstAvgLoss = losses.slice(0, period).reduce((sum, loss) => sum + loss, 0) / period;
  
  avgGains.push(firstAvgGain);
  avgLosses.push(firstAvgLoss);
  
  // Calculate subsequent values
  for (let i = period; i < gains.length; i++) {
    const avgGain = (avgGains[avgGains.length - 1] * (period - 1) + gains[i]) / period;
    const avgLoss = (avgLosses[avgLosses.length - 1] * (period - 1) + losses[i]) / period;
    
    avgGains.push(avgGain);
    avgLosses.push(avgLoss);
  }
  
  // Calculate RSI
  for (let i = 0; i < period; i++) {
    result.push(NaN); // Not enough data for the first period elements
  }
  
  for (let i = 0; i < avgGains.length; i++) {
    const rs = avgGains[i] / (avgLosses[i] === 0 ? 0.001 : avgLosses[i]); // Avoid division by zero
    const rsi = 100 - (100 / (1 + rs));
    result.push(rsi);
  }
  
  return result;
}

/**
 * Calculate Moving Average Convergence Divergence (MACD)
 * @param data - Array of price values
 * @param fastPeriod - Period for the fast EMA
 * @param slowPeriod - Period for the slow EMA
 * @param signalPeriod - Period for the signal line
 * @returns Object with MACD line, signal line, and histogram
 */
export function calculateMACD(
  data: number[],
  fastPeriod: number = 12,
  slowPeriod: number = 26,
  signalPeriod: number = 9
): { macdLine: number[]; signalLine: number[]; histogram: number[] } {
  const fastEMA = calculateEMA(data, fastPeriod);
  const slowEMA = calculateEMA(data, slowPeriod);
  
  // Calculate MACD line (fast EMA - slow EMA)
  const macdLine: number[] = [];
  for (let i = 0; i < data.length; i++) {
    if (isNaN(fastEMA[i]) || isNaN(slowEMA[i])) {
      macdLine.push(NaN);
    } else {
      macdLine.push(fastEMA[i] - slowEMA[i]);
    }
  }
  
  // Calculate signal line (EMA of MACD line)
  const validMacdValues = macdLine.filter(value => !isNaN(value));
  const signalLine: number[] = [];
  
  for (let i = 0; i < data.length; i++) {
    if (i < slowPeriod - 1 + signalPeriod - 1) {
      signalLine.push(NaN);
    } else {
      const macdSlice = macdLine.slice(slowPeriod - 1, i + 1);
      const ema = calculateEMA(macdSlice, signalPeriod);
      signalLine.push(ema[ema.length - 1]);
    }
  }
  
  // Calculate histogram (MACD line - signal line)
  const histogram: number[] = [];
  for (let i = 0; i < data.length; i++) {
    if (isNaN(macdLine[i]) || isNaN(signalLine[i])) {
      histogram.push(NaN);
    } else {
      histogram.push(macdLine[i] - signalLine[i]);
    }
  }
  
  return { macdLine, signalLine, histogram };
}

/**
 * Calculate Bollinger Bands
 * @param data - Array of price values
 * @param period - Period for the moving average
 * @param multiplier - Standard deviation multiplier
 * @returns Object with upper band, middle band, and lower band
 */
export function calculateBollingerBands(
  data: number[],
  period: number = 20,
  multiplier: number = 2
): { upper: number[]; middle: number[]; lower: number[] } {
  const middle = calculateSMA(data, period);
  const upper: number[] = [];
  const lower: number[] = [];
  
  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      upper.push(NaN);
      lower.push(NaN);
      continue;
    }
    
    // Calculate standard deviation
    let sum = 0;
    for (let j = 0; j < period; j++) {
      sum += Math.pow(data[i - j] - middle[i], 2);
    }
    const stdDev = Math.sqrt(sum / period);
    
    upper.push(middle[i] + (multiplier * stdDev));
    lower.push(middle[i] - (multiplier * stdDev));
  }
  
  return { upper, middle, lower };
}

/**
 * Calculate Average True Range (ATR)
 * @param data - Array of candle data
 * @param period - Period for ATR calculation
 * @returns Array of ATR values
 */
export function calculateATR(data: Candle[], period: number = 14): number[] {
  const trueRanges: number[] = [];
  const result: number[] = [];
  
  // Calculate true ranges
  for (let i = 0; i < data.length; i++) {
    if (i === 0) {
      // For the first candle, true range is simply high - low
      trueRanges.push(data[i].high - data[i].low);
    } else {
      // For subsequent candles, true range is the greatest of:
      // 1. Current high - current low
      // 2. Current high - previous close (absolute value)
      // 3. Current low - previous close (absolute value)
      const tr1 = data[i].high - data[i].low;
      const tr2 = Math.abs(data[i].high - data[i - 1].close);
      const tr3 = Math.abs(data[i].low - data[i - 1].close);
      
      trueRanges.push(Math.max(tr1, tr2, tr3));
    }
  }
  
  // Calculate ATR using Wilder's smoothing method
  for (let i = 0; i < data.length; i++) {
    if (i < period) {
      if (i === period - 1) {
        // First ATR value is simple average of first 'period' true ranges
        result.push(trueRanges.slice(0, period).reduce((sum, tr) => sum + tr, 0) / period);
      } else {
        result.push(NaN);
      }
    } else {
      // ATR = ((Previous ATR * (period - 1)) + Current TR) / period
      result.push((result[i - 1] * (period - 1) + trueRanges[i]) / period);
    }
  }
  
  return result;
}

/**
 * Calculate Bollinger Bands with squeeze detection
 * @param data - Array of price values
 * @param period - Period for the moving average
 * @param bbMultiplier - Bollinger Bands standard deviation multiplier
 * @param kcMultiplier - Keltner Channel ATR multiplier
 * @returns Object with Bollinger Bands and squeeze information
 */
export function calculateBollingerBandsWithSqueeze(
  data: Candle[],
  period: number = 20,
  bbMultiplier: number = 2,
  kcMultiplier: number = 1.5
): { 
  bollingerBands: { upper: number[]; middle: number[]; lower: number[] };
  keltnerChannels: { upper: number[]; middle: number[]; lower: number[] };
  isSqueezing: boolean[];
  squeezeIntensity: number[];
} {
  // Extract closing prices
  const closes = data.map(candle => candle.close);
  
  // Calculate Bollinger Bands
  const bollingerBands = calculateBollingerBands(closes, period, bbMultiplier);
  
  // Calculate ATR for Keltner Channels
  const atr = calculateATR(data, period);
  
  // Calculate Keltner Channels
  const keltnerMiddle = calculateSMA(closes, period);
  const keltnerUpper: number[] = [];
  const keltnerLower: number[] = [];
  
  for (let i = 0; i < data.length; i++) {
    if (i < period - 1 || isNaN(atr[i])) {
      keltnerUpper.push(NaN);
      keltnerLower.push(NaN);
    } else {
      keltnerUpper.push(keltnerMiddle[i] + (kcMultiplier * atr[i]));
      keltnerLower.push(keltnerMiddle[i] - (kcMultiplier * atr[i]));
    }
  }
  
  // Detect Bollinger Bands squeeze
  const isSqueezing: boolean[] = [];
  const squeezeIntensity: number[] = [];
  
  for (let i = 0; i < data.length; i++) {
    if (i < period - 1 || isNaN(bollingerBands.upper[i]) || isNaN(keltnerUpper[i])) {
      isSqueezing.push(false);
      squeezeIntensity.push(0);
    } else {
      // Squeeze occurs when Bollinger Bands are inside Keltner Channels
      const isSqueezed = bollingerBands.upper[i] < keltnerUpper[i] && bollingerBands.lower[i] > keltnerLower[i];
      isSqueezing.push(isSqueezed);
      
      // Calculate squeeze intensity (0-100)
      if (isSqueezed) {
        // Calculate the percentage of BB width relative to KC width
        const bbWidth = bollingerBands.upper[i] - bollingerBands.lower[i];
        const kcWidth = keltnerUpper[i] - keltnerLower[i];
        const widthRatio = bbWidth / kcWidth;
        
        // Convert to intensity (smaller ratio = higher intensity)
        const intensity = Math.min(100, Math.max(0, 100 * (1 - widthRatio)));
        squeezeIntensity.push(intensity);
      } else {
        squeezeIntensity.push(0);
      }
    }
  }
  
  return {
    bollingerBands,
    keltnerChannels: { upper: keltnerUpper, middle: keltnerMiddle, lower: keltnerLower },
    isSqueezing,
    squeezeIntensity
  };
}

/**
 * Detect Bollinger Bands squeeze
 * @param data - Array of candle data
 * @param period - Period for the calculations
 * @returns Object with squeeze information
 */
export function detectBollingerSqueeze(
  data: Candle[],
  period: number = 20
): { 
  isSqueezing: boolean;
  intensity: number;
  duration: number;
  nearBreakout: boolean;
  potentialDirection: 'up' | 'down' | 'unknown';
} {
  // Calculate Bollinger Bands with squeeze
  const bbSqueeze = calculateBollingerBandsWithSqueeze(data, period);
  
  // Check if currently in a squeeze
  const isSqueezing = bbSqueeze.isSqueezing[bbSqueeze.isSqueezing.length - 1];
  
  // Get current squeeze intensity
  const intensity = bbSqueeze.squeezeIntensity[bbSqueeze.squeezeIntensity.length - 1];
  
  // Calculate squeeze duration
  let duration = 0;
  if (isSqueezing) {
    for (let i = bbSqueeze.isSqueezing.length - 1; i >= 0; i--) {
      if (bbSqueeze.isSqueezing[i]) {
        duration++;
      } else {
        break;
      }
    }
  }
  
  // Determine if near breakout (high intensity and duration)
  const nearBreakout = isSqueezing && intensity > 70 && duration > 5;
  
  // Determine potential breakout direction
  let potentialDirection: 'up' | 'down' | 'unknown' = 'unknown';
  
  if (nearBreakout) {
    // Extract recent momentum
    const recentCloses = data.slice(-5).map(candle => candle.close);
    const recentOpens = data.slice(-5).map(candle => candle.open);
    
    // Count bullish vs bearish candles
    let bullishCount = 0;
    let bearishCount = 0;
    
    for (let i = 0; i < recentCloses.length; i++) {
      if (recentCloses[i] > recentOpens[i]) {
        bullishCount++;
      } else if (recentCloses[i] < recentOpens[i]) {
        bearishCount++;
      }
    }
    
    // Determine direction based on recent candles
    if (bullishCount > bearishCount) {
      potentialDirection = 'up';
    } else if (bearishCount > bullishCount) {
      potentialDirection = 'down';
    }
  }
  
  return {
    isSqueezing,
    intensity,
    duration,
    nearBreakout,
    potentialDirection
  };
}
