// Trade Setup Generator
// Generates high-probability trade setups based on multiple confluent factors

import { Candle } from './candlestick-patterns.ts';
import { ChartPattern } from './chart-patterns.ts';
import { CandleAnatomy } from './price-action.ts';

// Interface for trade setup
export interface TradeSetup {
  direction: 'LONG' | 'SHORT';
  entryPrice: number;
  stopLoss: number;
  takeProfit: number;
  riskRewardRatio: number;
  confidence: 'low' | 'medium' | 'high';
  setupType: string;
  description: string;
  invalidationCriteria: string;
  supportingFactors: string[];
  expectedTimeToTP?: string; // Expected time to reach take profit
  secondaryTargets?: { tp2: number, tp3: number }; // Additional take profit targets
}

// Interface for risk management parameters
export interface RiskManagement {
  entryPrice: number;
  stopLoss: number;
  takeProfit: number;
  riskRewardRatio: number;
  suggestedPositionSize: string;
  expectedTimeToTP?: string; // Expected time to reach take profit
}

// Interface for market context input
export interface MarketContextInput {
  environment: 'range' | 'trending' | 'breakout' | 'reversal';
  environmentConfidence: 'low' | 'medium' | 'high';
  volatilityRegime: 'low' | 'normal' | 'high';
  volatilityScore: number;
  volumeProfile?: {
    pointOfControl: number;
    valueArea?: { high: number, low: number };
  };
}

/**
 * Generate trade setups based on comprehensive technical analysis
 * Integrates multi-timeframe analysis, market structure, and indicators
 */
export function generateTradeSetups(
  candles: Candle[],
  chartPatterns: ChartPattern[],
  marketStructure: {
    structure: string;
    confidence?: string;
    higherHighs: boolean;
    higherLows: boolean;
    lowerHighs: boolean;
    lowerLows: boolean;
    keyLevels: {
      support: number[];
      resistance: number[];
    };
  },
  indicators: {
    rsi: { value: number; condition: string };
    macd: { condition: string };
    bollingerBands: { condition: string };
  },
  // Add multi-timeframe analysis as an optional parameter
  multiTimeframe?: {
    timeframes: string[];
    trends: Record<string, string>;
    dominantTrend: string;
    confirmation: {
      bullish: boolean;
      bearish: boolean;
      neutral: boolean;
      strength: string;
    };
  }
): TradeSetup[] {
  // Use only the most recent data to keep inputs small
  const recentCandles = candles.slice(-30);
  const currentPrice = recentCandles[recentCandles.length - 1].close;

  // Keep only the closest support and resistance levels
  const closestSupport = findClosestLevel(currentPrice, marketStructure.keyLevels.support, 'below');
  const closestResistance = findClosestLevel(currentPrice, marketStructure.keyLevels.resistance, 'above');

  // Determine the dominant bias from all available data
  let dominantBias: 'bullish' | 'bearish' | 'neutral' = 'neutral';
  let biasConfidence: 'low' | 'medium' | 'high' = 'low';

  // 1. Check multi-timeframe analysis first (highest priority)
  if (multiTimeframe) {
    if (multiTimeframe.confirmation.bullish) {
      dominantBias = 'bullish';
      biasConfidence = multiTimeframe.confirmation.strength === 'strong' ? 'high' :
                       multiTimeframe.confirmation.strength === 'moderate' ? 'medium' : 'low';
    } else if (multiTimeframe.confirmation.bearish) {
      dominantBias = 'bearish';
      biasConfidence = multiTimeframe.confirmation.strength === 'strong' ? 'high' :
                       multiTimeframe.confirmation.strength === 'moderate' ? 'medium' : 'low';
    }
  }

  // 2. If multi-timeframe is neutral or not available, check market structure
  if (dominantBias === 'neutral' || biasConfidence === 'low') {
    if (marketStructure.higherHighs && marketStructure.higherLows) {
      dominantBias = 'bullish';
      biasConfidence = marketStructure.confidence === 'high' ? 'high' : 'medium';
    } else if (marketStructure.lowerHighs && marketStructure.lowerLows) {
      dominantBias = 'bearish';
      biasConfidence = marketStructure.confidence === 'high' ? 'high' : 'medium';
    }
  }

  // 3. If still neutral, check indicators
  if (dominantBias === 'neutral') {
    // Count bullish and bearish indicators
    let bullishCount = 0;
    let bearishCount = 0;

    if (indicators.rsi.condition === "oversold") bullishCount++;
    if (indicators.rsi.condition === "overbought") bearishCount++;

    if (indicators.macd.condition === "bullish" || indicators.macd.condition === "bullish crossover") bullishCount++;
    if (indicators.macd.condition === "bearish" || indicators.macd.condition === "bearish crossover") bearishCount++;

    if (indicators.bollingerBands.condition === "oversold") bullishCount++;
    if (indicators.bollingerBands.condition === "overbought") bearishCount++;

    if (bullishCount > bearishCount) {
      dominantBias = 'bullish';
      biasConfidence = bullishCount >= 2 ? 'medium' : 'low';
    } else if (bearishCount > bullishCount) {
      dominantBias = 'bearish';
      biasConfidence = bearishCount >= 2 ? 'medium' : 'low';
    }
  }

  // Filter chart patterns to align with dominant bias
  const alignedPatterns = chartPatterns.filter(pattern => {
    // For strong bias, only include patterns that align with the bias
    if (biasConfidence === 'high') {
      return (dominantBias === 'bullish' && pattern.bullish) ||
             (dominantBias === 'bearish' && !pattern.bullish);
    }
    // For medium bias, include aligned patterns and strong counter-trend patterns
    else if (biasConfidence === 'medium') {
      return (dominantBias === 'bullish' && pattern.bullish) ||
             (dominantBias === 'bearish' && !pattern.bullish) ||
             (pattern.significance === 'strong');
    }
    // For low bias or neutral, include all significant patterns
    return pattern.significance !== 'weak';
  }).slice(-5); // Keep only the 5 most recent

  const tradeSetups: TradeSetup[] = [];

  // Generate setups based on filtered chart patterns
  for (const pattern of alignedPatterns) {
    if (pattern.bullish) {
      const setup = createBullishSetup(
        recentCandles,
        pattern,
        closestSupport,
        closestResistance,
        indicators,
        dominantBias,
        biasConfidence
      );

      if (setup) tradeSetups.push(setup);
    } else {
      const setup = createBearishSetup(
        recentCandles,
        pattern,
        closestSupport,
        closestResistance,
        indicators,
        dominantBias,
        biasConfidence
      );

      if (setup) tradeSetups.push(setup);
    }
  }

  // Generate trend continuation setup if market structure is clear
  if (dominantBias === 'bullish' && (marketStructure.higherHighs || marketStructure.higherLows)) {
    const setup = createTrendContinuationSetup(
      recentCandles,
      'LONG',
      closestSupport,
      closestResistance,
      indicators,
      biasConfidence
    );

    if (setup) tradeSetups.push(setup);
  } else if (dominantBias === 'bearish' && (marketStructure.lowerHighs || marketStructure.lowerLows)) {
    const setup = createTrendContinuationSetup(
      recentCandles,
      'SHORT',
      closestSupport,
      closestResistance,
      indicators,
      biasConfidence
    );

    if (setup) tradeSetups.push(setup);
  }

  // Generate indicator-based setups only if they align with dominant bias or bias is weak
  if ((dominantBias === 'bullish' || biasConfidence === 'low') &&
      indicators.rsi.condition === "oversold" &&
      currentPrice < closestSupport * 1.02) {
    const setup = createIndicatorBasedSetup(
      recentCandles,
      'LONG',
      'RSI_OVERSOLD',
      closestSupport,
      closestResistance,
      dominantBias === 'bullish' ? biasConfidence : 'low'
    );

    if (setup) tradeSetups.push(setup);
  } else if ((dominantBias === 'bearish' || biasConfidence === 'low') &&
             indicators.rsi.condition === "overbought" &&
             currentPrice > closestResistance * 0.98) {
    const setup = createIndicatorBasedSetup(
      recentCandles,
      'SHORT',
      'RSI_OVERBOUGHT',
      closestSupport,
      closestResistance,
      dominantBias === 'bearish' ? biasConfidence : 'low'
    );

    if (setup) tradeSetups.push(setup);
  }

  // Sort setups by alignment with dominant bias and confidence
  tradeSetups.sort((a, b) => {
    // First, prioritize setups that align with dominant bias
    const aAligned = (a.direction === 'LONG' && dominantBias === 'bullish') ||
                     (a.direction === 'SHORT' && dominantBias === 'bearish');
    const bAligned = (b.direction === 'LONG' && dominantBias === 'bullish') ||
                     (b.direction === 'SHORT' && dominantBias === 'bearish');

    if (aAligned && !bAligned) return -1;
    if (!aAligned && bAligned) return 1;

    // Then sort by confidence
    const confidenceOrder = { 'high': 0, 'medium': 1, 'low': 2 };
    return confidenceOrder[a.confidence] - confidenceOrder[b.confidence];
  });

  // Limit to top 3 setups to keep output small
  return tradeSetups.slice(0, 3);
}

/**
 * Find the closest support or resistance level
 */
function findClosestLevel(
  currentPrice: number,
  levels: number[],
  direction: 'above' | 'below'
): number {
  if (levels.length === 0) return direction === 'below' ? currentPrice * 0.95 : currentPrice * 1.05;

  if (direction === 'below') {
    // Find closest level below current price
    const belowLevels = levels.filter(level => level < currentPrice);
    if (belowLevels.length === 0) return currentPrice * 0.95;

    return Math.max(...belowLevels);
  } else {
    // Find closest level above current price
    const aboveLevels = levels.filter(level => level > currentPrice);
    if (aboveLevels.length === 0) return currentPrice * 1.05;

    return Math.min(...aboveLevels);
  }
}

/**
 * Create a bullish trade setup based on a chart pattern
 */
function createBullishSetup(
  candles: Candle[],
  pattern: ChartPattern,
  support: number,
  resistance: number,
  indicators: {
    rsi: { value: number; condition: string };
    macd: { condition: string };
    bollingerBands: { condition: string };
  },
  dominantBias: 'bullish' | 'bearish' | 'neutral',
  biasConfidence: 'low' | 'medium' | 'high'
): TradeSetup | null {
  const currentPrice = candles[candles.length - 1].close;

  // Calculate ATR for stop loss placement
  const atr = calculateATR(candles, 14);
  const lastAtr = atr[atr.length - 1];

  // Entry price slightly above current price
  const entryPrice = currentPrice * 1.002;

  // Stop loss based on pattern and ATR
  const stopLoss = Math.min(support, currentPrice - (lastAtr * 1.5));

  // Take profit based on next resistance or R:R ratio
  const riskAmount = entryPrice - stopLoss;
  const takeProfit = pattern.targetPrice || (entryPrice + (riskAmount * 2));

  // Calculate R:R ratio
  const riskRewardRatio = (takeProfit - entryPrice) / (entryPrice - stopLoss);

  // Only create setup if R:R ratio is favorable
  if (riskRewardRatio < 1.5) return null;

  // Determine confidence based on multiple factors
  let confidence: 'low' | 'medium' | 'high' = 'medium';
  const supportingFactors: string[] = [];

  // Adjust confidence based on bias alignment
  if (dominantBias === 'bullish') {
    confidence = biasConfidence;
    supportingFactors.push('Aligns with dominant bullish bias');
  } else if (dominantBias === 'bearish' && biasConfidence === 'high') {
    // Counter-trend setup with high confidence bearish bias - reduce confidence
    confidence = 'low';
    supportingFactors.push('Counter-trend setup (dominant bias is bearish)');
  }

  // Pattern significance
  if (pattern.significance === 'strong') {
    confidence = 'high';
    supportingFactors.push(`Strong ${pattern.pattern} pattern`);
  } else {
    supportingFactors.push(`${pattern.pattern} pattern`);
  }

  // Indicator confluence
  if (indicators.rsi.condition === "oversold") {
    confidence = confidence === 'medium' ? 'high' : confidence;
    supportingFactors.push('RSI oversold condition');
  }

  if (indicators.macd.condition === "bullish crossover" || indicators.macd.condition === "bullish") {
    confidence = confidence === 'medium' ? 'high' : confidence;
    supportingFactors.push('MACD bullish signal');
  }

  if (indicators.bollingerBands.condition === "oversold") {
    confidence = confidence === 'medium' ? 'high' : confidence;
    supportingFactors.push('Price near lower Bollinger Band');
  }

  // Support level confluence
  if (Math.abs(currentPrice - support) / currentPrice < 0.02) {
    supportingFactors.push('Price at key support level');
  }

  // Calculate expected time to take profit
  const expectedTimeToTP = calculateExpectedTimeToTP(
    candles,
    takeProfit,
    currentPrice,
    dominantBias === 'bullish' ? 'normal' : 'high' // Adjust based on market bias
  );

  return {
    direction: 'LONG',
    entryPrice,
    stopLoss,
    takeProfit,
    riskRewardRatio,
    confidence,
    setupType: `${pattern.pattern.toUpperCase()}_BULLISH`,
    description: `Bullish setup based on ${pattern.pattern} pattern with entry at ${entryPrice.toFixed(2)}, stop loss at ${stopLoss.toFixed(2)}, and take profit at ${takeProfit.toFixed(2)}. Risk:reward ratio is ${riskRewardRatio.toFixed(2)}.`,
    invalidationCriteria: `Trade is invalidated if price closes below ${stopLoss.toFixed(2)} or if a bearish reversal pattern forms before entry.`,
    supportingFactors,
    expectedTimeToTP
  };
}

/**
 * Create a bearish trade setup based on a chart pattern
 */
function createBearishSetup(
  candles: Candle[],
  pattern: ChartPattern,
  support: number,
  resistance: number,
  indicators: {
    rsi: { value: number; condition: string };
    macd: { condition: string };
    bollingerBands: { condition: string };
  },
  dominantBias: 'bullish' | 'bearish' | 'neutral',
  biasConfidence: 'low' | 'medium' | 'high'
): TradeSetup | null {
  const currentPrice = candles[candles.length - 1].close;

  // Calculate ATR for stop loss placement
  const atr = calculateATR(candles, 14);
  const lastAtr = atr[atr.length - 1];

  // Entry price slightly below current price
  const entryPrice = currentPrice * 0.998;

  // Stop loss based on pattern and ATR
  const stopLoss = Math.max(resistance, currentPrice + (lastAtr * 1.5));

  // Take profit based on next support or R:R ratio
  const riskAmount = stopLoss - entryPrice;
  const takeProfit = pattern.targetPrice || (entryPrice - (riskAmount * 2));

  // Calculate R:R ratio
  const riskRewardRatio = (entryPrice - takeProfit) / (stopLoss - entryPrice);

  // Only create setup if R:R ratio is favorable
  if (riskRewardRatio < 1.5) return null;

  // Determine confidence based on multiple factors
  let confidence: 'low' | 'medium' | 'high' = 'medium';
  const supportingFactors: string[] = [];

  // Adjust confidence based on bias alignment
  if (dominantBias === 'bearish') {
    confidence = biasConfidence;
    supportingFactors.push('Aligns with dominant bearish bias');
  } else if (dominantBias === 'bullish' && biasConfidence === 'high') {
    // Counter-trend setup with high confidence bullish bias - reduce confidence
    confidence = 'low';
    supportingFactors.push('Counter-trend setup (dominant bias is bullish)');
  }

  // Pattern significance
  if (pattern.significance === 'strong') {
    confidence = 'high';
    supportingFactors.push(`Strong ${pattern.pattern} pattern`);
  } else {
    supportingFactors.push(`${pattern.pattern} pattern`);
  }

  // Indicator confluence
  if (indicators.rsi.condition === "overbought") {
    confidence = confidence === 'medium' ? 'high' : confidence;
    supportingFactors.push('RSI overbought condition');
  }

  if (indicators.macd.condition === "bearish crossover" || indicators.macd.condition === "bearish") {
    confidence = confidence === 'medium' ? 'high' : confidence;
    supportingFactors.push('MACD bearish signal');
  }

  if (indicators.bollingerBands.condition === "overbought") {
    confidence = confidence === 'medium' ? 'high' : confidence;
    supportingFactors.push('Price near upper Bollinger Band');
  }

  // Resistance level confluence
  if (Math.abs(currentPrice - resistance) / currentPrice < 0.02) {
    supportingFactors.push('Price at key resistance level');
  }

  // Calculate expected time to take profit
  const expectedTimeToTP = calculateExpectedTimeToTP(
    candles,
    takeProfit,
    currentPrice,
    dominantBias === 'bearish' ? 'normal' : 'high' // Adjust based on market bias
  );

  return {
    direction: 'SHORT',
    entryPrice,
    stopLoss,
    takeProfit,
    riskRewardRatio,
    confidence,
    setupType: `${pattern.pattern.toUpperCase()}_BEARISH`,
    description: `Bearish setup based on ${pattern.pattern} pattern with entry at ${entryPrice.toFixed(2)}, stop loss at ${stopLoss.toFixed(2)}, and take profit at ${takeProfit.toFixed(2)}. Risk:reward ratio is ${riskRewardRatio.toFixed(2)}.`,
    invalidationCriteria: `Trade is invalidated if price closes above ${stopLoss.toFixed(2)} or if a bullish reversal pattern forms before entry.`,
    supportingFactors,
    expectedTimeToTP
  };
}

/**
 * Create a trend continuation trade setup
 */
function createTrendContinuationSetup(
  candles: Candle[],
  direction: 'LONG' | 'SHORT',
  support: number,
  resistance: number,
  indicators: {
    rsi: { value: number; condition: string };
    macd: { condition: string };
    bollingerBands: { condition: string };
  },
  biasConfidence: 'low' | 'medium' | 'high' = 'medium'
): TradeSetup | null {
  const currentPrice = candles[candles.length - 1].close;

  // Calculate ATR for stop loss placement
  const atr = calculateATR(candles, 14);
  const lastAtr = atr[atr.length - 1];

  let entryPrice: number;
  let stopLoss: number;
  let takeProfit: number;
  let riskRewardRatio: number;
  let setupType: string;
  let description: string;
  let invalidationCriteria: string;
  const supportingFactors: string[] = [];

  if (direction === 'LONG') {
    // Bullish trend continuation
    entryPrice = currentPrice * 1.002;
    stopLoss = Math.min(support, currentPrice - (lastAtr * 2));
    const riskAmount = entryPrice - stopLoss;
    takeProfit = entryPrice + (riskAmount * 2);
    riskRewardRatio = (takeProfit - entryPrice) / (entryPrice - stopLoss);
    setupType = 'UPTREND_CONTINUATION';
    description = `Bullish trend continuation setup with entry at ${entryPrice.toFixed(2)}, stop loss at ${stopLoss.toFixed(2)}, and take profit at ${takeProfit.toFixed(2)}. Risk:reward ratio is ${riskRewardRatio.toFixed(2)}.`;
    invalidationCriteria = `Trade is invalidated if price breaks below ${stopLoss.toFixed(2)} or if higher lows structure is broken.`;

    supportingFactors.push('Established uptrend with higher highs and higher lows');

    if (indicators.macd.condition === "bullish") {
      supportingFactors.push('MACD confirms uptrend');
    }
  } else {
    // Bearish trend continuation
    entryPrice = currentPrice * 0.998;
    stopLoss = Math.max(resistance, currentPrice + (lastAtr * 2));
    const riskAmount = stopLoss - entryPrice;
    takeProfit = entryPrice - (riskAmount * 2);
    riskRewardRatio = (entryPrice - takeProfit) / (stopLoss - entryPrice);
    setupType = 'DOWNTREND_CONTINUATION';
    description = `Bearish trend continuation setup with entry at ${entryPrice.toFixed(2)}, stop loss at ${stopLoss.toFixed(2)}, and take profit at ${takeProfit.toFixed(2)}. Risk:reward ratio is ${riskRewardRatio.toFixed(2)}.`;
    invalidationCriteria = `Trade is invalidated if price breaks above ${stopLoss.toFixed(2)} or if lower highs structure is broken.`;

    supportingFactors.push('Established downtrend with lower highs and lower lows');

    if (indicators.macd.condition === "bearish") {
      supportingFactors.push('MACD confirms downtrend');
    }
  }

  // Only create setup if R:R ratio is favorable
  if (riskRewardRatio < 1.5) return null;

  // Determine confidence based on indicator confluence
  let confidence: 'low' | 'medium' | 'high' = 'medium';

  // Count supporting indicators
  let supportingIndicators = 0;

  if (direction === 'LONG') {
    if (indicators.rsi.condition !== "overbought") supportingIndicators++;
    if (indicators.macd.condition === "bullish" || indicators.macd.condition === "bullish crossover") supportingIndicators++;
    if (indicators.bollingerBands.condition !== "overbought") supportingIndicators++;
  } else {
    if (indicators.rsi.condition !== "oversold") supportingIndicators++;
    if (indicators.macd.condition === "bearish" || indicators.macd.condition === "bearish crossover") supportingIndicators++;
    if (indicators.bollingerBands.condition !== "oversold") supportingIndicators++;
  }

  if (supportingIndicators >= 2) {
    confidence = 'high';
  }

  // Calculate expected time to take profit
  const expectedTimeToTP = calculateExpectedTimeToTP(
    candles,
    takeProfit,
    currentPrice,
    direction === 'LONG' ? 'normal' : 'normal' // Use normal volatility for trend continuation
  );

  return {
    direction,
    entryPrice,
    stopLoss,
    takeProfit,
    riskRewardRatio,
    confidence,
    setupType,
    description,
    invalidationCriteria,
    supportingFactors,
    expectedTimeToTP
  };
}

/**
 * Create a trade setup based on indicator signals
 */
function createIndicatorBasedSetup(
  candles: Candle[],
  direction: 'LONG' | 'SHORT',
  setupType: string,
  support: number,
  resistance: number,
  confidence: 'low' | 'medium' | 'high' = 'medium'
): TradeSetup | null {
  const currentPrice = candles[candles.length - 1].close;

  // Calculate ATR for stop loss placement
  const atr = calculateATR(candles, 14);
  const lastAtr = atr[atr.length - 1];

  let entryPrice: number;
  let stopLoss: number;
  let takeProfit: number;
  let riskRewardRatio: number;
  let description: string;
  let invalidationCriteria: string;
  const supportingFactors: string[] = [];

  if (direction === 'LONG') {
    // Bullish setup
    entryPrice = currentPrice * 1.002;
    stopLoss = Math.min(support, currentPrice - (lastAtr * 1.5));
    const riskAmount = entryPrice - stopLoss;
    takeProfit = entryPrice + (riskAmount * 1.5);
    riskRewardRatio = (takeProfit - entryPrice) / (entryPrice - stopLoss);
    description = `Bullish reversal setup based on oversold conditions with entry at ${entryPrice.toFixed(2)}, stop loss at ${stopLoss.toFixed(2)}, and take profit at ${takeProfit.toFixed(2)}. Risk:reward ratio is ${riskRewardRatio.toFixed(2)}.`;
    invalidationCriteria = `Trade is invalidated if price closes below ${stopLoss.toFixed(2)} or continues to make lower lows.`;

    supportingFactors.push('RSI oversold condition');
    supportingFactors.push('Price near support level');
  } else {
    // Bearish setup
    entryPrice = currentPrice * 0.998;
    stopLoss = Math.max(resistance, currentPrice + (lastAtr * 1.5));
    const riskAmount = stopLoss - entryPrice;
    takeProfit = entryPrice - (riskAmount * 1.5);
    riskRewardRatio = (entryPrice - takeProfit) / (stopLoss - entryPrice);
    description = `Bearish reversal setup based on overbought conditions with entry at ${entryPrice.toFixed(2)}, stop loss at ${stopLoss.toFixed(2)}, and take profit at ${takeProfit.toFixed(2)}. Risk:reward ratio is ${riskRewardRatio.toFixed(2)}.`;
    invalidationCriteria = `Trade is invalidated if price closes above ${stopLoss.toFixed(2)} or continues to make higher highs.`;

    supportingFactors.push('RSI overbought condition');
    supportingFactors.push('Price near resistance level');
  }

  // Only create setup if R:R ratio is favorable
  if (riskRewardRatio < 1.5) return null;

  // Calculate expected time to take profit
  const expectedTimeToTP = calculateExpectedTimeToTP(
    candles,
    takeProfit,
    currentPrice,
    direction === 'LONG' ? 'normal' : 'normal' // Use normal volatility for indicator-based setups
  );

  return {
    direction,
    entryPrice,
    stopLoss,
    takeProfit,
    riskRewardRatio,
    confidence, // Use the provided confidence level
    setupType,
    description,
    invalidationCriteria,
    supportingFactors,
    expectedTimeToTP
  };
}

/**
 * Calculate risk management parameters for a trade
 */
export function calculateRiskManagement(
  candles: Candle[],
  direction: 'LONG' | 'SHORT' | 'NEUTRAL',
  volatilityRegime: 'low' | 'normal' | 'high' = 'normal'
): RiskManagement {
  if (direction === 'NEUTRAL') {
    return {
      entryPrice: 0,
      stopLoss: 0,
      takeProfit: 0,
      riskRewardRatio: 0,
      suggestedPositionSize: 'No trade'
    };
  }

  const currentPrice = candles[candles.length - 1].close;

  // Calculate ATR for volatility-based stop loss
  const atr = calculateATR(candles, 14);
  const lastAtr = atr[atr.length - 1];

  // Adjust ATR multiplier based on volatility regime
  let atrMultiplier = 2;
  if (volatilityRegime === 'high') {
    atrMultiplier = 2.5; // Wider stops in high volatility
  } else if (volatilityRegime === 'low') {
    atrMultiplier = 1.5; // Tighter stops in low volatility
  }

  let entryPrice: number;
  let stopLoss: number;
  let takeProfit: number;

  if (direction === 'LONG') {
    entryPrice = currentPrice;
    stopLoss = currentPrice - (lastAtr * atrMultiplier);
    takeProfit = currentPrice + (lastAtr * atrMultiplier * 1.5); // 1.5:1 reward-to-risk ratio
  } else {
    entryPrice = currentPrice;
    stopLoss = currentPrice + (lastAtr * atrMultiplier);
    takeProfit = currentPrice - (lastAtr * atrMultiplier * 1.5); // 1.5:1 reward-to-risk ratio
  }

  // Calculate risk-reward ratio
  const riskAmount = Math.abs(entryPrice - stopLoss);
  const rewardAmount = Math.abs(takeProfit - entryPrice);
  const riskRewardRatio = rewardAmount / riskAmount;

  // Calculate expected time to take profit
  const expectedTimeToTP = calculateExpectedTimeToTP(candles, takeProfit, currentPrice, volatilityRegime);

  // Suggest position size based on 1% account risk
  const suggestedPositionSize = '1% of account';

  return {
    entryPrice,
    stopLoss,
    takeProfit,
    riskRewardRatio,
    suggestedPositionSize,
    expectedTimeToTP
  };
}

/**
 * Calculate Average True Range (ATR)
 * Optimized for small inputs
 */
function calculateATR(candles: Candle[], period: number): number[] {
  // Use only the data we need to keep inputs small
  const relevantCandles = candles.slice(-period * 2);
  const trueRanges: number[] = [];
  const atrs: number[] = [];

  // Calculate true ranges
  for (let i = 0; i < relevantCandles.length; i++) {
    if (i === 0) {
      trueRanges.push(relevantCandles[i].high - relevantCandles[i].low);
    } else {
      const tr1 = relevantCandles[i].high - relevantCandles[i].low;
      const tr2 = Math.abs(relevantCandles[i].high - relevantCandles[i-1].close);
      const tr3 = Math.abs(relevantCandles[i-1].close - relevantCandles[i].low);
      trueRanges.push(Math.max(tr1, tr2, tr3));
    }
  }

  // Calculate ATR using simple moving average
  for (let i = 0; i < relevantCandles.length; i++) {
    if (i < period - 1) {
      atrs.push(NaN);
    } else {
      let sum = 0;
      for (let j = 0; j < period; j++) {
        sum += trueRanges[i - j];
      }
      atrs.push(sum / period);
    }
  }

  return atrs;
}

/**
 * Calculate expected time to reach target price
 * Provides an estimate based on recent price movement and volatility
 */
export function calculateExpectedTimeToTP(
  candles: Candle[],
  targetPrice: number,
  currentPrice: number,
  volatilityRegime: 'low' | 'normal' | 'high' = 'normal'
): string {
  // Need at least 20 candles for meaningful calculation
  if (candles.length < 20) {
    return "Insufficient data";
  }

  // Calculate average daily price movement over the last 20 days
  const recentData = candles.slice(-20);
  let totalMovement = 0;

  for (let i = 1; i < recentData.length; i++) {
    totalMovement += Math.abs(recentData[i].close - recentData[i-1].close);
  }

  const avgDailyMovement = totalMovement / (recentData.length - 1);

  // Calculate price distance to target
  const priceDistance = Math.abs(targetPrice - currentPrice);

  // Estimate days to reach target based on average movement
  const estimatedDays = Math.ceil(priceDistance / avgDailyMovement);

  // Calculate volatility-adjusted estimate
  const atr = calculateATR(candles, 14);
  const lastAtr = atr[atr.length - 1];
  const volatilityAdjustedDays = Math.ceil(priceDistance / lastAtr);

  // Adjust based on volatility regime
  let adjustmentFactor = 1.0;
  if (volatilityRegime === 'high') {
    adjustmentFactor = 0.7; // Higher volatility = faster price movement
  } else if (volatilityRegime === 'low') {
    adjustmentFactor = 1.5; // Lower volatility = slower price movement
  }

  // Take the average of both methods for a more balanced estimate
  const finalEstimate = Math.round((estimatedDays + volatilityAdjustedDays) / 2 * adjustmentFactor);

  // Convert to a human-readable format
  if (finalEstimate <= 5) {
    return "~1 week";
  } else if (finalEstimate <= 10) {
    return "1-2 weeks";
  } else if (finalEstimate <= 20) {
    return "2-4 weeks";
  } else if (finalEstimate <= 60) {
    return "1-3 months";
  } else {
    return "3+ months";
  }
}

/**
 * Filter and prioritize trade setups to avoid conflicting signals
 */
export function filterAndPrioritizeSetups(
  setups: TradeSetup[],
  marketBias: 'bullish' | 'bearish' | 'neutral',
  biasConfidence: 'low' | 'medium' | 'high'
): TradeSetup[] {
  if (setups.length <= 1) return setups;

  // First, prioritize setups that align with market bias when bias confidence is high
  if (biasConfidence === 'high') {
    const alignedSetups = setups.filter(setup =>
      (marketBias === 'bullish' && setup.direction === 'LONG') ||
      (marketBias === 'bearish' && setup.direction === 'SHORT')
    );

    if (alignedSetups.length > 0) {
      setups = alignedSetups;
    }
  }

  // Sort by confidence and then by risk-reward ratio
  setups.sort((a, b) => {
    const confidenceOrder = { 'high': 3, 'medium': 2, 'low': 1 };
    const confidenceDiff = confidenceOrder[b.confidence] - confidenceOrder[a.confidence];

    if (confidenceDiff !== 0) return confidenceDiff;

    // If confidence is the same, sort by risk-reward ratio
    return b.riskRewardRatio - a.riskRewardRatio;
  });

  // Remove conflicting setups (e.g., don't suggest both LONG and SHORT)
  const filteredSetups: TradeSetup[] = [];
  const directions = new Set<string>();

  for (const setup of setups) {
    // If we already have a setup in the opposite direction with higher confidence, skip this one
    if (directions.has(setup.direction === 'LONG' ? 'SHORT' : 'LONG') && setup.confidence !== 'high') {
      continue;
    }

    filteredSetups.push(setup);
    directions.add(setup.direction);

    // Limit to 3 setups maximum
    if (filteredSetups.length >= 3) break;
  }

  return filteredSetups;
}
