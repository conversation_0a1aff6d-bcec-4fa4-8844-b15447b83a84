// Market Structure and Risk Management

import { Candle } from './candlestick-patterns.ts';

// Interface for market structure analysis
export interface MarketStructure {
  structure: 'uptrend' | 'downtrend' | 'range' | 'undefined';
  confidence: 'low' | 'medium' | 'high';
  swingHighs: number[];
  swingLows: number[];
  higherHighs: boolean;
  higherLows: boolean;
  lowerHighs: boolean;
  lowerLows: boolean;
  keyLevels: {
    support: number[];
    resistance: number[];
  };
  volatility: {
    atr: number;
    percentageVolatility: number;
  };
  // New fields for enhanced analysis
  trendStrength: number; // 0-100 scale
  rangeDeviation: number; // How much price deviates from the range midpoint
  priceLocation: 'bottom_range' | 'mid_range' | 'top_range' | 'above_range' | 'below_range';
}

// Interface for risk management analysis
export interface RiskManagement {
  entryPrice: number;
  stopLoss: number;
  takeProfit: number;
  riskRewardRatio: number;
  positionSize: number;
  maxRiskPercentage: number;
  suggestedPositionSize: number;
}

// Calculate Average True Range (ATR)
export function calculateATR(candles: Candle[], period: number = 14): number[] {
  const trueRanges: number[] = [];
  const atr: number[] = [];

  // Calculate True Range for each candle
  for (let i = 0; i < candles.length; i++) {
    if (i === 0) {
      // First candle, TR is simply High - Low
      trueRanges.push(candles[i].high - candles[i].low);
    } else {
      // TR is the greatest of:
      // 1. Current High - Current Low
      // 2. |Current High - Previous Close|
      // 3. |Current Low - Previous Close|
      const tr1 = candles[i].high - candles[i].low;
      const tr2 = Math.abs(candles[i].high - candles[i-1].close);
      const tr3 = Math.abs(candles[i].low - candles[i-1].close);

      trueRanges.push(Math.max(tr1, tr2, tr3));
    }
  }

  // Calculate ATR using simple moving average of true ranges
  for (let i = 0; i < candles.length; i++) {
    if (i < period - 1) {
      atr.push(NaN); // Not enough data yet
      continue;
    }

    let sum = 0;
    for (let j = 0; j < period; j++) {
      sum += trueRanges[i - j];
    }

    atr.push(sum / period);
  }

  return atr;
}

// Find swing highs and lows with improved significance detection
export function findSwingPoints(candles: Candle[], lookback: number = 5): { highs: number[], lows: number[] } {
  const swingHighs: number[] = [];
  const swingLows: number[] = [];

  // Use adaptive lookback based on volatility
  const volatility = calculateVolatility(candles);
  const adaptiveLookback = volatility > 0.03 ? Math.max(3, Math.floor(lookback * 0.7)) : lookback;

  // First pass: identify potential swing points
  const potentialHighs: {index: number, significance: number}[] = [];
  const potentialLows: {index: number, significance: number}[] = [];

  for (let i = adaptiveLookback; i < candles.length - adaptiveLookback; i++) {
    let isHigh = true;
    let isLow = true;
    let highSignificance = 0;
    let lowSignificance = 0;

    for (let j = i - adaptiveLookback; j <= i + adaptiveLookback; j++) {
      if (j === i) continue;

      // Check if this is a high
      if (candles[j].high >= candles[i].high) {
        isHigh = false;
      } else {
        // Calculate how much lower the other point is
        const diff = (candles[i].high - candles[j].high) / candles[i].high;
        highSignificance += diff * 100; // Convert to percentage points
      }

      // Check if this is a low
      if (candles[j].low <= candles[i].low) {
        isLow = false;
      } else {
        // Calculate how much higher the other point is
        const diff = (candles[j].low - candles[i].low) / candles[i].low;
        lowSignificance += diff * 100; // Convert to percentage points
      }
    }

    // Add to potential swing points with significance score
    if (isHigh) {
      potentialHighs.push({index: i, significance: highSignificance});
    }

    if (isLow) {
      potentialLows.push({index: i, significance: lowSignificance});
    }
  }

  // Second pass: filter out less significant points that are close to more significant ones
  potentialHighs.sort((a, b) => b.significance - a.significance); // Sort by significance (descending)
  potentialLows.sort((a, b) => b.significance - a.significance); // Sort by significance (descending)

  // Helper function to check if a point is too close to already selected points
  const isTooClose = (index: number, selectedIndices: number[], minDistance: number): boolean => {
    return selectedIndices.some(selectedIndex => Math.abs(index - selectedIndex) < minDistance);
  };

  // Select significant highs
  const selectedHighIndices: number[] = [];
  for (const high of potentialHighs) {
    // Only add if it's not too close to an already selected high
    if (!isTooClose(high.index, selectedHighIndices, Math.ceil(adaptiveLookback * 0.8))) {
      selectedHighIndices.push(high.index);
      swingHighs.push(high.index);
    }
  }

  // Select significant lows
  const selectedLowIndices: number[] = [];
  for (const low of potentialLows) {
    // Only add if it's not too close to an already selected low
    if (!isTooClose(low.index, selectedLowIndices, Math.ceil(adaptiveLookback * 0.8))) {
      selectedLowIndices.push(low.index);
      swingLows.push(low.index);
    }
  }

  return { highs: swingHighs, lows: swingLows };
}

// Helper function to calculate price volatility
function calculateVolatility(candles: Candle[], period: number = 20): number {
  if (candles.length < period) return 0;

  const recentCandles = candles.slice(-period);
  const returns: number[] = [];

  for (let i = 1; i < recentCandles.length; i++) {
    const dailyReturn = (recentCandles[i].close - recentCandles[i-1].close) / recentCandles[i-1].close;
    returns.push(dailyReturn);
  }

  // Calculate standard deviation of returns
  const mean = returns.reduce((sum, val) => sum + val, 0) / returns.length;
  const squaredDiffs = returns.map(val => Math.pow(val - mean, 2));
  const variance = squaredDiffs.reduce((sum, val) => sum + val, 0) / squaredDiffs.length;

  return Math.sqrt(variance);
}

// Calculate trend strength on a scale of 0-100
function calculateTrendStrength(candles: Candle[], swingHighs: number[], swingLows: number[]): number {
  // Use multiple factors to determine trend strength
  let strength = 50; // Start at neutral

  // Factor 1: Consistency of swing points (higher highs, higher lows or lower highs, lower lows)
  let consecutiveHigherHighs = 0;
  let consecutiveHigherLows = 0;
  let consecutiveLowerHighs = 0;
  let consecutiveLowerLows = 0;

  // Check last 3 swing highs/lows if available
  const highsToCheck = Math.min(swingHighs.length, 3);
  const lowsToCheck = Math.min(swingLows.length, 3);

  for (let i = 1; i < highsToCheck; i++) {
    if (candles[swingHighs[swingHighs.length - i]].high > candles[swingHighs[swingHighs.length - i - 1]].high) {
      consecutiveHigherHighs++;
    } else if (candles[swingHighs[swingHighs.length - i]].high < candles[swingHighs[swingHighs.length - i - 1]].high) {
      consecutiveLowerHighs++;
    }
  }

  for (let i = 1; i < lowsToCheck; i++) {
    if (candles[swingLows[swingLows.length - i]].low > candles[swingLows[swingLows.length - i - 1]].low) {
      consecutiveHigherLows++;
    } else if (candles[swingLows[swingLows.length - i]].low < candles[swingLows[swingLows.length - i - 1]].low) {
      consecutiveLowerLows++;
    }
  }

  // Adjust strength based on swing point consistency
  const uptrendConsistency = consecutiveHigherHighs + consecutiveHigherLows;
  const downtrendConsistency = consecutiveLowerHighs + consecutiveLowerLows;

  if (uptrendConsistency > downtrendConsistency) {
    // Uptrend signals
    strength += uptrendConsistency * 10;
  } else if (downtrendConsistency > uptrendConsistency) {
    // Downtrend signals
    strength -= downtrendConsistency * 10;
  }

  // Factor 2: Price momentum
  const recentCandles = candles.slice(-20); // Look at last 20 candles
  const firstPrice = recentCandles[0].close;
  const lastPrice = recentCandles[recentCandles.length - 1].close;
  const priceChange = (lastPrice - firstPrice) / firstPrice * 100;

  // Adjust strength based on price change
  strength += priceChange * 2; // Each percent change adds/subtracts 2 points

  // Factor 3: Volatility
  const atr = calculateATR(candles);
  const lastATR = atr[atr.length - 1] || 0;
  const currentPrice = candles[candles.length - 1].close;
  const percentageVolatility = (lastATR / currentPrice) * 100;

  // High volatility can indicate trend strength but also potential reversal
  // We'll use a moderate adjustment
  if (percentageVolatility > 3) {
    // High volatility - could strengthen existing trend
    if (priceChange > 0) {
      strength += 5;
    } else {
      strength -= 5;
    }
  }

  // Ensure strength is within 0-100 range
  return Math.max(0, Math.min(100, strength));
}

// Identify price range from daily candles
export function identifyPriceRange(candles: Candle[]): {
  rangeStart: number;  // Index where the range begins
  resistance: number;  // Upper boundary of the range (resistance)
  support: number;     // Lower boundary of the range (support)
  avgPercentChange: number; // Average percent change between swings
  swingHighs: number[]; // Indices of swing highs used
  swingLows: number[];  // Indices of swing lows used
} {
  if (candles.length < 10) {
    // Not enough data to identify a range
    return {
      rangeStart: 0,
      resistance: candles[candles.length - 1].high,
      support: candles[candles.length - 1].low,
      avgPercentChange: 0,
      swingHighs: [],
      swingLows: []
    };
  }

  // Step 1: Start at the most recent candle and identify swing highs and lows
  const lookbackPeriod = Math.min(100, candles.length); // Look back up to 100 candles
  const recentCandles = candles.slice(-lookbackPeriod);

  // Find swing points in the recent candles
  const { highs: swingHighIndices, lows: swingLowIndices } = findSwingPoints(recentCandles, 3);

  // Convert local indices to global indices
  const swingHighs = swingHighIndices.map(idx => idx + (candles.length - lookbackPeriod));
  const swingLows = swingLowIndices.map(idx => idx + (candles.length - lookbackPeriod));

  // Step 2: Calculate percent changes between consecutive swings
  const swingChanges: {
    fromIndex: number;
    toIndex: number;
    percentChange: number;
    isHighToLow: boolean;
  }[] = [];

  // Define a type for swing points
  type SwingPoint = {
    index: number;
    isHigh: boolean;
    isLow: boolean;
  };

  // Combine and sort all swing points by index (chronological order)
  const allSwings: SwingPoint[] = [
    ...swingHighs.map(idx => ({ index: idx, isHigh: true, isLow: false })),
    ...swingLows.map(idx => ({ index: idx, isHigh: false, isLow: true }))
  ].sort((a, b) => a.index - b.index);

  // Calculate percent changes between consecutive swing points
  for (let i = 0; i < allSwings.length - 1; i++) {
    const fromSwing = allSwings[i];
    const toSwing = allSwings[i + 1];

    let fromPrice: number, toPrice: number;

    if (fromSwing.isHigh) {
      fromPrice = candles[fromSwing.index].high;
    } else {
      fromPrice = candles[fromSwing.index].low;
    }

    if (toSwing.isHigh) {
      toPrice = candles[toSwing.index].high;
    } else {
      toPrice = candles[toSwing.index].low;
    }

    const absoluteChange = Math.abs(toPrice - fromPrice);
    const percentChange = (absoluteChange / fromPrice) * 100;

    swingChanges.push({
      fromIndex: fromSwing.index,
      toIndex: toSwing.index,
      percentChange,
      isHighToLow: fromSwing.isHigh && toSwing.isLow
    });
  }

  // Step 3: Compute the average percent change
  // Use the most recent 3-5 swing pairs for the average
  const recentChanges = swingChanges.slice(-Math.min(5, swingChanges.length));
  const avgPercentChange = recentChanges.reduce((sum, change) => sum + change.percentChange, 0) / recentChanges.length;

  console.log(`Average percent change between recent swings: ${avgPercentChange.toFixed(2)}%`);

  // Step 4: Continue going back until we find a significant deviation
  let rangeStartIndex = 0;
  const deviationThreshold = 1.5; // 50% larger than the average

  for (let i = swingChanges.length - 1; i >= 0; i--) {
    const change = swingChanges[i];

    // Check if this change is significantly larger than the average
    if (change.percentChange > avgPercentChange * deviationThreshold) {
      // We found a significant deviation - the range starts after this change
      rangeStartIndex = change.toIndex;
      console.log(`Found range start at index ${rangeStartIndex} with percent change ${change.percentChange.toFixed(2)}% (${deviationThreshold}x average)`);
      break;
    }
  }

  // Step 5: Set the range boundaries
  // Collect all swing highs and lows within the range
  const rangeSwingHighs = swingHighs.filter(idx => idx >= rangeStartIndex);
  const rangeSwingLows = swingLows.filter(idx => idx >= rangeStartIndex);

  // Calculate average swing high (resistance) and average swing low (support)
  // based on actual swing points without artificial overrides
  let resistance = 0;
  if (rangeSwingHighs.length > 0) {
    resistance = rangeSwingHighs.reduce((sum, idx) => sum + candles[idx].high, 0) / rangeSwingHighs.length;
  } else {
    // Fallback if no swing highs in range - use the highest high in the recent candles
    resistance = Math.max(...candles.slice(-20).map(c => c.high));
  }

  let support = 0;
  if (rangeSwingLows.length > 0) {
    support = rangeSwingLows.reduce((sum, idx) => sum + candles[idx].low, 0) / rangeSwingLows.length;
  } else {
    // Fallback if no swing lows in range - use the lowest low in the recent candles
    support = Math.min(...candles.slice(-20).map(c => c.low));
  }

  return {
    rangeStart: rangeStartIndex,
    resistance,
    support,
    avgPercentChange,
    swingHighs: rangeSwingHighs,
    swingLows: rangeSwingLows
  };
}

// Analyze price range characteristics
function analyzeRange(candles: Candle[], keyLevels: { support: number[], resistance: number[] }): { rangeDeviation: number, priceLocation: 'bottom_range' | 'mid_range' | 'top_range' | 'above_range' | 'below_range' } {
  const currentPrice = candles[candles.length - 1].close;

  // Get nearest support and resistance based on actual market structure
  // without artificial overrides
  if (keyLevels.support.length === 0 || keyLevels.resistance.length === 0) {
    // If we don't have valid support or resistance levels, return default values
    return {
      rangeDeviation: 0,
      priceLocation: 'mid_range'
    };
  }

  const nearestSupport = keyLevels.support[0];
  const nearestResistance = keyLevels.resistance[0];

  // Calculate range midpoint
  const rangeMidpoint = (nearestSupport + nearestResistance) / 2;

  // Calculate range size
  const rangeSize = nearestResistance - nearestSupport;

  // Calculate how much price deviates from the range midpoint as a percentage of range size
  const deviation = Math.abs(currentPrice - rangeMidpoint) / rangeSize;

  // Determine price location within the range
  let priceLocation: 'bottom_range' | 'mid_range' | 'top_range' | 'above_range' | 'below_range';

  if (currentPrice > nearestResistance) {
    priceLocation = 'above_range';
  } else if (currentPrice < nearestSupport) {
    priceLocation = 'below_range';
  } else {
    // Within range - determine position
    const relativePosition = (currentPrice - nearestSupport) / rangeSize;

    if (relativePosition < 0.33) {
      priceLocation = 'bottom_range';
    } else if (relativePosition > 0.67) {
      priceLocation = 'top_range';
    } else {
      priceLocation = 'mid_range';
    }
  }

  return {
    rangeDeviation: deviation,
    priceLocation
  };
}

// Analyze market structure
export function analyzeMarketStructure(candles: Candle[]): MarketStructure & { priceRange?: { resistance: number, support: number, rangeStart: number } } {
  // Find swing points
  const { highs: swingHighs, lows: swingLows } = findSwingPoints(candles);

  // Check for higher highs and higher lows (uptrend) - IMPROVED VERSION
  // Now requiring more confirmation points and consecutive patterns
  let higherHighs = false;
  let higherLows = false;
  let consecutiveHigherHighs = 0;
  let consecutiveHigherLows = 0;

  // Check for at least 3 consecutive higher highs
  if (swingHighs.length >= 3) {
    let isConsecutive = true;
    for (let i = swingHighs.length - 1; i >= Math.max(swingHighs.length - 3, 1); i--) {
      if (candles[swingHighs[i]].high <= candles[swingHighs[i-1]].high) {
        isConsecutive = false;
        break;
      }
      consecutiveHigherHighs++;
    }
    higherHighs = isConsecutive && consecutiveHigherHighs >= 2;
  }

  // Check for at least 3 consecutive higher lows
  if (swingLows.length >= 3) {
    let isConsecutive = true;
    for (let i = swingLows.length - 1; i >= Math.max(swingLows.length - 3, 1); i--) {
      if (candles[swingLows[i]].low <= candles[swingLows[i-1]].low) {
        isConsecutive = false;
        break;
      }
      consecutiveHigherLows++;
    }
    higherLows = isConsecutive && consecutiveHigherLows >= 2;
  }

  // Check for lower highs and lower lows (downtrend) - IMPROVED VERSION
  // Now requiring more confirmation points and consecutive patterns
  let lowerHighs = false;
  let lowerLows = false;
  let consecutiveLowerHighs = 0;
  let consecutiveLowerLows = 0;

  // Check for at least 3 consecutive lower highs
  if (swingHighs.length >= 3) {
    let isConsecutive = true;
    for (let i = swingHighs.length - 1; i >= Math.max(swingHighs.length - 3, 1); i--) {
      if (candles[swingHighs[i]].high >= candles[swingHighs[i-1]].high) {
        isConsecutive = false;
        break;
      }
      consecutiveLowerHighs++;
    }
    lowerHighs = isConsecutive && consecutiveLowerHighs >= 2;
  }

  // Check for at least 3 consecutive lower lows
  if (swingLows.length >= 3) {
    let isConsecutive = true;
    for (let i = swingLows.length - 1; i >= Math.max(swingLows.length - 3, 1); i--) {
      if (candles[swingLows[i]].low >= candles[swingLows[i-1]].low) {
        isConsecutive = false;
        break;
      }
      consecutiveLowerLows++;
    }
    lowerLows = isConsecutive && consecutiveLowerLows >= 2;
  }

  // Determine market structure
  let structure: 'uptrend' | 'downtrend' | 'range' | 'undefined' = 'undefined';
  let confidence: 'low' | 'medium' | 'high' = 'low';

  // Check price relative to moving averages (if we had them)
  // For now, use a simple approach based on recent price action
  const recentCandles = candles.slice(-20); // Look at last 20 candles
  const firstPrice = recentCandles[0].close;
  const lastPrice = recentCandles[recentCandles.length - 1].close;
  const priceChange = (lastPrice - firstPrice) / firstPrice * 100;

  // Use price change as an additional signal
  const strongPriceUptrend = priceChange > 10; // 10% up
  const moderatePriceUptrend = priceChange > 5; // 5% up
  const strongPriceDowntrend = priceChange < -10; // 10% down
  const moderatePriceDowntrend = priceChange < -5; // 5% down

  // More strict criteria for trend determination
  if (higherHighs && higherLows && (consecutiveHigherHighs >= 2 && consecutiveHigherLows >= 2)) {
    structure = 'uptrend';
    confidence = strongPriceUptrend ? 'high' : 'medium';
  } else if (lowerHighs && lowerLows && (consecutiveLowerHighs >= 2 && consecutiveLowerLows >= 2)) {
    structure = 'downtrend';
    confidence = strongPriceDowntrend ? 'high' : 'medium';
  } else if (higherHighs && higherLows) {
    // Less strict but still both conditions required
    structure = 'uptrend';
    confidence = 'low';
  } else if (lowerHighs && lowerLows) {
    // Less strict but still both conditions required
    structure = 'downtrend';
    confidence = 'low';
  } else {
    // Improved range detection - more explicit criteria
    // Check if price is oscillating between support and resistance
    const rangeOscillation = isRangeOscillation(candles, swingHighs, swingLows);

    if (rangeOscillation.isRange) {
      structure = 'range';
      confidence = rangeOscillation.confidence;
      console.log(`RANGE DETECTION: Detected range with ${rangeOscillation.touchPoints} touch points and ${rangeOscillation.confidence} confidence`);
    } else {
      // Default to range with low confidence if no clear pattern
      structure = 'range';
      confidence = 'low';
      console.log(`RANGE DETECTION: No clear trend pattern, defaulting to range with low confidence`);
    }
  }

  // Override with strong price action if present
  if (strongPriceUptrend) {
    structure = 'uptrend';
    confidence = 'high';
  } else if (strongPriceDowntrend) {
    structure = 'downtrend';
    confidence = 'high';
  }

  // Find key support and resistance levels
  const keyLevels = findKeyLevels(candles, swingHighs, swingLows);

  // Calculate volatility metrics
  const atr = calculateATR(candles);
  const lastATR = atr[atr.length - 1] || 0;
  const currentPrice = candles[candles.length - 1].close;
  const percentageVolatility = (lastATR / currentPrice) * 100;

  // Calculate trend strength
  const trendStrength = calculateTrendStrength(candles, swingHighs, swingLows);

  // Analyze range characteristics
  const { rangeDeviation, priceLocation } = analyzeRange(candles, keyLevels);

  // Refine market structure based on trend strength
  if (trendStrength > 70) {
    // Strong trend
    if (priceChange > 0) {
      structure = 'uptrend';
      confidence = 'high';
    } else {
      structure = 'downtrend';
      confidence = 'high';
    }
  } else if (trendStrength < 30) {
    // Weak trend - likely in a range
    structure = 'range';
    confidence = 'high';
  }

  // Identify price range using the new algorithm
  const priceRange = identifyPriceRange(candles);

  // If we're in a range structure, update the key levels with the identified range
  if (structure === 'range') {
    // Add the range support and resistance to the key levels
    keyLevels.support.unshift(priceRange.support);
    keyLevels.resistance.unshift(priceRange.resistance);

    // Remove duplicates and sort
    keyLevels.support = [...new Set(keyLevels.support)].sort((a, b) => a - b);
    keyLevels.resistance = [...new Set(keyLevels.resistance)].sort((a, b) => a - b);
  }

  return {
    structure,
    confidence,
    swingHighs,
    swingLows,
    higherHighs,
    higherLows,
    lowerHighs,
    lowerLows,
    keyLevels,
    volatility: {
      atr: lastATR,
      percentageVolatility
    },
    trendStrength,
    rangeDeviation,
    priceLocation,
    // Add the price range information
    priceRange: {
      support: priceRange.support,
      resistance: priceRange.resistance,
      rangeStart: priceRange.rangeStart
    }
  };
}

// Find key support and resistance levels by directly averaging swing highs and swing lows, removing outliers
function findKeyLevels(candles: Candle[], swingHighs: number[], swingLows: number[]): { support: number[], resistance: number[] } {
  const currentPrice = candles[candles.length - 1].close;

  // Assuming daily candles, 3 months is approximately 63 trading days
  // If we have less than 63 candles, use all available data
  const lookbackPeriod = Math.min(63, candles.length - 1);

  // Get the last 3 months of data
  // We use this period for filtering swing points

  // Get all swing highs and lows from the last 3 months
  const recentSwingHighs = swingHighs.filter(index => index >= candles.length - lookbackPeriod);
  const recentSwingLows = swingLows.filter(index => index >= candles.length - lookbackPeriod);

  // Get the high prices from swing highs above current price
  const highPrices = recentSwingHighs
    .map(index => candles[index].high)
    .filter(price => price > currentPrice);

  // Get the low prices from swing lows below current price
  const lowPrices = recentSwingLows
    .map(index => candles[index].low)
    .filter(price => price < currentPrice);

  // Function to remove outliers using the IQR method
  const removeOutliers = (prices: number[]): number[] => {
    if (prices.length <= 3) return prices; // Not enough data to reliably detect outliers

    // Sort the prices
    const sortedPrices = [...prices].sort((a, b) => a - b);

    // Calculate quartiles
    const q1Index = Math.floor(sortedPrices.length * 0.25);
    const q3Index = Math.floor(sortedPrices.length * 0.75);
    const q1 = sortedPrices[q1Index];
    const q3 = sortedPrices[q3Index];

    // Calculate IQR (Interquartile Range)
    const iqr = q3 - q1;

    // Define bounds for outliers (1.5 * IQR is a common threshold)
    const lowerBound = q1 - 1.5 * iqr;
    const upperBound = q3 + 1.5 * iqr;

    console.log(`Outlier detection - Q1: ${q1}, Q3: ${q3}, IQR: ${iqr}, Lower bound: ${lowerBound}, Upper bound: ${upperBound}`);

    // Filter out outliers
    return sortedPrices.filter(price => price >= lowerBound && price <= upperBound);
  };

  // Remove outliers from high and low prices
  const filteredHighPrices = removeOutliers(highPrices);
  const filteredLowPrices = removeOutliers(lowPrices);

  console.log(`High prices: ${highPrices.length} -> ${filteredHighPrices.length} after removing outliers`);
  console.log(`Low prices: ${lowPrices.length} -> ${filteredLowPrices.length} after removing outliers`);

  // Create arrays for support and resistance
  const supports: number[] = [];
  const resistances: number[] = [];

  // Function to find the mode (most frequent value) in an array
  // For price data, we need to group similar prices together
  const findMode = (prices: number[], tolerance: number = 0.005): number => {
    if (prices.length === 0) return 0;
    if (prices.length === 1) return prices[0];

    // Group similar prices together
    const groups: {price: number, count: number}[] = [];

    for (const price of prices) {
      // Check if this price is close to any existing group
      let foundGroup = false;

      for (const group of groups) {
        if (Math.abs(price - group.price) / price <= tolerance) {
          // This price is close to an existing group, update the group's average price
          group.price = (group.price * group.count + price) / (group.count + 1);
          group.count++;
          foundGroup = true;
          break;
        }
      }

      // If no matching group was found, create a new one
      if (!foundGroup) {
        groups.push({price, count: 1});
      }
    }

    // Find the group with the highest count
    let modeGroup = groups[0];

    for (const group of groups) {
      if (group.count > modeGroup.count) {
        modeGroup = group;
      }
    }

    return modeGroup.price;
  };

  // Calculate mode of swing lows for support
  if (filteredLowPrices.length > 0) {
    const modeLow = findMode(filteredLowPrices);

    console.log(`Support calculation: Using mode of prices ${filteredLowPrices.join(', ')}`);
    console.log(`Support calculation: Mode value is ${modeLow}`);

    supports.push(modeLow);
  } else {
    // Fallback: use 5% below current price
    supports.push(currentPrice * 0.95);
  }

  // Calculate mode of swing highs for resistance
  if (filteredHighPrices.length > 0) {
    const modeHigh = findMode(filteredHighPrices);

    console.log(`Resistance calculation: Using mode of prices ${filteredHighPrices.join(', ')}`);
    console.log(`Resistance calculation: Mode value is ${modeHigh}`);

    resistances.push(modeHigh);
  } else {
    // Fallback: use 5% above current price
    resistances.push(currentPrice * 1.05);
  }

  // No intermediate levels added - using only the actual swing points
  // This aligns with the Aura Importance Checklist which emphasizes
  // historical price levels with multiple tests (6%)

  // Sort supports in descending order (highest support first)
  supports.sort((a, b) => b - a);

  // Sort resistances in ascending order (lowest resistance first)
  resistances.sort((a, b) => a - b);

  return {
    support: supports,
    resistance: resistances
  };
}

// Find round numbers near the current price
function findRoundNumbers(price: number, range: number): number[] {
  const roundNumbers: number[] = [];
  const minPrice = price - range;
  const maxPrice = price + range;

  // Find the nearest powers of 10
  const magnitude = Math.floor(Math.log10(price));
  const powerOf10 = Math.pow(10, magnitude);

  // Add multiples of powers of 10
  for (let i = Math.floor(minPrice / powerOf10); i <= Math.ceil(maxPrice / powerOf10); i++) {
    const roundNumber = i * powerOf10;
    if (roundNumber >= minPrice && roundNumber <= maxPrice) {
      roundNumbers.push(roundNumber);
    }
  }

  // Add multiples of powers of 10 / 2
  for (let i = Math.floor(minPrice / (powerOf10 / 2)); i <= Math.ceil(maxPrice / (powerOf10 / 2)); i++) {
    const roundNumber = i * (powerOf10 / 2);
    if (roundNumber >= minPrice && roundNumber <= maxPrice) {
      roundNumbers.push(roundNumber);
    }
  }

  // Add multiples of powers of 10 / 4
  for (let i = Math.floor(minPrice / (powerOf10 / 4)); i <= Math.ceil(maxPrice / (powerOf10 / 4)); i++) {
    const roundNumber = i * (powerOf10 / 4);
    if (roundNumber >= minPrice && roundNumber <= maxPrice) {
      roundNumbers.push(roundNumber);
    }
  }

  return roundNumbers;
}

// Detect if price is oscillating in a range
function isRangeOscillation(candles: Candle[], swingHighs: number[], swingLows: number[]): {
  isRange: boolean;
  confidence: 'low' | 'medium' | 'high';
  touchPoints: number;
  rangeWidth: number;
} {
  // Default result
  const result = {
    isRange: false,
    confidence: 'low' as 'low' | 'medium' | 'high',
    touchPoints: 0,
    rangeWidth: 0
  };

  // Need at least 3 swing highs and 3 swing lows to detect a range
  if (swingHighs.length < 3 || swingLows.length < 3) {
    return result;
  }

  // Get the most recent swing highs and lows (last 5 of each)
  const recentHighs = swingHighs.slice(-Math.min(5, swingHighs.length))
    .map(idx => candles[idx].high);
  const recentLows = swingLows.slice(-Math.min(5, swingLows.length))
    .map(idx => candles[idx].low);

  // Calculate the average high and low
  const avgHigh = recentHighs.reduce((sum, val) => sum + val, 0) / recentHighs.length;
  const avgLow = recentLows.reduce((sum, val) => sum + val, 0) / recentLows.length;

  // Calculate the standard deviation of highs and lows
  const highStdDev = Math.sqrt(
    recentHighs.reduce((sum, val) => sum + Math.pow(val - avgHigh, 2), 0) / recentHighs.length
  );
  const lowStdDev = Math.sqrt(
    recentLows.reduce((sum, val) => sum + Math.pow(val - avgLow, 2), 0) / recentLows.length
  );

  // Calculate the range width as a percentage of the average price
  const avgPrice = (avgHigh + avgLow) / 2;
  const rangeWidth = (avgHigh - avgLow) / avgPrice * 100;

  // Calculate the coefficient of variation (CV) for highs and lows
  // CV = standard deviation / mean
  const highCV = highStdDev / avgHigh;
  const lowCV = lowStdDev / avgLow;

  // Count how many swing points are within 1 standard deviation of their average
  let highTouchPoints = recentHighs.filter(high =>
    Math.abs(high - avgHigh) <= highStdDev).length;
  let lowTouchPoints = recentLows.filter(low =>
    Math.abs(low - avgLow) <= lowStdDev).length;

  // Total touch points
  const touchPoints = highTouchPoints + lowTouchPoints;

  // Determine if this is a range based on:
  // 1. Low coefficient of variation for highs and lows (consistent levels)
  // 2. Sufficient touch points at support and resistance
  // 3. Range width is reasonable (not too wide or narrow)
  const isConsistentLevels = highCV < 0.05 && lowCV < 0.05;
  const hasSufficientTouchPoints = touchPoints >= 5;
  const isReasonableWidth = rangeWidth >= 3 && rangeWidth <= 20;

  // Determine if this is a range
  result.isRange = isConsistentLevels && hasSufficientTouchPoints && isReasonableWidth;
  result.touchPoints = touchPoints;
  result.rangeWidth = rangeWidth;

  // Set confidence based on the number of touch points and consistency
  if (result.isRange) {
    if (touchPoints >= 8 && highCV < 0.03 && lowCV < 0.03) {
      result.confidence = 'high';
    } else if (touchPoints >= 6) {
      result.confidence = 'medium';
    } else {
      result.confidence = 'low';
    }
  }

  console.log(`RANGE ANALYSIS: Width=${rangeWidth.toFixed(2)}%, HighCV=${highCV.toFixed(3)}, LowCV=${lowCV.toFixed(3)}, TouchPoints=${touchPoints}, IsRange=${result.isRange}`);

  return result;
}

// Group similar price levels
function groupSimilarLevels(levels: number[], tolerance: number = 0.01): number[] {
  if (levels.length === 0) return [];

  // Sort levels
  levels.sort((a, b) => a - b);

  const grouped: number[] = [];
  let currentGroup: number[] = [levels[0]];

  for (let i = 1; i < levels.length; i++) {
    const currentLevel = levels[i];
    const previousLevel = currentGroup[currentGroup.length - 1];

    // Check if the current level is within tolerance of the previous level
    if ((currentLevel - previousLevel) / previousLevel <= tolerance) {
      currentGroup.push(currentLevel);
    } else {
      // Calculate the average of the current group
      const average = currentGroup.reduce((sum, val) => sum + val, 0) / currentGroup.length;
      grouped.push(average);

      // Start a new group
      currentGroup = [currentLevel];
    }
  }

  // Add the last group
  if (currentGroup.length > 0) {
    const average = currentGroup.reduce((sum, val) => sum + val, 0) / currentGroup.length;
    grouped.push(average);
  }

  return grouped;
}

// Evaluate range-bound trading opportunities
export function evaluateRangeTrade(marketStructure: MarketStructure, currentPrice: number): { action: 'LONG' | 'SHORT' | 'NEUTRAL', confidence: 'HIGH' | 'MEDIUM' | 'LOW', reason: string } {
  // Default to neutral with low confidence
  let action: 'LONG' | 'SHORT' | 'NEUTRAL' = 'NEUTRAL';
  let confidence: 'HIGH' | 'MEDIUM' | 'LOW' = 'LOW';
  let reason = 'No clear range-bound trading opportunity';

  // Only evaluate if we're in a range-bound market
  if (marketStructure.structure !== 'range') {
    return { action, confidence, reason: `Not in a range-bound market (${marketStructure.structure})` };
  }

  // Get nearest support and resistance based on actual market structure
  // without artificial overrides
  const nearestSupport = marketStructure.keyLevels.support.length > 0 ?
    marketStructure.keyLevels.support[0] : null;
  const nearestResistance = marketStructure.keyLevels.resistance.length > 0 ?
    marketStructure.keyLevels.resistance[0] : null;

  // If we don't have valid support or resistance levels, we can't evaluate the range trade
  if (!nearestSupport || !nearestResistance) {
    return { action, confidence, reason: "Insufficient support/resistance data for range analysis" };
  }

  // Calculate distances to support and resistance
  const distanceToSupport = (currentPrice - nearestSupport) / currentPrice;
  const distanceToResistance = (nearestResistance - currentPrice) / currentPrice;

  // Check if we're near support or resistance (within 3% - more strict)
  const nearSupport = distanceToSupport <= 0.03;
  const nearResistance = distanceToResistance <= 0.03;

  // We could determine which level we're closer to if needed
  // const closerToSupport = distanceToSupport < distanceToResistance;

  // More conservative approach to range trading
  if (nearResistance && marketStructure.confidence === 'high') {
    action = 'SHORT';
    confidence = 'MEDIUM';
    reason = `Price is near resistance (${(distanceToResistance * 100).toFixed(2)}% away) with high confidence range`;
  } else if (nearSupport && marketStructure.confidence === 'high') {
    action = 'LONG';
    confidence = 'MEDIUM';
    reason = `Price is near support (${(distanceToSupport * 100).toFixed(2)}% away) with high confidence range`;
  } else if (nearResistance) {
    action = 'SHORT';
    confidence = 'LOW';
    reason = `Price is near resistance (${(distanceToResistance * 100).toFixed(2)}% away) but range confidence is not high`;
  } else if (nearSupport) {
    action = 'LONG';
    confidence = 'LOW';
    reason = `Price is near support (${(distanceToSupport * 100).toFixed(2)}% away) but range confidence is not high`;
  } else if (marketStructure.priceLocation === 'top_range' && marketStructure.confidence === 'high') {
    action = 'SHORT';
    confidence = 'LOW';
    reason = 'Price is in the top third of a high-confidence range';
  } else if (marketStructure.priceLocation === 'bottom_range' && marketStructure.confidence === 'high') {
    action = 'LONG';
    confidence = 'LOW';
    reason = 'Price is in the bottom third of a high-confidence range';
  } else {
    // No action if not near levels in a high-confidence range
    action = 'NEUTRAL';
    confidence = 'LOW';
    reason = 'Not near key levels in a high-confidence range';
  }

  return { action, confidence, reason };
}

// Calculate risk management parameters
export function calculateRiskManagement(
  candles: Candle[],
  signal: 'LONG' | 'SHORT' | 'NEUTRAL',
  accountSize: number = 10000,
  maxRiskPercentage: number = 1
): RiskManagement {
  // Get the latest price
  const lastCandle = candles[candles.length - 1];
  const entryPrice = lastCandle.close;

  // Calculate ATR for stop loss placement
  const atr = calculateATR(candles);
  const lastATR = atr[atr.length - 1] || (entryPrice * 0.02); // Default to 2% if ATR is not available

  // Calculate stop loss based on ATR and signal
  let stopLoss = 0;
  let takeProfit = 0;

  if (signal === 'LONG') {
    // For long positions, stop loss is below entry
    stopLoss = entryPrice - (lastATR * 2);

    // Take profit is a multiple of the risk
    const risk = entryPrice - stopLoss;
    takeProfit = entryPrice + (risk * 2); // 2:1 reward-to-risk ratio
  } else if (signal === 'SHORT') {
    // For short positions, stop loss is above entry
    stopLoss = entryPrice + (lastATR * 2);

    // Take profit is a multiple of the risk
    const risk = stopLoss - entryPrice;
    takeProfit = entryPrice - (risk * 2); // 2:1 reward-to-risk ratio
  } else {
    // Neutral signal, use default values
    stopLoss = entryPrice * 0.95; // 5% below entry
    takeProfit = entryPrice * 1.1; // 10% above entry
  }

  // Calculate risk-reward ratio
  const risk = Math.abs(entryPrice - stopLoss);
  const reward = Math.abs(entryPrice - takeProfit);
  const riskRewardRatio = reward / risk;

  // Calculate position size based on risk
  const riskAmount = accountSize * (maxRiskPercentage / 100);
  const positionSize = riskAmount / risk;

  // Calculate suggested position size (in shares/contracts)
  const suggestedPositionSize = Math.floor(positionSize);

  return {
    entryPrice,
    stopLoss,
    takeProfit,
    riskRewardRatio,
    positionSize,
    maxRiskPercentage,
    suggestedPositionSize
  };
}
