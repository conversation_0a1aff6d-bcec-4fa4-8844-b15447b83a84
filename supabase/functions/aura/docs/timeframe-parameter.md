# Timeframe Parameter for Aura API

The Aura API now supports a `timeframe` parameter that allows you to specify which timeframe you want to trade on. This document explains how to use this parameter.

## Supported Timeframes

The following timeframes are supported:

- `minute`: 1-minute bars
- `5minute`: 5-minute bars
- `15minute`: 15-minute bars
- `30minute`: 30-minute bars
- `hour`: 1-hour bars
- `day`: 1-day bars (default)
- `week`: 1-week bars
- `month`: 1-month bars
- `quarter`: 1-quarter bars
- `year`: 1-year bars

## API Usage

### Public Test Endpoint

```json
{
  "action": "public_test",
  "tickers": ["TSLA"],
  "timeframe": "15minute"
}
```

### Encrypted Endpoint

```json
{
  "action": "analyze",
  "tickers": ["TSLA"],
  "timeframe": "15minute"
}
```

## Response

The response will include the timeframe information in the `timeframe` field:

```json
{
  "success": true,
  "data": {
    "results": [...],
    "timeframe": {
      "start": "2023-01-01",
      "end": "2024-01-01",
      "interval": "15minute"
    }
  }
}
```

## Notes

- If no timeframe is specified, the default is `day`.
- Multi-timeframe analysis is only available when using the `day` timeframe.
- Different timeframes may require different analysis techniques, so the results may vary.
- For very short timeframes like `minute` or `15minute`, the system automatically limits the date range to 7 days to avoid exceeding API limits.
- When using `15minute` timeframe, if you specify a start date that is more than 7 days before the end date, the system will automatically adjust the start date to be 7 days before the end date.
- The system always ensures you get the most recent data for the specified timeframe, even when adjusting date ranges.
- Intraday timeframes like `15minute` are particularly useful for day trading and short-term swing trading strategies.
