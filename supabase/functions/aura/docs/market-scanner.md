# Market Scanner for Aura

The Market Scanner is a powerful feature that scans entire market indices (S&P 500, Russell 2000, NASDAQ, NASDAQ 100) or all stocks across major indices and returns only the highest confidence trade opportunities identified by Aura.

## Overview

The scanner analyzes all stocks in your chosen index (S&P 500, Russell 2000, NASDAQ, NASDAQ 100) or across all major indices and filters for only those trades where Aura has the highest confidence (90% or higher by default). This allows you to quickly identify the best trading opportunities across the entire market without having to analyze each stock individually.

The scanner uses real-time price data from Polygon API (with only a 15-minute delay) to ensure that all analysis is based on the most current prices available, not just the previous day's closing prices.

## API Usage

### S&P 500 Scanner

#### Public Endpoint

```json
{
  "action": "scan_sp500",
  "timeframe": "day",
  "min_confidence": 90,
  "max_results": 5
}
```

#### Encrypted Endpoint

```json
{
  "action": "scan_sp500",
  "timeframe": "day",
  "min_confidence": 90,
  "max_results": 5
}
```

### Russell 2000 Scanner

#### Public Endpoint

```json
{
  "action": "scan_russell2000",
  "timeframe": "day",
  "min_confidence": 90,
  "max_results": 5
}
```

#### Encrypted Endpoint

```json
{
  "action": "scan_russell2000",
  "timeframe": "day",
  "min_confidence": 90,
  "max_results": 5
}
```

### NASDAQ Scanner

#### Public Endpoint

```json
{
  "action": "scan_nasdaq",
  "timeframe": "day",
  "min_confidence": 90,
  "max_results": 5
}
```

#### Encrypted Endpoint

```json
{
  "action": "scan_nasdaq",
  "timeframe": "day",
  "min_confidence": 90,
  "max_results": 5
}
```

### NASDAQ 100 Scanner

#### Public Endpoint

```json
{
  "action": "scan_nasdaq100",
  "timeframe": "day",
  "min_confidence": 90,
  "max_results": 5
}
```

#### Encrypted Endpoint

```json
{
  "action": "scan_nasdaq100",
  "timeframe": "day",
  "min_confidence": 90,
  "max_results": 5
}
```

### All Stocks Scanner

#### Public Endpoint

```json
{
  "action": "scan_all_stocks",
  "timeframe": "day",
  "min_confidence": 90,
  "max_results": 5
}
```

#### Encrypted Endpoint

```json
{
  "action": "scan_all_stocks",
  "timeframe": "day",
  "min_confidence": 90,
  "max_results": 5
}
```

## Parameters

- `timeframe` (optional): The timeframe to analyze. Default is "day". Supported values are the same as for regular analysis.
- `min_confidence` (optional): The minimum confidence score (0-100) required for a trade to be included in the results. Default is 90.
- `max_results` (optional): The maximum number of results to return. Default is 10.
- `start_date` (optional): The start date for historical data. Default is 1 year ago.
- `end_date` (optional): The end date for historical data. Default is today.

## Response

The response includes a list of high-confidence trade opportunities, each with the following information:

```json
{
  "success": true,
  "data": {
    "results": [
      {
        "symbol": "AAPL",
        "signal": "LONG",
        "confidence": "VERY_HIGH",
        "confidenceScore": 95,
        "currentPrice": 150.25,
        "stopLoss": 145.50,
        "takeProfit": 160.75,
        "riskRewardRatio": 2.1,
        "potentialReturn": 10.50,
        "potentialReturnPercent": 6.99,
        "marketStructure": "uptrend",
        "keyLevels": {
          "support": 145.00,
          "resistance": 155.00
        },
        "analysis": "Strong bullish momentum with support from multiple technical indicators..."
      },
      // More results...
    ],
    "timeframe": {
      "start": "2023-05-01",
      "end": "2024-05-01",
      "interval": "day"
    },
    "scan_criteria": {
      "min_confidence": 90,
      "max_results": 5
    }
  }
}
```

## Confidence Levels

The scanner uses a sophisticated algorithm to calculate a confidence score (0-100) for each trade opportunity. This score is based on multiple factors:

1. **Base confidence** from technical analysis
2. **Multi-timeframe alignment**
3. **Market structure alignment**
4. **Technical indicator alignment**
5. **Risk-reward ratio**

The confidence levels are:
- **VERY_HIGH**: 90-100
- **HIGH**: 70-89
- **MEDIUM**: 50-69
- **LOW**: 0-49

## Performance Considerations

- The scanner analyzes hundreds or thousands of stocks, so it may take longer to respond than regular analysis requests.
- Stocks are processed in batches to avoid rate limits with the data provider.
- For faster results, consider using a smaller `max_results` value.
- Intraday timeframes (e.g., "15minute") will be limited to the most recent data to avoid excessive API calls.
- Response time varies by scanner type:
  - NASDAQ 100: Fastest (100 stocks)
  - S&P 500: Medium (500 stocks)
  - NASDAQ: Slower (thousands of stocks)
  - Russell 2000: Slower (2000 stocks)
  - All Stocks: Slowest (combines all indices, may take several minutes)

## Use Cases

1. **Market Overview**: Get a quick overview of the best trading opportunities across the entire market.
2. **Trade Idea Generation**: Discover new trading opportunities you might not have considered.
3. **Portfolio Management**: Identify high-confidence trades to add to your portfolio.
4. **Sector Analysis**: Analyze which sectors have the most high-confidence trades.
5. **Risk Management**: Focus your capital on only the highest-confidence trades.
6. **Small Cap Opportunities**: Use the Russell 2000 scanner to find high-potential small cap stocks.
7. **Tech Focus**: Use the NASDAQ or NASDAQ 100 scanner to focus on technology and growth stocks.
8. **Market Comparison**: Compare opportunities between different market segments:
   - Large Cap: S&P 500
   - Small Cap: Russell 2000
   - Tech/Growth: NASDAQ/NASDAQ 100
   - Comprehensive: All Stocks
9. **Market Breadth Analysis**: Use the All Stocks scanner to gauge overall market health and sentiment.
10. **Specialized Trading**: Focus on specific indices that match your trading style and risk tolerance.
