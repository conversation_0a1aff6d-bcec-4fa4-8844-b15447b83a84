# Aura Self-Improvement System

The Aura Self-Improvement System is a feature that analyzes backtesting results to identify patterns in correct and incorrect predictions, and provides specific recommendations for improving Aura's accuracy.

## How It Works

1. **Backtesting**: When you run a backtest, Aura analyzes a stock at different points in history and records its predictions.
2. **Performance Analysis**: The self-improvement system analyzes these predictions to identify patterns in correct and incorrect predictions.
3. **Recommendation Generation**: Based on this analysis, the system generates specific recommendations for improving Aura's accuracy.
4. **Implementation**: You can implement these recommendations to improve Aura's performance.

## Using the Self-Improvement System

1. **Run a Backtest**: Use the `public_backtest` or `backtest` endpoint to analyze a stock over a historical period.
2. **Review Recommendations**: The backtest results will include an `improvement` section with specific recommendations.
3. **Implement Changes**: Make the recommended changes to Aura's code to improve its accuracy.
4. **Verify Improvements**: Run another backtest to verify that the changes have improved Aura's performance.

## Example Request

```json
{
  "action": "public_backtest",
  "ticker": "AAPL",
  "start_date": "2022-01-01",
  "end_date": "2022-12-31",
  "timeframe": "day",
  "interval": "week",
  "include_details": false
}
```

## Example Response

```json
{
  "success": true,
  "data": {
    "ticker": "AAPL",
    "timeframe": "day",
    "interval": "week",
    "startDate": "2022-01-01",
    "endDate": "2022-12-31",
    "sampleCount": 52,
    "results": [...],
    "performance": {...},
    "improvement": {
      "summary": "Aura achieved 66.7% accuracy across 45 predictions. Performance is moderate and requires some adjustments. Key recommendation: Increase the required difference between bullish and bearish scores for LONG signals",
      "overallAccuracy": 66.7,
      "marketStructurePerformance": {
        "uptrend": {
          "name": "uptrend",
          "correctRate": 72.0,
          "totalSamples": 25,
          "averageConfidence": 78.5,
          "recommendation": "Increase weight of signals in uptrend market structure"
        },
        "downtrend": {
          "name": "downtrend",
          "correctRate": 66.67,
          "totalSamples": 12,
          "averageConfidence": 72.3,
          "recommendation": "Fine-tune downtrend market structure detection criteria"
        },
        "range": {
          "name": "range",
          "correctRate": 50.0,
          "totalSamples": 8,
          "averageConfidence": 65.8,
          "recommendation": "Reduce weight of signals in range market structure"
        }
      },
      "confidenceLevelPerformance": {...},
      "signalTypePerformance": {...},
      "keyFactors": [...],
      "topRecommendations": [
        "Increase the required difference between bullish and bearish scores for signal generation",
        "Review bullish factor weights; some factors may be overvalued",
        "Reduce weight of signals in range market structure",
        "Increase threshold for low confidence level",
        "Fine-tune downtrend market structure detection criteria"
      ]
    }
  }
}
```

## Understanding the Recommendations

The self-improvement system provides several types of recommendations:

1. **Signal Generation Thresholds**: Adjustments to the required difference between bullish and bearish scores for generating signals.
2. **Factor Weights**: Changes to the weights of different factors in the technical analysis.
3. **Market Structure Detection**: Improvements to how Aura identifies different market structures.
4. **Confidence Calculation**: Refinements to how confidence levels are determined.

## Implementing Recommendations

Here are some examples of how to implement common recommendations:

### Increase the Required Difference Between Bullish and Bearish Scores

In `technical-analysis.ts`, find the signal generation logic and increase the required difference:

```typescript
// Before
if (bullishScore > bearishScore) {
  signal = 'LONG';
} else if (bearishScore > bullishScore) {
  signal = 'SHORT';
} else {
  signal = 'NEUTRAL';
}

// After
const requiredDifference = 10; // Increased from 0
if (bullishScore > bearishScore + requiredDifference) {
  signal = 'LONG';
} else if (bearishScore > bullishScore + requiredDifference) {
  signal = 'SHORT';
} else {
  signal = 'NEUTRAL';
}
```

### Adjust Factor Weights

In `technical-analysis.ts`, find the factor weight definitions and adjust them:

```typescript
// Before
const factorWeights = {
  marketStructure: 0.3,
  candleAnalysis: 0.2,
  trendlines: 0.2,
  supportResistance: 0.15,
  fibonacci: 0.1,
  indicators: 0.05
};

// After (example: reduce weight of indicators, increase weight of market structure)
const factorWeights = {
  marketStructure: 0.35, // Increased
  candleAnalysis: 0.2,
  trendlines: 0.2,
  supportResistance: 0.15,
  fibonacci: 0.1,
  indicators: 0.0 // Reduced
};
```

### Fine-tune Market Structure Detection

In `market-structure.ts`, adjust the criteria for detecting different market structures:

```typescript
// Before
function isUptrend(data) {
  // Check for higher highs and higher lows
  return hasHigherHighs(data) && hasHigherLows(data);
}

// After (example: make uptrend detection more strict)
function isUptrend(data) {
  // Check for higher highs and higher lows with stronger criteria
  return hasHigherHighs(data, 3) && hasHigherLows(data, 2) && hasPositiveSlope(data);
}
```

## Continuous Improvement Process

The self-improvement system is designed to be part of a continuous improvement process:

1. **Run Backtests**: Regularly run backtests on different stocks and time periods.
2. **Implement Recommendations**: Implement the most important recommendations.
3. **Verify Improvements**: Run backtests again to verify that the changes have improved performance.
4. **Repeat**: Continue this process to steadily improve Aura's accuracy over time.

By following this process, you can create a feedback loop that continuously improves Aura's prediction accuracy.
