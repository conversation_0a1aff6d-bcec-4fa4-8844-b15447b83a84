# Backtesting Tool

The backtesting tool allows you to analyze how Aura would have evaluated a stock at different points in history. This is useful for understanding the system's performance over time and validating its analysis capabilities.

## Endpoints

There are two ways to access the backtesting functionality:

1. **Public Endpoint**: `public_backtest` - No authentication required
2. **Authenticated Endpoint**: `backtest` - Requires authentication

## Parameters

Both endpoints accept the same parameters:

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `ticker` | string | The stock symbol to analyze | `TSLA` |
| `start_date` | string | Start date in YYYY-MM-DD format | 1 year ago |
| `end_date` | string | End date in YYYY-MM-DD format | Current date |
| `timeframe` | string | Timeframe for analysis (day, hour, 15minute) | `day` |
| `interval` | string | How often to sample (day, week, month) | `week` |
| `include_details` | boolean | Whether to include detailed analysis | `false` |

## Response Format

The response includes:

- Basic information about the backtest (ticker, timeframe, date range)
- An array of results for each sample date
- Performance metrics summarizing the backtest results
- Self-improvement recommendations based on analysis of correct and incorrect predictions

Example response structure:

```json
{
  "success": true,
  "data": {
    "ticker": "AAPL",
    "timeframe": "day",
    "interval": "week",
    "startDate": "2023-01-01",
    "endDate": "2023-12-31",
    "sampleCount": 52,
    "results": [
      {
        "date": "2023-01-08",
        "price": 130.45,
        "signal": "LONG",
        "confidence": "HIGH",
        "confidenceScore": 75,
        "bullishScore": 75,
        "bearishScore": 25,
        "marketStructure": "uptrend",
        "stopLoss": 125.30,
        "takeProfit": 145.60
      },
      // More results...
    ],
    "performance": {
      "signalCounts": {
        "LONG": 30,
        "SHORT": 15,
        "NEUTRAL": 7
      },
      "signalAccuracy": {
        "LONG": { "correct": 20, "total": 30, "accuracy": 66.67 },
        "SHORT": { "correct": 10, "total": 15, "accuracy": 66.67 },
        "overall": { "correct": 30, "total": 45, "accuracy": 66.67 }
      },
      "profitLoss": {
        "totalPL": 125.5,
        "averagePL": 2.79,
        "maxProfit": 15.3,
        "maxLoss": 8.2,
        "profitFactor": 2.5,
        "winRate": 66.67,
        "totalWins": 30,
        "totalLosses": 15
      },
      "confidenceCorrelation": 0.72,
      "marketStructureAccuracy": {
        "uptrend": { "correct": 18, "total": 25, "accuracy": 72.0 },
        "downtrend": { "correct": 8, "total": 12, "accuracy": 66.67 },
        "range": { "correct": 4, "total": 8, "accuracy": 50.0 }
      }
    },
    "improvement": {
      "summary": "Aura achieved 66.7% accuracy across 45 predictions. Performance is moderate and requires some adjustments. Key recommendation: Increase the required difference between bullish and bearish scores for LONG signals",
      "overallAccuracy": 66.7,
      "marketStructurePerformance": {
        "uptrend": {
          "name": "uptrend",
          "correctRate": 72.0,
          "totalSamples": 25,
          "averageConfidence": 78.5,
          "recommendation": "Increase weight of signals in uptrend market structure"
        },
        "downtrend": {
          "name": "downtrend",
          "correctRate": 66.67,
          "totalSamples": 12,
          "averageConfidence": 72.3,
          "recommendation": "Fine-tune downtrend market structure detection criteria"
        },
        "range": {
          "name": "range",
          "correctRate": 50.0,
          "totalSamples": 8,
          "averageConfidence": 65.8,
          "recommendation": "Reduce weight of signals in range market structure"
        }
      },
      "confidenceLevelPerformance": {
        "high": {
          "name": "80-90%",
          "correctRate": 75.0,
          "totalSamples": 12,
          "averageConfidence": 85.3,
          "recommendation": "Current threshold for high confidence level is effective"
        },
        "medium": {
          "name": "70-80%",
          "correctRate": 68.2,
          "totalSamples": 22,
          "averageConfidence": 74.6,
          "recommendation": "Current threshold for medium confidence level is effective"
        },
        "low": {
          "name": "60-70%",
          "correctRate": 54.5,
          "totalSamples": 11,
          "averageConfidence": 65.2,
          "recommendation": "Increase threshold for low confidence level"
        }
      },
      "signalTypePerformance": {
        "LONG": {
          "name": "LONG",
          "correctRate": 66.67,
          "totalSamples": 30,
          "averageConfidence": 75.8,
          "recommendation": "Review criteria for LONG signal generation"
        },
        "SHORT": {
          "name": "SHORT",
          "correctRate": 66.67,
          "totalSamples": 15,
          "averageConfidence": 73.2,
          "recommendation": "Review criteria for SHORT signal generation"
        }
      },
      "keyFactors": [
        {
          "factor": "bullish_bearish_ratio",
          "description": "Ratio between bullish and bearish scores",
          "correctPredictions": 30,
          "incorrectPredictions": 15,
          "accuracy": 66.67,
          "averageImpact": 0.25,
          "recommendation": "Increase the required difference between bullish and bearish scores for signal generation"
        },
        {
          "factor": "confidence_score",
          "description": "Overall confidence score",
          "correctPredictions": 30,
          "incorrectPredictions": 15,
          "accuracy": 66.67,
          "averageImpact": 0.18,
          "recommendation": "Current confidence score threshold is appropriate"
        },
        {
          "factor": "incorrect_long_signals",
          "description": "LONG signals that should have been SHORT",
          "correctPredictions": 0,
          "incorrectPredictions": 10,
          "accuracy": 0,
          "averageImpact": 8.5,
          "recommendation": "Review bullish factor weights; some factors may be overvalued"
        }
      ],
      "topRecommendations": [
        "Increase the required difference between bullish and bearish scores for signal generation",
        "Review bullish factor weights; some factors may be overvalued",
        "Reduce weight of signals in range market structure",
        "Increase threshold for low confidence level",
        "Fine-tune downtrend market structure detection criteria"
      ]
    }
  }
}
```

## Examples

### Public Endpoint Example

```json
{
  "action": "public_backtest",
  "ticker": "AAPL",
  "start_date": "2023-01-01",
  "end_date": "2023-12-31",
  "timeframe": "day",
  "interval": "week",
  "include_details": false
}
```

### Authenticated Endpoint Example

```json
{
  "data": "ENCRYPTED_DATA_HERE",
  "action": "backtest",
  "ticker": "AAPL",
  "start_date": "2023-01-01",
  "end_date": "2023-12-31",
  "timeframe": "day",
  "interval": "week",
  "include_details": false
}
```

## Performance Metrics

The backtesting tool calculates several performance metrics:

- **Signal Counts**: How many LONG, SHORT, and NEUTRAL signals were generated
- **Signal Accuracy**: How often each signal type was correct
- **Profit/Loss**: Total and average P/L, max profit/loss, profit factor, win rate
- **Confidence Correlation**: Correlation between confidence scores and accuracy
- **Market Structure Accuracy**: How accurate signals were for each market structure type

## Self-Improvement Analysis

The backtesting tool now includes a self-improvement analysis that:

1. **Analyzes Prediction Patterns**: Identifies patterns in correct and incorrect predictions
2. **Evaluates Performance by Category**: Analyzes how well Aura performs in different market structures, confidence levels, and signal types
3. **Identifies Key Factors**: Determines which factors have the most impact on prediction accuracy
4. **Provides Specific Recommendations**: Suggests concrete changes to improve Aura's accuracy

The self-improvement analysis includes:

- **Summary**: A brief overview of Aura's performance and key recommendation
- **Market Structure Performance**: How well Aura performs in different market structures (uptrend, downtrend, range, etc.)
- **Confidence Level Performance**: How accuracy correlates with different confidence levels
- **Signal Type Performance**: How accurate LONG vs SHORT signals are
- **Key Factors**: Analysis of factors that influence prediction accuracy
- **Top Recommendations**: Prioritized list of changes to improve Aura's accuracy

## How to Use Self-Improvement Recommendations

The self-improvement recommendations can be used to:

1. **Adjust Signal Generation Thresholds**: Modify the required difference between bullish and bearish scores
2. **Reweight Analysis Factors**: Adjust the weights of different factors in the technical analysis
3. **Fine-tune Market Structure Detection**: Improve how Aura identifies different market structures
4. **Optimize Confidence Calculation**: Refine how confidence levels are determined

By implementing these recommendations, you can continuously improve Aura's prediction accuracy over time.

## Notes

- The backtesting tool uses historical data to simulate how Aura would have analyzed a stock at different points in time.
- Each sample represents a point in time where Aura analyzes all data up to that point.
- Performance is measured by comparing each signal to the price movement at the next sample point.
- The `include_details` parameter can be set to `true` to include full technical analysis details for each sample, but this will significantly increase the response size.
