# Price Range Identification

Aura now includes a sophisticated algorithm for identifying price ranges in market data. This feature helps traders identify when a stock is trading in a range and where the boundaries of that range are located.

## Algorithm Overview

The price range identification algorithm follows these steps:

1. **Start at the Most Recent Daily Candle**
   - Begin at the latest price candle and move backward through previous candles.

2. **Identify Swing Highs and Swing Lows**
   - While scanning backward, mark significant swing highs and swing lows (local peaks and troughs).

3. **Calculate Percent Changes Between Swings**
   - For each pair (high to low or low to high), calculate the percent change using:
   - (absolute value of Swing High minus Swing Low) divided by Swing Low, multiplied by 100.

4. **Compute the Average Percent Change**
   - Use several swing pairs (e.g., 3–5) to compute the average percent change during that period.

5. **Continue Going Back Until Deviation Occurs**
   - Keep moving backward through candles and calculating swings.
   - When you find a swing with a percent change significantly larger than the average, you've found when price exited the range.

6. **Mark That Candle as the Start of the Range**
   - The day after that breakout or breakdown candle marks the start of the range period.

7. **Set the Range Boundaries**
   - The average swing high becomes the top of the range and resistance level.
   - The average swing low becomes the bottom of the range and support level.

## API Response

The price range information is included in the API response in two places:

1. **In the summary section**:
   ```json
   "summary": {
     "findings": {
       "supportResistance": {
         "priceRange": {
           "support": 170.25,
           "resistance": 185.75,
           "rangeStart": 42,
           "supportDescription": "Range support at 170.25 (5.23% below current price)",
           "resistanceDescription": "Range resistance at 185.75 (3.45% above current price)",
           "rangeWidth": "9.10%",
           "positionInRange": "62.50%",
           "method": "Identified using swing high/low analysis with percent change calculations between swings"
         }
       }
     }
   }
   ```

2. **In the main analysis result**:
   ```json
   "supportResistance": {
     "support": [168.50, 172.30, 175.10],
     "resistance": [182.40, 185.75, 190.20],
     "priceRange": {
       "support": 170.25,
       "resistance": 185.75,
       "rangeStart": 42,
       "rangeWidth": "9.10%",
       "positionInRange": "62.50%"
     }
   }
   ```

## Understanding the Results

- **support**: The lower boundary of the price range.
- **resistance**: The upper boundary of the price range.
- **rangeStart**: The index in the candle array where the range begins.
- **rangeWidth**: The percentage difference between resistance and support.
- **positionInRange**: Where the current price is within the range (0% = at support, 100% = at resistance).
- **supportDescription/resistanceDescription**: Human-readable descriptions of the range boundaries.

## Trading Applications

1. **Range Trading Strategy**:
   - Buy near support and sell near resistance.
   - Use the positionInRange value to determine if price is near a boundary.

2. **Breakout Detection**:
   - Monitor when price moves outside the identified range.
   - Breakouts above resistance or below support may signal new trends.

3. **Risk Management**:
   - Use range boundaries to set stop-loss and take-profit levels.
   - For range trades, stop losses can be placed just outside the range.

4. **Position Sizing**:
   - The rangeWidth value can help determine appropriate position sizes.
   - Wider ranges may require smaller positions due to increased risk.

## Example Usage

```json
{
  "action": "public_test",
  "tickers": ["AAPL"],
  "timeframe": "day",
  "start_date": "2023-01-01",
  "end_date": "2024-05-07"
}
```

This request will analyze AAPL using daily candles from January 2023 to May 2024, and include the identified price range in the response.
