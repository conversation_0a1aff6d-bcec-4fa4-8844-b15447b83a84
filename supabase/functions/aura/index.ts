import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
import {
  fetchHistoricalData,
  generateTechnicalAnalysis
} from "./technical-analysis.ts"
import { analyzeMultipleTimeframes } from "./multi-timeframe.ts"
import { TradeTracker, Trade } from "./trade-review/trade-tracker.ts"
import {
  scanSP500,
  scanRussell2000,
  scanNASDAQ,
  scanNASDAQ100,
  scanAllStocks,
  ScannerResult,
  SimplifiedScannerResult,
  simplifyResults
} from "./market-scanner.ts"
import { analyzeBacktestResults } from "./self-improvement.ts"

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Allow-Methods': '*'
};

// Get the encryption key from environment variables
const ENCRYPTION_KEY = Deno.env.get('ENCRYPTION_KEY') || '';

// Initialize Supabase client with admin privileges
const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Get Polygon API key from environment variables
const POLYGON_API_KEY = Deno.env.get('POLYGON_API_KEY') || '';

// Helper function for logging errors
const logError = (message: string, details?: any) => {
  console.error(`[Aura ERROR] ${message}`, details ? JSON.stringify(details) : '');
};

// Helper function for logging info
const logInfo = (message: string, details?: any) => {
  console.log(`[Aura INFO] ${message}`, details ? JSON.stringify(details) : '');
};

/**
 * Decrypts data using AES-GCM
 * @param encryptedData - Base64 encoded encrypted data with IV
 * @param password - The password to use for decryption
 * @returns The decrypted data
 */
async function decryptData(encryptedData: string, password: string): Promise<string> {
  try {
    // Convert from base64
    const encryptedBytes = Uint8Array.from(atob(encryptedData), c => c.charCodeAt(0));

    // Extract IV (first 12 bytes)
    const iv = encryptedBytes.slice(0, 12);

    // Extract encrypted data (everything after IV)
    const data = encryptedBytes.slice(12);

    // Derive the key
    const encoder = new TextEncoder();
    const passwordBuffer = encoder.encode(password);

    // Import the password as a key
    const baseKey = await crypto.subtle.importKey(
      'raw',
      passwordBuffer,
      { name: 'PBKDF2' },
      false,
      ['deriveKey']
    );

    // Salt for key derivation
    const salt = new Uint8Array([
      0x63, 0x72, 0x79, 0x70, 0x74, 0x6f, 0x53, 0x61,
      0x6c, 0x74, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36
    ]);

    // Derive a key using PBKDF2
    const key = await crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt,
        iterations: 100000,
        hash: 'SHA-256'
      },
      baseKey,
      { name: 'AES-GCM', length: 256 },
      false,
      ['decrypt']
    );

    // Decrypt the data
    const decryptedBuffer = await crypto.subtle.decrypt(
      {
        name: 'AES-GCM',
        iv
      },
      key,
      data
    );

    // Convert the decrypted data to a string
    const decoder = new TextDecoder();
    return decoder.decode(decryptedBuffer);
  } catch (error) {
    logError('Decryption error', error);
    throw new Error('Failed to decrypt data');
  }
}

/**
 * Deobfuscates the data by extracting it from the generic object
 * @param data - The obfuscated data
 * @returns The original data
 */
function deobfuscateData(data: any): any {
  return data.client_data;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }

  try {
    // Parse the request body
    let body;
    try {
      const text = await req.text();
      body = JSON.parse(text);
    } catch (e) {
      logError('Failed to parse request body', e);
      return new Response(JSON.stringify({ error: 'Invalid request body', details: e.message }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Check if this is a public test request
    if (body.action === 'public_test') {
      logInfo('Public test endpoint called', body);
      try {
        const tickers = body.tickers || ['TSLA'];
        const startDate = body.start_date || getDefaultStartDate();
        const endDate = body.end_date || getCurrentDate();
        const timeframe = body.timeframe || 'day';

        const responseData = await analyzeSymbols({
          tickers,
          start_date: startDate,
          end_date: endDate,
          timeframe
        });

        return new Response(JSON.stringify({
          success: true,
          data: {
            results: responseData.results.map((result: any) => ({
              symbol: result.symbol,
              currentPrice: result.currentPrice,
              signal: result.signal,
              confidence: result.confidence,
              summary: result.summary
            })),
            timeframe: responseData.timeframe
          }
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      } catch (testError) {
        logError('Error in public test endpoint', testError);
        return new Response(JSON.stringify({
          error: 'Test endpoint error',
          details: testError.message
        }), {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }
    }

    // Check if this is a public backtest request
    if (body.action === 'public_backtest') {
      logInfo('Public backtest endpoint called', body);
      try {
        // Validate required parameters
        if (!body.ticker) {
          logError('Missing ticker parameter in public backtest endpoint');
          return new Response(JSON.stringify({
            error: 'Missing required parameter: ticker'
          }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        }

        const ticker = body.ticker;
        const startDate = body.start_date || getDefaultStartDate();
        const endDate = body.end_date || getCurrentDate();
        const timeframe = body.timeframe || 'day';
        const interval = body.interval || 'week'; // How often to sample (day, week, month)
        const includeDetails = body.include_details === true;

        // Log parameters for debugging
        logInfo('Backtest parameters', { ticker, startDate, endDate, timeframe, interval, includeDetails });

        // Validate the interval
        if (!['day', 'week', 'month'].includes(interval)) {
          logError('Invalid interval in public backtest endpoint', { interval });
          return new Response(JSON.stringify({
            error: 'Invalid interval. Must be one of: day, week, month'
          }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        }

        // Validate the timeframe
        if (!['day', 'hour', '15minute'].includes(timeframe)) {
          logError('Invalid timeframe in public backtest endpoint', { timeframe });
          return new Response(JSON.stringify({
            error: 'Invalid timeframe. Must be one of: day, hour, 15minute'
          }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        }

        // Validate dates
        try {
          const startDateObj = new Date(startDate);
          const endDateObj = new Date(endDate);

          if (isNaN(startDateObj.getTime())) {
            throw new Error(`Invalid start_date: ${startDate}`);
          }

          if (isNaN(endDateObj.getTime())) {
            throw new Error(`Invalid end_date: ${endDate}`);
          }

          if (startDateObj > endDateObj) {
            throw new Error(`start_date (${startDate}) must be before end_date (${endDate})`);
          }
        } catch (dateError) {
          logError('Date validation error in public backtest endpoint', dateError);
          return new Response(JSON.stringify({
            error: 'Date validation error',
            details: dateError.message
          }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        }

        // Run the backtest
        logInfo('Starting backtest execution', { ticker, startDate, endDate });
        const backtestResults = await runBacktest(
          ticker,
          startDate,
          endDate,
          timeframe,
          interval,
          includeDetails
        );

        logInfo('Backtest completed successfully', {
          ticker,
          sampleCount: backtestResults.sampleCount,
          resultsLength: backtestResults.results.length
        });

        return new Response(JSON.stringify({
          success: true,
          data: backtestResults
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      } catch (backtestError) {
        // Provide more detailed error information
        const errorMessage = backtestError.message || 'Unknown error';
        const errorStack = backtestError.stack || '';

        logError('Error in public backtest endpoint', {
          message: errorMessage,
          stack: errorStack,
          params: body
        });

        return new Response(JSON.stringify({
          error: 'Backtest error',
          details: errorMessage
        }), {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }
    }

    // Check if this is a market scanner request for S&P 500
    if (body.action === 'scan_sp500') {
      logInfo('S&P 500 scanner endpoint called', body);
      try {
        const startDate = body.start_date || getDefaultStartDate();
        const endDate = body.end_date || getCurrentDate();
        const timeframe = body.timeframe || 'day';
        const minConfidenceScore = body.min_confidence !== undefined ? Number(body.min_confidence) : 90;
        const maxResults = body.max_results || 10;
        const minPrice = body.min_price !== undefined ? Number(body.min_price) : 0;
        const maxPrice = body.max_price !== undefined ? Number(body.max_price) : Infinity;
        const marketStructureType = body.market_structure || 'any';

        // Scan the S&P 500 for high-confidence trades
        const scanResults = await scanSP500(
          POLYGON_API_KEY,
          startDate,
          endDate,
          timeframe,
          minConfidenceScore,
          maxResults,
          minPrice,
          maxPrice,
          marketStructureType
        );

        // Simplify the results and include options recommendations
        const simplifiedResults = await simplifyResults(scanResults, POLYGON_API_KEY);

        return new Response(JSON.stringify({
          success: true,
          data: {
            results: simplifiedResults,
            timeframe: {
              start: startDate,
              end: endDate,
              interval: timeframe
            },
            scan_criteria: {
              min_confidence: minConfidenceScore,
              max_results: maxResults,
              min_price: minPrice,
              max_price: maxPrice === Infinity ? "unlimited" : maxPrice,
              market_structure: marketStructureType
            }
          }
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      } catch (scanError) {
        logError('Error in S&P 500 scanner endpoint', scanError);
        return new Response(JSON.stringify({
          error: 'S&P 500 scanner error',
          details: scanError.message
        }), {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }
    }

    // Check if this is a market scanner request for Russell 2000
    if (body.action === 'scan_russell2000') {
      logInfo('Russell 2000 scanner endpoint called', body);
      try {
        const startDate = body.start_date || getDefaultStartDate();
        const endDate = body.end_date || getCurrentDate();
        const timeframe = body.timeframe || 'day';
        const minConfidenceScore = body.min_confidence !== undefined ? Number(body.min_confidence) : 90;
        const maxResults = body.max_results || 10;
        const minPrice = body.min_price !== undefined ? Number(body.min_price) : 0;
        const maxPrice = body.max_price !== undefined ? Number(body.max_price) : Infinity;
        const marketStructureType = body.market_structure || 'any';

        // Scan the Russell 2000 for high-confidence trades
        const scanResults = await scanRussell2000(
          POLYGON_API_KEY,
          startDate,
          endDate,
          timeframe,
          minConfidenceScore,
          maxResults,
          minPrice,
          maxPrice,
          marketStructureType
        );

        // Simplify the results and include options recommendations
        const simplifiedResults = await simplifyResults(scanResults, POLYGON_API_KEY);

        return new Response(JSON.stringify({
          success: true,
          data: {
            results: simplifiedResults,
            timeframe: {
              start: startDate,
              end: endDate,
              interval: timeframe
            },
            scan_criteria: {
              min_confidence: minConfidenceScore,
              max_results: maxResults,
              min_price: minPrice,
              max_price: maxPrice === Infinity ? "unlimited" : maxPrice,
              market_structure: marketStructureType
            }
          }
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      } catch (scanError) {
        logError('Error in Russell 2000 scanner endpoint', scanError);
        return new Response(JSON.stringify({
          error: 'Russell 2000 scanner error',
          details: scanError.message
        }), {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }
    }

    // Check if this is a market scanner request for NASDAQ
    if (body.action === 'scan_nasdaq') {
      logInfo('NASDAQ scanner endpoint called', body);
      try {
        const startDate = body.start_date || getDefaultStartDate();
        const endDate = body.end_date || getCurrentDate();
        const timeframe = body.timeframe || 'day';
        const minConfidenceScore = body.min_confidence !== undefined ? Number(body.min_confidence) : 90;
        const maxResults = body.max_results || 10;
        const minPrice = body.min_price !== undefined ? Number(body.min_price) : 0;
        const maxPrice = body.max_price !== undefined ? Number(body.max_price) : Infinity;
        const marketStructureType = body.market_structure || 'any';

        // Scan the NASDAQ for high-confidence trades
        const scanResults = await scanNASDAQ(
          POLYGON_API_KEY,
          startDate,
          endDate,
          timeframe,
          minConfidenceScore,
          maxResults,
          minPrice,
          maxPrice,
          marketStructureType
        );

        // Simplify the results and include options recommendations
        const simplifiedResults = await simplifyResults(scanResults, POLYGON_API_KEY);

        return new Response(JSON.stringify({
          success: true,
          data: {
            results: simplifiedResults,
            timeframe: {
              start: startDate,
              end: endDate,
              interval: timeframe
            },
            scan_criteria: {
              min_confidence: minConfidenceScore,
              max_results: maxResults,
              min_price: minPrice,
              max_price: maxPrice === Infinity ? "unlimited" : maxPrice,
              market_structure: marketStructureType
            }
          }
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      } catch (scanError) {
        logError('Error in NASDAQ scanner endpoint', scanError);
        return new Response(JSON.stringify({
          error: 'NASDAQ scanner error',
          details: scanError.message
        }), {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }
    }

    // Check if this is a market scanner request for NASDAQ 100
    if (body.action === 'scan_nasdaq100') {
      logInfo('NASDAQ 100 scanner endpoint called', body);
      try {
        const startDate = body.start_date || getDefaultStartDate();
        const endDate = body.end_date || getCurrentDate();
        const timeframe = body.timeframe || 'day';
        const minConfidenceScore = body.min_confidence !== undefined ? Number(body.min_confidence) : 90;
        const maxResults = body.max_results || 10;
        const minPrice = body.min_price !== undefined ? Number(body.min_price) : 0;
        const maxPrice = body.max_price !== undefined ? Number(body.max_price) : Infinity;
        const marketStructureType = body.market_structure || 'any';

        // Scan the NASDAQ 100 for high-confidence trades
        const scanResults = await scanNASDAQ100(
          POLYGON_API_KEY,
          startDate,
          endDate,
          timeframe,
          minConfidenceScore,
          maxResults,
          minPrice,
          maxPrice,
          marketStructureType
        );

        // Simplify the results and include options recommendations
        const simplifiedResults = await simplifyResults(scanResults, POLYGON_API_KEY);

        return new Response(JSON.stringify({
          success: true,
          data: {
            results: simplifiedResults,
            timeframe: {
              start: startDate,
              end: endDate,
              interval: timeframe
            },
            scan_criteria: {
              min_confidence: minConfidenceScore,
              max_results: maxResults,
              min_price: minPrice,
              max_price: maxPrice === Infinity ? "unlimited" : maxPrice,
              market_structure: marketStructureType
            }
          }
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      } catch (scanError) {
        logError('Error in NASDAQ 100 scanner endpoint', scanError);
        return new Response(JSON.stringify({
          error: 'NASDAQ 100 scanner error',
          details: scanError.message
        }), {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }
    }

    // Check if this is a market scanner request for All Stocks
    if (body.action === 'scan_all_stocks') {
      logInfo('All Stocks scanner endpoint called', body);
      try {
        const startDate = body.start_date || getDefaultStartDate();
        const endDate = body.end_date || getCurrentDate();
        const timeframe = body.timeframe || 'day';
        const minConfidenceScore = body.min_confidence !== undefined ? Number(body.min_confidence) : 90;
        const maxResults = body.max_results || 10;
        const minPrice = body.min_price !== undefined ? Number(body.min_price) : 0;
        const maxPrice = body.max_price !== undefined ? Number(body.max_price) : Infinity;
        const marketStructureType = body.market_structure || 'any';

        // Scan all stocks for high-confidence trades
        const scanResults = await scanAllStocks(
          POLYGON_API_KEY,
          startDate,
          endDate,
          timeframe,
          minConfidenceScore,
          maxResults,
          minPrice,
          maxPrice,
          marketStructureType
        );

        // Simplify the results and include options recommendations
        const simplifiedResults = await simplifyResults(scanResults, POLYGON_API_KEY);

        return new Response(JSON.stringify({
          success: true,
          data: {
            results: simplifiedResults,
            timeframe: {
              start: startDate,
              end: endDate,
              interval: timeframe
            },
            scan_criteria: {
              min_confidence: minConfidenceScore,
              max_results: maxResults,
              min_price: minPrice,
              max_price: maxPrice === Infinity ? "unlimited" : maxPrice,
              market_structure: marketStructureType
            }
          }
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      } catch (scanError) {
        logError('Error in All Stocks scanner endpoint', scanError);
        return new Response(JSON.stringify({
          error: 'All Stocks scanner error',
          details: scanError.message
        }), {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }
    }

    // Get the authenticated user's ID
    const authHeader = req.headers.get('Authorization');

    if (!authHeader) {
      logError('No authorization header');
      return new Response(JSON.stringify({ error: 'No authorization header' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Get the authenticated user
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''));

      if (userError || !user) {
        logError('Unauthorized user', { error: userError?.message, userId: user?.id });
        return new Response(JSON.stringify({ error: 'Unauthorized', details: userError?.message }), {
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      logInfo('Authenticated user', { userId: user.id });

      // Extract and decrypt the data
      const { data: encryptedData } = body;

      if (!encryptedData) {
        return new Response(JSON.stringify({ error: 'Missing data parameter' }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      // Decrypt the request data
      const decryptedData = await decryptData(encryptedData, ENCRYPTION_KEY);

      // Parse the decrypted data
      const parsedData = JSON.parse(decryptedData);

      // Extract action and parameters
      const { action, ...params } = parsedData;

      // Validate the action
      if (!action) {
        logError('Missing action parameter');
        return new Response(JSON.stringify({ error: 'Action is required' }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      logInfo('Processing action', { action });

      // Process the action
      let responseData: any;
      switch (action) {
        case 'analyze':
          // Add the user ID to the params
          const analyzeParams = { ...params, userId: user.id };
          const analyzeData = await analyzeSymbols(analyzeParams);

          // Return the full analysis for individual stock analysis
          responseData = analyzeData;
          break;
        case 'test':
          // Special test endpoint that bypasses encryption for easier testing
          logInfo('Test endpoint called', params);
          const testData = await analyzeSymbols({
            tickers: params.tickers || ['TSLA'],
            start_date: params.start_date || getDefaultStartDate(),
            end_date: params.end_date || getCurrentDate(),
            timeframe: params.timeframe || 'day'
          });

          // Simplify the response
          responseData = {
            results: testData.results.map((result: any) => ({
              symbol: result.symbol,
              currentPrice: result.currentPrice,
              signal: result.signal,
              confidence: result.confidence,
              summary: result.summary
            })),
            timeframe: testData.timeframe
          };
          break;
        case 'scan_sp500':
          // Scan the S&P 500 for high-confidence trades
          const sp500ScanParams = {
            start_date: params.start_date || getDefaultStartDate(),
            end_date: params.end_date || getCurrentDate(),
            timeframe: params.timeframe || 'day',
            min_confidence: params.min_confidence !== undefined ? Number(params.min_confidence) : 90,
            max_results: params.max_results || 10,
            min_price: params.min_price !== undefined ? Number(params.min_price) : 0,
            max_price: params.max_price !== undefined ? Number(params.max_price) : Infinity,
            market_structure: params.market_structure || 'any'
          };

          const sp500ScanResults = await scanSP500(
            POLYGON_API_KEY,
            sp500ScanParams.start_date,
            sp500ScanParams.end_date,
            sp500ScanParams.timeframe,
            sp500ScanParams.min_confidence,
            sp500ScanParams.max_results,
            sp500ScanParams.min_price,
            sp500ScanParams.max_price,
            sp500ScanParams.market_structure
          );

          // Simplify the results and include options recommendations
          const simplifiedSP500Results = await simplifyResults(sp500ScanResults, POLYGON_API_KEY);

          responseData = {
            results: simplifiedSP500Results,
            timeframe: {
              start: sp500ScanParams.start_date,
              end: sp500ScanParams.end_date,
              interval: sp500ScanParams.timeframe
            },
            scan_criteria: {
              min_confidence: sp500ScanParams.min_confidence,
              max_results: sp500ScanParams.max_results,
              min_price: sp500ScanParams.min_price,
              max_price: sp500ScanParams.max_price === Infinity ? "unlimited" : sp500ScanParams.max_price,
              market_structure: sp500ScanParams.market_structure
            }
          };
          break;

        case 'scan_russell2000':
          // Scan the Russell 2000 for high-confidence trades
          const russell2000ScanParams = {
            start_date: params.start_date || getDefaultStartDate(),
            end_date: params.end_date || getCurrentDate(),
            timeframe: params.timeframe || 'day',
            min_confidence: params.min_confidence !== undefined ? Number(params.min_confidence) : 90,
            max_results: params.max_results || 10,
            min_price: params.min_price !== undefined ? Number(params.min_price) : 0,
            max_price: params.max_price !== undefined ? Number(params.max_price) : Infinity,
            market_structure: params.market_structure || 'any'
          };

          const russell2000ScanResults = await scanRussell2000(
            POLYGON_API_KEY,
            russell2000ScanParams.start_date,
            russell2000ScanParams.end_date,
            russell2000ScanParams.timeframe,
            russell2000ScanParams.min_confidence,
            russell2000ScanParams.max_results,
            russell2000ScanParams.min_price,
            russell2000ScanParams.max_price,
            russell2000ScanParams.market_structure
          );

          // Simplify the results and include options recommendations
          const simplifiedRussell2000Results = await simplifyResults(russell2000ScanResults, POLYGON_API_KEY);

          responseData = {
            results: simplifiedRussell2000Results,
            timeframe: {
              start: russell2000ScanParams.start_date,
              end: russell2000ScanParams.end_date,
              interval: russell2000ScanParams.timeframe
            },
            scan_criteria: {
              min_confidence: russell2000ScanParams.min_confidence,
              max_results: russell2000ScanParams.max_results,
              min_price: russell2000ScanParams.min_price,
              max_price: russell2000ScanParams.max_price === Infinity ? "unlimited" : russell2000ScanParams.max_price,
              market_structure: russell2000ScanParams.market_structure
            }
          };
          break;

        case 'scan_nasdaq':
          // Scan the NASDAQ for high-confidence trades
          const nasdaqScanParams = {
            start_date: params.start_date || getDefaultStartDate(),
            end_date: params.end_date || getCurrentDate(),
            timeframe: params.timeframe || 'day',
            min_confidence: params.min_confidence !== undefined ? Number(params.min_confidence) : 90,
            max_results: params.max_results || 10,
            min_price: params.min_price !== undefined ? Number(params.min_price) : 0,
            max_price: params.max_price !== undefined ? Number(params.max_price) : Infinity,
            market_structure: params.market_structure || 'any'
          };

          const nasdaqScanResults = await scanNASDAQ(
            POLYGON_API_KEY,
            nasdaqScanParams.start_date,
            nasdaqScanParams.end_date,
            nasdaqScanParams.timeframe,
            nasdaqScanParams.min_confidence,
            nasdaqScanParams.max_results,
            nasdaqScanParams.min_price,
            nasdaqScanParams.max_price,
            nasdaqScanParams.market_structure
          );

          // Simplify the results and include options recommendations
          const simplifiedNasdaqResults = await simplifyResults(nasdaqScanResults, POLYGON_API_KEY);

          responseData = {
            results: simplifiedNasdaqResults,
            timeframe: {
              start: nasdaqScanParams.start_date,
              end: nasdaqScanParams.end_date,
              interval: nasdaqScanParams.timeframe
            },
            scan_criteria: {
              min_confidence: nasdaqScanParams.min_confidence,
              max_results: nasdaqScanParams.max_results,
              min_price: nasdaqScanParams.min_price,
              max_price: nasdaqScanParams.max_price === Infinity ? "unlimited" : nasdaqScanParams.max_price,
              market_structure: nasdaqScanParams.market_structure
            }
          };
          break;

        case 'scan_nasdaq100':
          // Scan the NASDAQ 100 for high-confidence trades
          const nasdaq100ScanParams = {
            start_date: params.start_date || getDefaultStartDate(),
            end_date: params.end_date || getCurrentDate(),
            timeframe: params.timeframe || 'day',
            min_confidence: params.min_confidence !== undefined ? Number(params.min_confidence) : 90,
            max_results: params.max_results || 10,
            min_price: params.min_price !== undefined ? Number(params.min_price) : 0,
            max_price: params.max_price !== undefined ? Number(params.max_price) : Infinity,
            market_structure: params.market_structure || 'any'
          };

          const nasdaq100ScanResults = await scanNASDAQ100(
            POLYGON_API_KEY,
            nasdaq100ScanParams.start_date,
            nasdaq100ScanParams.end_date,
            nasdaq100ScanParams.timeframe,
            nasdaq100ScanParams.min_confidence,
            nasdaq100ScanParams.max_results,
            nasdaq100ScanParams.min_price,
            nasdaq100ScanParams.max_price,
            nasdaq100ScanParams.market_structure
          );

          // Simplify the results and include options recommendations
          const simplifiedNasdaq100Results = await simplifyResults(nasdaq100ScanResults, POLYGON_API_KEY);

          responseData = {
            results: simplifiedNasdaq100Results,
            timeframe: {
              start: nasdaq100ScanParams.start_date,
              end: nasdaq100ScanParams.end_date,
              interval: nasdaq100ScanParams.timeframe
            },
            scan_criteria: {
              min_confidence: nasdaq100ScanParams.min_confidence,
              max_results: nasdaq100ScanParams.max_results,
              min_price: nasdaq100ScanParams.min_price,
              max_price: nasdaq100ScanParams.max_price === Infinity ? "unlimited" : nasdaq100ScanParams.max_price,
              market_structure: nasdaq100ScanParams.market_structure
            }
          };
          break;

        case 'scan_all_stocks':
          // Scan all stocks for high-confidence trades
          const allStocksScanParams = {
            start_date: params.start_date || getDefaultStartDate(),
            end_date: params.end_date || getCurrentDate(),
            timeframe: params.timeframe || 'day',
            min_confidence: params.min_confidence !== undefined ? Number(params.min_confidence) : 90,
            max_results: params.max_results || 10,
            min_price: params.min_price !== undefined ? Number(params.min_price) : 0,
            max_price: params.max_price !== undefined ? Number(params.max_price) : Infinity,
            market_structure: params.market_structure || 'any'
          };

          const allStocksScanResults = await scanAllStocks(
            POLYGON_API_KEY,
            allStocksScanParams.start_date,
            allStocksScanParams.end_date,
            allStocksScanParams.timeframe,
            allStocksScanParams.min_confidence,
            allStocksScanParams.max_results,
            allStocksScanParams.min_price,
            allStocksScanParams.max_price,
            allStocksScanParams.market_structure
          );

          // Simplify the results and include options recommendations
          const simplifiedAllStocksResults = await simplifyResults(allStocksScanResults, POLYGON_API_KEY);

          responseData = {
            results: simplifiedAllStocksResults,
            timeframe: {
              start: allStocksScanParams.start_date,
              end: allStocksScanParams.end_date,
              interval: allStocksScanParams.timeframe
            },
            scan_criteria: {
              min_confidence: allStocksScanParams.min_confidence,
              max_results: allStocksScanParams.max_results,
              min_price: allStocksScanParams.min_price,
              max_price: allStocksScanParams.max_price === Infinity ? "unlimited" : allStocksScanParams.max_price,
              market_structure: allStocksScanParams.market_structure
            }
          };
          break;

        case 'backtest':
          // Run a backtest for a specific ticker
          logInfo('Authenticated backtest endpoint called', params);

          const backtestParams = {
            ticker: params.ticker || 'TSLA',
            start_date: params.start_date || getDefaultStartDate(),
            end_date: params.end_date || getCurrentDate(),
            timeframe: params.timeframe || 'day',
            interval: params.interval || 'week',
            include_details: params.include_details === true
          };

          // Validate the interval
          if (!['day', 'week', 'month'].includes(backtestParams.interval)) {
            throw new Error('Invalid interval. Must be one of: day, week, month');
          }

          // Run the backtest
          const backtestResults = await runBacktest(
            backtestParams.ticker,
            backtestParams.start_date,
            backtestParams.end_date,
            backtestParams.timeframe,
            backtestParams.interval,
            backtestParams.include_details
          );

          responseData = backtestResults;
          break;
        default:
          return new Response(JSON.stringify({ error: `Unknown action: ${action}` }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
      }

      // Return the response data
      return new Response(JSON.stringify({
        success: true,
        data: responseData
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    } catch (authError) {
      logError('Error during authentication', { error: authError.message });
      return new Response(JSON.stringify({
        error: 'Authentication error',
        details: authError.message
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }
  } catch (error) {
    logError('Unhandled error in edge function', { error: error.message, stack: error.stack });
    return new Response(JSON.stringify({
      error: 'Internal server error',
      details: error.message,
      type: error.constructor.name
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});

// Analyze symbols and generate technical analysis
async function analyzeSymbols(params: any) {
  const { tickers, start_date, end_date, timeframe = 'day', userId } = params;

  if (!tickers || !Array.isArray(tickers) || tickers.length === 0) {
    throw new Error('No tickers provided for analysis');
  }

  // Validate dates
  const startDate = start_date || getDefaultStartDate();
  const endDate = end_date || getCurrentDate();

  logInfo(`Analyzing symbols with date range: ${startDate} to ${endDate}. Data will be filtered to this range.`);

  // Process each ticker
  const results: any[] = [];
  const analysisPromises: Promise<any>[] = [];

  for (const ticker of tickers) {
    analysisPromises.push(analyzeSymbol(ticker, startDate, endDate, timeframe, userId));
  }

  // Wait for all analyses to complete
  const analysisResults = await Promise.all(analysisPromises);

  // Process results
  for (const result of analysisResults) {
    if (result.error) {
      logError(`Error analyzing ${result.symbol}`, result.error);
    }
    results.push(result);
  }

  return {
    results,
    timeframe: {
      start: startDate,
      end: endDate,
      interval: timeframe
    }
  };
}

// Analyze a single symbol
async function analyzeSymbol(symbol: string, startDate: string, endDate: string, timeframe: string = 'day', userId?: string) {
  try {
    // Fetch historical data for the specified timeframe
    const historicalData = await fetchHistoricalData(symbol, startDate, endDate, POLYGON_API_KEY, timeframe);

    if (!historicalData.data || historicalData.data.length === 0) {
      return {
        symbol,
        error: "No historical data available"
      };
    }

    // Filter the data to ensure it's within the specified date range
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);

    // Set time to beginning/end of day to ensure proper comparison
    startDateObj.setHours(0, 0, 0, 0);
    endDateObj.setHours(23, 59, 59, 999);

    // Check if we're analyzing historical data or current data
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const isFutureEndDate = endDateObj > today;

    if (isFutureEndDate) {
      console.log(`End date ${endDate} is in the future. Will be capped at current date.`);
      endDateObj.setTime(today.getTime());
      endDateObj.setHours(23, 59, 59, 999);
    }

    console.log(`Filtering data for ${symbol} to date range: ${startDateObj.toISOString().split('T')[0]} to ${endDateObj.toISOString().split('T')[0]}`);

    const filteredData = historicalData.data.filter(candle => {
      const candleDate = new Date(candle.date);
      return candleDate >= startDateObj && candleDate <= endDateObj;
    });

    // Update the historical data with the filtered data
    historicalData.data = filteredData;

    if (filteredData.length === 0) {
      return {
        symbol,
        error: "No historical data available within the specified date range"
      };
    }

    // Log information about the date range
    if (filteredData.length > 0) {
      const firstDate = new Date(filteredData[0].date);
      const lastDate = new Date(filteredData[filteredData.length - 1].date);
      console.log(`Filtered data for ${symbol} contains ${filteredData.length} candles from ${firstDate.toISOString().split('T')[0]} to ${lastDate.toISOString().split('T')[0]}`);
    }

    // Generate technical analysis for the specified timeframe
    const analysis = await generateTechnicalAnalysis(symbol, historicalData.data, timeframe, POLYGON_API_KEY);

    // Always use full format for individual stock analysis

    // Add multi-timeframe analysis if we have enough data and we're using daily timeframe
    if (timeframe === 'day' && historicalData.data.length >= 50) {
      // Create weekly data by aggregating daily data
      const weeklyData = aggregateToWeekly(historicalData.data);

      // Create monthly data by aggregating daily data
      const monthlyData = aggregateToMonthly(historicalData.data);

      // Analyze multiple timeframes
      if (weeklyData.length > 0 && monthlyData.length > 0) {
        const timeframeData = {
          'daily': historicalData.data,
          'weekly': weeklyData,
          'monthly': monthlyData
        };

        const multiTimeframeAnalysis = analyzeMultipleTimeframes(timeframeData);
        analysis.multiTimeframe = multiTimeframeAnalysis;
      }
    }

    // Record the trade if userId is provided
    if (userId && analysis.signal && analysis.signal !== 'NEUTRAL') {
      try {
        await recordTrade(
          userId,
          symbol,
          analysis.signal as 'LONG' | 'SHORT',
          analysis.confidence as 'LOW' | 'MEDIUM' | 'HIGH',
          analysis.currentPrice,
          analysis.riskManagement.stopLoss,
          analysis.riskManagement.takeProfit,
          analysis.riskManagement.riskRewardRatio,
          analysis.marketStructure?.structure || 'undefined',
          analysis.keyLevels?.support || 0,
          analysis.keyLevels?.resistance || 0,
          timeframe
        );

        // Add a flag to indicate the trade was recorded
        analysis.tradeRecorded = true;
      } catch (tradeError) {
        logError(`Error recording trade for ${symbol}`, tradeError);
        // Don't fail the analysis if trade recording fails
        analysis.tradeRecordError = tradeError.message;
      }
    }

    // Return the full analysis for individual stock analysis
    return analysis;
  } catch (error) {
    logError(`Error analyzing ${symbol}`, error);
    return {
      symbol,
      error: error.message || "Unknown error during analysis"
    };
  }
}

// Record a trade in the database
async function recordTrade(
  userId: string,
  symbol: string,
  signal: 'LONG' | 'SHORT' | 'NEUTRAL',
  confidence: 'LOW' | 'MEDIUM' | 'HIGH',
  entryPrice: number,
  stopLoss: number,
  takeProfit: number,
  riskRewardRatio: number,
  marketStructure: string,
  keySupport: number,
  keyResistance: number,
  timeframe: string = 'day'
): Promise<{ success: boolean, data?: any, error?: any }> {
  try {
    // Create a trade tracker instance
    const tradeTracker = new TradeTracker(supabaseUrl, supabaseServiceKey);

    // Create a trade object
    const trade: Trade = {
      user_id: userId,
      symbol,
      signal,
      confidence,
      entry_price: entryPrice,
      stop_loss: stopLoss,
      take_profit: takeProfit,
      risk_reward_ratio: riskRewardRatio,
      market_structure: marketStructure,
      key_support: keySupport,
      key_resistance: keyResistance,
      timeframe
    };

    // Record the trade
    const result = await tradeTracker.recordTrade(trade);

    return result;
  } catch (error) {
    logError('Error recording trade', error);
    return { success: false, error };
  }
}

// Aggregate daily data to weekly timeframe
function aggregateToWeekly(dailyData: any[]): any[] {
  if (dailyData.length === 0) return [];

  const weeklyData: any[] = [];
  let currentWeek: any[] = [];
  let currentWeekNumber = -1;

  for (const candle of dailyData) {
    const date = new Date(candle.date);
    const weekNumber = getWeekNumber(date);

    if (weekNumber !== currentWeekNumber) {
      // Start a new week
      if (currentWeek.length > 0) {
        weeklyData.push(aggregateCandles(currentWeek));
      }
      currentWeek = [candle];
      currentWeekNumber = weekNumber;
    } else {
      // Add to current week
      currentWeek.push(candle);
    }
  }

  // Add the last week
  if (currentWeek.length > 0) {
    weeklyData.push(aggregateCandles(currentWeek));
  }

  return weeklyData;
}

// Aggregate daily data to monthly timeframe
function aggregateToMonthly(dailyData: any[]): any[] {
  if (dailyData.length === 0) return [];

  const monthlyData: any[] = [];
  let currentMonth: any[] = [];
  let currentMonthNumber = -1;

  for (const candle of dailyData) {
    const date = new Date(candle.date);
    const monthNumber = date.getMonth() + (date.getFullYear() * 12);

    if (monthNumber !== currentMonthNumber) {
      // Start a new month
      if (currentMonth.length > 0) {
        monthlyData.push(aggregateCandles(currentMonth));
      }
      currentMonth = [candle];
      currentMonthNumber = monthNumber;
    } else {
      // Add to current month
      currentMonth.push(candle);
    }
  }

  // Add the last month
  if (currentMonth.length > 0) {
    monthlyData.push(aggregateCandles(currentMonth));
  }

  return monthlyData;
}

// Helper function to get week number
function getWeekNumber(date: Date): number {
  const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
  const dayNum = d.getUTCDay() || 7;
  d.setUTCDate(d.getUTCDate() + 4 - dayNum);
  const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
  return Math.ceil((((d.getTime() - yearStart.getTime()) / 86400000) + 1) / 7);
}

// Helper function to aggregate candles
function aggregateCandles(candles: any[]): any {
  if (candles.length === 0) return null;

  const firstCandle = candles[0];
  const lastCandle = candles[candles.length - 1];

  let high = -Infinity;
  let low = Infinity;
  let volume = 0;

  for (const candle of candles) {
    high = Math.max(high, candle.high);
    low = Math.min(low, candle.low);
    volume += candle.volume;
  }

  return {
    date: firstCandle.date,
    open: firstCandle.open,
    high,
    low,
    close: lastCandle.close,
    volume
  };
}

// Helper function to get default start date (1 year ago)
function getDefaultStartDate(): string {
  const date = new Date();
  date.setFullYear(date.getFullYear() - 1);
  return date.toISOString().split('T')[0];
}

// Helper function to get current date
function getCurrentDate(): string {
  return new Date().toISOString().split('T')[0];
}

// Run a backtest for a ticker over a date range
async function runBacktest(
  ticker: string,
  startDate: string,
  endDate: string,
  timeframe: string = 'day',
  interval: string = 'week',
  includeDetails: boolean = false
): Promise<any> {
  try {
    logInfo(`Starting backtest for ${ticker}`, { startDate, endDate, timeframe, interval });

    // Fetch historical data for the entire period
    logInfo(`Fetching historical data for ${ticker}`);
    const historicalData = await fetchHistoricalData(ticker, startDate, endDate, POLYGON_API_KEY, timeframe);

    if (!historicalData) {
      throw new Error(`Failed to fetch historical data for ${ticker}`);
    }

    if (!historicalData.data || historicalData.data.length === 0) {
      throw new Error(`No historical data available for ${ticker} from ${startDate} to ${endDate}`);
    }

    logInfo(`Received ${historicalData.data.length} candles for ${ticker}`);

    // Filter the data to ensure it's within the specified date range
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);

    // Set time to beginning/end of day to ensure proper comparison
    startDateObj.setHours(0, 0, 0, 0);
    endDateObj.setHours(23, 59, 59, 999);

    // Check if we're analyzing historical data or current data
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const isFutureEndDate = endDateObj > today;

    if (isFutureEndDate) {
      logInfo(`End date ${endDate} is in the future. Will be capped at current date.`);
      endDateObj.setTime(today.getTime());
      endDateObj.setHours(23, 59, 59, 999);
    }

    logInfo(`Filtering data for ${ticker} to date range: ${startDateObj.toISOString().split('T')[0]} to ${endDateObj.toISOString().split('T')[0]}`);

    const filteredData = historicalData.data.filter(candle => {
      const candleDate = new Date(candle.date);
      return candleDate >= startDateObj && candleDate <= endDateObj;
    });

    // Update the historical data with the filtered data
    historicalData.data = filteredData;

    logInfo(`After filtering: ${filteredData.length} candles for ${ticker}`);

    if (filteredData.length === 0) {
      throw new Error(`No historical data available for ${ticker} within the specified date range`);
    }

    if (filteredData.length < 20) {
      throw new Error(`Insufficient data for ${ticker}. Need at least 20 candles for analysis, but only found ${filteredData.length}`);
    }

    // Sort data by date (ascending)
    filteredData.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    // Log the date range of the filtered data
    const firstDate = new Date(filteredData[0].date);
    const lastDate = new Date(filteredData[filteredData.length - 1].date);
    logInfo(`Filtered data for ${ticker} spans from ${firstDate.toISOString().split('T')[0]} to ${lastDate.toISOString().split('T')[0]}`);

    // Generate sample dates based on the interval
    logInfo(`Generating sample dates with interval: ${interval}`);
    const sampleDates: string[] = [];
    let currentDate = new Date(startDateObj);

    while (currentDate <= endDateObj) {
      sampleDates.push(currentDate.toISOString().split('T')[0]);

      // Increment based on interval
      if (interval === 'day') {
        currentDate.setDate(currentDate.getDate() + 1);
      } else if (interval === 'week') {
        currentDate.setDate(currentDate.getDate() + 7);
      } else if (interval === 'month') {
        currentDate.setMonth(currentDate.getMonth() + 1);
      }
    }

    logInfo(`Generated ${sampleDates.length} sample dates for backtest`);

    // For each sample date, run analysis up to that date
    const backtestResults: any[] = [];
    let processedSamples = 0;

    for (const sampleDate of sampleDates) {
      // Find the data up to this sample date
      const sampleDateObj = new Date(sampleDate);
      sampleDateObj.setHours(23, 59, 59, 999);

      const dataUpToSample = filteredData.filter(candle => {
        const candleDate = new Date(candle.date);
        return candleDate <= sampleDateObj;
      });

      // Skip if we don't have enough data
      if (dataUpToSample.length < 20) { // Need at least 20 candles for meaningful analysis
        continue;
      }

      processedSamples++;

      // Log progress periodically
      if (processedSamples % 10 === 0 || processedSamples === 1) {
        logInfo(`Processing sample ${processedSamples}/${sampleDates.length}: ${sampleDate}`);
      }

      try {
        // Run analysis on this data
        const analysis = await generateTechnicalAnalysis(ticker, dataUpToSample, timeframe, POLYGON_API_KEY);

        // Get the price at this sample date
        const lastCandle = dataUpToSample[dataUpToSample.length - 1];
        const price = lastCandle.close;

        // Create a result object
        const result: any = {
          date: sampleDate,
          price,
          signal: analysis.signal || 'NEUTRAL',
          confidence: analysis.confidence || 'LOW',
          confidenceScore: analysis.bullishScore > analysis.bearishScore ? analysis.bullishScore : analysis.bearishScore,
          bullishScore: analysis.bullishScore || 0,
          bearishScore: analysis.bearishScore || 0,
          marketStructure: analysis.marketStructure?.structure || 'unknown',
          stopLoss: analysis.riskManagement?.stopLoss || null,
          takeProfit: analysis.riskManagement?.takeProfit || null
        };

        // Include full analysis details if requested
        if (includeDetails) {
          result.details = {
            keyLevels: analysis.keyLevels || null,
            indicators: analysis.indicators || null,
            candlePatterns: analysis.candlePatterns || null,
            chartPatterns: analysis.chartPatterns || null,
            summary: analysis.summary || null
          };
        }

        backtestResults.push(result);
      } catch (analysisError) {
        logError(`Error analyzing sample date ${sampleDate}`, analysisError);
        // Continue with the next sample date instead of failing the entire backtest
      }
    }

    logInfo(`Completed processing ${processedSamples} samples, got ${backtestResults.length} valid results`);

    if (backtestResults.length === 0) {
      throw new Error(`No valid analysis results generated for ${ticker}`);
    }

    // Calculate performance metrics
    logInfo(`Calculating performance metrics for ${backtestResults.length} results`);
    const performanceMetrics = calculateBacktestPerformance(backtestResults, filteredData);

    // Generate self-improvement analysis
    logInfo(`Generating self-improvement analysis for ${backtestResults.length} results`);
    const improvementAnalysis = analyzeBacktestResults(backtestResults);

    logInfo(`Backtest completed successfully for ${ticker}`);

    return {
      ticker,
      timeframe,
      interval,
      startDate,
      endDate,
      sampleCount: backtestResults.length,
      results: backtestResults,
      performance: performanceMetrics,
      improvement: improvementAnalysis
    };
  } catch (error) {
    logError(`Error running backtest for ${ticker}`, error);
    throw error;
  }
}

// Calculate performance metrics for backtest results
function calculateBacktestPerformance(backtestResults: any[], priceData: any[]): any {
  // Sort price data by date
  priceData.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

  // Initialize metrics
  const metrics = {
    signalCounts: {
      LONG: 0,
      SHORT: 0,
      NEUTRAL: 0
    },
    signalAccuracy: {
      LONG: { correct: 0, total: 0, accuracy: 0 },
      SHORT: { correct: 0, total: 0, accuracy: 0 },
      overall: { correct: 0, total: 0, accuracy: 0 }
    },
    profitLoss: {
      totalPL: 0,
      averagePL: 0,
      maxProfit: 0,
      maxLoss: 0,
      profitFactor: 0,
      winRate: 0,
      totalWins: 0,
      totalLosses: 0
    },
    confidenceCorrelation: 0,
    marketStructureAccuracy: {}
  };

  // Count signals
  for (const result of backtestResults) {
    if (result.signal) {
      metrics.signalCounts[result.signal] = (metrics.signalCounts[result.signal] || 0) + 1;
    }
  }

  // Calculate signal accuracy and profit/loss
  let totalProfits = 0;
  let totalLosses = 0;

  for (let i = 0; i < backtestResults.length; i++) {
    const result = backtestResults[i];
    const signal = result.signal;

    // Skip NEUTRAL signals for accuracy calculation
    if (signal === 'NEUTRAL') continue;

    // Find the next sample date to measure performance
    let nextSampleIndex = i + 1;
    let futurePriceChange = 0;

    if (nextSampleIndex < backtestResults.length) {
      const currentPrice = result.price;
      const nextPrice = backtestResults[nextSampleIndex].price;
      futurePriceChange = ((nextPrice - currentPrice) / currentPrice) * 100;

      // Determine if the signal was correct
      let isCorrect = false;

      if (signal === 'LONG' && futurePriceChange > 0) {
        isCorrect = true;
      } else if (signal === 'SHORT' && futurePriceChange < 0) {
        isCorrect = true;
      }

      // Update accuracy metrics
      metrics.signalAccuracy[signal].total++;
      metrics.signalAccuracy.overall.total++;

      if (isCorrect) {
        metrics.signalAccuracy[signal].correct++;
        metrics.signalAccuracy.overall.correct++;
      }

      // Calculate profit/loss
      let pl = 0;
      if (signal === 'LONG') {
        pl = futurePriceChange;
      } else if (signal === 'SHORT') {
        pl = -futurePriceChange;
      }

      // Update profit/loss metrics
      metrics.profitLoss.totalPL += pl;

      if (pl > 0) {
        metrics.profitLoss.totalWins++;
        totalProfits += pl;
        metrics.profitLoss.maxProfit = Math.max(metrics.profitLoss.maxProfit, pl);
      } else if (pl < 0) {
        metrics.profitLoss.totalLosses++;
        totalLosses += Math.abs(pl);
        metrics.profitLoss.maxLoss = Math.max(metrics.profitLoss.maxLoss, Math.abs(pl));
      }
    }
  }

  // Calculate accuracy percentages
  if (metrics.signalAccuracy.LONG.total > 0) {
    metrics.signalAccuracy.LONG.accuracy = (metrics.signalAccuracy.LONG.correct / metrics.signalAccuracy.LONG.total) * 100;
  }

  if (metrics.signalAccuracy.SHORT.total > 0) {
    metrics.signalAccuracy.SHORT.accuracy = (metrics.signalAccuracy.SHORT.correct / metrics.signalAccuracy.SHORT.total) * 100;
  }

  if (metrics.signalAccuracy.overall.total > 0) {
    metrics.signalAccuracy.overall.accuracy = (metrics.signalAccuracy.overall.correct / metrics.signalAccuracy.overall.total) * 100;
  }

  // Calculate average P/L and profit factor
  const totalTrades = metrics.profitLoss.totalWins + metrics.profitLoss.totalLosses;

  if (totalTrades > 0) {
    metrics.profitLoss.averagePL = metrics.profitLoss.totalPL / totalTrades;
    metrics.profitLoss.winRate = (metrics.profitLoss.totalWins / totalTrades) * 100;
  }

  if (totalLosses > 0) {
    metrics.profitLoss.profitFactor = totalProfits / totalLosses;
  }

  // Calculate correlation between confidence and accuracy
  const confidenceScores: number[] = [];
  const accuracyValues: number[] = [];

  for (let i = 0; i < backtestResults.length; i++) {
    const result = backtestResults[i];
    const signal = result.signal;

    // Skip NEUTRAL signals
    if (signal === 'NEUTRAL') continue;

    // Find the next sample to measure performance
    let nextSampleIndex = i + 1;

    if (nextSampleIndex < backtestResults.length) {
      const currentPrice = result.price;
      const nextPrice = backtestResults[nextSampleIndex].price;
      const futurePriceChange = ((nextPrice - currentPrice) / currentPrice) * 100;

      // Determine if the signal was correct
      let isCorrect = false;

      if (signal === 'LONG' && futurePriceChange > 0) {
        isCorrect = true;
      } else if (signal === 'SHORT' && futurePriceChange < 0) {
        isCorrect = true;
      }

      // Add to correlation arrays
      confidenceScores.push(result.confidenceScore || 0);
      accuracyValues.push(isCorrect ? 1 : 0);
    }
  }

  // Calculate correlation if we have enough data points
  if (confidenceScores.length > 5) {
    metrics.confidenceCorrelation = calculateCorrelation(confidenceScores, accuracyValues);
  }

  // Calculate market structure accuracy
  const marketStructureCounts: Record<string, { correct: number, total: number }> = {};

  for (let i = 0; i < backtestResults.length; i++) {
    const result = backtestResults[i];
    const signal = result.signal;
    const marketStructure = result.marketStructure || 'unknown';

    // Skip NEUTRAL signals
    if (signal === 'NEUTRAL') continue;

    // Initialize market structure entry if it doesn't exist
    if (!marketStructureCounts[marketStructure]) {
      marketStructureCounts[marketStructure] = { correct: 0, total: 0 };
    }

    // Find the next sample to measure performance
    let nextSampleIndex = i + 1;

    if (nextSampleIndex < backtestResults.length) {
      const currentPrice = result.price;
      const nextPrice = backtestResults[nextSampleIndex].price;
      const futurePriceChange = ((nextPrice - currentPrice) / currentPrice) * 100;

      // Determine if the signal was correct
      let isCorrect = false;

      if (signal === 'LONG' && futurePriceChange > 0) {
        isCorrect = true;
      } else if (signal === 'SHORT' && futurePriceChange < 0) {
        isCorrect = true;
      }

      // Update market structure accuracy
      marketStructureCounts[marketStructure].total++;

      if (isCorrect) {
        marketStructureCounts[marketStructure].correct++;
      }
    }
  }

  // Calculate accuracy percentages for each market structure
  for (const structure in marketStructureCounts) {
    const counts = marketStructureCounts[structure];

    if (counts.total > 0) {
      metrics.marketStructureAccuracy[structure] = {
        correct: counts.correct,
        total: counts.total,
        accuracy: (counts.correct / counts.total) * 100
      };
    }
  }

  return metrics;
}

// Helper function to calculate correlation coefficient
function calculateCorrelation(x: number[], y: number[]): number {
  const n = x.length;

  // Calculate means
  const xMean = x.reduce((sum, val) => sum + val, 0) / n;
  const yMean = y.reduce((sum, val) => sum + val, 0) / n;

  // Calculate covariance and variances
  let covariance = 0;
  let xVariance = 0;
  let yVariance = 0;

  for (let i = 0; i < n; i++) {
    const xDiff = x[i] - xMean;
    const yDiff = y[i] - yMean;

    covariance += xDiff * yDiff;
    xVariance += xDiff * xDiff;
    yVariance += yDiff * yDiff;
  }

  // Calculate correlation coefficient
  if (xVariance === 0 || yVariance === 0) {
    return 0; // Avoid division by zero
  }

  return covariance / (Math.sqrt(xVariance) * Math.sqrt(yVariance));
}
