// Advanced Price Action Analysis
// Implements Al <PERSON>-inspired price action analysis techniques

import { Candle } from './candlestick-patterns.ts';

// Interface for candle anatomy analysis
export interface CandleAnatomy {
  position: number;
  bodySize: 'small' | 'medium' | 'large';
  bodyToRangeRatio: number;
  upperWickRatio: number;
  lowerWickRatio: number;
  wickDominance: 'upper' | 'lower' | 'balanced' | 'none';
  intent: 'strong_bullish' | 'moderate_bullish' | 'weak_bullish' | 'neutral' | 'weak_bearish' | 'moderate_bearish' | 'strong_bearish';
  closeLocation: 'near_high' | 'near_low' | 'mid_range';
  description: string;
}

// Interface for price action analysis
export interface PriceActionAnalysis {
  // Candle anatomy for recent candles
  recentCandles: CandleAnatomy[];

  // Candle sequence patterns
  sequences: Array<{
    type: string;
    startIndex: number;
    endIndex: number;
    significance: 'weak' | 'moderate' | 'strong';
    description: string;
  }>;

  // Bar-by-bar analysis (<PERSON> style)
  barByBar: {
    twoBarReversals: Array<{position: number, direction: 'up' | 'down', strength: 'weak' | 'moderate' | 'strong'}>;
    microTrends: Array<{startIndex: number, endIndex: number, direction: 'up' | 'down', strength: 'weak' | 'moderate' | 'strong'}>;
    failedBreakouts: Array<{position: number, level: number, direction: 'up' | 'down'}>;
    strongCloses: Array<{position: number, direction: 'up' | 'down'}>;
  };

  // Momentum analysis
  momentum: {
    buying: Array<{startIndex: number, endIndex: number, strength: 'weak' | 'moderate' | 'strong'}>;
    selling: Array<{startIndex: number, endIndex: number, strength: 'weak' | 'moderate' | 'strong'}>;
    exhaustion: Array<{position: number, direction: 'up' | 'down'}>;
  };
}

/**
 * Analyze price action using Al Brooks-inspired techniques
 */
export function analyzePriceAction(candles: Candle[]): PriceActionAnalysis {
  // Need at least 20 candles for meaningful analysis
  if (candles.length < 20) {
    throw new Error('Insufficient data for price action analysis');
  }

  // Analyze candle anatomy for recent candles
  const recentCandles = candles.slice(-10).map((candle, idx) =>
    analyzeCandleAnatomy(candles, candles.length - 10 + idx)
  );

  // Analyze candle sequences
  const sequences = analyzeCandleSequences(candles);

  // Perform bar-by-bar analysis
  const barByBar = analyzeBarByBar(candles);

  // Analyze momentum
  const momentum = analyzeMomentum(candles);

  return {
    recentCandles,
    sequences,
    barByBar,
    momentum
  };
}

/**
 * Analyze the anatomy of a single candlestick
 * Focuses on body/wick relationships and close location
 */
export function analyzeCandleAnatomy(candles: Candle[], position: number): CandleAnatomy {
  if (position < 0 || position >= candles.length) {
    throw new Error('Invalid candle position');
  }

  const candle = candles[position];

  // Calculate body and wick measurements
  const body = Math.abs(candle.close - candle.open);
  const range = candle.high - candle.low;
  const bodyToRangeRatio = body / range;

  // Calculate wick sizes
  const upperWick = candle.close > candle.open
    ? candle.high - candle.close
    : candle.high - candle.open;

  const lowerWick = candle.close > candle.open
    ? candle.open - candle.low
    : candle.close - candle.low;

  // Calculate wick ratios
  const upperWickRatio = upperWick / range;
  const lowerWickRatio = lowerWick / range;

  // Determine body size category
  let bodySize: 'small' | 'medium' | 'large';
  if (bodyToRangeRatio < 0.3) {
    bodySize = 'small';
  } else if (bodyToRangeRatio < 0.7) {
    bodySize = 'medium';
  } else {
    bodySize = 'large';
  }

  // Determine wick dominance
  let wickDominance: 'upper' | 'lower' | 'balanced' | 'none';
  if (upperWickRatio > 0.6 && upperWickRatio > lowerWickRatio * 2) {
    wickDominance = 'upper';
  } else if (lowerWickRatio > 0.6 && lowerWickRatio > upperWickRatio * 2) {
    wickDominance = 'lower';
  } else if (upperWickRatio < 0.1 && lowerWickRatio < 0.1) {
    wickDominance = 'none';
  } else {
    wickDominance = 'balanced';
  }

  // Determine close location (Al Brooks emphasis on close location)
  let closeLocation: 'near_high' | 'near_low' | 'mid_range';
  const closeToHighRatio = (candle.high - candle.close) / range;
  const closeToLowRatio = (candle.close - candle.low) / range;

  if (closeToHighRatio < 0.2) {
    closeLocation = 'near_high';
  } else if (closeToLowRatio < 0.2) {
    closeLocation = 'near_low';
  } else {
    closeLocation = 'mid_range';
  }

  // Determine trading intent based on candle anatomy
  let intent: 'strong_bullish' | 'moderate_bullish' | 'weak_bullish' | 'neutral' | 'weak_bearish' | 'moderate_bearish' | 'strong_bearish';

  const isBullish = candle.close > candle.open;

  if (isBullish) {
    if (bodySize === 'large' && closeLocation === 'near_high' && wickDominance !== 'upper') {
      intent = 'strong_bullish'; // Strong bullish intent
    } else if (bodySize === 'medium' && closeLocation === 'near_high') {
      intent = 'moderate_bullish';
    } else if (wickDominance === 'lower' && closeLocation !== 'near_low') {
      intent = 'moderate_bullish'; // Rejected lows
    } else {
      intent = 'weak_bullish';
    }
  } else {
    if (bodySize === 'large' && closeLocation === 'near_low' && wickDominance !== 'lower') {
      intent = 'strong_bearish'; // Strong bearish intent
    } else if (bodySize === 'medium' && closeLocation === 'near_low') {
      intent = 'moderate_bearish';
    } else if (wickDominance === 'upper' && closeLocation !== 'near_high') {
      intent = 'moderate_bearish'; // Rejected highs
    } else {
      intent = 'weak_bearish';
    }
  }

  // Special case for dojis
  if (bodySize === 'small' && (wickDominance === 'balanced' || wickDominance === 'none')) {
    intent = 'neutral';
  }

  // Generate description
  let description = '';

  if (intent === 'strong_bullish') {
    description = 'Strong bullish candle with decisive close near the high, showing clear buying pressure';
  } else if (intent === 'moderate_bullish') {
    description = 'Moderate bullish candle with buyers in control but some selling pressure present';
  } else if (intent === 'weak_bullish') {
    description = 'Weak bullish candle with limited conviction from buyers';
  } else if (intent === 'neutral') {
    description = 'Neutral candle showing indecision between buyers and sellers';
  } else if (intent === 'weak_bearish') {
    description = 'Weak bearish candle with limited conviction from sellers';
  } else if (intent === 'moderate_bearish') {
    description = 'Moderate bearish candle with sellers in control but some buying pressure present';
  } else if (intent === 'strong_bearish') {
    description = 'Strong bearish candle with decisive close near the low, showing clear selling pressure';
  }

  // Add context about wicks if significant
  if (wickDominance === 'upper' && upperWickRatio > 0.5) {
    description += '. Long upper wick indicates rejection of higher prices';
  } else if (wickDominance === 'lower' && lowerWickRatio > 0.5) {
    description += '. Long lower wick indicates rejection of lower prices';
  }

  return {
    position,
    bodySize,
    bodyToRangeRatio,
    upperWickRatio,
    lowerWickRatio,
    wickDominance,
    intent,
    closeLocation,
    description
  };
}

/**
 * Analyze candle sequences for patterns
 * Focuses on multi-bar patterns that Al Brooks emphasizes
 */
function analyzeCandleSequences(candles: Candle[]): Array<{
  type: string;
  startIndex: number;
  endIndex: number;
  significance: 'weak' | 'moderate' | 'strong';
  description: string;
}> {
  const sequences = [];

  // Need at least 3 candles for sequence analysis
  if (candles.length < 3) return sequences;

  // Detect two-bar reversal patterns
  for (let i = 1; i < candles.length; i++) {
    const prev = candles[i-1];
    const curr = candles[i];

    // Bullish reversal (bear bar followed by bull bar)
    if (prev.close < prev.open && curr.close > curr.open) {
      // Check if current bar closes above previous bar's midpoint
      const prevMidpoint = (prev.high + prev.low) / 2;

      if (curr.close > prevMidpoint) {
        sequences.push({
          type: 'two_bar_bullish_reversal',
          startIndex: i-1,
          endIndex: i,
          significance: curr.close > prev.high ? 'strong' : 'moderate',
          description: 'Two-bar bullish reversal pattern showing shift from selling to buying pressure'
        });
      }
    }

    // Bearish reversal (bull bar followed by bear bar)
    if (prev.close > prev.open && curr.close < curr.open) {
      // Check if current bar closes below previous bar's midpoint
      const prevMidpoint = (prev.high + prev.low) / 2;

      if (curr.close < prevMidpoint) {
        sequences.push({
          type: 'two_bar_bearish_reversal',
          startIndex: i-1,
          endIndex: i,
          significance: curr.close < prev.low ? 'strong' : 'moderate',
          description: 'Two-bar bearish reversal pattern showing shift from buying to selling pressure'
        });
      }
    }
  }

  // Detect three-bar patterns
  for (let i = 2; i < candles.length; i++) {
    const first = candles[i-2];
    const second = candles[i-1];
    const third = candles[i];

    // Three-bar pullback in bull trend
    if (first.close > first.open && second.close < second.open && third.close > third.open) {
      if (third.close > second.high) {
        sequences.push({
          type: 'three_bar_bull_pullback',
          startIndex: i-2,
          endIndex: i,
          significance: 'strong',
          description: 'Three-bar bullish pattern showing successful test of support after pullback'
        });
      }
    }

    // Three-bar pullback in bear trend
    if (first.close < first.open && second.close > second.open && third.close < third.open) {
      if (third.close < second.low) {
        sequences.push({
          type: 'three_bar_bear_pullback',
          startIndex: i-2,
          endIndex: i,
          significance: 'strong',
          description: 'Three-bar bearish pattern showing successful test of resistance after pullback'
        });
      }
    }

    // Three-bar reversal patterns

    // Bullish reversal (three pushes down followed by strong bull bar)
    if (i >= 3 && candles[i-3].close < candles[i-3].open &&
        first.close < first.open && second.close < second.open &&
        third.close > third.open && third.close > second.high) {
      sequences.push({
        type: 'three_push_bullish_reversal',
        startIndex: i-3,
        endIndex: i,
        significance: 'strong',
        description: 'Three pushes down followed by strong bullish reversal, indicating seller exhaustion'
      });
    }

    // Bearish reversal (three pushes up followed by strong bear bar)
    if (i >= 3 && candles[i-3].close > candles[i-3].open &&
        first.close > first.open && second.close > second.open &&
        third.close < third.open && third.close < second.low) {
      sequences.push({
        type: 'three_push_bearish_reversal',
        startIndex: i-3,
        endIndex: i,
        significance: 'strong',
        description: 'Three pushes up followed by strong bearish reversal, indicating buyer exhaustion'
      });
    }
  }

  return sequences;
}

/**
 * Perform bar-by-bar analysis in Al Brooks style
 */
function analyzeBarByBar(candles: Candle[]): {
  twoBarReversals: Array<{position: number, direction: 'up' | 'down', strength: 'weak' | 'moderate' | 'strong'}>;
  microTrends: Array<{startIndex: number, endIndex: number, direction: 'up' | 'down', strength: 'weak' | 'moderate' | 'strong'}>;
  failedBreakouts: Array<{position: number, level: number, direction: 'up' | 'down'}>;
  strongCloses: Array<{position: number, direction: 'up' | 'down'}>;
} {
  const twoBarReversals: Array<{position: number, direction: 'up' | 'down', strength: 'weak' | 'moderate' | 'strong'}> = [];
  const microTrends = [];
  const failedBreakouts = [];
  const strongCloses = [];

  // Detect two-bar reversals
  for (let i = 1; i < candles.length; i++) {
    const prev = candles[i-1];
    const curr = candles[i];

    // Bullish reversal
    if (prev.close < prev.open && curr.close > curr.open && curr.low <= prev.low) {
      let strength: 'weak' | 'moderate' | 'strong' = 'weak';

      if (curr.close > prev.high) {
        strength = 'strong';
      } else if (curr.close > (prev.high + prev.low) / 2) {
        strength = 'moderate';
      }

      twoBarReversals.push({
        position: i,
        direction: 'up',
        strength
      });
    }

    // Bearish reversal
    if (prev.close > prev.open && curr.close < curr.open && curr.high >= prev.high) {
      let strength: 'weak' | 'moderate' | 'strong' = 'weak';

      if (curr.close < prev.low) {
        strength = 'strong';
      } else if (curr.close < (prev.high + prev.low) / 2) {
        strength = 'moderate';
      }

      twoBarReversals.push({
        position: i,
        direction: 'down',
        strength
      });
    }

    // Detect strong closes (Al Brooks emphasis)
    const bodySize = Math.abs(curr.close - curr.open);
    const range = curr.high - curr.low;

    if (bodySize / range > 0.7) {
      if (curr.close > curr.open && (curr.high - curr.close) / range < 0.1) {
        strongCloses.push({
          position: i,
          direction: 'up'
        });
      } else if (curr.close < curr.open && (curr.close - curr.low) / range < 0.1) {
        strongCloses.push({
          position: i,
          direction: 'down'
        });
      }
    }
  }

  // Detect micro trends (3+ consecutive bars in same direction)
  let currentTrend: {startIndex: number, direction: 'up' | 'down', count: number} | null = null;

  for (let i = 1; i < candles.length; i++) {
    const prev = candles[i-1];
    const curr = candles[i];

    const isBullish = curr.close > curr.open;

    if (!currentTrend) {
      currentTrend = {
        startIndex: i-1,
        direction: isBullish ? 'up' : 'down',
        count: 1
      };
    } else if ((currentTrend.direction === 'up' && isBullish) ||
               (currentTrend.direction === 'down' && !isBullish)) {
      currentTrend.count++;
    } else {
      // Trend changed direction
      if (currentTrend.count >= 3) {
        // Record completed micro trend
        microTrends.push({
          startIndex: currentTrend.startIndex,
          endIndex: i-1,
          direction: currentTrend.direction,
          strength: currentTrend.count >= 5 ? 'strong' :
                   currentTrend.count >= 4 ? 'moderate' : 'weak'
        });
      }

      // Start new trend
      currentTrend = {
        startIndex: i,
        direction: isBullish ? 'up' : 'down',
        count: 1
      };
    }
  }

  // Record final micro trend if it exists
  if (currentTrend && currentTrend.count >= 3) {
    microTrends.push({
      startIndex: currentTrend.startIndex,
      endIndex: candles.length - 1,
      direction: currentTrend.direction,
      strength: currentTrend.count >= 5 ? 'strong' :
               currentTrend.count >= 4 ? 'moderate' : 'weak'
    });
  }

  // Detect failed breakouts
  for (let i = 5; i < candles.length; i++) {
    const curr = candles[i];
    const prev = candles[i-1];

    // Find recent swing high/low
    let recentHigh = -Infinity;
    let recentLow = Infinity;

    for (let j = i-5; j < i; j++) {
      recentHigh = Math.max(recentHigh, candles[j].high);
      recentLow = Math.min(recentLow, candles[j].low);
    }

    // Failed upside breakout
    if (prev.high > recentHigh && curr.close < recentHigh) {
      failedBreakouts.push({
        position: i,
        level: recentHigh,
        direction: 'up'
      });
    }

    // Failed downside breakout
    if (prev.low < recentLow && curr.close > recentLow) {
      failedBreakouts.push({
        position: i,
        level: recentLow,
        direction: 'down'
      });
    }
  }

  return {
    twoBarReversals,
    microTrends,
    failedBreakouts,
    strongCloses
  };
}

/**
 * Analyze momentum characteristics
 */
function analyzeMomentum(candles: Candle[]): {
  buying: Array<{startIndex: number, endIndex: number, strength: 'weak' | 'moderate' | 'strong'}>;
  selling: Array<{startIndex: number, endIndex: number, strength: 'weak' | 'moderate' | 'strong'}>;
  exhaustion: Array<{position: number, direction: 'up' | 'down'}>;
} {
  const buying = [];
  const selling = [];
  const exhaustion = [];

  // Detect momentum moves (consecutive strong bars in same direction)
  let buyingStart = -1;
  let sellingStart = -1;
  let consecutiveBuying = 0;
  let consecutiveSelling = 0;

  for (let i = 0; i < candles.length; i++) {
    const candle = candles[i];
    const bodySize = Math.abs(candle.close - candle.open);
    const range = candle.high - candle.low;
    const bodyToRangeRatio = bodySize / range;

    // Strong bullish candle
    if (candle.close > candle.open && bodyToRangeRatio > 0.6) {
      if (buyingStart === -1) buyingStart = i;
      consecutiveBuying++;
      consecutiveSelling = 0;
      sellingStart = -1;
    }
    // Strong bearish candle
    else if (candle.close < candle.open && bodyToRangeRatio > 0.6) {
      if (sellingStart === -1) sellingStart = i;
      consecutiveSelling++;
      consecutiveBuying = 0;
      buyingStart = -1;
    }
    // Neither strong bullish nor strong bearish
    else {
      // Record buying momentum if it was significant
      if (consecutiveBuying >= 2) {
        buying.push({
          startIndex: buyingStart,
          endIndex: i - 1,
          strength: consecutiveBuying >= 4 ? 'strong' :
                   consecutiveBuying >= 3 ? 'moderate' : 'weak'
        });
      }

      // Record selling momentum if it was significant
      if (consecutiveSelling >= 2) {
        selling.push({
          startIndex: sellingStart,
          endIndex: i - 1,
          strength: consecutiveSelling >= 4 ? 'strong' :
                   consecutiveSelling >= 3 ? 'moderate' : 'weak'
        });
      }

      // Reset counters
      consecutiveBuying = 0;
      consecutiveSelling = 0;
      buyingStart = -1;
      sellingStart = -1;
    }
  }

  // Record final momentum if it exists
  if (consecutiveBuying >= 2) {
    buying.push({
      startIndex: buyingStart,
      endIndex: candles.length - 1,
      strength: consecutiveBuying >= 4 ? 'strong' :
               consecutiveBuying >= 3 ? 'moderate' : 'weak'
    });
  }

  if (consecutiveSelling >= 2) {
    selling.push({
      startIndex: sellingStart,
      endIndex: candles.length - 1,
      strength: consecutiveSelling >= 4 ? 'strong' :
               consecutiveSelling >= 3 ? 'moderate' : 'weak'
    });
  }

  // Detect exhaustion (strong move followed by reversal)
  for (let i = 3; i < candles.length; i++) {
    const curr = candles[i];
    const prev = candles[i-1];
    const prev2 = candles[i-2];
    const prev3 = candles[i-3];

    // Bullish exhaustion (three strong up bars followed by strong down bar)
    if (prev3.close > prev3.open && prev2.close > prev2.open &&
        prev.close > prev.open && curr.close < curr.open) {

      const prevBodySize = prev.close - prev.open;
      const currBodySize = curr.open - curr.close;

      // Current bar must be strong and close below previous bar's midpoint
      if (currBodySize > prevBodySize * 0.8 &&
          curr.close < (prev.high + prev.low) / 2) {
        exhaustion.push({
          position: i,
          direction: 'up'
        });
      }
    }

    // Bearish exhaustion (three strong down bars followed by strong up bar)
    if (prev3.close < prev3.open && prev2.close < prev2.open &&
        prev.close < prev.open && curr.close > curr.open) {

      const prevBodySize = prev.open - prev.close;
      const currBodySize = curr.close - curr.open;

      // Current bar must be strong and close above previous bar's midpoint
      if (currBodySize > prevBodySize * 0.8 &&
          curr.close > (prev.high + prev.low) / 2) {
        exhaustion.push({
          position: i,
          direction: 'down'
        });
      }
    }
  }

  return { buying, selling, exhaustion };
}
