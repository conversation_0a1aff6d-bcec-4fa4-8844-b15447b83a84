import { WhopServerSdk, makeUserTokenVerifier } from '@whop/api';

// Initialize Whop SDK (server-side only)
const whopSdk = WhopServerSdk({
  appId: process.env.VITE_WHOP_APP_ID,
  appApiKey: process.env.WHOP_API_KEY,
  onBehalfOfUserId: process.env.VITE_WHOP_AGENT_USER_ID,
  companyId: process.env.VITE_WHOP_COMPANY_ID,
});

// Create a custom user token verifier as fallback
const userTokenVerifier = makeUserTokenVerifier({
  appId: process.env.VITE_WHOP_APP_ID,
});

export default async (req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, x-whop-user-token');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  try {
    console.log('🔍 Checking for Whop user token in headers...');
    console.log('Headers received:', Object.keys(req.headers));

    // Extract the user token from headers (as per Whop documentation)
    const userToken = req.headers['x-whop-user-token'];

    if (!userToken) {
      console.log('❌ No x-whop-user-token header found');
      return res.status(400).json({
        success: false,
        error: 'No user token provided in headers',
        headers: Object.keys(req.headers),
        debug: 'Try /api/whop/debug-headers to see all headers'
      });
    }

    console.log('🔐 Verifying Whop user token from headers...');
    console.log('Token preview:', userToken.substring(0, 20) + '...');
    console.log('Full user token:', userToken);

    let userId;

    try {
      // Try the main SDK method first
      console.log('🔄 Attempting SDK verifyUserToken...');

      // Create a headers-like object that the SDK expects
      const headersObj = {
        get: (name) => req.headers[name.toLowerCase()],
        has: (name) => name.toLowerCase() in req.headers,
        forEach: (callback) => {
          Object.entries(req.headers).forEach(([key, value]) => {
            callback(value, key);
          });
        }
      };

      const result = await whopSdk.verifyUserToken(headersObj);
      userId = result.userId;
      console.log('✅ SDK verification successful:', userId);
    } catch (sdkError) {
      console.log('⚠️ SDK verification failed, trying custom verifier...', sdkError.message);

      try {
        // Fallback to custom verifier
        const result = await userTokenVerifier(userToken);
        userId = result?.userId; // Custom verifier returns userId field
        console.log('✅ Custom verification successful, userId:', userId);
      } catch (customError) {
        console.error('❌ Both verification methods failed:', {
          sdkError: sdkError.message,
          customError: customError.message
        });
        return res.status(401).json({
          success: false,
          error: 'Token verification failed',
          details: {
            sdkError: sdkError.message,
            customError: customError.message
          }
        });
      }
    }

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Invalid user token - no userId extracted'
      });
    }

    // Get user information from Whop API
    const user = await whopSdk.users.getUser({ userId });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    console.log('✅ Whop user verified:', { userId: user.id, username: user.username });

    res.json({
      success: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        profilePicUrl: user.profilePicture?.url || user.profilePicUrl
      }
    });
  } catch (error) {
    console.error('❌ Error verifying user:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to verify user',
      message: error.message
    });
  }
};
