import { WhopServerSdk } from '@whop/api';

// Initialize Whop SDK (server-side only)
const whopSdk = WhopServerSdk({
  appId: process.env.VITE_WHOP_APP_ID,
  appApiKey: process.env.WHOP_API_KEY,
  onBehalfOfUserId: process.env.VITE_WHOP_AGENT_USER_ID,
  companyId: process.env.VITE_WHOP_COMPANY_ID,
});

export default async (req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, x-whop-user-token');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    });
  }

  try {
    console.log('🧪 Testing Whop connection...');

    // Try to get agent user information
    const agentUserId = process.env.VITE_WHOP_AGENT_USER_ID;
    
    if (!agentUserId) {
      return res.status(400).json({ 
        success: false, 
        error: 'No agent user ID configured' 
      });
    }

    const user = await whopSdk.users.getUser({ userId: agentUserId });

    if (!user) {
      return res.status(404).json({ 
        success: false, 
        error: 'Could not fetch agent user' 
      });
    }

    console.log('✅ Whop connection test successful');

    res.json({
      success: true,
      connected: true,
      agentUser: {
        id: user.id,
        username: user.username
      },
      config: {
        appId: process.env.VITE_WHOP_APP_ID,
        hasApiKey: !!process.env.WHOP_API_KEY,
        agentUserId: process.env.VITE_WHOP_AGENT_USER_ID,
        companyId: process.env.VITE_WHOP_COMPANY_ID
      }
    });
  } catch (error) {
    console.error('❌ Whop connection test failed:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Connection test failed',
      message: error.message 
    });
  }
};
