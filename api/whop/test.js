export default async (req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, x-whop-user-token');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  res.json({
    success: true,
    message: 'Whop API server is working!',
    timestamp: new Date().toISOString(),
    headers: Object.keys(req.headers),
    hasWhopToken: !!req.headers['x-whop-user-token'],
    environment: {
      nodeEnv: process.env.NODE_ENV,
      hasWhopConfig: !!(process.env.WHOP_API_KEY && process.env.VITE_WHOP_APP_ID),
      platform: process.platform,
      nodeVersion: process.version,
      runtime: process.env.VERCEL ? 'vercel' : 'local'
    }
  });
};
