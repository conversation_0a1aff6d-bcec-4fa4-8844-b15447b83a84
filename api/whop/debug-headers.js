export default async (req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, x-whop-user-token');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    });
  }

  console.log('🔍 Debug: All headers received:');
  console.log(JSON.stringify(req.headers, null, 2));

  res.json({
    success: true,
    headers: req.headers,
    hasWhopToken: !!req.headers['x-whop-user-token'],
    whopToken: req.headers['x-whop-user-token'] ? 'Present' : 'Missing'
  });
};
