export default async (req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, x-whop-user-token');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  const whopEnvVars = {};

  // Only show Whop-related environment variables (safely)
  Object.keys(process.env).forEach(key => {
    if (key.includes('WHOP') || key.includes('VITE')) {
      if (key.includes('API_KEY')) {
        whopEnvVars[key] = process.env[key] ? `${process.env[key].substring(0, 10)}...` : 'Missing';
      } else {
        whopEnvVars[key] = process.env[key] || 'Missing';
      }
    }
  });

  res.json({
    success: true,
    timestamp: new Date().toISOString(),
    runtime: process.env.VERCEL ? 'vercel' : 'local',
    nodeVersion: process.version,
    whopEnvironmentVariables: whopEnvVars,
    sdkConfig: {
      appId: process.env.VITE_WHOP_APP_ID || 'Missing',
      hasApiKey: !!process.env.WHOP_API_KEY,
      agentUserId: process.env.VITE_WHOP_AGENT_USER_ID || 'Missing',
      companyId: process.env.VITE_WHOP_COMPANY_ID || 'Missing'
    }
  });
};
