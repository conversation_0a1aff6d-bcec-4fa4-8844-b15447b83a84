// Legacy verify-user endpoint for backward compatibility
// Redirects to the current-user endpoint

export default async (req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, x-whop-user-token');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  // Import the current-user handler
  const { default: currentUserHandler } = await import('./current-user.js');
  
  // Call the current-user handler with the same request/response
  return currentUserHandler(req, res);
};
