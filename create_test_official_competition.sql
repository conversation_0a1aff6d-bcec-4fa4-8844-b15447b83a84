-- Create a test official competition that starts in the future
-- This will trigger the countdown modal for ALL users

-- First, let's use an existing user as the creator (from auth.users table)
-- Option 1: Use the first available user in the system
WITH first_user AS (
  SELECT id FROM auth.users LIMIT 1
)
INSERT INTO paper_trading_competitions (
  id,
  name,
  description,
  creator_id,
  starting_balance,
  max_participants,
  entry_fee,
  prize_pool,
  status,
  competition_start,
  competition_end,
  created_at,
  updated_at,
  whop_company_id,
  whop_business_id,
  whop_business_handle,
  is_cross_community,
  allowed_whop_communities,
  competition_scope
)
SELECT
  gen_random_uuid(),
  'Official Trading Competition',
  'The official paper trading competition. App access is blocked until this competition begins.',
  first_user.id, -- Use existing user
  100000.00,
  10000,
  0.00,
  50000.00,
  'open',
  -- Set competition start time to 5 minutes from now (adjust as needed)
  NOW() + INTERVAL '5 minutes',
  -- Set competition end time to 7 days from start
  NOW() + INTERVAL '7 days 5 minutes',
  NOW(),
  NOW(),
  'official',
  'official',
  'official',
  true,
  ARRAY['official'],
  'public'
FROM first_user;

-- Alternative approach if no users exist yet:
-- First create a system user, then create the competition

/*
-- Option 2: Create a system user first (uncomment if needed)
-- Note: You would typically create users through Supabase Auth, not directly in auth.users
-- This is just for testing purposes
INSERT INTO auth.users (id, email, created_at, updated_at, email_confirmed_at)
VALUES (
  '00000000-0000-0000-0000-000000000001',
  '<EMAIL>',
  NOW(),
  NOW(),
  NOW()
) ON CONFLICT (id) DO NOTHING;

-- Then create the competition with the system user
INSERT INTO paper_trading_competitions (
  id,
  name,
  description,
  creator_id,
  starting_balance,
  max_participants,
  entry_fee,
  prize_pool,
  status,
  competition_start,
  competition_end,
  created_at,
  updated_at,
  whop_company_id,
  whop_business_id,
  whop_business_handle,
  is_cross_community,
  allowed_whop_communities,
  competition_scope
) VALUES (
  gen_random_uuid(),
  'Official Trading Competition',
  'The official paper trading competition. App access is blocked until this competition begins.',
  '00000000-0000-0000-0000-000000000001',
  100000.00,
  10000,
  0.00,
  50000.00,
  'open',
  NOW() + INTERVAL '5 minutes',
  NOW() + INTERVAL '7 days 5 minutes',
  NOW(),
  NOW(),
  'official',
  'official',
  'official',
  true,
  ARRAY['official'],
  'public'
);
*/

-- To test immediately, you can change the competition_start to:
-- NOW() + INTERVAL '30 seconds'  -- for 30 seconds from now
-- NOW() + INTERVAL '2 minutes'   -- for 2 minutes from now
-- NOW() + INTERVAL '1 hour'      -- for 1 hour from now

-- To disable the countdown modal (allow app access), set competition_start to past:
-- NOW() - INTERVAL '1 hour'      -- started 1 hour ago

-- To check if the competition was created successfully:
-- SELECT * FROM paper_trading_competitions WHERE name = 'Official Trading Competition';
